<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\EventController;
use App\Http\Controllers\Api\ReservationController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Public events routes
Route::get('/events', [EventController::class, 'index']);
Route::get('/events/{event}', [EventController::class, 'show']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/me', [AuthController::class, 'me']);
    
    // User reservations
    Route::get('/user/reservations', [ReservationController::class, 'userReservations']);
    Route::post('/events/{event}/reserve', [ReservationController::class, 'store']);
    
    // Organizer routes
    Route::middleware('role:organizer,admin')->group(function () {
        Route::post('/organizer/events', [EventController::class, 'store']);
        Route::get('/organizer/events', [EventController::class, 'organizerEvents']);
        Route::put('/organizer/events/{event}', [EventController::class, 'update']);
        Route::delete('/organizer/events/{event}', [EventController::class, 'destroy']);
        Route::get('/organizer/events/{event}/reservations', [ReservationController::class, 'eventReservations']);
    });
    
    // Admin routes
    Route::middleware('role:admin')->group(function () {
        Route::get('/admin/dashboard', [EventController::class, 'adminDashboard']);
        Route::get('/admin/users', [AuthController::class, 'adminUsers']);
        Route::get('/admin/events', [EventController::class, 'adminEvents']);
        Route::get('/admin/reservations', [ReservationController::class, 'adminReservations']);
    });
});

// Payment routes (will be implemented later)
Route::prefix('payment')->group(function () {
    Route::post('/initiate', function () {
        return response()->json(['message' => 'Payment initiation - To be implemented']);
    });
    Route::post('/success', function () {
        return response()->json(['message' => 'Payment success - To be implemented']);
    });
    Route::post('/failure', function () {
        return response()->json(['message' => 'Payment failure - To be implemented']);
    });
});
