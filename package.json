{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vite": "^6.2.4"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.8", "@vitejs/plugin-vue": "^5.2.4", "vue": "^3.5.16", "vue-router": "^4.5.1"}}