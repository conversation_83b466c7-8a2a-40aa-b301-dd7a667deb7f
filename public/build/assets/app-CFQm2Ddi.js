function Ho(e,t){return function(){return e.apply(t,arguments)}}const{toString:jl}=Object.prototype,{getPrototypeOf:ir}=Object,{iterator:on,toStringTag:qo}=Symbol,ln=(e=>t=>{const s=jl.call(t);return e[s]||(e[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Je=e=>(e=e.toLowerCase(),t=>ln(t)===e),an=e=>t=>typeof t===e,{isArray:rs}=Array,_s=an("undefined");function Nl(e){return e!==null&&!_s(e)&&e.constructor!==null&&!_s(e.constructor)&&Me(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ko=Je("ArrayBuffer");function Fl(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ko(e.buffer),t}const Il=an("string"),Me=an("function"),Wo=an("number"),cn=e=>e!==null&&typeof e=="object",Ul=e=>e===!0||e===!1,Us=e=>{if(ln(e)!=="object")return!1;const t=ir(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(qo in e)&&!(on in e)},Vl=Je("Date"),Bl=Je("File"),Ll=Je("Blob"),zl=Je("FileList"),Hl=e=>cn(e)&&Me(e.pipe),ql=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Me(e.append)&&((t=ln(e))==="formdata"||t==="object"&&Me(e.toString)&&e.toString()==="[object FormData]"))},Kl=Je("URLSearchParams"),[Wl,Jl,Gl,Xl]=["ReadableStream","Request","Response","Headers"].map(Je),Ql=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Os(e,t,{allOwnKeys:s=!1}={}){if(e===null||typeof e>"u")return;let r,n;if(typeof e!="object"&&(e=[e]),rs(e))for(r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else{const i=s?Object.getOwnPropertyNames(e):Object.keys(e),a=i.length;let l;for(r=0;r<a;r++)l=i[r],t.call(null,e[l],l,e)}}function Jo(e,t){t=t.toLowerCase();const s=Object.keys(e);let r=s.length,n;for(;r-- >0;)if(n=s[r],t===n.toLowerCase())return n;return null}const jt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Go=e=>!_s(e)&&e!==jt;function Bn(){const{caseless:e}=Go(this)&&this||{},t={},s=(r,n)=>{const i=e&&Jo(t,n)||n;Us(t[i])&&Us(r)?t[i]=Bn(t[i],r):Us(r)?t[i]=Bn({},r):rs(r)?t[i]=r.slice():t[i]=r};for(let r=0,n=arguments.length;r<n;r++)arguments[r]&&Os(arguments[r],s);return t}const Yl=(e,t,s,{allOwnKeys:r}={})=>(Os(t,(n,i)=>{s&&Me(n)?e[i]=Ho(n,s):e[i]=n},{allOwnKeys:r}),e),Zl=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),ea=(e,t,s,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),s&&Object.assign(e.prototype,s)},ta=(e,t,s,r)=>{let n,i,a;const l={};if(t=t||{},e==null)return t;do{for(n=Object.getOwnPropertyNames(e),i=n.length;i-- >0;)a=n[i],(!r||r(a,e,t))&&!l[a]&&(t[a]=e[a],l[a]=!0);e=s!==!1&&ir(e)}while(e&&(!s||s(e,t))&&e!==Object.prototype);return t},sa=(e,t,s)=>{e=String(e),(s===void 0||s>e.length)&&(s=e.length),s-=t.length;const r=e.indexOf(t,s);return r!==-1&&r===s},na=e=>{if(!e)return null;if(rs(e))return e;let t=e.length;if(!Wo(t))return null;const s=new Array(t);for(;t-- >0;)s[t]=e[t];return s},ra=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ir(Uint8Array)),oa=(e,t)=>{const r=(e&&e[on]).call(e);let n;for(;(n=r.next())&&!n.done;){const i=n.value;t.call(e,i[0],i[1])}},ia=(e,t)=>{let s;const r=[];for(;(s=e.exec(t))!==null;)r.push(s);return r},la=Je("HTMLFormElement"),aa=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,r,n){return r.toUpperCase()+n}),Pr=(({hasOwnProperty:e})=>(t,s)=>e.call(t,s))(Object.prototype),ca=Je("RegExp"),Xo=(e,t)=>{const s=Object.getOwnPropertyDescriptors(e),r={};Os(s,(n,i)=>{let a;(a=t(n,i,e))!==!1&&(r[i]=a||n)}),Object.defineProperties(e,r)},ua=e=>{Xo(e,(t,s)=>{if(Me(e)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const r=e[s];if(Me(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},da=(e,t)=>{const s={},r=n=>{n.forEach(i=>{s[i]=!0})};return rs(e)?r(e):r(String(e).split(t)),s},fa=()=>{},pa=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function ma(e){return!!(e&&Me(e.append)&&e[qo]==="FormData"&&e[on])}const ha=e=>{const t=new Array(10),s=(r,n)=>{if(cn(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[n]=r;const i=rs(r)?[]:{};return Os(r,(a,l)=>{const c=s(a,n+1);!_s(c)&&(i[l]=c)}),t[n]=void 0,i}}return r};return s(e,0)},ga=Je("AsyncFunction"),va=e=>e&&(cn(e)||Me(e))&&Me(e.then)&&Me(e.catch),Qo=((e,t)=>e?setImmediate:t?((s,r)=>(jt.addEventListener("message",({source:n,data:i})=>{n===jt&&i===s&&r.length&&r.shift()()},!1),n=>{r.push(n),jt.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",Me(jt.postMessage)),xa=typeof queueMicrotask<"u"?queueMicrotask.bind(jt):typeof process<"u"&&process.nextTick||Qo,ba=e=>e!=null&&Me(e[on]),b={isArray:rs,isArrayBuffer:Ko,isBuffer:Nl,isFormData:ql,isArrayBufferView:Fl,isString:Il,isNumber:Wo,isBoolean:Ul,isObject:cn,isPlainObject:Us,isReadableStream:Wl,isRequest:Jl,isResponse:Gl,isHeaders:Xl,isUndefined:_s,isDate:Vl,isFile:Bl,isBlob:Ll,isRegExp:ca,isFunction:Me,isStream:Hl,isURLSearchParams:Kl,isTypedArray:ra,isFileList:zl,forEach:Os,merge:Bn,extend:Yl,trim:Ql,stripBOM:Zl,inherits:ea,toFlatObject:ta,kindOf:ln,kindOfTest:Je,endsWith:sa,toArray:na,forEachEntry:oa,matchAll:ia,isHTMLForm:la,hasOwnProperty:Pr,hasOwnProp:Pr,reduceDescriptors:Xo,freezeMethods:ua,toObjectSet:da,toCamelCase:aa,noop:fa,toFiniteNumber:pa,findKey:Jo,global:jt,isContextDefined:Go,isSpecCompliantForm:ma,toJSONObject:ha,isAsyncFn:ga,isThenable:va,setImmediate:Qo,asap:xa,isIterable:ba};function G(e,t,s,r,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),s&&(this.config=s),r&&(this.request=r),n&&(this.response=n,this.status=n.status?n.status:null)}b.inherits(G,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:b.toJSONObject(this.config),code:this.code,status:this.status}}});const Yo=G.prototype,Zo={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Zo[e]={value:e}});Object.defineProperties(G,Zo);Object.defineProperty(Yo,"isAxiosError",{value:!0});G.from=(e,t,s,r,n,i)=>{const a=Object.create(Yo);return b.toFlatObject(e,a,function(c){return c!==Error.prototype},l=>l!=="isAxiosError"),G.call(a,e.message,t,s,r,n),a.cause=e,a.name=e.name,i&&Object.assign(a,i),a};const ya=null;function Ln(e){return b.isPlainObject(e)||b.isArray(e)}function ei(e){return b.endsWith(e,"[]")?e.slice(0,-2):e}function Dr(e,t,s){return e?e.concat(t).map(function(n,i){return n=ei(n),!s&&i?"["+n+"]":n}).join(s?".":""):t}function wa(e){return b.isArray(e)&&!e.some(Ln)}const _a=b.toFlatObject(b,{},null,function(t){return/^is[A-Z]/.test(t)});function un(e,t,s){if(!b.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,s=b.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,C){return!b.isUndefined(C[w])});const r=s.metaTokens,n=s.visitor||u,i=s.dots,a=s.indexes,c=(s.Blob||typeof Blob<"u"&&Blob)&&b.isSpecCompliantForm(t);if(!b.isFunction(n))throw new TypeError("visitor must be a function");function d(h){if(h===null)return"";if(b.isDate(h))return h.toISOString();if(!c&&b.isBlob(h))throw new G("Blob is not supported. Use a Buffer instead.");return b.isArrayBuffer(h)||b.isTypedArray(h)?c&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function u(h,w,C){let O=h;if(h&&!C&&typeof h=="object"){if(b.endsWith(w,"{}"))w=r?w:w.slice(0,-2),h=JSON.stringify(h);else if(b.isArray(h)&&wa(h)||(b.isFileList(h)||b.endsWith(w,"[]"))&&(O=b.toArray(h)))return w=ei(w),O.forEach(function(j,I){!(b.isUndefined(j)||j===null)&&t.append(a===!0?Dr([w],I,i):a===null?w:w+"[]",d(j))}),!1}return Ln(h)?!0:(t.append(Dr(C,w,i),d(h)),!1)}const f=[],g=Object.assign(_a,{defaultVisitor:u,convertValue:d,isVisitable:Ln});function v(h,w){if(!b.isUndefined(h)){if(f.indexOf(h)!==-1)throw Error("Circular reference detected in "+w.join("."));f.push(h),b.forEach(h,function(O,$){(!(b.isUndefined(O)||O===null)&&n.call(t,O,b.isString($)?$.trim():$,w,g))===!0&&v(O,w?w.concat($):[$])}),f.pop()}}if(!b.isObject(e))throw new TypeError("data must be an object");return v(e),t}function jr(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function lr(e,t){this._pairs=[],e&&un(e,this,t)}const ti=lr.prototype;ti.append=function(t,s){this._pairs.push([t,s])};ti.toString=function(t){const s=t?function(r){return t.call(this,r,jr)}:jr;return this._pairs.map(function(n){return s(n[0])+"="+s(n[1])},"").join("&")};function ka(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function si(e,t,s){if(!t)return e;const r=s&&s.encode||ka;b.isFunction(s)&&(s={serialize:s});const n=s&&s.serialize;let i;if(n?i=n(t,s):i=b.isURLSearchParams(t)?t.toString():new lr(t,s).toString(r),i){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class Nr{constructor(){this.handlers=[]}use(t,s,r){return this.handlers.push({fulfilled:t,rejected:s,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){b.forEach(this.handlers,function(r){r!==null&&t(r)})}}const ni={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ea=typeof URLSearchParams<"u"?URLSearchParams:lr,Sa=typeof FormData<"u"?FormData:null,Ca=typeof Blob<"u"?Blob:null,Ra={isBrowser:!0,classes:{URLSearchParams:Ea,FormData:Sa,Blob:Ca},protocols:["http","https","file","blob","url","data"]},ar=typeof window<"u"&&typeof document<"u",zn=typeof navigator=="object"&&navigator||void 0,Ta=ar&&(!zn||["ReactNative","NativeScript","NS"].indexOf(zn.product)<0),Aa=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Oa=ar&&window.location.href||"http://localhost",$a=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ar,hasStandardBrowserEnv:Ta,hasStandardBrowserWebWorkerEnv:Aa,navigator:zn,origin:Oa},Symbol.toStringTag,{value:"Module"})),we={...$a,...Ra};function Ma(e,t){return un(e,new we.classes.URLSearchParams,Object.assign({visitor:function(s,r,n,i){return we.isNode&&b.isBuffer(s)?(this.append(r,s.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function Pa(e){return b.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Da(e){const t={},s=Object.keys(e);let r;const n=s.length;let i;for(r=0;r<n;r++)i=s[r],t[i]=e[i];return t}function ri(e){function t(s,r,n,i){let a=s[i++];if(a==="__proto__")return!0;const l=Number.isFinite(+a),c=i>=s.length;return a=!a&&b.isArray(n)?n.length:a,c?(b.hasOwnProp(n,a)?n[a]=[n[a],r]:n[a]=r,!l):((!n[a]||!b.isObject(n[a]))&&(n[a]=[]),t(s,r,n[a],i)&&b.isArray(n[a])&&(n[a]=Da(n[a])),!l)}if(b.isFormData(e)&&b.isFunction(e.entries)){const s={};return b.forEachEntry(e,(r,n)=>{t(Pa(r),n,s,0)}),s}return null}function ja(e,t,s){if(b.isString(e))try{return(t||JSON.parse)(e),b.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(s||JSON.stringify)(e)}const $s={transitional:ni,adapter:["xhr","http","fetch"],transformRequest:[function(t,s){const r=s.getContentType()||"",n=r.indexOf("application/json")>-1,i=b.isObject(t);if(i&&b.isHTMLForm(t)&&(t=new FormData(t)),b.isFormData(t))return n?JSON.stringify(ri(t)):t;if(b.isArrayBuffer(t)||b.isBuffer(t)||b.isStream(t)||b.isFile(t)||b.isBlob(t)||b.isReadableStream(t))return t;if(b.isArrayBufferView(t))return t.buffer;if(b.isURLSearchParams(t))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(i){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Ma(t,this.formSerializer).toString();if((l=b.isFileList(t))||r.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return un(l?{"files[]":t}:t,c&&new c,this.formSerializer)}}return i||n?(s.setContentType("application/json",!1),ja(t)):t}],transformResponse:[function(t){const s=this.transitional||$s.transitional,r=s&&s.forcedJSONParsing,n=this.responseType==="json";if(b.isResponse(t)||b.isReadableStream(t))return t;if(t&&b.isString(t)&&(r&&!this.responseType||n)){const a=!(s&&s.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(l){if(a)throw l.name==="SyntaxError"?G.from(l,G.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:we.classes.FormData,Blob:we.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};b.forEach(["delete","get","head","post","put","patch"],e=>{$s.headers[e]={}});const Na=b.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Fa=e=>{const t={};let s,r,n;return e&&e.split(`
`).forEach(function(a){n=a.indexOf(":"),s=a.substring(0,n).trim().toLowerCase(),r=a.substring(n+1).trim(),!(!s||t[s]&&Na[s])&&(s==="set-cookie"?t[s]?t[s].push(r):t[s]=[r]:t[s]=t[s]?t[s]+", "+r:r)}),t},Fr=Symbol("internals");function cs(e){return e&&String(e).trim().toLowerCase()}function Vs(e){return e===!1||e==null?e:b.isArray(e)?e.map(Vs):String(e)}function Ia(e){const t=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=s.exec(e);)t[r[1]]=r[2];return t}const Ua=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Cn(e,t,s,r,n){if(b.isFunction(r))return r.call(this,t,s);if(n&&(t=s),!!b.isString(t)){if(b.isString(r))return t.indexOf(r)!==-1;if(b.isRegExp(r))return r.test(t)}}function Va(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,s,r)=>s.toUpperCase()+r)}function Ba(e,t){const s=b.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+s,{value:function(n,i,a){return this[r].call(this,t,n,i,a)},configurable:!0})})}let Pe=class{constructor(t){t&&this.set(t)}set(t,s,r){const n=this;function i(l,c,d){const u=cs(c);if(!u)throw new Error("header name must be a non-empty string");const f=b.findKey(n,u);(!f||n[f]===void 0||d===!0||d===void 0&&n[f]!==!1)&&(n[f||c]=Vs(l))}const a=(l,c)=>b.forEach(l,(d,u)=>i(d,u,c));if(b.isPlainObject(t)||t instanceof this.constructor)a(t,s);else if(b.isString(t)&&(t=t.trim())&&!Ua(t))a(Fa(t),s);else if(b.isObject(t)&&b.isIterable(t)){let l={},c,d;for(const u of t){if(!b.isArray(u))throw TypeError("Object iterator must return a key-value pair");l[d=u[0]]=(c=l[d])?b.isArray(c)?[...c,u[1]]:[c,u[1]]:u[1]}a(l,s)}else t!=null&&i(s,t,r);return this}get(t,s){if(t=cs(t),t){const r=b.findKey(this,t);if(r){const n=this[r];if(!s)return n;if(s===!0)return Ia(n);if(b.isFunction(s))return s.call(this,n,r);if(b.isRegExp(s))return s.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,s){if(t=cs(t),t){const r=b.findKey(this,t);return!!(r&&this[r]!==void 0&&(!s||Cn(this,this[r],r,s)))}return!1}delete(t,s){const r=this;let n=!1;function i(a){if(a=cs(a),a){const l=b.findKey(r,a);l&&(!s||Cn(r,r[l],l,s))&&(delete r[l],n=!0)}}return b.isArray(t)?t.forEach(i):i(t),n}clear(t){const s=Object.keys(this);let r=s.length,n=!1;for(;r--;){const i=s[r];(!t||Cn(this,this[i],i,t,!0))&&(delete this[i],n=!0)}return n}normalize(t){const s=this,r={};return b.forEach(this,(n,i)=>{const a=b.findKey(r,i);if(a){s[a]=Vs(n),delete s[i];return}const l=t?Va(i):String(i).trim();l!==i&&delete s[i],s[l]=Vs(n),r[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const s=Object.create(null);return b.forEach(this,(r,n)=>{r!=null&&r!==!1&&(s[n]=t&&b.isArray(r)?r.join(", "):r)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,s])=>t+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...s){const r=new this(t);return s.forEach(n=>r.set(n)),r}static accessor(t){const r=(this[Fr]=this[Fr]={accessors:{}}).accessors,n=this.prototype;function i(a){const l=cs(a);r[l]||(Ba(n,a),r[l]=!0)}return b.isArray(t)?t.forEach(i):i(t),this}};Pe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);b.reduceDescriptors(Pe.prototype,({value:e},t)=>{let s=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[s]=r}}});b.freezeMethods(Pe);function Rn(e,t){const s=this||$s,r=t||s,n=Pe.from(r.headers);let i=r.data;return b.forEach(e,function(l){i=l.call(s,i,n.normalize(),t?t.status:void 0)}),n.normalize(),i}function oi(e){return!!(e&&e.__CANCEL__)}function os(e,t,s){G.call(this,e??"canceled",G.ERR_CANCELED,t,s),this.name="CanceledError"}b.inherits(os,G,{__CANCEL__:!0});function ii(e,t,s){const r=s.config.validateStatus;!s.status||!r||r(s.status)?e(s):t(new G("Request failed with status code "+s.status,[G.ERR_BAD_REQUEST,G.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function La(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function za(e,t){e=e||10;const s=new Array(e),r=new Array(e);let n=0,i=0,a;return t=t!==void 0?t:1e3,function(c){const d=Date.now(),u=r[i];a||(a=d),s[n]=c,r[n]=d;let f=i,g=0;for(;f!==n;)g+=s[f++],f=f%e;if(n=(n+1)%e,n===i&&(i=(i+1)%e),d-a<t)return;const v=u&&d-u;return v?Math.round(g*1e3/v):void 0}}function Ha(e,t){let s=0,r=1e3/t,n,i;const a=(d,u=Date.now())=>{s=u,n=null,i&&(clearTimeout(i),i=null),e.apply(null,d)};return[(...d)=>{const u=Date.now(),f=u-s;f>=r?a(d,u):(n=d,i||(i=setTimeout(()=>{i=null,a(n)},r-f)))},()=>n&&a(n)]}const Js=(e,t,s=3)=>{let r=0;const n=za(50,250);return Ha(i=>{const a=i.loaded,l=i.lengthComputable?i.total:void 0,c=a-r,d=n(c),u=a<=l;r=a;const f={loaded:a,total:l,progress:l?a/l:void 0,bytes:c,rate:d||void 0,estimated:d&&l&&u?(l-a)/d:void 0,event:i,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},s)},Ir=(e,t)=>{const s=e!=null;return[r=>t[0]({lengthComputable:s,total:e,loaded:r}),t[1]]},Ur=e=>(...t)=>b.asap(()=>e(...t)),qa=we.hasStandardBrowserEnv?((e,t)=>s=>(s=new URL(s,we.origin),e.protocol===s.protocol&&e.host===s.host&&(t||e.port===s.port)))(new URL(we.origin),we.navigator&&/(msie|trident)/i.test(we.navigator.userAgent)):()=>!0,Ka=we.hasStandardBrowserEnv?{write(e,t,s,r,n,i){const a=[e+"="+encodeURIComponent(t)];b.isNumber(s)&&a.push("expires="+new Date(s).toGMTString()),b.isString(r)&&a.push("path="+r),b.isString(n)&&a.push("domain="+n),i===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Wa(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Ja(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function li(e,t,s){let r=!Wa(t);return e&&(r||s==!1)?Ja(e,t):t}const Vr=e=>e instanceof Pe?{...e}:e;function It(e,t){t=t||{};const s={};function r(d,u,f,g){return b.isPlainObject(d)&&b.isPlainObject(u)?b.merge.call({caseless:g},d,u):b.isPlainObject(u)?b.merge({},u):b.isArray(u)?u.slice():u}function n(d,u,f,g){if(b.isUndefined(u)){if(!b.isUndefined(d))return r(void 0,d,f,g)}else return r(d,u,f,g)}function i(d,u){if(!b.isUndefined(u))return r(void 0,u)}function a(d,u){if(b.isUndefined(u)){if(!b.isUndefined(d))return r(void 0,d)}else return r(void 0,u)}function l(d,u,f){if(f in t)return r(d,u);if(f in e)return r(void 0,d)}const c={url:i,method:i,data:i,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:l,headers:(d,u,f)=>n(Vr(d),Vr(u),f,!0)};return b.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=c[u]||n,g=f(e[u],t[u],u);b.isUndefined(g)&&f!==l||(s[u]=g)}),s}const ai=e=>{const t=It({},e);let{data:s,withXSRFToken:r,xsrfHeaderName:n,xsrfCookieName:i,headers:a,auth:l}=t;t.headers=a=Pe.from(a),t.url=si(li(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&a.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let c;if(b.isFormData(s)){if(we.hasStandardBrowserEnv||we.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((c=a.getContentType())!==!1){const[d,...u]=c?c.split(";").map(f=>f.trim()).filter(Boolean):[];a.setContentType([d||"multipart/form-data",...u].join("; "))}}if(we.hasStandardBrowserEnv&&(r&&b.isFunction(r)&&(r=r(t)),r||r!==!1&&qa(t.url))){const d=n&&i&&Ka.read(i);d&&a.set(n,d)}return t},Ga=typeof XMLHttpRequest<"u",Xa=Ga&&function(e){return new Promise(function(s,r){const n=ai(e);let i=n.data;const a=Pe.from(n.headers).normalize();let{responseType:l,onUploadProgress:c,onDownloadProgress:d}=n,u,f,g,v,h;function w(){v&&v(),h&&h(),n.cancelToken&&n.cancelToken.unsubscribe(u),n.signal&&n.signal.removeEventListener("abort",u)}let C=new XMLHttpRequest;C.open(n.method.toUpperCase(),n.url,!0),C.timeout=n.timeout;function O(){if(!C)return;const j=Pe.from("getAllResponseHeaders"in C&&C.getAllResponseHeaders()),J={data:!l||l==="text"||l==="json"?C.responseText:C.response,status:C.status,statusText:C.statusText,headers:j,config:e,request:C};ii(function(Y){s(Y),w()},function(Y){r(Y),w()},J),C=null}"onloadend"in C?C.onloadend=O:C.onreadystatechange=function(){!C||C.readyState!==4||C.status===0&&!(C.responseURL&&C.responseURL.indexOf("file:")===0)||setTimeout(O)},C.onabort=function(){C&&(r(new G("Request aborted",G.ECONNABORTED,e,C)),C=null)},C.onerror=function(){r(new G("Network Error",G.ERR_NETWORK,e,C)),C=null},C.ontimeout=function(){let I=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const J=n.transitional||ni;n.timeoutErrorMessage&&(I=n.timeoutErrorMessage),r(new G(I,J.clarifyTimeoutError?G.ETIMEDOUT:G.ECONNABORTED,e,C)),C=null},i===void 0&&a.setContentType(null),"setRequestHeader"in C&&b.forEach(a.toJSON(),function(I,J){C.setRequestHeader(J,I)}),b.isUndefined(n.withCredentials)||(C.withCredentials=!!n.withCredentials),l&&l!=="json"&&(C.responseType=n.responseType),d&&([g,h]=Js(d,!0),C.addEventListener("progress",g)),c&&C.upload&&([f,v]=Js(c),C.upload.addEventListener("progress",f),C.upload.addEventListener("loadend",v)),(n.cancelToken||n.signal)&&(u=j=>{C&&(r(!j||j.type?new os(null,e,C):j),C.abort(),C=null)},n.cancelToken&&n.cancelToken.subscribe(u),n.signal&&(n.signal.aborted?u():n.signal.addEventListener("abort",u)));const $=La(n.url);if($&&we.protocols.indexOf($)===-1){r(new G("Unsupported protocol "+$+":",G.ERR_BAD_REQUEST,e));return}C.send(i||null)})},Qa=(e,t)=>{const{length:s}=e=e?e.filter(Boolean):[];if(t||s){let r=new AbortController,n;const i=function(d){if(!n){n=!0,l();const u=d instanceof Error?d:this.reason;r.abort(u instanceof G?u:new os(u instanceof Error?u.message:u))}};let a=t&&setTimeout(()=>{a=null,i(new G(`timeout ${t} of ms exceeded`,G.ETIMEDOUT))},t);const l=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(d=>{d.unsubscribe?d.unsubscribe(i):d.removeEventListener("abort",i)}),e=null)};e.forEach(d=>d.addEventListener("abort",i));const{signal:c}=r;return c.unsubscribe=()=>b.asap(l),c}},Ya=function*(e,t){let s=e.byteLength;if(s<t){yield e;return}let r=0,n;for(;r<s;)n=r+t,yield e.slice(r,n),r=n},Za=async function*(e,t){for await(const s of ec(e))yield*Ya(s,t)},ec=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:s,value:r}=await t.read();if(s)break;yield r}}finally{await t.cancel()}},Br=(e,t,s,r)=>{const n=Za(e,t);let i=0,a,l=c=>{a||(a=!0,r&&r(c))};return new ReadableStream({async pull(c){try{const{done:d,value:u}=await n.next();if(d){l(),c.close();return}let f=u.byteLength;if(s){let g=i+=f;s(g)}c.enqueue(new Uint8Array(u))}catch(d){throw l(d),d}},cancel(c){return l(c),n.return()}},{highWaterMark:2})},dn=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ci=dn&&typeof ReadableStream=="function",tc=dn&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ui=(e,...t)=>{try{return!!e(...t)}catch{return!1}},sc=ci&&ui(()=>{let e=!1;const t=new Request(we.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Lr=64*1024,Hn=ci&&ui(()=>b.isReadableStream(new Response("").body)),Gs={stream:Hn&&(e=>e.body)};dn&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Gs[t]&&(Gs[t]=b.isFunction(e[t])?s=>s[t]():(s,r)=>{throw new G(`Response type '${t}' is not supported`,G.ERR_NOT_SUPPORT,r)})})})(new Response);const nc=async e=>{if(e==null)return 0;if(b.isBlob(e))return e.size;if(b.isSpecCompliantForm(e))return(await new Request(we.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(b.isArrayBufferView(e)||b.isArrayBuffer(e))return e.byteLength;if(b.isURLSearchParams(e)&&(e=e+""),b.isString(e))return(await tc(e)).byteLength},rc=async(e,t)=>{const s=b.toFiniteNumber(e.getContentLength());return s??nc(t)},oc=dn&&(async e=>{let{url:t,method:s,data:r,signal:n,cancelToken:i,timeout:a,onDownloadProgress:l,onUploadProgress:c,responseType:d,headers:u,withCredentials:f="same-origin",fetchOptions:g}=ai(e);d=d?(d+"").toLowerCase():"text";let v=Qa([n,i&&i.toAbortSignal()],a),h;const w=v&&v.unsubscribe&&(()=>{v.unsubscribe()});let C;try{if(c&&sc&&s!=="get"&&s!=="head"&&(C=await rc(u,r))!==0){let J=new Request(t,{method:"POST",body:r,duplex:"half"}),ce;if(b.isFormData(r)&&(ce=J.headers.get("content-type"))&&u.setContentType(ce),J.body){const[Y,Se]=Ir(C,Js(Ur(c)));r=Br(J.body,Lr,Y,Se)}}b.isString(f)||(f=f?"include":"omit");const O="credentials"in Request.prototype;h=new Request(t,{...g,signal:v,method:s.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:O?f:void 0});let $=await fetch(h);const j=Hn&&(d==="stream"||d==="response");if(Hn&&(l||j&&w)){const J={};["status","statusText","headers"].forEach(He=>{J[He]=$[He]});const ce=b.toFiniteNumber($.headers.get("content-length")),[Y,Se]=l&&Ir(ce,Js(Ur(l),!0))||[];$=new Response(Br($.body,Lr,Y,()=>{Se&&Se(),w&&w()}),J)}d=d||"text";let I=await Gs[b.findKey(Gs,d)||"text"]($,e);return!j&&w&&w(),await new Promise((J,ce)=>{ii(J,ce,{data:I,headers:Pe.from($.headers),status:$.status,statusText:$.statusText,config:e,request:h})})}catch(O){throw w&&w(),O&&O.name==="TypeError"&&/Load failed|fetch/i.test(O.message)?Object.assign(new G("Network Error",G.ERR_NETWORK,e,h),{cause:O.cause||O}):G.from(O,O&&O.code,e,h)}}),qn={http:ya,xhr:Xa,fetch:oc};b.forEach(qn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const zr=e=>`- ${e}`,ic=e=>b.isFunction(e)||e===null||e===!1,di={getAdapter:e=>{e=b.isArray(e)?e:[e];const{length:t}=e;let s,r;const n={};for(let i=0;i<t;i++){s=e[i];let a;if(r=s,!ic(s)&&(r=qn[(a=String(s)).toLowerCase()],r===void 0))throw new G(`Unknown adapter '${a}'`);if(r)break;n[a||"#"+i]=r}if(!r){const i=Object.entries(n).map(([l,c])=>`adapter ${l} `+(c===!1?"is not supported by the environment":"is not available in the build"));let a=t?i.length>1?`since :
`+i.map(zr).join(`
`):" "+zr(i[0]):"as no adapter specified";throw new G("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return r},adapters:qn};function Tn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new os(null,e)}function Hr(e){return Tn(e),e.headers=Pe.from(e.headers),e.data=Rn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),di.getAdapter(e.adapter||$s.adapter)(e).then(function(r){return Tn(e),r.data=Rn.call(e,e.transformResponse,r),r.headers=Pe.from(r.headers),r},function(r){return oi(r)||(Tn(e),r&&r.response&&(r.response.data=Rn.call(e,e.transformResponse,r.response),r.response.headers=Pe.from(r.response.headers))),Promise.reject(r)})}const fi="1.9.0",fn={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{fn[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const qr={};fn.transitional=function(t,s,r){function n(i,a){return"[Axios v"+fi+"] Transitional option '"+i+"'"+a+(r?". "+r:"")}return(i,a,l)=>{if(t===!1)throw new G(n(a," has been removed"+(s?" in "+s:"")),G.ERR_DEPRECATED);return s&&!qr[a]&&(qr[a]=!0,console.warn(n(a," has been deprecated since v"+s+" and will be removed in the near future"))),t?t(i,a,l):!0}};fn.spelling=function(t){return(s,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function lc(e,t,s){if(typeof e!="object")throw new G("options must be an object",G.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let n=r.length;for(;n-- >0;){const i=r[n],a=t[i];if(a){const l=e[i],c=l===void 0||a(l,i,e);if(c!==!0)throw new G("option "+i+" must be "+c,G.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new G("Unknown option "+i,G.ERR_BAD_OPTION)}}const Bs={assertOptions:lc,validators:fn},st=Bs.validators;let Nt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Nr,response:new Nr}}async request(t,s){try{return await this._request(t,s)}catch(r){if(r instanceof Error){let n={};Error.captureStackTrace?Error.captureStackTrace(n):n=new Error;const i=n.stack?n.stack.replace(/^.+\n/,""):"";try{r.stack?i&&!String(r.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+i):r.stack=i}catch{}}throw r}}_request(t,s){typeof t=="string"?(s=s||{},s.url=t):s=t||{},s=It(this.defaults,s);const{transitional:r,paramsSerializer:n,headers:i}=s;r!==void 0&&Bs.assertOptions(r,{silentJSONParsing:st.transitional(st.boolean),forcedJSONParsing:st.transitional(st.boolean),clarifyTimeoutError:st.transitional(st.boolean)},!1),n!=null&&(b.isFunction(n)?s.paramsSerializer={serialize:n}:Bs.assertOptions(n,{encode:st.function,serialize:st.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Bs.assertOptions(s,{baseUrl:st.spelling("baseURL"),withXsrfToken:st.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let a=i&&b.merge(i.common,i[s.method]);i&&b.forEach(["delete","get","head","post","put","patch","common"],h=>{delete i[h]}),s.headers=Pe.concat(a,i);const l=[];let c=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(s)===!1||(c=c&&w.synchronous,l.unshift(w.fulfilled,w.rejected))});const d=[];this.interceptors.response.forEach(function(w){d.push(w.fulfilled,w.rejected)});let u,f=0,g;if(!c){const h=[Hr.bind(this),void 0];for(h.unshift.apply(h,l),h.push.apply(h,d),g=h.length,u=Promise.resolve(s);f<g;)u=u.then(h[f++],h[f++]);return u}g=l.length;let v=s;for(f=0;f<g;){const h=l[f++],w=l[f++];try{v=h(v)}catch(C){w.call(this,C);break}}try{u=Hr.call(this,v)}catch(h){return Promise.reject(h)}for(f=0,g=d.length;f<g;)u=u.then(d[f++],d[f++]);return u}getUri(t){t=It(this.defaults,t);const s=li(t.baseURL,t.url,t.allowAbsoluteUrls);return si(s,t.params,t.paramsSerializer)}};b.forEach(["delete","get","head","options"],function(t){Nt.prototype[t]=function(s,r){return this.request(It(r||{},{method:t,url:s,data:(r||{}).data}))}});b.forEach(["post","put","patch"],function(t){function s(r){return function(i,a,l){return this.request(It(l||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:i,data:a}))}}Nt.prototype[t]=s(),Nt.prototype[t+"Form"]=s(!0)});let ac=class pi{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(i){s=i});const r=this;this.promise.then(n=>{if(!r._listeners)return;let i=r._listeners.length;for(;i-- >0;)r._listeners[i](n);r._listeners=null}),this.promise.then=n=>{let i;const a=new Promise(l=>{r.subscribe(l),i=l}).then(n);return a.cancel=function(){r.unsubscribe(i)},a},t(function(i,a,l){r.reason||(r.reason=new os(i,a,l),s(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const s=this._listeners.indexOf(t);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const t=new AbortController,s=r=>{t.abort(r)};return this.subscribe(s),t.signal.unsubscribe=()=>this.unsubscribe(s),t.signal}static source(){let t;return{token:new pi(function(n){t=n}),cancel:t}}};function cc(e){return function(s){return e.apply(null,s)}}function uc(e){return b.isObject(e)&&e.isAxiosError===!0}const Kn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Kn).forEach(([e,t])=>{Kn[t]=e});function mi(e){const t=new Nt(e),s=Ho(Nt.prototype.request,t);return b.extend(s,Nt.prototype,t,{allOwnKeys:!0}),b.extend(s,t,null,{allOwnKeys:!0}),s.create=function(n){return mi(It(e,n))},s}const pe=mi($s);pe.Axios=Nt;pe.CanceledError=os;pe.CancelToken=ac;pe.isCancel=oi;pe.VERSION=fi;pe.toFormData=un;pe.AxiosError=G;pe.Cancel=pe.CanceledError;pe.all=function(t){return Promise.all(t)};pe.spread=cc;pe.isAxiosError=uc;pe.mergeConfig=It;pe.AxiosHeaders=Pe;pe.formToJSON=e=>ri(b.isHTMLForm(e)?new FormData(e):e);pe.getAdapter=di.getAdapter;pe.HttpStatusCode=Kn;pe.default=pe;const{Axios:Zv,AxiosError:ex,CanceledError:tx,isCancel:sx,CancelToken:nx,VERSION:rx,all:ox,Cancel:ix,isAxiosError:lx,spread:ax,toFormData:cx,AxiosHeaders:ux,HttpStatusCode:dx,formToJSON:fx,getAdapter:px,mergeConfig:mx}=pe;window.axios=pe;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function cr(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const le={},Wt=[],it=()=>{},dc=()=>!1,pn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ur=e=>e.startsWith("onUpdate:"),Ee=Object.assign,dr=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},fc=Object.prototype.hasOwnProperty,se=(e,t)=>fc.call(e,t),z=Array.isArray,Jt=e=>Ms(e)==="[object Map]",is=e=>Ms(e)==="[object Set]",Kr=e=>Ms(e)==="[object Date]",W=e=>typeof e=="function",ve=e=>typeof e=="string",lt=e=>typeof e=="symbol",me=e=>e!==null&&typeof e=="object",hi=e=>(me(e)||W(e))&&W(e.then)&&W(e.catch),gi=Object.prototype.toString,Ms=e=>gi.call(e),pc=e=>Ms(e).slice(8,-1),vi=e=>Ms(e)==="[object Object]",fr=e=>ve(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ms=cr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),mn=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},mc=/-(\w)/g,ze=mn(e=>e.replace(mc,(t,s)=>s?s.toUpperCase():"")),hc=/\B([A-Z])/g,Vt=mn(e=>e.replace(hc,"-$1").toLowerCase()),hn=mn(e=>e.charAt(0).toUpperCase()+e.slice(1)),An=mn(e=>e?`on${hn(e)}`:""),Rt=(e,t)=>!Object.is(e,t),Ls=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},xi=(e,t,s,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:s})},Xs=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Wr;const gn=()=>Wr||(Wr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function pr(e){if(z(e)){const t={};for(let s=0;s<e.length;s++){const r=e[s],n=ve(r)?bc(r):pr(r);if(n)for(const i in n)t[i]=n[i]}return t}else if(ve(e)||me(e))return e}const gc=/;(?![^(]*\))/g,vc=/:([^]+)/,xc=/\/\*[^]*?\*\//g;function bc(e){const t={};return e.replace(xc,"").split(gc).forEach(s=>{if(s){const r=s.split(vc);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function ae(e){let t="";if(ve(e))t=e;else if(z(e))for(let s=0;s<e.length;s++){const r=ae(e[s]);r&&(t+=r+" ")}else if(me(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const yc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",wc=cr(yc);function bi(e){return!!e||e===""}function _c(e,t){if(e.length!==t.length)return!1;let s=!0;for(let r=0;s&&r<e.length;r++)s=Ut(e[r],t[r]);return s}function Ut(e,t){if(e===t)return!0;let s=Kr(e),r=Kr(t);if(s||r)return s&&r?e.getTime()===t.getTime():!1;if(s=lt(e),r=lt(t),s||r)return e===t;if(s=z(e),r=z(t),s||r)return s&&r?_c(e,t):!1;if(s=me(e),r=me(t),s||r){if(!s||!r)return!1;const n=Object.keys(e).length,i=Object.keys(t).length;if(n!==i)return!1;for(const a in e){const l=e.hasOwnProperty(a),c=t.hasOwnProperty(a);if(l&&!c||!l&&c||!Ut(e[a],t[a]))return!1}}return String(e)===String(t)}function mr(e,t){return e.findIndex(s=>Ut(s,t))}const yi=e=>!!(e&&e.__v_isRef===!0),y=e=>ve(e)?e:e==null?"":z(e)||me(e)&&(e.toString===gi||!W(e.toString))?yi(e)?y(e.value):JSON.stringify(e,wi,2):String(e),wi=(e,t)=>yi(t)?wi(e,t.value):Jt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[r,n],i)=>(s[On(r,i)+" =>"]=n,s),{})}:is(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>On(s))}:lt(t)?On(t):me(t)&&!z(t)&&!vi(t)?String(t):t,On=(e,t="")=>{var s;return lt(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let $e;class kc{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=$e,!t&&$e&&(this.index=($e.scopes||($e.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=$e;try{return $e=this,t()}finally{$e=s}}}on(){++this._on===1&&(this.prevScope=$e,$e=this)}off(){this._on>0&&--this._on===0&&($e=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,r;for(s=0,r=this.effects.length;s<r;s++)this.effects[s].stop();for(this.effects.length=0,s=0,r=this.cleanups.length;s<r;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,r=this.scopes.length;s<r;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function Ec(){return $e}let de;const $n=new WeakSet;class _i{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,$e&&$e.active&&$e.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,$n.has(this)&&($n.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ei(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Jr(this),Si(this);const t=de,s=Ke;de=this,Ke=!0;try{return this.fn()}finally{Ci(this),de=t,Ke=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)vr(t);this.deps=this.depsTail=void 0,Jr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?$n.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Wn(this)&&this.run()}get dirty(){return Wn(this)}}let ki=0,hs,gs;function Ei(e,t=!1){if(e.flags|=8,t){e.next=gs,gs=e;return}e.next=hs,hs=e}function hr(){ki++}function gr(){if(--ki>0)return;if(gs){let t=gs;for(gs=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;hs;){let t=hs;for(hs=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=s}}if(e)throw e}function Si(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ci(e){let t,s=e.depsTail,r=s;for(;r;){const n=r.prevDep;r.version===-1?(r===s&&(s=n),vr(r),Sc(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=n}e.deps=t,e.depsTail=s}function Wn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ri(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ri(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===ks)||(e.globalVersion=ks,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Wn(e))))return;e.flags|=2;const t=e.dep,s=de,r=Ke;de=e,Ke=!0;try{Si(e);const n=e.fn(e._value);(t.version===0||Rt(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(n){throw t.version++,n}finally{de=s,Ke=r,Ci(e),e.flags&=-3}}function vr(e,t=!1){const{dep:s,prevSub:r,nextSub:n}=e;if(r&&(r.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=r,e.nextSub=void 0),s.subs===e&&(s.subs=r,!r&&s.computed)){s.computed.flags&=-5;for(let i=s.computed.deps;i;i=i.nextDep)vr(i,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function Sc(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Ke=!0;const Ti=[];function xt(){Ti.push(Ke),Ke=!1}function bt(){const e=Ti.pop();Ke=e===void 0?!0:e}function Jr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=de;de=void 0;try{t()}finally{de=s}}}let ks=0;class Cc{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class xr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!de||!Ke||de===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==de)s=this.activeLink=new Cc(de,this),de.deps?(s.prevDep=de.depsTail,de.depsTail.nextDep=s,de.depsTail=s):de.deps=de.depsTail=s,Ai(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const r=s.nextDep;r.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=r),s.prevDep=de.depsTail,s.nextDep=void 0,de.depsTail.nextDep=s,de.depsTail=s,de.deps===s&&(de.deps=r)}return s}trigger(t){this.version++,ks++,this.notify(t)}notify(t){hr();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{gr()}}}function Ai(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Ai(r)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const Jn=new WeakMap,Ft=Symbol(""),Gn=Symbol(""),Es=Symbol("");function ye(e,t,s){if(Ke&&de){let r=Jn.get(e);r||Jn.set(e,r=new Map);let n=r.get(s);n||(r.set(s,n=new xr),n.map=r,n.key=s),n.track()}}function mt(e,t,s,r,n,i){const a=Jn.get(e);if(!a){ks++;return}const l=c=>{c&&c.trigger()};if(hr(),t==="clear")a.forEach(l);else{const c=z(e),d=c&&fr(s);if(c&&s==="length"){const u=Number(r);a.forEach((f,g)=>{(g==="length"||g===Es||!lt(g)&&g>=u)&&l(f)})}else switch((s!==void 0||a.has(void 0))&&l(a.get(s)),d&&l(a.get(Es)),t){case"add":c?d&&l(a.get("length")):(l(a.get(Ft)),Jt(e)&&l(a.get(Gn)));break;case"delete":c||(l(a.get(Ft)),Jt(e)&&l(a.get(Gn)));break;case"set":Jt(e)&&l(a.get(Ft));break}}gr()}function Ht(e){const t=te(e);return t===e?t:(ye(t,"iterate",Es),Ve(e)?t:t.map(be))}function vn(e){return ye(e=te(e),"iterate",Es),e}const Rc={__proto__:null,[Symbol.iterator](){return Mn(this,Symbol.iterator,be)},concat(...e){return Ht(this).concat(...e.map(t=>z(t)?Ht(t):t))},entries(){return Mn(this,"entries",e=>(e[1]=be(e[1]),e))},every(e,t){return dt(this,"every",e,t,void 0,arguments)},filter(e,t){return dt(this,"filter",e,t,s=>s.map(be),arguments)},find(e,t){return dt(this,"find",e,t,be,arguments)},findIndex(e,t){return dt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return dt(this,"findLast",e,t,be,arguments)},findLastIndex(e,t){return dt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return dt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Pn(this,"includes",e)},indexOf(...e){return Pn(this,"indexOf",e)},join(e){return Ht(this).join(e)},lastIndexOf(...e){return Pn(this,"lastIndexOf",e)},map(e,t){return dt(this,"map",e,t,void 0,arguments)},pop(){return us(this,"pop")},push(...e){return us(this,"push",e)},reduce(e,...t){return Gr(this,"reduce",e,t)},reduceRight(e,...t){return Gr(this,"reduceRight",e,t)},shift(){return us(this,"shift")},some(e,t){return dt(this,"some",e,t,void 0,arguments)},splice(...e){return us(this,"splice",e)},toReversed(){return Ht(this).toReversed()},toSorted(e){return Ht(this).toSorted(e)},toSpliced(...e){return Ht(this).toSpliced(...e)},unshift(...e){return us(this,"unshift",e)},values(){return Mn(this,"values",be)}};function Mn(e,t,s){const r=vn(e),n=r[t]();return r!==e&&!Ve(e)&&(n._next=n.next,n.next=()=>{const i=n._next();return i.value&&(i.value=s(i.value)),i}),n}const Tc=Array.prototype;function dt(e,t,s,r,n,i){const a=vn(e),l=a!==e&&!Ve(e),c=a[t];if(c!==Tc[t]){const f=c.apply(e,i);return l?be(f):f}let d=s;a!==e&&(l?d=function(f,g){return s.call(this,be(f),g,e)}:s.length>2&&(d=function(f,g){return s.call(this,f,g,e)}));const u=c.call(a,d,r);return l&&n?n(u):u}function Gr(e,t,s,r){const n=vn(e);let i=s;return n!==e&&(Ve(e)?s.length>3&&(i=function(a,l,c){return s.call(this,a,l,c,e)}):i=function(a,l,c){return s.call(this,a,be(l),c,e)}),n[t](i,...r)}function Pn(e,t,s){const r=te(e);ye(r,"iterate",Es);const n=r[t](...s);return(n===-1||n===!1)&&wr(s[0])?(s[0]=te(s[0]),r[t](...s)):n}function us(e,t,s=[]){xt(),hr();const r=te(e)[t].apply(e,s);return gr(),bt(),r}const Ac=cr("__proto__,__v_isRef,__isVue"),Oi=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(lt));function Oc(e){lt(e)||(e=String(e));const t=te(this);return ye(t,"has",e),t.hasOwnProperty(e)}class $i{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,r){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,i=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return i;if(s==="__v_raw")return r===(n?i?Vc:ji:i?Di:Pi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const a=z(t);if(!n){let c;if(a&&(c=Rc[s]))return c;if(s==="hasOwnProperty")return Oc}const l=Reflect.get(t,s,ke(t)?t:r);return(lt(s)?Oi.has(s):Ac(s))||(n||ye(t,"get",s),i)?l:ke(l)?a&&fr(s)?l:l.value:me(l)?n?Fi(l):xn(l):l}}class Mi extends $i{constructor(t=!1){super(!1,t)}set(t,s,r,n){let i=t[s];if(!this._isShallow){const c=Tt(i);if(!Ve(r)&&!Tt(r)&&(i=te(i),r=te(r)),!z(t)&&ke(i)&&!ke(r))return c?!1:(i.value=r,!0)}const a=z(t)&&fr(s)?Number(s)<t.length:se(t,s),l=Reflect.set(t,s,r,ke(t)?t:n);return t===te(n)&&(a?Rt(r,i)&&mt(t,"set",s,r):mt(t,"add",s,r)),l}deleteProperty(t,s){const r=se(t,s);t[s];const n=Reflect.deleteProperty(t,s);return n&&r&&mt(t,"delete",s,void 0),n}has(t,s){const r=Reflect.has(t,s);return(!lt(s)||!Oi.has(s))&&ye(t,"has",s),r}ownKeys(t){return ye(t,"iterate",z(t)?"length":Ft),Reflect.ownKeys(t)}}class $c extends $i{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const Mc=new Mi,Pc=new $c,Dc=new Mi(!0);const Xn=e=>e,Ns=e=>Reflect.getPrototypeOf(e);function jc(e,t,s){return function(...r){const n=this.__v_raw,i=te(n),a=Jt(i),l=e==="entries"||e===Symbol.iterator&&a,c=e==="keys"&&a,d=n[e](...r),u=s?Xn:t?Qs:be;return!t&&ye(i,"iterate",c?Gn:Ft),{next(){const{value:f,done:g}=d.next();return g?{value:f,done:g}:{value:l?[u(f[0]),u(f[1])]:u(f),done:g}},[Symbol.iterator](){return this}}}}function Fs(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Nc(e,t){const s={get(n){const i=this.__v_raw,a=te(i),l=te(n);e||(Rt(n,l)&&ye(a,"get",n),ye(a,"get",l));const{has:c}=Ns(a),d=t?Xn:e?Qs:be;if(c.call(a,n))return d(i.get(n));if(c.call(a,l))return d(i.get(l));i!==a&&i.get(n)},get size(){const n=this.__v_raw;return!e&&ye(te(n),"iterate",Ft),Reflect.get(n,"size",n)},has(n){const i=this.__v_raw,a=te(i),l=te(n);return e||(Rt(n,l)&&ye(a,"has",n),ye(a,"has",l)),n===l?i.has(n):i.has(n)||i.has(l)},forEach(n,i){const a=this,l=a.__v_raw,c=te(l),d=t?Xn:e?Qs:be;return!e&&ye(c,"iterate",Ft),l.forEach((u,f)=>n.call(i,d(u),d(f),a))}};return Ee(s,e?{add:Fs("add"),set:Fs("set"),delete:Fs("delete"),clear:Fs("clear")}:{add(n){!t&&!Ve(n)&&!Tt(n)&&(n=te(n));const i=te(this);return Ns(i).has.call(i,n)||(i.add(n),mt(i,"add",n,n)),this},set(n,i){!t&&!Ve(i)&&!Tt(i)&&(i=te(i));const a=te(this),{has:l,get:c}=Ns(a);let d=l.call(a,n);d||(n=te(n),d=l.call(a,n));const u=c.call(a,n);return a.set(n,i),d?Rt(i,u)&&mt(a,"set",n,i):mt(a,"add",n,i),this},delete(n){const i=te(this),{has:a,get:l}=Ns(i);let c=a.call(i,n);c||(n=te(n),c=a.call(i,n)),l&&l.call(i,n);const d=i.delete(n);return c&&mt(i,"delete",n,void 0),d},clear(){const n=te(this),i=n.size!==0,a=n.clear();return i&&mt(n,"clear",void 0,void 0),a}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=jc(n,e,t)}),s}function br(e,t){const s=Nc(e,t);return(r,n,i)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?r:Reflect.get(se(s,n)&&n in r?s:r,n,i)}const Fc={get:br(!1,!1)},Ic={get:br(!1,!0)},Uc={get:br(!0,!1)};const Pi=new WeakMap,Di=new WeakMap,ji=new WeakMap,Vc=new WeakMap;function Bc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Lc(e){return e.__v_skip||!Object.isExtensible(e)?0:Bc(pc(e))}function xn(e){return Tt(e)?e:yr(e,!1,Mc,Fc,Pi)}function Ni(e){return yr(e,!1,Dc,Ic,Di)}function Fi(e){return yr(e,!0,Pc,Uc,ji)}function yr(e,t,s,r,n){if(!me(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=Lc(e);if(i===0)return e;const a=n.get(e);if(a)return a;const l=new Proxy(e,i===2?r:s);return n.set(e,l),l}function Gt(e){return Tt(e)?Gt(e.__v_raw):!!(e&&e.__v_isReactive)}function Tt(e){return!!(e&&e.__v_isReadonly)}function Ve(e){return!!(e&&e.__v_isShallow)}function wr(e){return e?!!e.__v_raw:!1}function te(e){const t=e&&e.__v_raw;return t?te(t):e}function zc(e){return!se(e,"__v_skip")&&Object.isExtensible(e)&&xi(e,"__v_skip",!0),e}const be=e=>me(e)?xn(e):e,Qs=e=>me(e)?Fi(e):e;function ke(e){return e?e.__v_isRef===!0:!1}function Hc(e){return Ii(e,!1)}function qc(e){return Ii(e,!0)}function Ii(e,t){return ke(e)?e:new Kc(e,t)}class Kc{constructor(t,s){this.dep=new xr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:te(t),this._value=s?t:be(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,r=this.__v_isShallow||Ve(t)||Tt(t);t=r?t:te(t),Rt(t,s)&&(this._rawValue=t,this._value=r?t:be(t),this.dep.trigger())}}function Xt(e){return ke(e)?e.value:e}const Wc={get:(e,t,s)=>t==="__v_raw"?e:Xt(Reflect.get(e,t,s)),set:(e,t,s,r)=>{const n=e[t];return ke(n)&&!ke(s)?(n.value=s,!0):Reflect.set(e,t,s,r)}};function Ui(e){return Gt(e)?e:new Proxy(e,Wc)}class Jc{constructor(t,s,r){this.fn=t,this.setter=s,this._value=void 0,this.dep=new xr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=ks-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&de!==this)return Ei(this,!0),!0}get value(){const t=this.dep.track();return Ri(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Gc(e,t,s=!1){let r,n;return W(e)?r=e:(r=e.get,n=e.set),new Jc(r,n,s)}const Is={},Ys=new WeakMap;let Dt;function Xc(e,t=!1,s=Dt){if(s){let r=Ys.get(s);r||Ys.set(s,r=[]),r.push(e)}}function Qc(e,t,s=le){const{immediate:r,deep:n,once:i,scheduler:a,augmentJob:l,call:c}=s,d=I=>n?I:Ve(I)||n===!1||n===0?ht(I,1):ht(I);let u,f,g,v,h=!1,w=!1;if(ke(e)?(f=()=>e.value,h=Ve(e)):Gt(e)?(f=()=>d(e),h=!0):z(e)?(w=!0,h=e.some(I=>Gt(I)||Ve(I)),f=()=>e.map(I=>{if(ke(I))return I.value;if(Gt(I))return d(I);if(W(I))return c?c(I,2):I()})):W(e)?t?f=c?()=>c(e,2):e:f=()=>{if(g){xt();try{g()}finally{bt()}}const I=Dt;Dt=u;try{return c?c(e,3,[v]):e(v)}finally{Dt=I}}:f=it,t&&n){const I=f,J=n===!0?1/0:n;f=()=>ht(I(),J)}const C=Ec(),O=()=>{u.stop(),C&&C.active&&dr(C.effects,u)};if(i&&t){const I=t;t=(...J)=>{I(...J),O()}}let $=w?new Array(e.length).fill(Is):Is;const j=I=>{if(!(!(u.flags&1)||!u.dirty&&!I))if(t){const J=u.run();if(n||h||(w?J.some((ce,Y)=>Rt(ce,$[Y])):Rt(J,$))){g&&g();const ce=Dt;Dt=u;try{const Y=[J,$===Is?void 0:w&&$[0]===Is?[]:$,v];$=J,c?c(t,3,Y):t(...Y)}finally{Dt=ce}}}else u.run()};return l&&l(j),u=new _i(f),u.scheduler=a?()=>a(j,!1):j,v=I=>Xc(I,!1,u),g=u.onStop=()=>{const I=Ys.get(u);if(I){if(c)c(I,4);else for(const J of I)J();Ys.delete(u)}},t?r?j(!0):$=u.run():a?a(j.bind(null,!0),!0):u.run(),O.pause=u.pause.bind(u),O.resume=u.resume.bind(u),O.stop=O,O}function ht(e,t=1/0,s){if(t<=0||!me(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,ke(e))ht(e.value,t,s);else if(z(e))for(let r=0;r<e.length;r++)ht(e[r],t,s);else if(is(e)||Jt(e))e.forEach(r=>{ht(r,t,s)});else if(vi(e)){for(const r in e)ht(e[r],t,s);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&ht(e[r],t,s)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ps(e,t,s,r){try{return r?e(...r):e()}catch(n){bn(n,t,s)}}function at(e,t,s,r){if(W(e)){const n=Ps(e,t,s,r);return n&&hi(n)&&n.catch(i=>{bn(i,t,s)}),n}if(z(e)){const n=[];for(let i=0;i<e.length;i++)n.push(at(e[i],t,s,r));return n}}function bn(e,t,s,r=!0){const n=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:a}=t&&t.appContext.config||le;if(t){let l=t.parent;const c=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const u=l.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,c,d)===!1)return}l=l.parent}if(i){xt(),Ps(i,null,10,[e,c,d]),bt();return}}Yc(e,s,n,r,a)}function Yc(e,t,s,r=!0,n=!1){if(n)throw e;console.error(e)}const Re=[];let rt=-1;const Qt=[];let Et=null,qt=0;const Vi=Promise.resolve();let Zs=null;function _r(e){const t=Zs||Vi;return e?t.then(this?e.bind(this):e):t}function Zc(e){let t=rt+1,s=Re.length;for(;t<s;){const r=t+s>>>1,n=Re[r],i=Ss(n);i<e||i===e&&n.flags&2?t=r+1:s=r}return t}function kr(e){if(!(e.flags&1)){const t=Ss(e),s=Re[Re.length-1];!s||!(e.flags&2)&&t>=Ss(s)?Re.push(e):Re.splice(Zc(t),0,e),e.flags|=1,Bi()}}function Bi(){Zs||(Zs=Vi.then(zi))}function eu(e){z(e)?Qt.push(...e):Et&&e.id===-1?Et.splice(qt+1,0,e):e.flags&1||(Qt.push(e),e.flags|=1),Bi()}function Xr(e,t,s=rt+1){for(;s<Re.length;s++){const r=Re[s];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Re.splice(s,1),s--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Li(e){if(Qt.length){const t=[...new Set(Qt)].sort((s,r)=>Ss(s)-Ss(r));if(Qt.length=0,Et){Et.push(...t);return}for(Et=t,qt=0;qt<Et.length;qt++){const s=Et[qt];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}Et=null,qt=0}}const Ss=e=>e.id==null?e.flags&2?-1:1/0:e.id;function zi(e){try{for(rt=0;rt<Re.length;rt++){const t=Re[rt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ps(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;rt<Re.length;rt++){const t=Re[rt];t&&(t.flags&=-2)}rt=-1,Re.length=0,Li(),Zs=null,(Re.length||Qt.length)&&zi()}}let Fe=null,Hi=null;function en(e){const t=Fe;return Fe=e,Hi=e&&e.type.__scopeId||null,t}function ne(e,t=Fe,s){if(!t||e._n)return e;const r=(...n)=>{r._d&&io(-1);const i=en(t);let a;try{a=e(...n)}finally{en(i),r._d&&io(1)}return a};return r._n=!0,r._c=!0,r._d=!0,r}function X(e,t){if(Fe===null)return e;const s=kn(Fe),r=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[i,a,l,c=le]=t[n];i&&(W(i)&&(i={mounted:i,updated:i}),i.deep&&ht(a),r.push({dir:i,instance:s,value:a,oldValue:void 0,arg:l,modifiers:c}))}return e}function Mt(e,t,s,r){const n=e.dirs,i=t&&t.dirs;for(let a=0;a<n.length;a++){const l=n[a];i&&(l.oldValue=i[a].value);let c=l.dir[r];c&&(xt(),at(c,s,8,[e.el,l,e,t]),bt())}}const tu=Symbol("_vte"),su=e=>e.__isTeleport;function Er(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Er(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function qi(e,t){return W(e)?Ee({name:e.name},t,{setup:e}):e}function Ki(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function tn(e,t,s,r,n=!1){if(z(e)){e.forEach((h,w)=>tn(h,t&&(z(t)?t[w]:t),s,r,n));return}if(vs(r)&&!n){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&tn(e,t,s,r.component.subTree);return}const i=r.shapeFlag&4?kn(r.component):r.el,a=n?null:i,{i:l,r:c}=e,d=t&&t.r,u=l.refs===le?l.refs={}:l.refs,f=l.setupState,g=te(f),v=f===le?()=>!1:h=>se(g,h);if(d!=null&&d!==c&&(ve(d)?(u[d]=null,v(d)&&(f[d]=null)):ke(d)&&(d.value=null)),W(c))Ps(c,l,12,[a,u]);else{const h=ve(c),w=ke(c);if(h||w){const C=()=>{if(e.f){const O=h?v(c)?f[c]:u[c]:c.value;n?z(O)&&dr(O,i):z(O)?O.includes(i)||O.push(i):h?(u[c]=[i],v(c)&&(f[c]=u[c])):(c.value=[i],e.k&&(u[e.k]=c.value))}else h?(u[c]=a,v(c)&&(f[c]=a)):w&&(c.value=a,e.k&&(u[e.k]=a))};a?(C.id=-1,Ne(C,s)):C()}}}gn().requestIdleCallback;gn().cancelIdleCallback;const vs=e=>!!e.type.__asyncLoader,Wi=e=>e.type.__isKeepAlive;function nu(e,t){Ji(e,"a",t)}function ru(e,t){Ji(e,"da",t)}function Ji(e,t,s=_e){const r=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(yn(t,r,s),s){let n=s.parent;for(;n&&n.parent;)Wi(n.parent.vnode)&&ou(r,t,s,n),n=n.parent}}function ou(e,t,s,r){const n=yn(t,e,r,!0);Gi(()=>{dr(r[t],n)},s)}function yn(e,t,s=_e,r=!1){if(s){const n=s[e]||(s[e]=[]),i=t.__weh||(t.__weh=(...a)=>{xt();const l=Ds(s),c=at(t,s,e,a);return l(),bt(),c});return r?n.unshift(i):n.push(i),i}}const yt=e=>(t,s=_e)=>{(!Rs||e==="sp")&&yn(e,(...r)=>t(...r),s)},iu=yt("bm"),lu=yt("m"),au=yt("bu"),cu=yt("u"),uu=yt("bum"),Gi=yt("um"),du=yt("sp"),fu=yt("rtg"),pu=yt("rtc");function mu(e,t=_e){yn("ec",e,t)}const hu="components";function ct(e,t){return vu(hu,e,!0,t)||e}const gu=Symbol.for("v-ndc");function vu(e,t,s=!0,r=!1){const n=Fe||_e;if(n){const i=n.type;{const l=rd(i,!1);if(l&&(l===t||l===ze(t)||l===hn(ze(t))))return i}const a=Qr(n[e]||i[e],t)||Qr(n.appContext[e],t);return!a&&r?i:a}}function Qr(e,t){return e&&(e[t]||e[ze(t)]||e[hn(ze(t))])}function Te(e,t,s,r){let n;const i=s,a=z(e);if(a||ve(e)){const l=a&&Gt(e);let c=!1,d=!1;l&&(c=!Ve(e),d=Tt(e),e=vn(e)),n=new Array(e.length);for(let u=0,f=e.length;u<f;u++)n[u]=t(c?d?Qs(be(e[u])):be(e[u]):e[u],u,void 0,i)}else if(typeof e=="number"){n=new Array(e);for(let l=0;l<e;l++)n[l]=t(l+1,l,void 0,i)}else if(me(e))if(e[Symbol.iterator])n=Array.from(e,(l,c)=>t(l,c,void 0,i));else{const l=Object.keys(e);n=new Array(l.length);for(let c=0,d=l.length;c<d;c++){const u=l[c];n[c]=t(e[u],u,c,i)}}else n=[];return n}const Qn=e=>e?hl(e)?kn(e):Qn(e.parent):null,xs=Ee(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Qn(e.parent),$root:e=>Qn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Qi(e),$forceUpdate:e=>e.f||(e.f=()=>{kr(e.update)}),$nextTick:e=>e.n||(e.n=_r.bind(e.proxy)),$watch:e=>Iu.bind(e)}),Dn=(e,t)=>e!==le&&!e.__isScriptSetup&&se(e,t),xu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:r,data:n,props:i,accessCache:a,type:l,appContext:c}=e;let d;if(t[0]!=="$"){const v=a[t];if(v!==void 0)switch(v){case 1:return r[t];case 2:return n[t];case 4:return s[t];case 3:return i[t]}else{if(Dn(r,t))return a[t]=1,r[t];if(n!==le&&se(n,t))return a[t]=2,n[t];if((d=e.propsOptions[0])&&se(d,t))return a[t]=3,i[t];if(s!==le&&se(s,t))return a[t]=4,s[t];Yn&&(a[t]=0)}}const u=xs[t];let f,g;if(u)return t==="$attrs"&&ye(e.attrs,"get",""),u(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(s!==le&&se(s,t))return a[t]=4,s[t];if(g=c.config.globalProperties,se(g,t))return g[t]},set({_:e},t,s){const{data:r,setupState:n,ctx:i}=e;return Dn(n,t)?(n[t]=s,!0):r!==le&&se(r,t)?(r[t]=s,!0):se(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:r,appContext:n,propsOptions:i}},a){let l;return!!s[a]||e!==le&&se(e,a)||Dn(t,a)||(l=i[0])&&se(l,a)||se(r,a)||se(xs,a)||se(n.config.globalProperties,a)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:se(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function Yr(e){return z(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let Yn=!0;function bu(e){const t=Qi(e),s=e.proxy,r=e.ctx;Yn=!1,t.beforeCreate&&Zr(t.beforeCreate,e,"bc");const{data:n,computed:i,methods:a,watch:l,provide:c,inject:d,created:u,beforeMount:f,mounted:g,beforeUpdate:v,updated:h,activated:w,deactivated:C,beforeDestroy:O,beforeUnmount:$,destroyed:j,unmounted:I,render:J,renderTracked:ce,renderTriggered:Y,errorCaptured:Se,serverPrefetch:He,expose:Xe,inheritAttrs:wt,components:$t,directives:Qe,filters:ls}=t;if(d&&yu(d,r,null),a)for(const oe in a){const Z=a[oe];W(Z)&&(r[oe]=Z.bind(s))}if(n){const oe=n.call(s,s);me(oe)&&(e.data=xn(oe))}if(Yn=!0,i)for(const oe in i){const Z=i[oe],ut=W(Z)?Z.bind(s,s):W(Z.get)?Z.get.bind(s,s):it,_t=!W(Z)&&W(Z.set)?Z.set.bind(s):it,Ye=qe({get:ut,set:_t});Object.defineProperty(r,oe,{enumerable:!0,configurable:!0,get:()=>Ye.value,set:Ae=>Ye.value=Ae})}if(l)for(const oe in l)Xi(l[oe],r,s,oe);if(c){const oe=W(c)?c.call(s):c;Reflect.ownKeys(oe).forEach(Z=>{zs(Z,oe[Z])})}u&&Zr(u,e,"c");function xe(oe,Z){z(Z)?Z.forEach(ut=>oe(ut.bind(s))):Z&&oe(Z.bind(s))}if(xe(iu,f),xe(lu,g),xe(au,v),xe(cu,h),xe(nu,w),xe(ru,C),xe(mu,Se),xe(pu,ce),xe(fu,Y),xe(uu,$),xe(Gi,I),xe(du,He),z(Xe))if(Xe.length){const oe=e.exposed||(e.exposed={});Xe.forEach(Z=>{Object.defineProperty(oe,Z,{get:()=>s[Z],set:ut=>s[Z]=ut})})}else e.exposed||(e.exposed={});J&&e.render===it&&(e.render=J),wt!=null&&(e.inheritAttrs=wt),$t&&(e.components=$t),Qe&&(e.directives=Qe),He&&Ki(e)}function yu(e,t,s=it){z(e)&&(e=Zn(e));for(const r in e){const n=e[r];let i;me(n)?"default"in n?i=vt(n.from||r,n.default,!0):i=vt(n.from||r):i=vt(n),ke(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:a=>i.value=a}):t[r]=i}}function Zr(e,t,s){at(z(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,s)}function Xi(e,t,s,r){let n=r.includes(".")?ul(s,r):()=>s[r];if(ve(e)){const i=t[e];W(i)&&Hs(n,i)}else if(W(e))Hs(n,e.bind(s));else if(me(e))if(z(e))e.forEach(i=>Xi(i,t,s,r));else{const i=W(e.handler)?e.handler.bind(s):t[e.handler];W(i)&&Hs(n,i,e)}}function Qi(e){const t=e.type,{mixins:s,extends:r}=t,{mixins:n,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,l=i.get(t);let c;return l?c=l:!n.length&&!s&&!r?c=t:(c={},n.length&&n.forEach(d=>sn(c,d,a,!0)),sn(c,t,a)),me(t)&&i.set(t,c),c}function sn(e,t,s,r=!1){const{mixins:n,extends:i}=t;i&&sn(e,i,s,!0),n&&n.forEach(a=>sn(e,a,s,!0));for(const a in t)if(!(r&&a==="expose")){const l=wu[a]||s&&s[a];e[a]=l?l(e[a],t[a]):t[a]}return e}const wu={data:eo,props:to,emits:to,methods:ps,computed:ps,beforeCreate:Ce,created:Ce,beforeMount:Ce,mounted:Ce,beforeUpdate:Ce,updated:Ce,beforeDestroy:Ce,beforeUnmount:Ce,destroyed:Ce,unmounted:Ce,activated:Ce,deactivated:Ce,errorCaptured:Ce,serverPrefetch:Ce,components:ps,directives:ps,watch:ku,provide:eo,inject:_u};function eo(e,t){return t?e?function(){return Ee(W(e)?e.call(this,this):e,W(t)?t.call(this,this):t)}:t:e}function _u(e,t){return ps(Zn(e),Zn(t))}function Zn(e){if(z(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Ce(e,t){return e?[...new Set([].concat(e,t))]:t}function ps(e,t){return e?Ee(Object.create(null),e,t):t}function to(e,t){return e?z(e)&&z(t)?[...new Set([...e,...t])]:Ee(Object.create(null),Yr(e),Yr(t??{})):t}function ku(e,t){if(!e)return t;if(!t)return e;const s=Ee(Object.create(null),e);for(const r in t)s[r]=Ce(e[r],t[r]);return s}function Yi(){return{app:null,config:{isNativeTag:dc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Eu=0;function Su(e,t){return function(r,n=null){W(r)||(r=Ee({},r)),n!=null&&!me(n)&&(n=null);const i=Yi(),a=new WeakSet,l=[];let c=!1;const d=i.app={_uid:Eu++,_component:r,_props:n,_container:null,_context:i,_instance:null,version:id,get config(){return i.config},set config(u){},use(u,...f){return a.has(u)||(u&&W(u.install)?(a.add(u),u.install(d,...f)):W(u)&&(a.add(u),u(d,...f))),d},mixin(u){return i.mixins.includes(u)||i.mixins.push(u),d},component(u,f){return f?(i.components[u]=f,d):i.components[u]},directive(u,f){return f?(i.directives[u]=f,d):i.directives[u]},mount(u,f,g){if(!c){const v=d._ceVNode||K(r,n);return v.appContext=i,g===!0?g="svg":g===!1&&(g=void 0),e(v,u,g),c=!0,d._container=u,u.__vue_app__=d,kn(v.component)}},onUnmount(u){l.push(u)},unmount(){c&&(at(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(u,f){return i.provides[u]=f,d},runWithContext(u){const f=Yt;Yt=d;try{return u()}finally{Yt=f}}};return d}}let Yt=null;function zs(e,t){if(_e){let s=_e.provides;const r=_e.parent&&_e.parent.provides;r===s&&(s=_e.provides=Object.create(r)),s[e]=t}}function vt(e,t,s=!1){const r=_e||Fe;if(r||Yt){let n=Yt?Yt._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&W(t)?t.call(r&&r.proxy):t}}const Zi={},el=()=>Object.create(Zi),tl=e=>Object.getPrototypeOf(e)===Zi;function Cu(e,t,s,r=!1){const n={},i=el();e.propsDefaults=Object.create(null),sl(e,t,n,i);for(const a in e.propsOptions[0])a in n||(n[a]=void 0);s?e.props=r?n:Ni(n):e.type.props?e.props=n:e.props=i,e.attrs=i}function Ru(e,t,s,r){const{props:n,attrs:i,vnode:{patchFlag:a}}=e,l=te(n),[c]=e.propsOptions;let d=!1;if((r||a>0)&&!(a&16)){if(a&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let g=u[f];if(wn(e.emitsOptions,g))continue;const v=t[g];if(c)if(se(i,g))v!==i[g]&&(i[g]=v,d=!0);else{const h=ze(g);n[h]=er(c,l,h,v,e,!1)}else v!==i[g]&&(i[g]=v,d=!0)}}}else{sl(e,t,n,i)&&(d=!0);let u;for(const f in l)(!t||!se(t,f)&&((u=Vt(f))===f||!se(t,u)))&&(c?s&&(s[f]!==void 0||s[u]!==void 0)&&(n[f]=er(c,l,f,void 0,e,!0)):delete n[f]);if(i!==l)for(const f in i)(!t||!se(t,f))&&(delete i[f],d=!0)}d&&mt(e.attrs,"set","")}function sl(e,t,s,r){const[n,i]=e.propsOptions;let a=!1,l;if(t)for(let c in t){if(ms(c))continue;const d=t[c];let u;n&&se(n,u=ze(c))?!i||!i.includes(u)?s[u]=d:(l||(l={}))[u]=d:wn(e.emitsOptions,c)||(!(c in r)||d!==r[c])&&(r[c]=d,a=!0)}if(i){const c=te(s),d=l||le;for(let u=0;u<i.length;u++){const f=i[u];s[f]=er(n,c,f,d[f],e,!se(d,f))}}return a}function er(e,t,s,r,n,i){const a=e[s];if(a!=null){const l=se(a,"default");if(l&&r===void 0){const c=a.default;if(a.type!==Function&&!a.skipFactory&&W(c)){const{propsDefaults:d}=n;if(s in d)r=d[s];else{const u=Ds(n);r=d[s]=c.call(null,t),u()}}else r=c;n.ce&&n.ce._setProp(s,r)}a[0]&&(i&&!l?r=!1:a[1]&&(r===""||r===Vt(s))&&(r=!0))}return r}const Tu=new WeakMap;function nl(e,t,s=!1){const r=s?Tu:t.propsCache,n=r.get(e);if(n)return n;const i=e.props,a={},l=[];let c=!1;if(!W(e)){const u=f=>{c=!0;const[g,v]=nl(f,t,!0);Ee(a,g),v&&l.push(...v)};!s&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!i&&!c)return me(e)&&r.set(e,Wt),Wt;if(z(i))for(let u=0;u<i.length;u++){const f=ze(i[u]);so(f)&&(a[f]=le)}else if(i)for(const u in i){const f=ze(u);if(so(f)){const g=i[u],v=a[f]=z(g)||W(g)?{type:g}:Ee({},g),h=v.type;let w=!1,C=!0;if(z(h))for(let O=0;O<h.length;++O){const $=h[O],j=W($)&&$.name;if(j==="Boolean"){w=!0;break}else j==="String"&&(C=!1)}else w=W(h)&&h.name==="Boolean";v[0]=w,v[1]=C,(w||se(v,"default"))&&l.push(f)}}const d=[a,l];return me(e)&&r.set(e,d),d}function so(e){return e[0]!=="$"&&!ms(e)}const Sr=e=>e[0]==="_"||e==="$stable",Cr=e=>z(e)?e.map(ot):[ot(e)],Au=(e,t,s)=>{if(t._n)return t;const r=ne((...n)=>Cr(t(...n)),s);return r._c=!1,r},rl=(e,t,s)=>{const r=e._ctx;for(const n in e){if(Sr(n))continue;const i=e[n];if(W(i))t[n]=Au(n,i,r);else if(i!=null){const a=Cr(i);t[n]=()=>a}}},ol=(e,t)=>{const s=Cr(t);e.slots.default=()=>s},il=(e,t,s)=>{for(const r in t)(s||!Sr(r))&&(e[r]=t[r])},Ou=(e,t,s)=>{const r=e.slots=el();if(e.vnode.shapeFlag&32){const n=t._;n?(il(r,t,s),s&&xi(r,"_",n,!0)):rl(t,r)}else t&&ol(e,t)},$u=(e,t,s)=>{const{vnode:r,slots:n}=e;let i=!0,a=le;if(r.shapeFlag&32){const l=t._;l?s&&l===1?i=!1:il(n,t,s):(i=!t.$stable,rl(t,n)),a=t}else t&&(ol(e,t),a={default:1});if(i)for(const l in n)!Sr(l)&&a[l]==null&&delete n[l]},Ne=qu;function Mu(e){return Pu(e)}function Pu(e,t){const s=gn();s.__VUE__=!0;const{insert:r,remove:n,patchProp:i,createElement:a,createText:l,createComment:c,setText:d,setElementText:u,parentNode:f,nextSibling:g,setScopeId:v=it,insertStaticContent:h}=e,w=(p,m,x,_=null,S=null,E=null,D=void 0,P=null,M=!!m.dynamicChildren)=>{if(p===m)return;p&&!ds(p,m)&&(_=k(p),Ae(p,S,E,!0),p=null),m.patchFlag===-2&&(M=!1,m.dynamicChildren=null);const{type:A,ref:L,shapeFlag:F}=m;switch(A){case _n:C(p,m,x,_);break;case At:O(p,m,x,_);break;case qs:p==null&&$(m,x,_,D);break;case ge:$t(p,m,x,_,S,E,D,P,M);break;default:F&1?J(p,m,x,_,S,E,D,P,M):F&6?Qe(p,m,x,_,S,E,D,P,M):(F&64||F&128)&&A.process(p,m,x,_,S,E,D,P,M,V)}L!=null&&S&&tn(L,p&&p.ref,E,m||p,!m)},C=(p,m,x,_)=>{if(p==null)r(m.el=l(m.children),x,_);else{const S=m.el=p.el;m.children!==p.children&&d(S,m.children)}},O=(p,m,x,_)=>{p==null?r(m.el=c(m.children||""),x,_):m.el=p.el},$=(p,m,x,_)=>{[p.el,p.anchor]=h(p.children,m,x,_,p.el,p.anchor)},j=({el:p,anchor:m},x,_)=>{let S;for(;p&&p!==m;)S=g(p),r(p,x,_),p=S;r(m,x,_)},I=({el:p,anchor:m})=>{let x;for(;p&&p!==m;)x=g(p),n(p),p=x;n(m)},J=(p,m,x,_,S,E,D,P,M)=>{m.type==="svg"?D="svg":m.type==="math"&&(D="mathml"),p==null?ce(m,x,_,S,E,D,P,M):He(p,m,S,E,D,P,M)},ce=(p,m,x,_,S,E,D,P)=>{let M,A;const{props:L,shapeFlag:F,transition:B,dirs:q}=p;if(M=p.el=a(p.type,E,L&&L.is,L),F&8?u(M,p.children):F&16&&Se(p.children,M,null,_,S,jn(p,E),D,P),q&&Mt(p,null,_,"created"),Y(M,p,p.scopeId,D,_),L){for(const ue in L)ue!=="value"&&!ms(ue)&&i(M,ue,null,L[ue],E,_);"value"in L&&i(M,"value",null,L.value,E),(A=L.onVnodeBeforeMount)&&nt(A,_,p)}q&&Mt(p,null,_,"beforeMount");const Q=Du(S,B);Q&&B.beforeEnter(M),r(M,m,x),((A=L&&L.onVnodeMounted)||Q||q)&&Ne(()=>{A&&nt(A,_,p),Q&&B.enter(M),q&&Mt(p,null,_,"mounted")},S)},Y=(p,m,x,_,S)=>{if(x&&v(p,x),_)for(let E=0;E<_.length;E++)v(p,_[E]);if(S){let E=S.subTree;if(m===E||fl(E.type)&&(E.ssContent===m||E.ssFallback===m)){const D=S.vnode;Y(p,D,D.scopeId,D.slotScopeIds,S.parent)}}},Se=(p,m,x,_,S,E,D,P,M=0)=>{for(let A=M;A<p.length;A++){const L=p[A]=P?St(p[A]):ot(p[A]);w(null,L,m,x,_,S,E,D,P)}},He=(p,m,x,_,S,E,D)=>{const P=m.el=p.el;let{patchFlag:M,dynamicChildren:A,dirs:L}=m;M|=p.patchFlag&16;const F=p.props||le,B=m.props||le;let q;if(x&&Pt(x,!1),(q=B.onVnodeBeforeUpdate)&&nt(q,x,m,p),L&&Mt(m,p,x,"beforeUpdate"),x&&Pt(x,!0),(F.innerHTML&&B.innerHTML==null||F.textContent&&B.textContent==null)&&u(P,""),A?Xe(p.dynamicChildren,A,P,x,_,jn(m,S),E):D||Z(p,m,P,null,x,_,jn(m,S),E,!1),M>0){if(M&16)wt(P,F,B,x,S);else if(M&2&&F.class!==B.class&&i(P,"class",null,B.class,S),M&4&&i(P,"style",F.style,B.style,S),M&8){const Q=m.dynamicProps;for(let ue=0;ue<Q.length;ue++){const re=Q[ue],De=F[re],Oe=B[re];(Oe!==De||re==="value")&&i(P,re,De,Oe,S,x)}}M&1&&p.children!==m.children&&u(P,m.children)}else!D&&A==null&&wt(P,F,B,x,S);((q=B.onVnodeUpdated)||L)&&Ne(()=>{q&&nt(q,x,m,p),L&&Mt(m,p,x,"updated")},_)},Xe=(p,m,x,_,S,E,D)=>{for(let P=0;P<m.length;P++){const M=p[P],A=m[P],L=M.el&&(M.type===ge||!ds(M,A)||M.shapeFlag&198)?f(M.el):x;w(M,A,L,null,_,S,E,D,!0)}},wt=(p,m,x,_,S)=>{if(m!==x){if(m!==le)for(const E in m)!ms(E)&&!(E in x)&&i(p,E,m[E],null,S,_);for(const E in x){if(ms(E))continue;const D=x[E],P=m[E];D!==P&&E!=="value"&&i(p,E,P,D,S,_)}"value"in x&&i(p,"value",m.value,x.value,S)}},$t=(p,m,x,_,S,E,D,P,M)=>{const A=m.el=p?p.el:l(""),L=m.anchor=p?p.anchor:l("");let{patchFlag:F,dynamicChildren:B,slotScopeIds:q}=m;q&&(P=P?P.concat(q):q),p==null?(r(A,x,_),r(L,x,_),Se(m.children||[],x,L,S,E,D,P,M)):F>0&&F&64&&B&&p.dynamicChildren?(Xe(p.dynamicChildren,B,x,S,E,D,P),(m.key!=null||S&&m===S.subTree)&&ll(p,m,!0)):Z(p,m,x,L,S,E,D,P,M)},Qe=(p,m,x,_,S,E,D,P,M)=>{m.slotScopeIds=P,p==null?m.shapeFlag&512?S.ctx.activate(m,x,_,D,M):ls(m,x,_,S,E,D,M):Bt(p,m,M)},ls=(p,m,x,_,S,E,D)=>{const P=p.component=Zu(p,_,S);if(Wi(p)&&(P.ctx.renderer=V),ed(P,!1,D),P.asyncDep){if(S&&S.registerDep(P,xe,D),!p.el){const M=P.subTree=K(At);O(null,M,m,x)}}else xe(P,p,m,x,S,E,D)},Bt=(p,m,x)=>{const _=m.component=p.component;if(zu(p,m,x))if(_.asyncDep&&!_.asyncResolved){oe(_,m,x);return}else _.next=m,_.update();else m.el=p.el,_.vnode=m},xe=(p,m,x,_,S,E,D)=>{const P=()=>{if(p.isMounted){let{next:F,bu:B,u:q,parent:Q,vnode:ue}=p;{const et=al(p);if(et){F&&(F.el=ue.el,oe(p,F,D)),et.asyncDep.then(()=>{p.isUnmounted||P()});return}}let re=F,De;Pt(p,!1),F?(F.el=ue.el,oe(p,F,D)):F=ue,B&&Ls(B),(De=F.props&&F.props.onVnodeBeforeUpdate)&&nt(De,Q,F,ue),Pt(p,!0);const Oe=ro(p),Ze=p.subTree;p.subTree=Oe,w(Ze,Oe,f(Ze.el),k(Ze),p,S,E),F.el=Oe.el,re===null&&Hu(p,Oe.el),q&&Ne(q,S),(De=F.props&&F.props.onVnodeUpdated)&&Ne(()=>nt(De,Q,F,ue),S)}else{let F;const{el:B,props:q}=m,{bm:Q,m:ue,parent:re,root:De,type:Oe}=p,Ze=vs(m);Pt(p,!1),Q&&Ls(Q),!Ze&&(F=q&&q.onVnodeBeforeMount)&&nt(F,re,m),Pt(p,!0);{De.ce&&De.ce._injectChildStyle(Oe);const et=p.subTree=ro(p);w(null,et,x,_,p,S,E),m.el=et.el}if(ue&&Ne(ue,S),!Ze&&(F=q&&q.onVnodeMounted)){const et=m;Ne(()=>nt(F,re,et),S)}(m.shapeFlag&256||re&&vs(re.vnode)&&re.vnode.shapeFlag&256)&&p.a&&Ne(p.a,S),p.isMounted=!0,m=x=_=null}};p.scope.on();const M=p.effect=new _i(P);p.scope.off();const A=p.update=M.run.bind(M),L=p.job=M.runIfDirty.bind(M);L.i=p,L.id=p.uid,M.scheduler=()=>kr(L),Pt(p,!0),A()},oe=(p,m,x)=>{m.component=p;const _=p.vnode.props;p.vnode=m,p.next=null,Ru(p,m.props,_,x),$u(p,m.children,x),xt(),Xr(p),bt()},Z=(p,m,x,_,S,E,D,P,M=!1)=>{const A=p&&p.children,L=p?p.shapeFlag:0,F=m.children,{patchFlag:B,shapeFlag:q}=m;if(B>0){if(B&128){_t(A,F,x,_,S,E,D,P,M);return}else if(B&256){ut(A,F,x,_,S,E,D,P,M);return}}q&8?(L&16&&Ue(A,S,E),F!==A&&u(x,F)):L&16?q&16?_t(A,F,x,_,S,E,D,P,M):Ue(A,S,E,!0):(L&8&&u(x,""),q&16&&Se(F,x,_,S,E,D,P,M))},ut=(p,m,x,_,S,E,D,P,M)=>{p=p||Wt,m=m||Wt;const A=p.length,L=m.length,F=Math.min(A,L);let B;for(B=0;B<F;B++){const q=m[B]=M?St(m[B]):ot(m[B]);w(p[B],q,x,null,S,E,D,P,M)}A>L?Ue(p,S,E,!0,!1,F):Se(m,x,_,S,E,D,P,M,F)},_t=(p,m,x,_,S,E,D,P,M)=>{let A=0;const L=m.length;let F=p.length-1,B=L-1;for(;A<=F&&A<=B;){const q=p[A],Q=m[A]=M?St(m[A]):ot(m[A]);if(ds(q,Q))w(q,Q,x,null,S,E,D,P,M);else break;A++}for(;A<=F&&A<=B;){const q=p[F],Q=m[B]=M?St(m[B]):ot(m[B]);if(ds(q,Q))w(q,Q,x,null,S,E,D,P,M);else break;F--,B--}if(A>F){if(A<=B){const q=B+1,Q=q<L?m[q].el:_;for(;A<=B;)w(null,m[A]=M?St(m[A]):ot(m[A]),x,Q,S,E,D,P,M),A++}}else if(A>B)for(;A<=F;)Ae(p[A],S,E,!0),A++;else{const q=A,Q=A,ue=new Map;for(A=Q;A<=B;A++){const je=m[A]=M?St(m[A]):ot(m[A]);je.key!=null&&ue.set(je.key,A)}let re,De=0;const Oe=B-Q+1;let Ze=!1,et=0;const as=new Array(Oe);for(A=0;A<Oe;A++)as[A]=0;for(A=q;A<=F;A++){const je=p[A];if(De>=Oe){Ae(je,S,E,!0);continue}let tt;if(je.key!=null)tt=ue.get(je.key);else for(re=Q;re<=B;re++)if(as[re-Q]===0&&ds(je,m[re])){tt=re;break}tt===void 0?Ae(je,S,E,!0):(as[tt-Q]=A+1,tt>=et?et=tt:Ze=!0,w(je,m[tt],x,null,S,E,D,P,M),De++)}const $r=Ze?ju(as):Wt;for(re=$r.length-1,A=Oe-1;A>=0;A--){const je=Q+A,tt=m[je],Mr=je+1<L?m[je+1].el:_;as[A]===0?w(null,tt,x,Mr,S,E,D,P,M):Ze&&(re<0||A!==$r[re]?Ye(tt,x,Mr,2):re--)}}},Ye=(p,m,x,_,S=null)=>{const{el:E,type:D,transition:P,children:M,shapeFlag:A}=p;if(A&6){Ye(p.component.subTree,m,x,_);return}if(A&128){p.suspense.move(m,x,_);return}if(A&64){D.move(p,m,x,V);return}if(D===ge){r(E,m,x);for(let F=0;F<M.length;F++)Ye(M[F],m,x,_);r(p.anchor,m,x);return}if(D===qs){j(p,m,x);return}if(_!==2&&A&1&&P)if(_===0)P.beforeEnter(E),r(E,m,x),Ne(()=>P.enter(E),S);else{const{leave:F,delayLeave:B,afterLeave:q}=P,Q=()=>{p.ctx.isUnmounted?n(E):r(E,m,x)},ue=()=>{F(E,()=>{Q(),q&&q()})};B?B(E,Q,ue):ue()}else r(E,m,x)},Ae=(p,m,x,_=!1,S=!1)=>{const{type:E,props:D,ref:P,children:M,dynamicChildren:A,shapeFlag:L,patchFlag:F,dirs:B,cacheIndex:q}=p;if(F===-2&&(S=!1),P!=null&&(xt(),tn(P,null,x,p,!0),bt()),q!=null&&(m.renderCache[q]=void 0),L&256){m.ctx.deactivate(p);return}const Q=L&1&&B,ue=!vs(p);let re;if(ue&&(re=D&&D.onVnodeBeforeUnmount)&&nt(re,m,p),L&6)js(p.component,x,_);else{if(L&128){p.suspense.unmount(x,_);return}Q&&Mt(p,null,m,"beforeUnmount"),L&64?p.type.remove(p,m,x,V,_):A&&!A.hasOnce&&(E!==ge||F>0&&F&64)?Ue(A,m,x,!1,!0):(E===ge&&F&384||!S&&L&16)&&Ue(M,m,x),_&&Lt(p)}(ue&&(re=D&&D.onVnodeUnmounted)||Q)&&Ne(()=>{re&&nt(re,m,p),Q&&Mt(p,null,m,"unmounted")},x)},Lt=p=>{const{type:m,el:x,anchor:_,transition:S}=p;if(m===ge){zt(x,_);return}if(m===qs){I(p);return}const E=()=>{n(x),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(p.shapeFlag&1&&S&&!S.persisted){const{leave:D,delayLeave:P}=S,M=()=>D(x,E);P?P(p.el,E,M):M()}else E()},zt=(p,m)=>{let x;for(;p!==m;)x=g(p),n(p),p=x;n(m)},js=(p,m,x)=>{const{bum:_,scope:S,job:E,subTree:D,um:P,m:M,a:A,parent:L,slots:{__:F}}=p;no(M),no(A),_&&Ls(_),L&&z(F)&&F.forEach(B=>{L.renderCache[B]=void 0}),S.stop(),E&&(E.flags|=8,Ae(D,p,m,x)),P&&Ne(P,m),Ne(()=>{p.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},Ue=(p,m,x,_=!1,S=!1,E=0)=>{for(let D=E;D<p.length;D++)Ae(p[D],m,x,_,S)},k=p=>{if(p.shapeFlag&6)return k(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const m=g(p.anchor||p.el),x=m&&m[tu];return x?g(x):m};let U=!1;const N=(p,m,x)=>{p==null?m._vnode&&Ae(m._vnode,null,null,!0):w(m._vnode||null,p,m,null,null,null,x),m._vnode=p,U||(U=!0,Xr(),Li(),U=!1)},V={p:w,um:Ae,m:Ye,r:Lt,mt:ls,mc:Se,pc:Z,pbc:Xe,n:k,o:e};return{render:N,hydrate:void 0,createApp:Su(N)}}function jn({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function Pt({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Du(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ll(e,t,s=!1){const r=e.children,n=t.children;if(z(r)&&z(n))for(let i=0;i<r.length;i++){const a=r[i];let l=n[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=n[i]=St(n[i]),l.el=a.el),!s&&l.patchFlag!==-2&&ll(a,l)),l.type===_n&&(l.el=a.el),l.type===At&&!l.el&&(l.el=a.el)}}function ju(e){const t=e.slice(),s=[0];let r,n,i,a,l;const c=e.length;for(r=0;r<c;r++){const d=e[r];if(d!==0){if(n=s[s.length-1],e[n]<d){t[r]=n,s.push(r);continue}for(i=0,a=s.length-1;i<a;)l=i+a>>1,e[s[l]]<d?i=l+1:a=l;d<e[s[i]]&&(i>0&&(t[r]=s[i-1]),s[i]=r)}}for(i=s.length,a=s[i-1];i-- >0;)s[i]=a,a=t[a];return s}function al(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:al(t)}function no(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Nu=Symbol.for("v-scx"),Fu=()=>vt(Nu);function Hs(e,t,s){return cl(e,t,s)}function cl(e,t,s=le){const{immediate:r,deep:n,flush:i,once:a}=s,l=Ee({},s),c=t&&r||!t&&i!=="post";let d;if(Rs){if(i==="sync"){const v=Fu();d=v.__watcherHandles||(v.__watcherHandles=[])}else if(!c){const v=()=>{};return v.stop=it,v.resume=it,v.pause=it,v}}const u=_e;l.call=(v,h,w)=>at(v,u,h,w);let f=!1;i==="post"?l.scheduler=v=>{Ne(v,u&&u.suspense)}:i!=="sync"&&(f=!0,l.scheduler=(v,h)=>{h?v():kr(v)}),l.augmentJob=v=>{t&&(v.flags|=4),f&&(v.flags|=2,u&&(v.id=u.uid,v.i=u))};const g=Qc(e,t,l);return Rs&&(d?d.push(g):c&&g()),g}function Iu(e,t,s){const r=this.proxy,n=ve(e)?e.includes(".")?ul(r,e):()=>r[e]:e.bind(r,r);let i;W(t)?i=t:(i=t.handler,s=t);const a=Ds(this),l=cl(n,i.bind(r),s);return a(),l}function ul(e,t){const s=t.split(".");return()=>{let r=e;for(let n=0;n<s.length&&r;n++)r=r[s[n]];return r}}const Uu=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ze(t)}Modifiers`]||e[`${Vt(t)}Modifiers`];function Vu(e,t,...s){if(e.isUnmounted)return;const r=e.vnode.props||le;let n=s;const i=t.startsWith("update:"),a=i&&Uu(r,t.slice(7));a&&(a.trim&&(n=s.map(u=>ve(u)?u.trim():u)),a.number&&(n=s.map(Xs)));let l,c=r[l=An(t)]||r[l=An(ze(t))];!c&&i&&(c=r[l=An(Vt(t))]),c&&at(c,e,6,n);const d=r[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,at(d,e,6,n)}}function dl(e,t,s=!1){const r=t.emitsCache,n=r.get(e);if(n!==void 0)return n;const i=e.emits;let a={},l=!1;if(!W(e)){const c=d=>{const u=dl(d,t,!0);u&&(l=!0,Ee(a,u))};!s&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!l?(me(e)&&r.set(e,null),null):(z(i)?i.forEach(c=>a[c]=null):Ee(a,i),me(e)&&r.set(e,a),a)}function wn(e,t){return!e||!pn(t)?!1:(t=t.slice(2).replace(/Once$/,""),se(e,t[0].toLowerCase()+t.slice(1))||se(e,Vt(t))||se(e,t))}function ro(e){const{type:t,vnode:s,proxy:r,withProxy:n,propsOptions:[i],slots:a,attrs:l,emit:c,render:d,renderCache:u,props:f,data:g,setupState:v,ctx:h,inheritAttrs:w}=e,C=en(e);let O,$;try{if(s.shapeFlag&4){const I=n||r,J=I;O=ot(d.call(J,I,u,f,v,g,h)),$=l}else{const I=t;O=ot(I.length>1?I(f,{attrs:l,slots:a,emit:c}):I(f,null)),$=t.props?l:Bu(l)}}catch(I){bs.length=0,bn(I,e,1),O=K(At)}let j=O;if($&&w!==!1){const I=Object.keys($),{shapeFlag:J}=j;I.length&&J&7&&(i&&I.some(ur)&&($=Lu($,i)),j=es(j,$,!1,!0))}return s.dirs&&(j=es(j,null,!1,!0),j.dirs=j.dirs?j.dirs.concat(s.dirs):s.dirs),s.transition&&Er(j,s.transition),O=j,en(C),O}const Bu=e=>{let t;for(const s in e)(s==="class"||s==="style"||pn(s))&&((t||(t={}))[s]=e[s]);return t},Lu=(e,t)=>{const s={};for(const r in e)(!ur(r)||!(r.slice(9)in t))&&(s[r]=e[r]);return s};function zu(e,t,s){const{props:r,children:n,component:i}=e,{props:a,children:l,patchFlag:c}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&c>=0){if(c&1024)return!0;if(c&16)return r?oo(r,a,d):!!a;if(c&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const g=u[f];if(a[g]!==r[g]&&!wn(d,g))return!0}}}else return(n||l)&&(!l||!l.$stable)?!0:r===a?!1:r?a?oo(r,a,d):!0:!!a;return!1}function oo(e,t,s){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let n=0;n<r.length;n++){const i=r[n];if(t[i]!==e[i]&&!wn(s,i))return!0}return!1}function Hu({vnode:e,parent:t},s){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=s,t=t.parent;else break}}const fl=e=>e.__isSuspense;function qu(e,t){t&&t.pendingBranch?z(e)?t.effects.push(...e):t.effects.push(e):eu(e)}const ge=Symbol.for("v-fgt"),_n=Symbol.for("v-txt"),At=Symbol.for("v-cmt"),qs=Symbol.for("v-stc"),bs=[];let Ie=null;function R(e=!1){bs.push(Ie=e?null:[])}function Ku(){bs.pop(),Ie=bs[bs.length-1]||null}let Cs=1;function io(e,t=!1){Cs+=e,e<0&&Ie&&t&&(Ie.hasOnce=!0)}function pl(e){return e.dynamicChildren=Cs>0?Ie||Wt:null,Ku(),Cs>0&&Ie&&Ie.push(e),e}function T(e,t,s,r,n,i){return pl(o(e,t,s,r,n,i,!0))}function Wu(e,t,s,r,n){return pl(K(e,t,s,r,n,!0))}function nn(e){return e?e.__v_isVNode===!0:!1}function ds(e,t){return e.type===t.type&&e.key===t.key}const ml=({key:e})=>e??null,Ks=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?ve(e)||ke(e)||W(e)?{i:Fe,r:e,k:t,f:!!s}:e:null);function o(e,t=null,s=null,r=0,n=null,i=e===ge?0:1,a=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ml(t),ref:t&&Ks(t),scopeId:Hi,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:Fe};return l?(Rr(c,s),i&128&&e.normalize(c)):s&&(c.shapeFlag|=ve(s)?8:16),Cs>0&&!a&&Ie&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&Ie.push(c),c}const K=Ju;function Ju(e,t=null,s=null,r=0,n=null,i=!1){if((!e||e===gu)&&(e=At),nn(e)){const l=es(e,t,!0);return s&&Rr(l,s),Cs>0&&!i&&Ie&&(l.shapeFlag&6?Ie[Ie.indexOf(e)]=l:Ie.push(l)),l.patchFlag=-2,l}if(od(e)&&(e=e.__vccOpts),t){t=Gu(t);let{class:l,style:c}=t;l&&!ve(l)&&(t.class=ae(l)),me(c)&&(wr(c)&&!z(c)&&(c=Ee({},c)),t.style=pr(c))}const a=ve(e)?1:fl(e)?128:su(e)?64:me(e)?4:W(e)?2:0;return o(e,t,s,r,n,a,i,!0)}function Gu(e){return e?wr(e)||tl(e)?Ee({},e):e:null}function es(e,t,s=!1,r=!1){const{props:n,ref:i,patchFlag:a,children:l,transition:c}=e,d=t?Xu(n||{},t):n,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&ml(d),ref:t&&t.ref?s&&i?z(i)?i.concat(Ks(t)):[i,Ks(t)]:Ks(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ge?a===-1?16:a|16:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&es(e.ssContent),ssFallback:e.ssFallback&&es(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&Er(u,c.clone(u)),u}function H(e=" ",t=0){return K(_n,null,e,t)}function Be(e,t){const s=K(qs,null,e);return s.staticCount=t,s}function fe(e="",t=!1){return t?(R(),Wu(At,null,e)):K(At,null,e)}function ot(e){return e==null||typeof e=="boolean"?K(At):z(e)?K(ge,null,e.slice()):nn(e)?St(e):K(_n,null,String(e))}function St(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:es(e)}function Rr(e,t){let s=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(z(t))s=16;else if(typeof t=="object")if(r&65){const n=t.default;n&&(n._c&&(n._d=!1),Rr(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!tl(t)?t._ctx=Fe:n===3&&Fe&&(Fe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else W(t)?(t={default:t,_ctx:Fe},s=32):(t=String(t),r&64?(s=16,t=[H(t)]):s=8);e.children=t,e.shapeFlag|=s}function Xu(...e){const t={};for(let s=0;s<e.length;s++){const r=e[s];for(const n in r)if(n==="class")t.class!==r.class&&(t.class=ae([t.class,r.class]));else if(n==="style")t.style=pr([t.style,r.style]);else if(pn(n)){const i=t[n],a=r[n];a&&i!==a&&!(z(i)&&i.includes(a))&&(t[n]=i?[].concat(i,a):a)}else n!==""&&(t[n]=r[n])}return t}function nt(e,t,s,r=null){at(e,t,7,[s,r])}const Qu=Yi();let Yu=0;function Zu(e,t,s){const r=e.type,n=(t?t.appContext:e.appContext)||Qu,i={uid:Yu++,vnode:e,type:r,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new kc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:nl(r,n),emitsOptions:dl(r,n),emit:null,emitted:null,propsDefaults:le,inheritAttrs:r.inheritAttrs,ctx:le,data:le,props:le,attrs:le,slots:le,refs:le,setupState:le,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Vu.bind(null,i),e.ce&&e.ce(i),i}let _e=null,rn,tr;{const e=gn(),t=(s,r)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(r),i=>{n.length>1?n.forEach(a=>a(i)):n[0](i)}};rn=t("__VUE_INSTANCE_SETTERS__",s=>_e=s),tr=t("__VUE_SSR_SETTERS__",s=>Rs=s)}const Ds=e=>{const t=_e;return rn(e),e.scope.on(),()=>{e.scope.off(),rn(t)}},lo=()=>{_e&&_e.scope.off(),rn(null)};function hl(e){return e.vnode.shapeFlag&4}let Rs=!1;function ed(e,t=!1,s=!1){t&&tr(t);const{props:r,children:n}=e.vnode,i=hl(e);Cu(e,r,i,t),Ou(e,n,s||t);const a=i?td(e,t):void 0;return t&&tr(!1),a}function td(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,xu);const{setup:r}=s;if(r){xt();const n=e.setupContext=r.length>1?nd(e):null,i=Ds(e),a=Ps(r,e,0,[e.props,n]),l=hi(a);if(bt(),i(),(l||e.sp)&&!vs(e)&&Ki(e),l){if(a.then(lo,lo),t)return a.then(c=>{ao(e,c)}).catch(c=>{bn(c,e,0)});e.asyncDep=a}else ao(e,a)}else gl(e)}function ao(e,t,s){W(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:me(t)&&(e.setupState=Ui(t)),gl(e)}function gl(e,t,s){const r=e.type;e.render||(e.render=r.render||it);{const n=Ds(e);xt();try{bu(e)}finally{bt(),n()}}}const sd={get(e,t){return ye(e,"get",""),e[t]}};function nd(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,sd),slots:e.slots,emit:e.emit,expose:t}}function kn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Ui(zc(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in xs)return xs[s](e)},has(t,s){return s in t||s in xs}})):e.proxy}function rd(e,t=!0){return W(e)?e.displayName||e.name:e.name||t&&e.__name}function od(e){return W(e)&&"__vccOpts"in e}const qe=(e,t)=>Gc(e,t,Rs);function vl(e,t,s){const r=arguments.length;return r===2?me(t)&&!z(t)?nn(t)?K(e,null,[t]):K(e,t):K(e,null,t):(r>3?s=Array.prototype.slice.call(arguments,2):r===3&&nn(s)&&(s=[s]),K(e,t,s))}const id="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let sr;const co=typeof window<"u"&&window.trustedTypes;if(co)try{sr=co.createPolicy("vue",{createHTML:e=>e})}catch{}const xl=sr?e=>sr.createHTML(e):e=>e,ld="http://www.w3.org/2000/svg",ad="http://www.w3.org/1998/Math/MathML",pt=typeof document<"u"?document:null,uo=pt&&pt.createElement("template"),cd={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,r)=>{const n=t==="svg"?pt.createElementNS(ld,e):t==="mathml"?pt.createElementNS(ad,e):s?pt.createElement(e,{is:s}):pt.createElement(e);return e==="select"&&r&&r.multiple!=null&&n.setAttribute("multiple",r.multiple),n},createText:e=>pt.createTextNode(e),createComment:e=>pt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>pt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,r,n,i){const a=s?s.previousSibling:t.lastChild;if(n&&(n===i||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===i||!(n=n.nextSibling)););else{uo.innerHTML=xl(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const l=uo.content;if(r==="svg"||r==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,s)}return[a?a.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},ud=Symbol("_vtc");function dd(e,t,s){const r=e[ud];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const fo=Symbol("_vod"),fd=Symbol("_vsh"),pd=Symbol(""),md=/(^|;)\s*display\s*:/;function hd(e,t,s){const r=e.style,n=ve(s);let i=!1;if(s&&!n){if(t)if(ve(t))for(const a of t.split(";")){const l=a.slice(0,a.indexOf(":")).trim();s[l]==null&&Ws(r,l,"")}else for(const a in t)s[a]==null&&Ws(r,a,"");for(const a in s)a==="display"&&(i=!0),Ws(r,a,s[a])}else if(n){if(t!==s){const a=r[pd];a&&(s+=";"+a),r.cssText=s,i=md.test(s)}}else t&&e.removeAttribute("style");fo in e&&(e[fo]=i?r.display:"",e[fd]&&(r.display="none"))}const po=/\s*!important$/;function Ws(e,t,s){if(z(s))s.forEach(r=>Ws(e,t,r));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const r=gd(e,t);po.test(s)?e.setProperty(Vt(r),s.replace(po,""),"important"):e[r]=s}}const mo=["Webkit","Moz","ms"],Nn={};function gd(e,t){const s=Nn[t];if(s)return s;let r=ze(t);if(r!=="filter"&&r in e)return Nn[t]=r;r=hn(r);for(let n=0;n<mo.length;n++){const i=mo[n]+r;if(i in e)return Nn[t]=i}return t}const ho="http://www.w3.org/1999/xlink";function go(e,t,s,r,n,i=wc(t)){r&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(ho,t.slice(6,t.length)):e.setAttributeNS(ho,t,s):s==null||i&&!bi(s)?e.removeAttribute(t):e.setAttribute(t,i?"":lt(s)?String(s):s)}function vo(e,t,s,r,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?xl(s):s);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,c=s==null?e.type==="checkbox"?"on":"":String(s);(l!==c||!("_value"in e))&&(e.value=c),s==null&&e.removeAttribute(t),e._value=s;return}let a=!1;if(s===""||s==null){const l=typeof e[t];l==="boolean"?s=bi(s):s==null&&l==="string"?(s="",a=!0):l==="number"&&(s=0,a=!0)}try{e[t]=s}catch{}a&&e.removeAttribute(n||t)}function gt(e,t,s,r){e.addEventListener(t,s,r)}function vd(e,t,s,r){e.removeEventListener(t,s,r)}const xo=Symbol("_vei");function xd(e,t,s,r,n=null){const i=e[xo]||(e[xo]={}),a=i[t];if(r&&a)a.value=r;else{const[l,c]=bd(t);if(r){const d=i[t]=_d(r,n);gt(e,l,d,c)}else a&&(vd(e,l,a,c),i[t]=void 0)}}const bo=/(?:Once|Passive|Capture)$/;function bd(e){let t;if(bo.test(e)){t={};let r;for(;r=e.match(bo);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Vt(e.slice(2)),t]}let Fn=0;const yd=Promise.resolve(),wd=()=>Fn||(yd.then(()=>Fn=0),Fn=Date.now());function _d(e,t){const s=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=s.attached)return;at(kd(r,s.value),t,5,[r])};return s.value=e,s.attached=wd(),s}function kd(e,t){if(z(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(r=>n=>!n._stopped&&r&&r(n))}else return t}const yo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Ed=(e,t,s,r,n,i)=>{const a=n==="svg";t==="class"?dd(e,r,a):t==="style"?hd(e,s,r):pn(t)?ur(t)||xd(e,t,s,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Sd(e,t,r,a))?(vo(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&go(e,t,r,a,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ve(r))?vo(e,ze(t),r,i,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),go(e,t,r,a))};function Sd(e,t,s,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&yo(t)&&W(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return yo(t)&&ve(s)?!1:t in e}const Ot=e=>{const t=e.props["onUpdate:modelValue"]||!1;return z(t)?s=>Ls(t,s):t};function Cd(e){e.target.composing=!0}function wo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Le=Symbol("_assign"),he={created(e,{modifiers:{lazy:t,trim:s,number:r}},n){e[Le]=Ot(n);const i=r||n.props&&n.props.type==="number";gt(e,t?"change":"input",a=>{if(a.target.composing)return;let l=e.value;s&&(l=l.trim()),i&&(l=Xs(l)),e[Le](l)}),s&&gt(e,"change",()=>{e.value=e.value.trim()}),t||(gt(e,"compositionstart",Cd),gt(e,"compositionend",wo),gt(e,"change",wo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:r,trim:n,number:i}},a){if(e[Le]=Ot(a),e.composing)return;const l=(i||e.type==="number")&&!/^0\d/.test(e.value)?Xs(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(r&&t===s||n&&e.value.trim()===c)||(e.value=c))}},bl={deep:!0,created(e,t,s){e[Le]=Ot(s),gt(e,"change",()=>{const r=e._modelValue,n=ts(e),i=e.checked,a=e[Le];if(z(r)){const l=mr(r,n),c=l!==-1;if(i&&!c)a(r.concat(n));else if(!i&&c){const d=[...r];d.splice(l,1),a(d)}}else if(is(r)){const l=new Set(r);i?l.add(n):l.delete(n),a(l)}else a(yl(e,i))})},mounted:_o,beforeUpdate(e,t,s){e[Le]=Ot(s),_o(e,t,s)}};function _o(e,{value:t,oldValue:s},r){e._modelValue=t;let n;if(z(t))n=mr(t,r.props.value)>-1;else if(is(t))n=t.has(r.props.value);else{if(t===s)return;n=Ut(t,yl(e,!0))}e.checked!==n&&(e.checked=n)}const ko={created(e,{value:t},s){e.checked=Ut(t,s.props.value),e[Le]=Ot(s),gt(e,"change",()=>{e[Le](ts(e))})},beforeUpdate(e,{value:t,oldValue:s},r){e[Le]=Ot(r),t!==s&&(e.checked=Ut(t,r.props.value))}},Zt={deep:!0,created(e,{value:t,modifiers:{number:s}},r){const n=is(t);gt(e,"change",()=>{const i=Array.prototype.filter.call(e.options,a=>a.selected).map(a=>s?Xs(ts(a)):ts(a));e[Le](e.multiple?n?new Set(i):i:i[0]),e._assigning=!0,_r(()=>{e._assigning=!1})}),e[Le]=Ot(r)},mounted(e,{value:t}){Eo(e,t)},beforeUpdate(e,t,s){e[Le]=Ot(s)},updated(e,{value:t}){e._assigning||Eo(e,t)}};function Eo(e,t){const s=e.multiple,r=z(t);if(!(s&&!r&&!is(t))){for(let n=0,i=e.options.length;n<i;n++){const a=e.options[n],l=ts(a);if(s)if(r){const c=typeof l;c==="string"||c==="number"?a.selected=t.some(d=>String(d)===String(l)):a.selected=mr(t,l)>-1}else a.selected=t.has(l);else if(Ut(ts(a),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function ts(e){return"_value"in e?e._value:e.value}function yl(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const Rd=["ctrl","shift","alt","meta"],Td={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Rd.some(s=>e[`${s}Key`]&&!t.includes(s))},En=(e,t)=>{const s=e._withMods||(e._withMods={}),r=t.join(".");return s[r]||(s[r]=(n,...i)=>{for(let a=0;a<t.length;a++){const l=Td[t[a]];if(l&&l(n,t))return}return e(n,...i)})},Ad=Ee({patchProp:Ed},cd);let So;function Od(){return So||(So=Mu(Ad))}const $d=(...e)=>{const t=Od().createApp(...e),{mount:s}=t;return t.mount=r=>{const n=Pd(r);if(!n)return;const i=t._component;!W(i)&&!i.render&&!i.template&&(i.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const a=s(n,!1,Md(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),a},t};function Md(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Pd(e){return ve(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Kt=typeof document<"u";function wl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Dd(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&wl(e.default)}const ee=Object.assign;function In(e,t){const s={};for(const r in t){const n=t[r];s[r]=We(n)?n.map(e):e(n)}return s}const ys=()=>{},We=Array.isArray,_l=/#/g,jd=/&/g,Nd=/\//g,Fd=/=/g,Id=/\?/g,kl=/\+/g,Ud=/%5B/g,Vd=/%5D/g,El=/%5E/g,Bd=/%60/g,Sl=/%7B/g,Ld=/%7C/g,Cl=/%7D/g,zd=/%20/g;function Tr(e){return encodeURI(""+e).replace(Ld,"|").replace(Ud,"[").replace(Vd,"]")}function Hd(e){return Tr(e).replace(Sl,"{").replace(Cl,"}").replace(El,"^")}function nr(e){return Tr(e).replace(kl,"%2B").replace(zd,"+").replace(_l,"%23").replace(jd,"%26").replace(Bd,"`").replace(Sl,"{").replace(Cl,"}").replace(El,"^")}function qd(e){return nr(e).replace(Fd,"%3D")}function Kd(e){return Tr(e).replace(_l,"%23").replace(Id,"%3F")}function Wd(e){return e==null?"":Kd(e).replace(Nd,"%2F")}function Ts(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Jd=/\/$/,Gd=e=>e.replace(Jd,"");function Un(e,t,s="/"){let r,n={},i="",a="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(r=t.slice(0,c),i=t.slice(c+1,l>-1?l:t.length),n=e(i)),l>-1&&(r=r||t.slice(0,l),a=t.slice(l,t.length)),r=Zd(r??t,s),{fullPath:r+(i&&"?")+i+a,path:r,query:n,hash:Ts(a)}}function Xd(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function Co(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Qd(e,t,s){const r=t.matched.length-1,n=s.matched.length-1;return r>-1&&r===n&&ss(t.matched[r],s.matched[n])&&Rl(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function ss(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Rl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!Yd(e[s],t[s]))return!1;return!0}function Yd(e,t){return We(e)?Ro(e,t):We(t)?Ro(t,e):e===t}function Ro(e,t){return We(t)?e.length===t.length&&e.every((s,r)=>s===t[r]):e.length===1&&e[0]===t}function Zd(e,t){if(e.startsWith("/"))return e;if(!e)return t;const s=t.split("/"),r=e.split("/"),n=r[r.length-1];(n===".."||n===".")&&r.push("");let i=s.length-1,a,l;for(a=0;a<r.length;a++)if(l=r[a],l!==".")if(l==="..")i>1&&i--;else break;return s.slice(0,i).join("/")+"/"+r.slice(a).join("/")}const kt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var As;(function(e){e.pop="pop",e.push="push"})(As||(As={}));var ws;(function(e){e.back="back",e.forward="forward",e.unknown=""})(ws||(ws={}));function ef(e){if(!e)if(Kt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Gd(e)}const tf=/^[^#]+#/;function sf(e,t){return e.replace(tf,"#")+t}function nf(e,t){const s=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-s.left-(t.left||0),top:r.top-s.top-(t.top||0)}}const Sn=()=>({left:window.scrollX,top:window.scrollY});function rf(e){let t;if("el"in e){const s=e.el,r=typeof s=="string"&&s.startsWith("#"),n=typeof s=="string"?r?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n)return;t=nf(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function To(e,t){return(history.state?history.state.position-t:-1)+e}const rr=new Map;function of(e,t){rr.set(e,t)}function lf(e){const t=rr.get(e);return rr.delete(e),t}let af=()=>location.protocol+"//"+location.host;function Tl(e,t){const{pathname:s,search:r,hash:n}=t,i=e.indexOf("#");if(i>-1){let l=n.includes(e.slice(i))?e.slice(i).length:1,c=n.slice(l);return c[0]!=="/"&&(c="/"+c),Co(c,"")}return Co(s,e)+r+n}function cf(e,t,s,r){let n=[],i=[],a=null;const l=({state:g})=>{const v=Tl(e,location),h=s.value,w=t.value;let C=0;if(g){if(s.value=v,t.value=g,a&&a===h){a=null;return}C=w?g.position-w.position:0}else r(v);n.forEach(O=>{O(s.value,h,{delta:C,type:As.pop,direction:C?C>0?ws.forward:ws.back:ws.unknown})})};function c(){a=s.value}function d(g){n.push(g);const v=()=>{const h=n.indexOf(g);h>-1&&n.splice(h,1)};return i.push(v),v}function u(){const{history:g}=window;g.state&&g.replaceState(ee({},g.state,{scroll:Sn()}),"")}function f(){for(const g of i)g();i=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:d,destroy:f}}function Ao(e,t,s,r=!1,n=!1){return{back:e,current:t,forward:s,replaced:r,position:window.history.length,scroll:n?Sn():null}}function uf(e){const{history:t,location:s}=window,r={value:Tl(e,s)},n={value:t.state};n.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(c,d,u){const f=e.indexOf("#"),g=f>-1?(s.host&&document.querySelector("base")?e:e.slice(f))+c:af()+e+c;try{t[u?"replaceState":"pushState"](d,"",g),n.value=d}catch(v){console.error(v),s[u?"replace":"assign"](g)}}function a(c,d){const u=ee({},t.state,Ao(n.value.back,c,n.value.forward,!0),d,{position:n.value.position});i(c,u,!0),r.value=c}function l(c,d){const u=ee({},n.value,t.state,{forward:c,scroll:Sn()});i(u.current,u,!0);const f=ee({},Ao(r.value,c,null),{position:u.position+1},d);i(c,f,!1),r.value=c}return{location:r,state:n,push:l,replace:a}}function df(e){e=ef(e);const t=uf(e),s=cf(e,t.state,t.location,t.replace);function r(i,a=!0){a||s.pauseListeners(),history.go(i)}const n=ee({location:"",base:e,go:r,createHref:sf.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function ff(e){return typeof e=="string"||e&&typeof e=="object"}function Al(e){return typeof e=="string"||typeof e=="symbol"}const Ol=Symbol("");var Oo;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Oo||(Oo={}));function ns(e,t){return ee(new Error,{type:e,[Ol]:!0},t)}function ft(e,t){return e instanceof Error&&Ol in e&&(t==null||!!(e.type&t))}const $o="[^/]+?",pf={sensitive:!1,strict:!1,start:!0,end:!0},mf=/[.+*?^${}()[\]/\\]/g;function hf(e,t){const s=ee({},pf,t),r=[];let n=s.start?"^":"";const i=[];for(const d of e){const u=d.length?[]:[90];s.strict&&!d.length&&(n+="/");for(let f=0;f<d.length;f++){const g=d[f];let v=40+(s.sensitive?.25:0);if(g.type===0)f||(n+="/"),n+=g.value.replace(mf,"\\$&"),v+=40;else if(g.type===1){const{value:h,repeatable:w,optional:C,regexp:O}=g;i.push({name:h,repeatable:w,optional:C});const $=O||$o;if($!==$o){v+=10;try{new RegExp(`(${$})`)}catch(I){throw new Error(`Invalid custom RegExp for param "${h}" (${$}): `+I.message)}}let j=w?`((?:${$})(?:/(?:${$}))*)`:`(${$})`;f||(j=C&&d.length<2?`(?:/${j})`:"/"+j),C&&(j+="?"),n+=j,v+=20,C&&(v+=-8),w&&(v+=-20),$===".*"&&(v+=-50)}u.push(v)}r.push(u)}if(s.strict&&s.end){const d=r.length-1;r[d][r[d].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const a=new RegExp(n,s.sensitive?"":"i");function l(d){const u=d.match(a),f={};if(!u)return null;for(let g=1;g<u.length;g++){const v=u[g]||"",h=i[g-1];f[h.name]=v&&h.repeatable?v.split("/"):v}return f}function c(d){let u="",f=!1;for(const g of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const v of g)if(v.type===0)u+=v.value;else if(v.type===1){const{value:h,repeatable:w,optional:C}=v,O=h in d?d[h]:"";if(We(O)&&!w)throw new Error(`Provided param "${h}" is an array but it is not repeatable (* or + modifiers)`);const $=We(O)?O.join("/"):O;if(!$)if(C)g.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${h}"`);u+=$}}return u||"/"}return{re:a,score:r,keys:i,parse:l,stringify:c}}function gf(e,t){let s=0;for(;s<e.length&&s<t.length;){const r=t[s]-e[s];if(r)return r;s++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function $l(e,t){let s=0;const r=e.score,n=t.score;for(;s<r.length&&s<n.length;){const i=gf(r[s],n[s]);if(i)return i;s++}if(Math.abs(n.length-r.length)===1){if(Mo(r))return 1;if(Mo(n))return-1}return n.length-r.length}function Mo(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const vf={type:0,value:""},xf=/[a-zA-Z0-9_]/;function bf(e){if(!e)return[[]];if(e==="/")return[[vf]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(v){throw new Error(`ERR (${s})/"${d}": ${v}`)}let s=0,r=s;const n=[];let i;function a(){i&&n.push(i),i=[]}let l=0,c,d="",u="";function f(){d&&(s===0?i.push({type:0,value:d}):s===1||s===2||s===3?(i.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:d,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),d="")}function g(){d+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&s!==2){r=s,s=4;continue}switch(s){case 0:c==="/"?(d&&f(),a()):c===":"?(f(),s=1):g();break;case 4:g(),s=r;break;case 1:c==="("?s=2:xf.test(c)?g():(f(),s=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:s=3:u+=c;break;case 3:f(),s=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,u="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${d}"`),f(),a(),n}function yf(e,t,s){const r=hf(bf(e.path),s),n=ee(r,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function wf(e,t){const s=[],r=new Map;t=No({strict:!1,end:!0,sensitive:!1},t);function n(f){return r.get(f)}function i(f,g,v){const h=!v,w=Do(f);w.aliasOf=v&&v.record;const C=No(t,f),O=[w];if("alias"in f){const I=typeof f.alias=="string"?[f.alias]:f.alias;for(const J of I)O.push(Do(ee({},w,{components:v?v.record.components:w.components,path:J,aliasOf:v?v.record:w})))}let $,j;for(const I of O){const{path:J}=I;if(g&&J[0]!=="/"){const ce=g.record.path,Y=ce[ce.length-1]==="/"?"":"/";I.path=g.record.path+(J&&Y+J)}if($=yf(I,g,C),v?v.alias.push($):(j=j||$,j!==$&&j.alias.push($),h&&f.name&&!jo($)&&a(f.name)),Ml($)&&c($),w.children){const ce=w.children;for(let Y=0;Y<ce.length;Y++)i(ce[Y],$,v&&v.children[Y])}v=v||$}return j?()=>{a(j)}:ys}function a(f){if(Al(f)){const g=r.get(f);g&&(r.delete(f),s.splice(s.indexOf(g),1),g.children.forEach(a),g.alias.forEach(a))}else{const g=s.indexOf(f);g>-1&&(s.splice(g,1),f.record.name&&r.delete(f.record.name),f.children.forEach(a),f.alias.forEach(a))}}function l(){return s}function c(f){const g=Ef(f,s);s.splice(g,0,f),f.record.name&&!jo(f)&&r.set(f.record.name,f)}function d(f,g){let v,h={},w,C;if("name"in f&&f.name){if(v=r.get(f.name),!v)throw ns(1,{location:f});C=v.record.name,h=ee(Po(g.params,v.keys.filter(j=>!j.optional).concat(v.parent?v.parent.keys.filter(j=>j.optional):[]).map(j=>j.name)),f.params&&Po(f.params,v.keys.map(j=>j.name))),w=v.stringify(h)}else if(f.path!=null)w=f.path,v=s.find(j=>j.re.test(w)),v&&(h=v.parse(w),C=v.record.name);else{if(v=g.name?r.get(g.name):s.find(j=>j.re.test(g.path)),!v)throw ns(1,{location:f,currentLocation:g});C=v.record.name,h=ee({},g.params,f.params),w=v.stringify(h)}const O=[];let $=v;for(;$;)O.unshift($.record),$=$.parent;return{name:C,path:w,params:h,matched:O,meta:kf(O)}}e.forEach(f=>i(f));function u(){s.length=0,r.clear()}return{addRoute:i,resolve:d,removeRoute:a,clearRoutes:u,getRoutes:l,getRecordMatcher:n}}function Po(e,t){const s={};for(const r of t)r in e&&(s[r]=e[r]);return s}function Do(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:_f(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function _f(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const r in e.components)t[r]=typeof s=="object"?s[r]:s;return t}function jo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function kf(e){return e.reduce((t,s)=>ee(t,s.meta),{})}function No(e,t){const s={};for(const r in e)s[r]=r in t?t[r]:e[r];return s}function Ef(e,t){let s=0,r=t.length;for(;s!==r;){const i=s+r>>1;$l(e,t[i])<0?r=i:s=i+1}const n=Sf(e);return n&&(r=t.lastIndexOf(n,r-1)),r}function Sf(e){let t=e;for(;t=t.parent;)if(Ml(t)&&$l(e,t)===0)return t}function Ml({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Cf(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<r.length;++n){const i=r[n].replace(kl," "),a=i.indexOf("="),l=Ts(a<0?i:i.slice(0,a)),c=a<0?null:Ts(i.slice(a+1));if(l in t){let d=t[l];We(d)||(d=t[l]=[d]),d.push(c)}else t[l]=c}return t}function Fo(e){let t="";for(let s in e){const r=e[s];if(s=qd(s),r==null){r!==void 0&&(t+=(t.length?"&":"")+s);continue}(We(r)?r.map(i=>i&&nr(i)):[r&&nr(r)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+s,i!=null&&(t+="="+i))})}return t}function Rf(e){const t={};for(const s in e){const r=e[s];r!==void 0&&(t[s]=We(r)?r.map(n=>n==null?null:""+n):r==null?r:""+r)}return t}const Tf=Symbol(""),Io=Symbol(""),Ar=Symbol(""),Pl=Symbol(""),or=Symbol("");function fs(){let e=[];function t(r){return e.push(r),()=>{const n=e.indexOf(r);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Ct(e,t,s,r,n,i=a=>a()){const a=r&&(r.enterCallbacks[n]=r.enterCallbacks[n]||[]);return()=>new Promise((l,c)=>{const d=g=>{g===!1?c(ns(4,{from:s,to:t})):g instanceof Error?c(g):ff(g)?c(ns(2,{from:t,to:g})):(a&&r.enterCallbacks[n]===a&&typeof g=="function"&&a.push(g),l())},u=i(()=>e.call(r&&r.instances[n],t,s,d));let f=Promise.resolve(u);e.length<3&&(f=f.then(d)),f.catch(g=>c(g))})}function Vn(e,t,s,r,n=i=>i()){const i=[];for(const a of e)for(const l in a.components){let c=a.components[l];if(!(t!=="beforeRouteEnter"&&!a.instances[l]))if(wl(c)){const u=(c.__vccOpts||c)[t];u&&i.push(Ct(u,s,r,a,l,n))}else{let d=c();i.push(()=>d.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${l}" at "${a.path}"`);const f=Dd(u)?u.default:u;a.mods[l]=u,a.components[l]=f;const v=(f.__vccOpts||f)[t];return v&&Ct(v,s,r,a,l,n)()}))}}return i}function Uo(e){const t=vt(Ar),s=vt(Pl),r=qe(()=>{const c=Xt(e.to);return t.resolve(c)}),n=qe(()=>{const{matched:c}=r.value,{length:d}=c,u=c[d-1],f=s.matched;if(!u||!f.length)return-1;const g=f.findIndex(ss.bind(null,u));if(g>-1)return g;const v=Vo(c[d-2]);return d>1&&Vo(u)===v&&f[f.length-1].path!==v?f.findIndex(ss.bind(null,c[d-2])):g}),i=qe(()=>n.value>-1&&Pf(s.params,r.value.params)),a=qe(()=>n.value>-1&&n.value===s.matched.length-1&&Rl(s.params,r.value.params));function l(c={}){if(Mf(c)){const d=t[Xt(e.replace)?"replace":"push"](Xt(e.to)).catch(ys);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:r,href:qe(()=>r.value.href),isActive:i,isExactActive:a,navigate:l}}function Af(e){return e.length===1?e[0]:e}const Of=qi({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Uo,setup(e,{slots:t}){const s=xn(Uo(e)),{options:r}=vt(Ar),n=qe(()=>({[Bo(e.activeClass,r.linkActiveClass,"router-link-active")]:s.isActive,[Bo(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const i=t.default&&Af(t.default(s));return e.custom?i:vl("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},i)}}}),$f=Of;function Mf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Pf(e,t){for(const s in t){const r=t[s],n=e[s];if(typeof r=="string"){if(r!==n)return!1}else if(!We(n)||n.length!==r.length||r.some((i,a)=>i!==n[a]))return!1}return!0}function Vo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Bo=(e,t,s)=>e??t??s,Df=qi({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){const r=vt(or),n=qe(()=>e.route||r.value),i=vt(Io,0),a=qe(()=>{let d=Xt(i);const{matched:u}=n.value;let f;for(;(f=u[d])&&!f.components;)d++;return d}),l=qe(()=>n.value.matched[a.value]);zs(Io,qe(()=>a.value+1)),zs(Tf,l),zs(or,n);const c=Hc();return Hs(()=>[c.value,l.value,e.name],([d,u,f],[g,v,h])=>{u&&(u.instances[f]=d,v&&v!==u&&d&&d===g&&(u.leaveGuards.size||(u.leaveGuards=v.leaveGuards),u.updateGuards.size||(u.updateGuards=v.updateGuards))),d&&u&&(!v||!ss(u,v)||!g)&&(u.enterCallbacks[f]||[]).forEach(w=>w(d))},{flush:"post"}),()=>{const d=n.value,u=e.name,f=l.value,g=f&&f.components[u];if(!g)return Lo(s.default,{Component:g,route:d});const v=f.props[u],h=v?v===!0?d.params:typeof v=="function"?v(d):v:null,C=vl(g,ee({},h,t,{onVnodeUnmounted:O=>{O.component.isUnmounted&&(f.instances[u]=null)},ref:c}));return Lo(s.default,{Component:C,route:d})||C}}});function Lo(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const jf=Df;function Nf(e){const t=wf(e.routes,e),s=e.parseQuery||Cf,r=e.stringifyQuery||Fo,n=e.history,i=fs(),a=fs(),l=fs(),c=qc(kt);let d=kt;Kt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=In.bind(null,k=>""+k),f=In.bind(null,Wd),g=In.bind(null,Ts);function v(k,U){let N,V;return Al(k)?(N=t.getRecordMatcher(k),V=U):V=k,t.addRoute(V,N)}function h(k){const U=t.getRecordMatcher(k);U&&t.removeRoute(U)}function w(){return t.getRoutes().map(k=>k.record)}function C(k){return!!t.getRecordMatcher(k)}function O(k,U){if(U=ee({},U||c.value),typeof k=="string"){const x=Un(s,k,U.path),_=t.resolve({path:x.path},U),S=n.createHref(x.fullPath);return ee(x,_,{params:g(_.params),hash:Ts(x.hash),redirectedFrom:void 0,href:S})}let N;if(k.path!=null)N=ee({},k,{path:Un(s,k.path,U.path).path});else{const x=ee({},k.params);for(const _ in x)x[_]==null&&delete x[_];N=ee({},k,{params:f(x)}),U.params=f(U.params)}const V=t.resolve(N,U),ie=k.hash||"";V.params=u(g(V.params));const p=Xd(r,ee({},k,{hash:Hd(ie),path:V.path})),m=n.createHref(p);return ee({fullPath:p,hash:ie,query:r===Fo?Rf(k.query):k.query||{}},V,{redirectedFrom:void 0,href:m})}function $(k){return typeof k=="string"?Un(s,k,c.value.path):ee({},k)}function j(k,U){if(d!==k)return ns(8,{from:U,to:k})}function I(k){return Y(k)}function J(k){return I(ee($(k),{replace:!0}))}function ce(k){const U=k.matched[k.matched.length-1];if(U&&U.redirect){const{redirect:N}=U;let V=typeof N=="function"?N(k):N;return typeof V=="string"&&(V=V.includes("?")||V.includes("#")?V=$(V):{path:V},V.params={}),ee({query:k.query,hash:k.hash,params:V.path!=null?{}:k.params},V)}}function Y(k,U){const N=d=O(k),V=c.value,ie=k.state,p=k.force,m=k.replace===!0,x=ce(N);if(x)return Y(ee($(x),{state:typeof x=="object"?ee({},ie,x.state):ie,force:p,replace:m}),U||N);const _=N;_.redirectedFrom=U;let S;return!p&&Qd(r,V,N)&&(S=ns(16,{to:_,from:V}),Ye(V,V,!0,!1)),(S?Promise.resolve(S):Xe(_,V)).catch(E=>ft(E)?ft(E,2)?E:_t(E):Z(E,_,V)).then(E=>{if(E){if(ft(E,2))return Y(ee({replace:m},$(E.to),{state:typeof E.to=="object"?ee({},ie,E.to.state):ie,force:p}),U||_)}else E=$t(_,V,!0,m,ie);return wt(_,V,E),E})}function Se(k,U){const N=j(k,U);return N?Promise.reject(N):Promise.resolve()}function He(k){const U=zt.values().next().value;return U&&typeof U.runWithContext=="function"?U.runWithContext(k):k()}function Xe(k,U){let N;const[V,ie,p]=Ff(k,U);N=Vn(V.reverse(),"beforeRouteLeave",k,U);for(const x of V)x.leaveGuards.forEach(_=>{N.push(Ct(_,k,U))});const m=Se.bind(null,k,U);return N.push(m),Ue(N).then(()=>{N=[];for(const x of i.list())N.push(Ct(x,k,U));return N.push(m),Ue(N)}).then(()=>{N=Vn(ie,"beforeRouteUpdate",k,U);for(const x of ie)x.updateGuards.forEach(_=>{N.push(Ct(_,k,U))});return N.push(m),Ue(N)}).then(()=>{N=[];for(const x of p)if(x.beforeEnter)if(We(x.beforeEnter))for(const _ of x.beforeEnter)N.push(Ct(_,k,U));else N.push(Ct(x.beforeEnter,k,U));return N.push(m),Ue(N)}).then(()=>(k.matched.forEach(x=>x.enterCallbacks={}),N=Vn(p,"beforeRouteEnter",k,U,He),N.push(m),Ue(N))).then(()=>{N=[];for(const x of a.list())N.push(Ct(x,k,U));return N.push(m),Ue(N)}).catch(x=>ft(x,8)?x:Promise.reject(x))}function wt(k,U,N){l.list().forEach(V=>He(()=>V(k,U,N)))}function $t(k,U,N,V,ie){const p=j(k,U);if(p)return p;const m=U===kt,x=Kt?history.state:{};N&&(V||m?n.replace(k.fullPath,ee({scroll:m&&x&&x.scroll},ie)):n.push(k.fullPath,ie)),c.value=k,Ye(k,U,N,m),_t()}let Qe;function ls(){Qe||(Qe=n.listen((k,U,N)=>{if(!js.listening)return;const V=O(k),ie=ce(V);if(ie){Y(ee(ie,{replace:!0,force:!0}),V).catch(ys);return}d=V;const p=c.value;Kt&&of(To(p.fullPath,N.delta),Sn()),Xe(V,p).catch(m=>ft(m,12)?m:ft(m,2)?(Y(ee($(m.to),{force:!0}),V).then(x=>{ft(x,20)&&!N.delta&&N.type===As.pop&&n.go(-1,!1)}).catch(ys),Promise.reject()):(N.delta&&n.go(-N.delta,!1),Z(m,V,p))).then(m=>{m=m||$t(V,p,!1),m&&(N.delta&&!ft(m,8)?n.go(-N.delta,!1):N.type===As.pop&&ft(m,20)&&n.go(-1,!1)),wt(V,p,m)}).catch(ys)}))}let Bt=fs(),xe=fs(),oe;function Z(k,U,N){_t(k);const V=xe.list();return V.length?V.forEach(ie=>ie(k,U,N)):console.error(k),Promise.reject(k)}function ut(){return oe&&c.value!==kt?Promise.resolve():new Promise((k,U)=>{Bt.add([k,U])})}function _t(k){return oe||(oe=!k,ls(),Bt.list().forEach(([U,N])=>k?N(k):U()),Bt.reset()),k}function Ye(k,U,N,V){const{scrollBehavior:ie}=e;if(!Kt||!ie)return Promise.resolve();const p=!N&&lf(To(k.fullPath,0))||(V||!N)&&history.state&&history.state.scroll||null;return _r().then(()=>ie(k,U,p)).then(m=>m&&rf(m)).catch(m=>Z(m,k,U))}const Ae=k=>n.go(k);let Lt;const zt=new Set,js={currentRoute:c,listening:!0,addRoute:v,removeRoute:h,clearRoutes:t.clearRoutes,hasRoute:C,getRoutes:w,resolve:O,options:e,push:I,replace:J,go:Ae,back:()=>Ae(-1),forward:()=>Ae(1),beforeEach:i.add,beforeResolve:a.add,afterEach:l.add,onError:xe.add,isReady:ut,install(k){const U=this;k.component("RouterLink",$f),k.component("RouterView",jf),k.config.globalProperties.$router=U,Object.defineProperty(k.config.globalProperties,"$route",{enumerable:!0,get:()=>Xt(c)}),Kt&&!Lt&&c.value===kt&&(Lt=!0,I(n.location).catch(ie=>{}));const N={};for(const ie in kt)Object.defineProperty(N,ie,{get:()=>c.value[ie],enumerable:!0});k.provide(Ar,U),k.provide(Pl,Ni(N)),k.provide(or,c);const V=k.unmount;zt.add(k),k.unmount=function(){zt.delete(k),zt.size<1&&(d=kt,Qe&&Qe(),Qe=null,c.value=kt,Lt=!1,oe=!1),V()}}};function Ue(k){return k.reduce((U,N)=>U.then(()=>He(N)),Promise.resolve())}return js}function Ff(e,t){const s=[],r=[],n=[],i=Math.max(t.matched.length,e.matched.length);for(let a=0;a<i;a++){const l=t.matched[a];l&&(e.matched.find(d=>ss(d,l))?r.push(l):s.push(l));const c=e.matched[a];c&&(t.matched.find(d=>ss(d,c))||n.push(c))}return[s,r,n]}const Ge=(e,t)=>{const s=e.__vccOpts||e;for(const[r,n]of t)s[r]=n;return s},If={name:"App",data(){return{user:null,mobileMenuOpen:!1,isAuthenticated:!1}},mounted(){this.checkAuthStatus(),window.addEventListener("storage",this.handleStorageChange),this.authCheckInterval=setInterval(()=>{this.checkAuthStatus()},5e3)},beforeUnmount(){window.removeEventListener("storage",this.handleStorageChange),this.authCheckInterval&&clearInterval(this.authCheckInterval)},methods:{async logout(){try{await this.$http.post("/logout"),this.clearAuthData()}catch(e){console.error("Logout error:",e),this.clearAuthData()}},clearAuthData(){localStorage.removeItem("auth_token"),localStorage.removeItem("user"),delete this.$http.defaults.headers.common.Authorization,this.user=null,this.isAuthenticated=!1,this.mobileMenuOpen=!1,this.$forceUpdate(),this.$router.push("/").then(()=>{this.$nextTick(()=>{this.checkAuthStatus()})})},checkAuthStatus(){const e=localStorage.getItem("auth_token"),t=localStorage.getItem("user");e&&t?(this.isAuthenticated=!0,this.user=JSON.parse(t),this.$http.defaults.headers.common.Authorization=`Bearer ${e}`):(this.isAuthenticated=!1,this.user=null)},handleStorageChange(e){(e.key==="auth_token"||e.key==="user")&&this.checkAuthStatus()}},watch:{$route(){this.checkAuthStatus()}}},Uf={id:"app",class:"min-h-screen"},Vf={class:"glass-effect fixed w-full z-50 backdrop-blur-md"},Bf={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Lf={class:"flex justify-between h-20"},zf={class:"flex items-center"},Hf={class:"hidden md:flex items-center space-x-6"},qf={key:0,class:"flex items-center space-x-4"},Kf={key:1,class:"flex items-center space-x-4"},Wf={class:"md:hidden flex items-center"},Jf={class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Gf={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"},Xf={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},Qf={key:0,class:"md:hidden py-4 border-t border-white/20"},Yf={class:"flex flex-col space-y-2"},Zf={key:0,class:"flex flex-col space-y-2"},ep={key:1,class:"flex flex-col space-y-2"},tp={class:"flex-1"},sp={class:"bg-gradient-to-br from-accent-900 via-accent-800 to-primary-900 text-white relative overflow-hidden"},np={class:"relative max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8"},rp={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},op={class:"space-y-3"};function ip(e,t,s,r,n,i){const a=ct("router-link"),l=ct("router-view");return R(),T("div",Uf,[o("nav",Vf,[o("div",Bf,[o("div",Lf,[o("div",zf,[K(a,{to:"/",class:"flex items-center space-x-3"},{default:ne(()=>t[7]||(t[7]=[o("div",{class:"w-12 h-12 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200"},[o("span",{class:"text-white font-bold text-xl"},"K")],-1),o("span",{class:"text-2xl font-bold text-gradient"},"KabEvents",-1)])),_:1,__:[7]})]),o("div",Hf,[K(a,{to:"/events",class:"nav-link"},{default:ne(()=>t[8]||(t[8]=[H(" Événements ")])),_:1,__:[8]}),n.isAuthenticated?(R(),T("div",Kf,[K(a,{to:"/dashboard",class:"nav-link"},{default:ne(()=>t[11]||(t[11]=[H(" Dashboard ")])),_:1,__:[11]}),o("button",{onClick:t[0]||(t[0]=(...c)=>i.logout&&i.logout(...c)),class:"nav-link"}," Déconnexion ")])):(R(),T("div",qf,[K(a,{to:"/login",class:"nav-link"},{default:ne(()=>t[9]||(t[9]=[H(" Connexion ")])),_:1,__:[9]}),K(a,{to:"/register",class:"btn-primary"},{default:ne(()=>t[10]||(t[10]=[H(" S'inscrire ")])),_:1,__:[10]})]))]),o("div",Wf,[o("button",{onClick:t[1]||(t[1]=c=>n.mobileMenuOpen=!n.mobileMenuOpen),class:"text-accent-700 hover:text-primary-600 p-2 rounded-lg transition-colors duration-200"},[(R(),T("svg",Jf,[n.mobileMenuOpen?(R(),T("path",Xf)):(R(),T("path",Gf))]))])])]),n.mobileMenuOpen?(R(),T("div",Qf,[o("div",Yf,[K(a,{to:"/events",class:"nav-link",onClick:t[2]||(t[2]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[12]||(t[12]=[H(" Événements ")])),_:1,__:[12]}),n.isAuthenticated?(R(),T("div",ep,[K(a,{to:"/dashboard",class:"nav-link",onClick:t[5]||(t[5]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[15]||(t[15]=[H(" Dashboard ")])),_:1,__:[15]}),o("button",{onClick:t[6]||(t[6]=c=>{i.logout,n.mobileMenuOpen=!1}),class:"nav-link text-left"}," Déconnexion ")])):(R(),T("div",Zf,[K(a,{to:"/login",class:"nav-link",onClick:t[3]||(t[3]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[13]||(t[13]=[H(" Connexion ")])),_:1,__:[13]}),K(a,{to:"/register",class:"btn-primary text-center",onClick:t[4]||(t[4]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[14]||(t[14]=[H(" S'inscrire ")])),_:1,__:[14]})]))])])):fe("",!0)])]),o("main",tp,[K(l)]),o("footer",sp,[t[23]||(t[23]=o("div",{class:"absolute inset-0 bg-hero-pattern opacity-10"},null,-1)),o("div",np,[o("div",rp,[t[20]||(t[20]=Be('<div class="md:col-span-2"><div class="flex items-center space-x-3 mb-6"><div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-lg"><span class="text-white font-bold text-xl">K</span></div><span class="text-2xl font-bold">KabEvents</span></div><p class="text-accent-200 text-lg leading-relaxed mb-6"> La plateforme de référence pour les événements culturels kabyles au Canada. Connectons notre communauté à travers la culture et les traditions. </p><div class="flex space-x-4"><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg></a><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"></path></svg></a><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"></path></svg></a></div></div>',1)),o("div",null,[t[19]||(t[19]=o("h3",{class:"text-xl font-semibold mb-6 text-white"},"Liens rapides",-1)),o("ul",op,[o("li",null,[K(a,{to:"/events",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},{default:ne(()=>t[16]||(t[16]=[o("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"},null,-1),H(" Événements ")])),_:1,__:[16]})]),o("li",null,[K(a,{to:"/register",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},{default:ne(()=>t[17]||(t[17]=[o("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"},null,-1),H(" Devenir organisateur ")])),_:1,__:[17]})]),t[18]||(t[18]=o("li",null,[o("a",{href:"#",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},[o("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"}),H(" À propos ")])],-1))])]),t[21]||(t[21]=Be('<div><h3 class="text-xl font-semibold mb-6 text-white">Contact</h3><div class="space-y-4"><div class="flex items-start space-x-3"><svg class="w-5 h-5 text-secondary-400 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg><div><p class="text-accent-200"><EMAIL></p></div></div><div class="flex items-start space-x-3"><svg class="w-5 h-5 text-secondary-400 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg><div><p class="text-accent-200">+1 (555) 123-4567</p></div></div></div></div>',1))]),t[22]||(t[22]=Be('<div class="mt-12 pt-8 border-t border-white/20"><div class="flex flex-col md:flex-row justify-between items-center"><p class="text-accent-200 text-sm"> © 2024 KabEvents. Tous droits réservés. </p><div class="flex space-x-6 mt-4 md:mt-0"><a href="#" class="text-accent-200 hover:text-white text-sm transition-colors duration-200">Politique de confidentialité</a><a href="#" class="text-accent-200 hover:text-white text-sm transition-colors duration-200">Conditions d&#39;utilisation</a></div></div></div>',1))])])])}const lp=Ge(If,[["render",ip]]),ap={name:"Home",data(){return{featuredEvents:[],loading:!0}},methods:{async fetchFeaturedEvents(){try{const e=await this.$http.get("/events?limit=3");e.data.data&&e.data.data.data?this.featuredEvents=e.data.data.data:e.data.data?this.featuredEvents=e.data.data:this.featuredEvents=e.data}catch(e){console.error("Error fetching events:",e)}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric"})}},mounted(){this.fetchFeaturedEvents()}},cp={class:"relative min-h-screen flex items-center justify-center overflow-hidden"},up={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 text-center"},dp={class:"animate-fade-in"},fp={class:"flex flex-col sm:flex-row gap-6 justify-center items-center animate-slide-up"},pp={class:"py-24 relative"},mp={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},hp={key:0,class:"text-center py-16"},gp={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},vp={class:"event-image"},xp={class:"text-center"},bp={class:"text-white text-3xl font-bold"},yp={class:"mt-2 text-white/80 text-sm font-medium"},wp={class:"p-6"},_p={class:"flex items-center justify-between mb-3"},kp={class:"price-tag"},Ep={class:"text-sm text-accent-500 font-medium"},Sp={class:"text-xl font-bold text-accent-900 mb-3 line-clamp-2"},Cp={class:"text-accent-600 mb-6 line-clamp-3 leading-relaxed"},Rp={class:"flex items-center justify-between"},Tp={class:"flex items-center space-x-2"},Ap={class:"text-sm text-accent-500"},Op={class:"text-center mt-16"},$p={class:"py-24 relative overflow-hidden"},Mp={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Pp={class:"text-center mt-20"},Dp={class:"card max-w-4xl mx-auto"},jp={class:"flex flex-col sm:flex-row gap-4 justify-center"};function Np(e,t,s,r,n,i){const a=ct("router-link");return R(),T("div",null,[o("section",cp,[t[5]||(t[5]=Be('<div class="absolute inset-0 hero-gradient" data-v-1138dda5></div><div class="absolute inset-0 bg-hero-pattern opacity-20" data-v-1138dda5></div><div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-bounce-gentle" data-v-1138dda5></div><div class="absolute top-40 right-20 w-16 h-16 bg-secondary-400/20 rounded-full animate-bounce-gentle" style="animation-delay:1s;" data-v-1138dda5></div><div class="absolute bottom-40 left-20 w-12 h-12 bg-primary-400/20 rounded-full animate-bounce-gentle" style="animation-delay:2s;" data-v-1138dda5></div>',5)),o("div",up,[o("div",dp,[t[2]||(t[2]=o("h1",{class:"text-5xl md:text-7xl font-bold mb-8 text-white leading-tight"},[H(" Découvrez la "),o("span",{class:"bg-gradient-to-r from-secondary-300 to-secondary-100 bg-clip-text text-transparent"}," Culture Kabyle ")],-1)),t[3]||(t[3]=o("p",{class:"text-xl md:text-2xl mb-12 text-white/90 max-w-3xl mx-auto leading-relaxed"}," Participez aux plus beaux événements culturels kabyles au Canada. Connectez-vous avec votre communauté et célébrez nos traditions ancestrales. ",-1)),o("div",fp,[K(a,{to:"/events",class:"btn-secondary text-lg px-8 py-4"},{default:ne(()=>t[0]||(t[0]=[o("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),H(" Voir les événements ")])),_:1,__:[0]}),K(a,{to:"/register",class:"btn-outline text-lg px-8 py-4 bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-primary-600"},{default:ne(()=>t[1]||(t[1]=[o("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),H(" Devenir organisateur ")])),_:1,__:[1]})])]),t[4]||(t[4]=o("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"},[o("svg",{class:"w-6 h-6 text-white/70",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))])]),o("section",pp,[t[11]||(t[11]=o("div",{class:"absolute inset-0 bg-gradient-to-b from-white via-accent-50/30 to-white"},null,-1)),o("div",mp,[t[10]||(t[10]=o("div",{class:"text-center mb-16"},[o("h2",{class:"text-4xl md:text-5xl font-bold text-gradient mb-6"},"Événements à venir"),o("p",{class:"text-xl text-accent-600 max-w-2xl mx-auto leading-relaxed"}," Ne manquez pas ces événements exceptionnels qui célèbrent notre riche patrimoine culturel ")],-1)),n.loading?(R(),T("div",hp,t[6]||(t[6]=[o("div",{class:"loading-spinner w-12 h-12 mx-auto"},null,-1),o("p",{class:"text-accent-600 mt-4"},"Chargement des événements...",-1)]))):(R(),T("div",gp,[(R(!0),T(ge,null,Te(n.featuredEvents,l=>(R(),T("div",{key:l.id,class:"event-card"},[o("div",vp,[o("div",xp,[o("span",bp,y(l.title.substring(0,2).toUpperCase()),1),o("div",yp,y(l.location||"Canada"),1)])]),o("div",wp,[o("div",_p,[o("span",kp,"$"+y(l.ticket_price),1),o("span",Ep,y(i.formatDate(l.event_date)),1)]),o("h3",Sp,y(l.title),1),o("p",Cp,y(l.description),1),o("div",Rp,[o("div",Tp,[t[7]||(t[7]=o("svg",{class:"w-4 h-4 text-accent-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),o("span",Ap,y(l.location||"Canada"),1)]),K(a,{to:`/events/${l.id}`,class:"btn-primary text-sm px-4 py-2"},{default:ne(()=>t[8]||(t[8]=[H(" Voir détails ")])),_:2,__:[8]},1032,["to"])])])]))),128))])),o("div",Op,[K(a,{to:"/events",class:"btn-outline text-lg px-8 py-4"},{default:ne(()=>t[9]||(t[9]=[o("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1),H(" Voir tous les événements ")])),_:1,__:[9]})])])]),o("section",$p,[t[17]||(t[17]=o("div",{class:"absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-secondary-50"},null,-1)),t[18]||(t[18]=o("div",{class:"absolute top-0 left-0 w-full h-full bg-hero-pattern opacity-5"},null,-1)),o("div",Mp,[t[16]||(t[16]=Be('<div class="text-center mb-20" data-v-1138dda5><h2 class="text-4xl md:text-5xl font-bold text-gradient mb-6" data-v-1138dda5>Pourquoi choisir KabEvents ?</h2><p class="text-xl text-accent-600 max-w-3xl mx-auto leading-relaxed" data-v-1138dda5> Une plateforme moderne et sécurisée dédiée à la préservation et à la célébration de notre patrimoine culturel </p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-12" data-v-1138dda5><div class="text-center group" data-v-1138dda5><div class="relative mb-8" data-v-1138dda5><div class="w-24 h-24 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-1138dda5><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-1138dda5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-1138dda5></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-secondary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-1138dda5></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-1138dda5>Événements authentiques</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-1138dda5> Des événements culturels kabyles authentiques organisés par et pour notre communauté, préservant nos traditions ancestrales </p></div><div class="text-center group" data-v-1138dda5><div class="relative mb-8" data-v-1138dda5><div class="w-24 h-24 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-1138dda5><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-1138dda5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" data-v-1138dda5></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-primary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-1138dda5></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-1138dda5>Réservation sécurisée</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-1138dda5> Système de paiement sécurisé avec billets électroniques, QR codes et protection complète de vos données personnelles </p></div><div class="text-center group" data-v-1138dda5><div class="relative mb-8" data-v-1138dda5><div class="w-24 h-24 bg-gradient-to-br from-success-500 to-success-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-1138dda5><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-1138dda5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" data-v-1138dda5></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-secondary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-1138dda5></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-1138dda5>Communauté unie</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-1138dda5> Rassemblez-vous avec la communauté kabyle du Canada et créez des liens durables autour de notre culture commune </p></div></div>',2)),o("div",Pp,[o("div",Dp,[t[14]||(t[14]=o("h3",{class:"text-3xl font-bold text-accent-900 mb-4"},"Prêt à rejoindre notre communauté ?",-1)),t[15]||(t[15]=o("p",{class:"text-xl text-accent-600 mb-8 leading-relaxed"}," Découvrez des événements exceptionnels et connectez-vous avec des milliers de membres de notre communauté ",-1)),o("div",jp,[K(a,{to:"/register",class:"btn-primary text-lg px-8 py-4"},{default:ne(()=>t[12]||(t[12]=[H(" Créer un compte gratuit ")])),_:1,__:[12]}),K(a,{to:"/events",class:"btn-outline text-lg px-8 py-4"},{default:ne(()=>t[13]||(t[13]=[H(" Explorer les événements ")])),_:1,__:[13]})])])])])])])}const Fp=Ge(ap,[["render",Np],["__scopeId","data-v-1138dda5"]]),Ip={name:"Login",data(){return{form:{email:"",password:"",remember:!1},loading:!1,error:null}},methods:{async login(){this.loading=!0,this.error=null;try{const e=await this.$http.post("/login",{email:this.form.email,password:this.form.password});localStorage.setItem("auth_token",e.data.token),localStorage.setItem("user",JSON.stringify(e.data.user)),this.$http.defaults.headers.common.Authorization=`Bearer ${e.data.token}`;const t=this.$route.query.redirect||"/dashboard";this.$router.push(t)}catch(e){e.response&&e.response.data?this.error=e.response.data.message||"Erreur de connexion":this.error="Erreur de connexion. Veuillez réessayer."}finally{this.loading=!1}}},mounted(){localStorage.getItem("auth_token")&&this.$router.push("/dashboard")}},Up={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Vp={class:"max-w-md w-full space-y-8"},Bp={class:"mt-2 text-center text-sm text-gray-600"},Lp={key:0,class:"bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded"},zp={class:"space-y-4"},Hp={class:"flex items-center justify-between"},qp={class:"flex items-center"},Kp=["disabled"],Wp={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function Jp(e,t,s,r,n,i){const a=ct("router-link");return R(),T("div",Up,[o("div",Vp,[o("div",null,[t[6]||(t[6]=o("div",{class:"mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-blue-500 rounded-lg flex items-center justify-center"},[o("span",{class:"text-white font-bold text-xl"},"K")],-1)),t[7]||(t[7]=o("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Connexion à votre compte ",-1)),o("p",Bp,[t[5]||(t[5]=H(" Ou ")),K(a,{to:"/register",class:"font-medium text-orange-600 hover:text-orange-500"},{default:ne(()=>t[4]||(t[4]=[H(" créez un nouveau compte ")])),_:1,__:[4]})])]),o("form",{class:"mt-8 space-y-6",onSubmit:t[3]||(t[3]=En((...l)=>i.login&&i.login(...l),["prevent"]))},[n.error?(R(),T("div",Lp,y(n.error),1)):fe("",!0),o("div",zp,[o("div",null,[t[8]||(t[8]=o("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Adresse email ",-1)),X(o("input",{id:"email","onUpdate:modelValue":t[0]||(t[0]=l=>n.form.email=l),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"<EMAIL>"},null,512),[[he,n.form.email]])]),o("div",null,[t[9]||(t[9]=o("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Mot de passe ",-1)),X(o("input",{id:"password","onUpdate:modelValue":t[1]||(t[1]=l=>n.form.password=l),name:"password",type:"password",autocomplete:"current-password",required:"",class:"input-field mt-1",placeholder:"Votre mot de passe"},null,512),[[he,n.form.password]])])]),o("div",Hp,[o("div",qp,[X(o("input",{id:"remember-me","onUpdate:modelValue":t[2]||(t[2]=l=>n.form.remember=l),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"},null,512),[[bl,n.form.remember]]),t[10]||(t[10]=o("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Se souvenir de moi ",-1))]),t[11]||(t[11]=o("div",{class:"text-sm"},[o("a",{href:"#",class:"font-medium text-orange-600 hover:text-orange-500"}," Mot de passe oublié ? ")],-1))]),o("div",null,[o("button",{type:"submit",disabled:n.loading,class:"btn-primary w-full flex justify-center items-center"},[n.loading?(R(),T("svg",Wp,t[12]||(t[12]=[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):fe("",!0),H(" "+y(n.loading?"Connexion...":"Se connecter"),1)],8,Kp)])],32)])])}const Gp=Ge(Ip,[["render",Jp]]),Xp={name:"Register",data(){return{form:{name:"",email:"",password:"",password_confirmation:"",role:"user",terms:!1},loading:!1,error:null}},methods:{async register(){this.loading=!0,this.error=null;try{const e=await this.$http.post("/register",{name:this.form.name,email:this.form.email,password:this.form.password,password_confirmation:this.form.password_confirmation,role:this.form.role});localStorage.setItem("auth_token",e.data.token),localStorage.setItem("user",JSON.stringify(e.data.user)),this.$http.defaults.headers.common.Authorization=`Bearer ${e.data.token}`,this.$router.push("/dashboard")}catch(e){if(e.response&&e.response.data)if(e.response.data.errors){const t=Object.values(e.response.data.errors).flat();this.error=t.join(", ")}else this.error=e.response.data.message||"Erreur lors de l'inscription";else this.error="Erreur lors de l'inscription. Veuillez réessayer."}finally{this.loading=!1}}},mounted(){localStorage.getItem("auth_token")&&this.$router.push("/dashboard")}},Qp={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Yp={class:"max-w-md w-full space-y-8"},Zp={class:"mt-2 text-center text-sm text-gray-600"},em={key:0,class:"bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded"},tm={class:"space-y-4"},sm={class:"flex items-center"},nm=["disabled"],rm={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function om(e,t,s,r,n,i){const a=ct("router-link");return R(),T("div",Qp,[o("div",Yp,[o("div",null,[t[9]||(t[9]=o("div",{class:"mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-blue-500 rounded-lg flex items-center justify-center"},[o("span",{class:"text-white font-bold text-xl"},"K")],-1)),t[10]||(t[10]=o("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Créer votre compte ",-1)),o("p",Zp,[t[8]||(t[8]=H(" Ou ")),K(a,{to:"/login",class:"font-medium text-orange-600 hover:text-orange-500"},{default:ne(()=>t[7]||(t[7]=[H(" connectez-vous à votre compte existant ")])),_:1,__:[7]})])]),o("form",{class:"mt-8 space-y-6",onSubmit:t[6]||(t[6]=En((...l)=>i.register&&i.register(...l),["prevent"]))},[n.error?(R(),T("div",em,y(n.error),1)):fe("",!0),o("div",tm,[o("div",null,[t[11]||(t[11]=o("label",{for:"name",class:"block text-sm font-medium text-gray-700"}," Nom complet ",-1)),X(o("input",{id:"name","onUpdate:modelValue":t[0]||(t[0]=l=>n.form.name=l),name:"name",type:"text",autocomplete:"name",required:"",class:"input-field mt-1",placeholder:"Votre nom complet"},null,512),[[he,n.form.name]])]),o("div",null,[t[12]||(t[12]=o("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Adresse email ",-1)),X(o("input",{id:"email","onUpdate:modelValue":t[1]||(t[1]=l=>n.form.email=l),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"<EMAIL>"},null,512),[[he,n.form.email]])]),o("div",null,[t[14]||(t[14]=o("label",{for:"role",class:"block text-sm font-medium text-gray-700"}," Type de compte ",-1)),X(o("select",{id:"role","onUpdate:modelValue":t[2]||(t[2]=l=>n.form.role=l),name:"role",class:"input-field mt-1"},t[13]||(t[13]=[o("option",{value:"user"},"Utilisateur (participer aux événements)",-1),o("option",{value:"organizer"},"Organisateur (créer des événements)",-1)]),512),[[Zt,n.form.role]])]),o("div",null,[t[15]||(t[15]=o("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Mot de passe ",-1)),X(o("input",{id:"password","onUpdate:modelValue":t[3]||(t[3]=l=>n.form.password=l),name:"password",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Minimum 8 caractères"},null,512),[[he,n.form.password]])]),o("div",null,[t[16]||(t[16]=o("label",{for:"password_confirmation",class:"block text-sm font-medium text-gray-700"}," Confirmer le mot de passe ",-1)),X(o("input",{id:"password_confirmation","onUpdate:modelValue":t[4]||(t[4]=l=>n.form.password_confirmation=l),name:"password_confirmation",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Répétez votre mot de passe"},null,512),[[he,n.form.password_confirmation]])])]),o("div",sm,[X(o("input",{id:"terms","onUpdate:modelValue":t[5]||(t[5]=l=>n.form.terms=l),name:"terms",type:"checkbox",required:"",class:"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"},null,512),[[bl,n.form.terms]]),t[17]||(t[17]=o("label",{for:"terms",class:"ml-2 block text-sm text-gray-900"},[H(" J'accepte les "),o("a",{href:"#",class:"text-orange-600 hover:text-orange-500"},"conditions d'utilisation"),H(" et la "),o("a",{href:"#",class:"text-orange-600 hover:text-orange-500"},"politique de confidentialité")],-1))]),o("div",null,[o("button",{type:"submit",disabled:n.loading,class:"btn-primary w-full flex justify-center items-center"},[n.loading?(R(),T("svg",rm,t[18]||(t[18]=[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):fe("",!0),H(" "+y(n.loading?"Création...":"Créer mon compte"),1)],8,nm)])],32)])])}const im=Ge(Xp,[["render",om]]),lm={name:"Events",data(){return{events:[],filteredEvents:[],loading:!0,filters:{search:"",city:"",maxPrice:"",sortBy:"date"}}},methods:{async fetchEvents(){try{const e=await this.$http.get("/events");e.data.data&&e.data.data.data?this.events=e.data.data.data:e.data.data?this.events=e.data.data:this.events=e.data,this.filteredEvents=[...this.events],this.filterEvents()}catch(e){console.error("Error fetching events:",e)}finally{this.loading=!1}},filterEvents(){let e=[...this.events];this.filters.search&&(e=e.filter(t=>t.title.toLowerCase().includes(this.filters.search.toLowerCase())||t.description.toLowerCase().includes(this.filters.search.toLowerCase()))),this.filters.city&&(e=e.filter(t=>t.location.includes(this.filters.city))),this.filters.maxPrice&&(e=e.filter(t=>parseFloat(t.ticket_price)<=parseFloat(this.filters.maxPrice))),e.sort((t,s)=>{switch(this.filters.sortBy){case"price":return parseFloat(t.ticket_price)-parseFloat(s.ticket_price);case"title":return t.title.localeCompare(s.title);case"date":default:return new Date(t.event_date)-new Date(s.event_date)}}),this.filteredEvents=e},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvents()}},am={class:"min-h-screen bg-gray-50"},cm={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},um={class:"bg-white rounded-lg shadow p-6"},dm={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},fm={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12"},pm={key:0,class:"text-center py-12"},mm={key:1,class:"text-center py-12"},hm={key:2,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},gm={class:"h-48 bg-gradient-to-r from-orange-400 to-blue-400 rounded-lg mb-4 flex items-center justify-center"},vm={class:"text-white text-2xl font-bold"},xm={class:"space-y-3"},bm={class:"text-xl font-semibold text-gray-900"},ym={class:"text-gray-600 text-sm line-clamp-3"},wm={class:"flex items-center text-sm text-gray-500"},_m={class:"flex items-center text-sm text-gray-500"},km={class:"flex items-center justify-between"},Em={class:"flex items-center text-sm text-gray-500"},Sm={class:"text-2xl font-bold text-orange-600"};function Cm(e,t,s,r,n,i){const a=ct("router-link");return R(),T("div",am,[t[21]||(t[21]=o("div",{class:"bg-white shadow"},[o("div",{class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},[o("h1",{class:"text-3xl font-bold text-gray-900"},"Événements culturels kabyles"),o("p",{class:"mt-2 text-gray-600"},"Découvrez tous les événements à venir")])],-1)),o("div",cm,[o("div",um,[o("div",dm,[o("div",null,[t[8]||(t[8]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Rechercher",-1)),X(o("input",{"onUpdate:modelValue":t[0]||(t[0]=l=>n.filters.search=l),type:"text",placeholder:"Nom de l'événement...",class:"input-field",onInput:t[1]||(t[1]=(...l)=>i.filterEvents&&i.filterEvents(...l))},null,544),[[he,n.filters.search]])]),o("div",null,[t[10]||(t[10]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Ville",-1)),X(o("select",{"onUpdate:modelValue":t[2]||(t[2]=l=>n.filters.city=l),class:"input-field",onChange:t[3]||(t[3]=(...l)=>i.filterEvents&&i.filterEvents(...l))},t[9]||(t[9]=[Be('<option value="" data-v-a6e5b925>Toutes les villes</option><option value="Toronto" data-v-a6e5b925>Toronto</option><option value="Montréal" data-v-a6e5b925>Montréal</option><option value="Vancouver" data-v-a6e5b925>Vancouver</option><option value="Calgary" data-v-a6e5b925>Calgary</option>',5)]),544),[[Zt,n.filters.city]])]),o("div",null,[t[12]||(t[12]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Prix max",-1)),X(o("select",{"onUpdate:modelValue":t[4]||(t[4]=l=>n.filters.maxPrice=l),class:"input-field",onChange:t[5]||(t[5]=(...l)=>i.filterEvents&&i.filterEvents(...l))},t[11]||(t[11]=[o("option",{value:""},"Tous les prix",-1),o("option",{value:"50"},"Moins de 50$",-1),o("option",{value:"75"},"Moins de 75$",-1),o("option",{value:"100"},"Moins de 100$",-1)]),544),[[Zt,n.filters.maxPrice]])]),o("div",null,[t[14]||(t[14]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Trier par",-1)),X(o("select",{"onUpdate:modelValue":t[6]||(t[6]=l=>n.filters.sortBy=l),class:"input-field",onChange:t[7]||(t[7]=(...l)=>i.filterEvents&&i.filterEvents(...l))},t[13]||(t[13]=[o("option",{value:"date"},"Date",-1),o("option",{value:"price"},"Prix",-1),o("option",{value:"title"},"Nom",-1)]),544),[[Zt,n.filters.sortBy]])])])])]),o("div",fm,[n.loading?(R(),T("div",pm,t[15]||(t[15]=[o("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"},null,-1),o("p",{class:"mt-2 text-gray-600"},"Chargement des événements...",-1)]))):n.filteredEvents.length===0?(R(),T("div",mm,t[16]||(t[16]=[o("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),o("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Aucun événement trouvé",-1),o("p",{class:"mt-1 text-sm text-gray-500"},"Essayez de modifier vos critères de recherche.",-1)]))):(R(),T("div",hm,[(R(!0),T(ge,null,Te(n.filteredEvents,l=>(R(),T("div",{key:l.id,class:"card hover:shadow-xl transition duration-300"},[o("div",gm,[o("span",vm,y(l.title.substring(0,2).toUpperCase()),1)]),o("div",xm,[o("h3",bm,y(l.title),1),o("p",ym,y(l.description),1),o("div",wm,[t[17]||(t[17]=o("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),H(" "+y(i.formatDate(l.event_date)),1)]),o("div",_m,[t[18]||(t[18]=o("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),H(" "+y(l.location),1)]),o("div",km,[o("div",Em,[t[19]||(t[19]=o("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1)),H(" "+y(l.available_tickets)+" places disponibles ",1)]),o("span",Sm,"$"+y(l.ticket_price),1)]),K(a,{to:`/events/${l.id}`,class:"btn-primary w-full text-center block"},{default:ne(()=>t[20]||(t[20]=[H(" Voir détails ")])),_:2,__:[20]},1032,["to"])])]))),128))]))])])}const Rm=Ge(lm,[["render",Cm],["__scopeId","data-v-a6e5b925"]]),Tm={name:"EventDetail",props:["id"],data(){return{event:null,loading:!0,quantity:1}},computed:{isAuthenticated(){return!!localStorage.getItem("auth_token")}},methods:{async fetchEvent(){try{const e=await this.$http.get(`/events/${this.id}`);this.event=e.data.data||e.data}catch(e){console.error("Error fetching event:",e),this.event=null}finally{this.loading=!1}},goToCheckout(){localStorage.setItem("checkout_quantity",this.quantity),this.$router.push(`/checkout/${this.id}`)},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric"})},formatTime(e){return new Date(e).toLocaleTimeString("fr-CA",{hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvent()},watch:{id(){this.fetchEvent()}}},Am={class:"min-h-screen bg-gray-50"},Om={key:0,class:"flex items-center justify-center min-h-screen"},$m={key:1,class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Mm={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Pm={class:"lg:col-span-2"},Dm={class:"h-64 md:h-96 bg-gradient-to-r from-orange-400 to-blue-400 rounded-xl mb-6 flex items-center justify-center"},jm={class:"text-white text-4xl font-bold"},Nm={class:"bg-white rounded-xl shadow-lg p-6"},Fm={class:"text-3xl font-bold text-gray-900 mb-4"},Im={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"},Um={class:"flex items-center text-gray-600"},Vm={class:"font-medium"},Bm={class:"text-sm"},Lm={class:"flex items-center text-gray-600"},zm={class:"font-medium"},Hm={class:"flex items-center text-gray-600"},qm={class:"font-medium"},Km={class:"text-sm"},Wm={class:"flex items-center text-gray-600"},Jm={class:"text-sm"},Gm={class:"border-t pt-6"},Xm={class:"text-gray-700 leading-relaxed"},Qm={class:"lg:col-span-1"},Ym={class:"bg-white rounded-xl shadow-lg p-6 sticky top-8"},Zm={class:"text-center mb-6"},e0={class:"text-3xl font-bold text-orange-600"},t0={key:0,class:"space-y-4"},s0={key:1,class:"space-y-4"},n0=["value"],r0={class:"border-t pt-4"},o0={class:"flex justify-between mb-2"},i0={class:"font-semibold"},l0={class:"flex justify-between text-lg font-bold"},a0={key:2,class:"text-center"},c0={key:2,class:"flex items-center justify-center min-h-screen"},u0={class:"text-center"};function d0(e,t,s,r,n,i){var l;const a=ct("router-link");return R(),T("div",Am,[n.loading?(R(),T("div",Om,t[3]||(t[3]=[o("div",{class:"text-center"},[o("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"}),o("p",{class:"mt-2 text-gray-600"},"Chargement de l'événement...")],-1)]))):n.event?(R(),T("div",$m,[o("button",{onClick:t[0]||(t[0]=c=>e.$router.go(-1)),class:"mb-6 flex items-center text-orange-600 hover:text-orange-700"},t[4]||(t[4]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1),H(" Retour ")])),o("div",Mm,[o("div",Pm,[o("div",Dm,[o("span",jm,y(n.event.title.substring(0,2).toUpperCase()),1)]),o("div",Nm,[o("h1",Fm,y(n.event.title),1),o("div",Im,[o("div",Um,[t[5]||(t[5]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),o("div",null,[o("p",Vm,y(i.formatDate(n.event.event_date)),1),o("p",Bm,y(i.formatTime(n.event.event_date)),1)])]),o("div",Lm,[t[6]||(t[6]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),o("div",null,[o("p",zm,y(n.event.location),1)])]),o("div",Hm,[t[7]||(t[7]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"})],-1)),o("div",null,[o("p",qm,y(n.event.available_tickets)+" places disponibles",1),o("p",Km,"sur "+y(n.event.total_tickets)+" places",1)])]),o("div",Wm,[t[9]||(t[9]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"})],-1)),o("div",null,[t[8]||(t[8]=o("p",{class:"font-medium"},"Organisé par",-1)),o("p",Jm,y(((l=n.event.organizer)==null?void 0:l.name)||"Organisateur"),1)])])]),o("div",Gm,[t[10]||(t[10]=o("h2",{class:"text-xl font-semibold mb-3"},"Description",-1)),o("p",Xm,y(n.event.description),1)])])]),o("div",Qm,[o("div",Ym,[o("div",Zm,[o("p",e0,"$"+y(n.event.ticket_price),1),t[11]||(t[11]=o("p",{class:"text-gray-600"},"par billet",-1))]),i.isAuthenticated?n.event.available_tickets>0?(R(),T("div",s0,[o("div",null,[t[15]||(t[15]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Nombre de billets ",-1)),X(o("select",{"onUpdate:modelValue":t[1]||(t[1]=c=>n.quantity=c),class:"input-field"},[(R(!0),T(ge,null,Te(Math.min(10,n.event.available_tickets),c=>(R(),T("option",{key:c,value:c},y(c)+" billet"+y(c>1?"s":""),9,n0))),128))],512),[[Zt,n.quantity]])]),o("div",r0,[o("div",o0,[o("span",null,y(n.quantity)+" × $"+y(n.event.ticket_price),1),o("span",i0,"$"+y((n.quantity*parseFloat(n.event.ticket_price)).toFixed(2)),1)]),o("div",l0,[t[16]||(t[16]=o("span",null,"Total",-1)),o("span",null,"$"+y((n.quantity*parseFloat(n.event.ticket_price)).toFixed(2)),1)])]),o("button",{onClick:t[2]||(t[2]=(...c)=>i.goToCheckout&&i.goToCheckout(...c)),class:"btn-primary w-full flex justify-center items-center"}," Acheter maintenant ")])):(R(),T("div",a0,t[17]||(t[17]=[o("p",{class:"text-orange-600 font-semibold mb-4"},"Événement complet",-1),o("p",{class:"text-gray-600 text-sm"}," Cet événement n'a plus de places disponibles. ",-1)]))):(R(),T("div",t0,[t[14]||(t[14]=o("p",{class:"text-center text-gray-600 mb-4"}," Connectez-vous pour réserver vos billets ",-1)),K(a,{to:"/login",class:"btn-primary w-full text-center block"},{default:ne(()=>t[12]||(t[12]=[H(" Se connecter ")])),_:1,__:[12]}),K(a,{to:"/register",class:"btn-secondary w-full text-center block"},{default:ne(()=>t[13]||(t[13]=[H(" Créer un compte ")])),_:1,__:[13]})]))])])])])):(R(),T("div",c0,[o("div",u0,[t[19]||(t[19]=o("h2",{class:"text-2xl font-bold text-gray-900 mb-2"},"Événement non trouvé",-1)),t[20]||(t[20]=o("p",{class:"text-gray-600 mb-4"},"L'événement que vous recherchez n'existe pas.",-1)),K(a,{to:"/events",class:"btn-primary"},{default:ne(()=>t[18]||(t[18]=[H(" Voir tous les événements ")])),_:1,__:[18]})])]))])}const f0=Ge(Tm,[["render",d0]]),p0={name:"Checkout",props:["eventId"],data(){return{step:1,event:null,loading:!0,processing:!1,confirmationNumber:null,orderData:{quantity:1,firstName:"",lastName:"",email:"",phone:""},paymentData:{method:"card",cardNumber:"",expiryDate:"",cvv:"",cardName:""}}},computed:{subtotal(){var e;return(this.orderData.quantity*parseFloat(((e=this.event)==null?void 0:e.ticket_price)||0)).toFixed(2)},serviceFee(){return(parseFloat(this.subtotal)*.05).toFixed(2)},totalAmount(){return(parseFloat(this.subtotal)+parseFloat(this.serviceFee)).toFixed(2)}},methods:{async fetchEvent(){var e,t;try{const s=await this.$http.get(`/events/${this.eventId}`);this.event=s.data.data||s.data;const r=localStorage.getItem("user");if(r){const i=JSON.parse(r);this.orderData.firstName=((e=i.name)==null?void 0:e.split(" ")[0])||"",this.orderData.lastName=((t=i.name)==null?void 0:t.split(" ").slice(1).join(" "))||"",this.orderData.email=i.email||""}const n=localStorage.getItem("checkout_quantity");n&&(this.orderData.quantity=parseInt(n),localStorage.removeItem("checkout_quantity"))}catch(s){console.error("Error fetching event:",s),this.$router.push("/events")}finally{this.loading=!1}},nextStep(){this.validateStep()&&this.step++},prevStep(){this.step--},validateStep(){return this.step===1?this.orderData.firstName&&this.orderData.lastName&&this.orderData.email:!0},formatCardNumber(e){var r;let t=e.target.value.replace(/\s/g,"").replace(/[^0-9]/gi,""),s=((r=t.match(/.{1,4}/g))==null?void 0:r.join(" "))||t;this.paymentData.cardNumber=s},formatExpiryDate(e){let t=e.target.value.replace(/\D/g,"");t.length>=2&&(t=t.substring(0,2)+"/"+t.substring(2,4)),this.paymentData.expiryDate=t},async processPayment(){this.processing=!0;try{await new Promise(t=>setTimeout(t,2e3));const e=await this.$http.post(`/events/${this.eventId}/reserve`,{quantity:this.orderData.quantity,customer_info:this.orderData,payment_info:this.paymentData});this.confirmationNumber=e.data.data.payment_reference||"CONF_"+Date.now(),this.step=3}catch(e){console.error("Payment error:",e),alert("Erreur lors du paiement. Veuillez réessayer.")}finally{this.processing=!1}},downloadTicket(){alert("Téléchargement des billets (à implémenter)")},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvent()}},m0={class:"min-h-screen bg-gray-50 py-8"},h0={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"},g0={class:"mb-8"},v0={class:"flex items-center justify-center space-x-4"},x0={class:"flex items-center"},b0={class:"flex items-center"},y0={class:"flex items-center"},w0={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},_0={class:"lg:col-span-2"},k0={key:0,class:"bg-white rounded-lg shadow p-6"},E0={class:"border rounded-lg p-4 mb-6"},S0={class:"flex items-start space-x-4"},C0={class:"w-16 h-16 bg-gradient-to-r from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center"},R0={class:"text-white font-bold"},T0={class:"flex-1"},A0={class:"font-semibold text-gray-900"},O0={class:"text-gray-600 text-sm"},$0={class:"text-gray-600 text-sm"},M0={class:"mb-6"},P0=["value"],D0={class:"space-y-4"},j0={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},N0={class:"flex justify-end mt-6"},F0={key:1,class:"bg-white rounded-lg shadow p-6"},I0={class:"mb-6"},U0={class:"space-y-3"},V0={class:"flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50"},B0={class:"flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50"},L0={key:0,class:"space-y-4"},z0={class:"grid grid-cols-2 gap-4"},H0={class:"flex justify-between mt-6"},q0=["disabled"],K0={key:0,class:"loading-spinner w-4 h-4 mr-2"},W0={key:2,class:"bg-white rounded-lg shadow p-6 text-center"},J0={class:"bg-gray-50 rounded-lg p-4 mb-6"},G0={class:"text-lg font-bold text-gray-900"},X0={class:"space-y-3"},Q0={class:"lg:col-span-1"},Y0={class:"bg-white rounded-lg shadow p-6 sticky top-8"},Z0={class:"space-y-3 mb-4"},eh={class:"flex justify-between"},th={class:"flex justify-between"},sh={class:"border-t pt-3 flex justify-between font-bold text-lg"};function nh(e,t,s,r,n,i){var l,c,d,u,f,g,v;const a=ct("router-link");return R(),T("div",m0,[o("div",h0,[t[44]||(t[44]=o("div",{class:"text-center mb-8"},[o("h1",{class:"text-3xl font-bold text-gray-900"},"Finaliser votre commande"),o("p",{class:"text-gray-600 mt-2"},"Complétez votre achat en toute sécurité")],-1)),o("div",g0,[o("div",v0,[o("div",x0,[o("div",{class:ae([n.step>=1?"bg-primary-600 text-white":"bg-gray-300 text-gray-600","w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"])}," 1 ",2),t[17]||(t[17]=o("span",{class:"ml-2 text-sm font-medium text-gray-900"},"Détails",-1))]),t[20]||(t[20]=o("div",{class:"w-16 h-0.5 bg-gray-300"},null,-1)),o("div",b0,[o("div",{class:ae([n.step>=2?"bg-primary-600 text-white":"bg-gray-300 text-gray-600","w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"])}," 2 ",2),t[18]||(t[18]=o("span",{class:"ml-2 text-sm font-medium text-gray-900"},"Paiement",-1))]),t[21]||(t[21]=o("div",{class:"w-16 h-0.5 bg-gray-300"},null,-1)),o("div",y0,[o("div",{class:ae([n.step>=3?"bg-primary-600 text-white":"bg-gray-300 text-gray-600","w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"])}," 3 ",2),t[19]||(t[19]=o("span",{class:"ml-2 text-sm font-medium text-gray-900"},"Confirmation",-1))])])]),o("div",w0,[o("div",_0,[n.step===1?(R(),T("div",k0,[t[28]||(t[28]=o("h2",{class:"text-xl font-semibold mb-6"},"Détails de votre commande",-1)),o("div",E0,[o("div",S0,[o("div",C0,[o("span",R0,y((c=(l=n.event)==null?void 0:l.title)==null?void 0:c.substring(0,2).toUpperCase()),1)]),o("div",T0,[o("h3",A0,y((d=n.event)==null?void 0:d.title),1),o("p",O0,y((u=n.event)==null?void 0:u.location),1),o("p",$0,y(i.formatDate((f=n.event)==null?void 0:f.event_date)),1)])])]),o("div",M0,[t[22]||(t[22]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Nombre de billets ",-1)),X(o("select",{"onUpdate:modelValue":t[0]||(t[0]=h=>n.orderData.quantity=h),class:"input-field max-w-xs"},[(R(!0),T(ge,null,Te(Math.min(10,((g=n.event)==null?void 0:g.available_tickets)||1),h=>(R(),T("option",{key:h,value:h},y(h)+" billet"+y(h>1?"s":""),9,P0))),128))],512),[[Zt,n.orderData.quantity]])]),o("div",D0,[t[27]||(t[27]=o("h3",{class:"text-lg font-medium"},"Informations personnelles",-1)),o("div",j0,[o("div",null,[t[23]||(t[23]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Prénom",-1)),X(o("input",{"onUpdate:modelValue":t[1]||(t[1]=h=>n.orderData.firstName=h),type:"text",class:"input-field",required:""},null,512),[[he,n.orderData.firstName]])]),o("div",null,[t[24]||(t[24]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Nom",-1)),X(o("input",{"onUpdate:modelValue":t[2]||(t[2]=h=>n.orderData.lastName=h),type:"text",class:"input-field",required:""},null,512),[[he,n.orderData.lastName]])]),o("div",null,[t[25]||(t[25]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Email",-1)),X(o("input",{"onUpdate:modelValue":t[3]||(t[3]=h=>n.orderData.email=h),type:"email",class:"input-field",required:""},null,512),[[he,n.orderData.email]])]),o("div",null,[t[26]||(t[26]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Téléphone",-1)),X(o("input",{"onUpdate:modelValue":t[4]||(t[4]=h=>n.orderData.phone=h),type:"tel",class:"input-field"},null,512),[[he,n.orderData.phone]])])])]),o("div",N0,[o("button",{onClick:t[5]||(t[5]=(...h)=>i.nextStep&&i.nextStep(...h)),class:"btn-primary"}," Continuer vers le paiement ")])])):fe("",!0),n.step===2?(R(),T("div",F0,[t[36]||(t[36]=o("h2",{class:"text-xl font-semibold mb-6"},"Informations de paiement",-1)),o("div",I0,[t[31]||(t[31]=o("h3",{class:"text-lg font-medium mb-4"},"Méthode de paiement",-1)),o("div",U0,[o("label",V0,[X(o("input",{"onUpdate:modelValue":t[6]||(t[6]=h=>n.paymentData.method=h),type:"radio",value:"card",class:"mr-3"},null,512),[[ko,n.paymentData.method]]),t[29]||(t[29]=o("div",{class:"flex items-center"},[o("svg",{class:"w-6 h-6 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})]),o("span",{class:"font-medium"},"Carte de crédit/débit")],-1))]),o("label",B0,[X(o("input",{"onUpdate:modelValue":t[7]||(t[7]=h=>n.paymentData.method=h),type:"radio",value:"paypal",class:"mr-3"},null,512),[[ko,n.paymentData.method]]),t[30]||(t[30]=o("div",{class:"flex items-center"},[o("svg",{class:"w-6 h-6 mr-2 text-blue-600",fill:"currentColor",viewBox:"0 0 24 24"},[o("path",{d:"M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.254-.93 4.778-4.005 6.430-7.97 6.430h-1.758a.525.525 0 0 0-.518.439l-1.24 7.858h2.302c.456 0 .845-.334.923-.773l.738-4.68a.525.525 0 0 1 .518-.439h1.05c3.365 0 6.001-1.369 6.766-5.32.319-1.648.141-3.027-.663-4.228z"})]),o("span",{class:"font-medium"},"PayPal")],-1))])])]),n.paymentData.method==="card"?(R(),T("div",L0,[o("div",null,[t[32]||(t[32]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Numéro de carte",-1)),X(o("input",{"onUpdate:modelValue":t[8]||(t[8]=h=>n.paymentData.cardNumber=h),type:"text",placeholder:"1234 5678 9012 3456",class:"input-field",maxlength:"19",onInput:t[9]||(t[9]=(...h)=>i.formatCardNumber&&i.formatCardNumber(...h))},null,544),[[he,n.paymentData.cardNumber]])]),o("div",z0,[o("div",null,[t[33]||(t[33]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Date d'expiration",-1)),X(o("input",{"onUpdate:modelValue":t[10]||(t[10]=h=>n.paymentData.expiryDate=h),type:"text",placeholder:"MM/AA",class:"input-field",maxlength:"5",onInput:t[11]||(t[11]=(...h)=>i.formatExpiryDate&&i.formatExpiryDate(...h))},null,544),[[he,n.paymentData.expiryDate]])]),o("div",null,[t[34]||(t[34]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"CVV",-1)),X(o("input",{"onUpdate:modelValue":t[12]||(t[12]=h=>n.paymentData.cvv=h),type:"text",placeholder:"123",class:"input-field",maxlength:"4"},null,512),[[he,n.paymentData.cvv]])])]),o("div",null,[t[35]||(t[35]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Nom sur la carte",-1)),X(o("input",{"onUpdate:modelValue":t[13]||(t[13]=h=>n.paymentData.cardName=h),type:"text",class:"input-field"},null,512),[[he,n.paymentData.cardName]])])])):fe("",!0),o("div",H0,[o("button",{onClick:t[14]||(t[14]=(...h)=>i.prevStep&&i.prevStep(...h)),class:"btn-outline"}," Retour "),o("button",{onClick:t[15]||(t[15]=(...h)=>i.processPayment&&i.processPayment(...h)),disabled:n.processing,class:"btn-primary"},[n.processing?(R(),T("span",K0)):fe("",!0),H(" "+y(n.processing?"Traitement...":`Payer $${i.totalAmount}`),1)],8,q0)])])):fe("",!0),n.step===3?(R(),T("div",W0,[t[39]||(t[39]=Be('<div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg></div><h2 class="text-2xl font-bold text-gray-900 mb-2">Paiement confirmé !</h2><p class="text-gray-600 mb-6">Votre réservation a été effectuée avec succès.</p>',3)),o("div",J0,[t[37]||(t[37]=o("p",{class:"text-sm text-gray-600"},"Numéro de confirmation",-1)),o("p",G0,y(n.confirmationNumber),1)]),o("div",X0,[o("button",{onClick:t[16]||(t[16]=(...h)=>i.downloadTicket&&i.downloadTicket(...h)),class:"btn-primary w-full"}," Télécharger les billets "),K(a,{to:"/dashboard",class:"btn-outline w-full block text-center"},{default:ne(()=>t[38]||(t[38]=[H(" Voir mes réservations ")])),_:1,__:[38]})])])):fe("",!0)]),o("div",Q0,[o("div",Y0,[t[42]||(t[42]=o("h3",{class:"text-lg font-semibold mb-4"},"Résumé de la commande",-1)),o("div",Z0,[o("div",eh,[o("span",null,y(n.orderData.quantity)+" × $"+y((v=n.event)==null?void 0:v.ticket_price),1),o("span",null,"$"+y(i.subtotal),1)]),o("div",th,[t[40]||(t[40]=o("span",null,"Frais de service",-1)),o("span",null,"$"+y(i.serviceFee),1)]),o("div",sh,[t[41]||(t[41]=o("span",null,"Total",-1)),o("span",null,"$"+y(i.totalAmount),1)])]),t[43]||(t[43]=o("div",{class:"text-xs text-gray-500"},[o("p",null,"• Billets électroniques envoyés par email"),o("p",null,"• Remboursement possible jusqu'à 24h avant l'événement"),o("p",null,"• Support client disponible 24/7")],-1))])])])])])}const rh=Ge(p0,[["render",nh]]),oh={name:"Dashboard",data(){return{user:null,reservations:[],userStats:{totalReservations:0,upcomingEvents:0,totalSpent:0},adminStats:{totalUsers:0,totalEvents:0,totalReservations:0,totalRevenue:0},loading:!0}},methods:{async fetchUserData(){try{const e=await this.$http.get("/me");if(this.user=e.data.user,this.user.role==="user"){const t=await this.$http.get("/user/reservations");this.reservations=t.data.data||t.data,this.userStats.totalReservations=this.reservations.length,this.userStats.upcomingEvents=this.reservations.filter(s=>{var r;return new Date((r=s.event)==null?void 0:r.event_date)>new Date}).length,this.userStats.totalSpent=this.reservations.filter(s=>s.status==="paid").reduce((s,r)=>s+parseFloat(r.total_amount),0).toFixed(2)}}catch(e){console.error("Error fetching user data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})},getStatusClass(e){switch(e){case"paid":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},getStatusText(e){switch(e){case"paid":return"Payé";case"pending":return"En attente";case"cancelled":return"Annulé";default:return e}}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchUserData()}},ih={class:"min-h-screen bg-gray-50"},lh={class:"bg-white shadow"},ah={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},ch={class:"flex justify-between items-center"},uh={class:"text-gray-600"},dh={class:"flex items-center space-x-2"},fh={class:"px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium"},ph={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},mh={key:0,class:"space-y-8"},hh={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},gh={class:"card"},vh={class:"flex items-center"},xh={class:"ml-4"},bh={class:"text-2xl font-bold text-gray-900"},yh={class:"card"},wh={class:"flex items-center"},_h={class:"ml-4"},kh={class:"text-2xl font-bold text-gray-900"},Eh={class:"card"},Sh={class:"flex items-center"},Ch={class:"ml-4"},Rh={class:"text-2xl font-bold text-gray-900"},Th={class:"card"},Ah={class:"flex justify-between items-center mb-6"},Oh={key:0,class:"text-center py-8"},$h={key:1,class:"text-center py-8"},Mh={key:2,class:"space-y-4"},Ph={class:"flex justify-between items-start"},Dh={class:"flex-1"},jh={class:"font-semibold text-lg"},Nh={class:"text-gray-600 text-sm mb-2"},Fh={class:"text-gray-600 text-sm"},Ih={class:"mt-2 flex items-center space-x-4"},Uh={class:"text-sm text-gray-500"},Vh={class:"text-sm font-medium"},Bh={class:"ml-4"},Lh={key:1,class:"space-y-8"},zh={key:2,class:"space-y-8"},Hh={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},qh={class:"card text-center"},Kh={class:"text-3xl font-bold text-orange-600 mb-2"},Wh={class:"card text-center"},Jh={class:"text-3xl font-bold text-blue-600 mb-2"},Gh={class:"card text-center"},Xh={class:"text-3xl font-bold text-green-600 mb-2"},Qh={class:"card text-center"},Yh={class:"text-3xl font-bold text-purple-600 mb-2"};function Zh(e,t,s,r,n,i){var l,c,d,u,f,g;const a=ct("router-link");return R(),T("div",ih,[o("div",lh,[o("div",ah,[o("div",ch,[o("div",null,[t[0]||(t[0]=o("h1",{class:"text-2xl font-bold text-gray-900"},"Dashboard",-1)),o("p",uh,"Bienvenue, "+y((l=n.user)==null?void 0:l.name),1)]),o("div",dh,[o("span",fh,y(((c=n.user)==null?void 0:c.role)==="admin"?"Administrateur":((d=n.user)==null?void 0:d.role)==="organizer"?"Organisateur":"Utilisateur"),1)])])])]),o("div",ph,[((u=n.user)==null?void 0:u.role)==="user"?(R(),T("div",mh,[o("div",hh,[o("div",gh,[o("div",vh,[t[2]||(t[2]=o("div",{class:"p-3 bg-orange-100 rounded-lg"},[o("svg",{class:"w-6 h-6 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",xh,[t[1]||(t[1]=o("p",{class:"text-sm font-medium text-gray-600"},"Réservations totales",-1)),o("p",bh,y(n.userStats.totalReservations),1)])])]),o("div",yh,[o("div",wh,[t[4]||(t[4]=o("div",{class:"p-3 bg-blue-100 rounded-lg"},[o("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",_h,[t[3]||(t[3]=o("p",{class:"text-sm font-medium text-gray-600"},"Événements à venir",-1)),o("p",kh,y(n.userStats.upcomingEvents),1)])])]),o("div",Eh,[o("div",Sh,[t[6]||(t[6]=o("div",{class:"p-3 bg-green-100 rounded-lg"},[o("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",Ch,[t[5]||(t[5]=o("p",{class:"text-sm font-medium text-gray-600"},"Total dépensé",-1)),o("p",Rh,"$"+y(n.userStats.totalSpent),1)])])])]),o("div",Th,[o("div",Ah,[t[8]||(t[8]=o("h2",{class:"text-xl font-semibold"},"Mes réservations",-1)),K(a,{to:"/events",class:"btn-primary"},{default:ne(()=>t[7]||(t[7]=[H(" Réserver un événement ")])),_:1,__:[7]})]),n.loading?(R(),T("div",Oh,t[9]||(t[9]=[o("div",{class:"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-orange-600"},null,-1)]))):n.reservations.length===0?(R(),T("div",$h,t[10]||(t[10]=[o("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})],-1),o("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Aucune réservation",-1),o("p",{class:"mt-1 text-sm text-gray-500"},"Commencez par réserver votre premier événement.",-1)]))):(R(),T("div",Mh,[(R(!0),T(ge,null,Te(n.reservations,v=>{var h,w,C,O;return R(),T("div",{key:v.id,class:"border rounded-lg p-4 hover:bg-gray-50"},[o("div",Ph,[o("div",Dh,[o("h3",jh,y((h=v.event)==null?void 0:h.title),1),o("p",Nh,y((w=v.event)==null?void 0:w.location),1),o("p",Fh,y(i.formatDate((C=v.event)==null?void 0:C.event_date)),1),o("div",Ih,[o("span",Uh,y(v.quantity)+" billet(s)",1),o("span",Vh,"$"+y(v.total_amount),1),o("span",{class:ae([i.getStatusClass(v.status),"px-2 py-1 rounded-full text-xs font-medium"])},y(i.getStatusText(v.status)),3)])]),o("div",Bh,[K(a,{to:`/events/${(O=v.event)==null?void 0:O.id}`,class:"text-orange-600 hover:text-orange-700 text-sm"},{default:ne(()=>t[11]||(t[11]=[H(" Voir l'événement ")])),_:2,__:[11]},1032,["to"])])])])}),128))]))])])):((f=n.user)==null?void 0:f.role)==="organizer"?(R(),T("div",Lh,t[12]||(t[12]=[Be('<div class="text-center"><h2 class="text-2xl font-bold text-gray-900 mb-4">Dashboard Organisateur</h2><p class="text-gray-600">Gérez vos événements et suivez vos ventes</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="card text-center"><svg class="mx-auto h-12 w-12 text-orange-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg><h3 class="text-lg font-semibold mb-2">Créer un événement</h3><p class="text-gray-600 mb-4">Organisez votre prochain événement culturel</p><button class="btn-primary">Créer un événement</button></div><div class="card text-center"><svg class="mx-auto h-12 w-12 text-blue-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg><h3 class="text-lg font-semibold mb-2">Voir les statistiques</h3><p class="text-gray-600 mb-4">Analysez les performances de vos événements</p><button class="btn-secondary">Voir les stats</button></div></div>',2)]))):((g=n.user)==null?void 0:g.role)==="admin"?(R(),T("div",zh,[t[17]||(t[17]=o("div",{class:"text-center"},[o("h2",{class:"text-2xl font-bold text-gray-900 mb-4"},"Dashboard Administrateur"),o("p",{class:"text-gray-600"},"Gérez la plateforme KabEvents")],-1)),o("div",Hh,[o("div",qh,[o("div",Kh,y(n.adminStats.totalUsers),1),t[13]||(t[13]=o("div",{class:"text-gray-600"},"Utilisateurs",-1))]),o("div",Wh,[o("div",Jh,y(n.adminStats.totalEvents),1),t[14]||(t[14]=o("div",{class:"text-gray-600"},"Événements",-1))]),o("div",Gh,[o("div",Xh,y(n.adminStats.totalReservations),1),t[15]||(t[15]=o("div",{class:"text-gray-600"},"Réservations",-1))]),o("div",Qh,[o("div",Yh,"$"+y(n.adminStats.totalRevenue),1),t[16]||(t[16]=o("div",{class:"text-gray-600"},"Revenus",-1))])])])):fe("",!0)])])}const e1=Ge(oh,[["render",Zh]]),t1={name:"UserDashboard",data(){return{user:null,activeTab:"overview",userStats:{totalReservations:0,upcomingEvents:0,totalSpent:0},reservations:[],upcomingReservations:[],availableEvents:[],profileForm:{name:"",email:"",password:"",password_confirmation:""},loading:!0,updating:!1}},methods:{async fetchData(){try{const e=await this.$http.get("/me");this.user=e.data.user,this.profileForm.name=this.user.name,this.profileForm.email=this.user.email;const t=await this.$http.get("/user/reservations");this.reservations=t.data.data||t.data,this.userStats.totalReservations=this.reservations.length,this.upcomingReservations=this.reservations.filter(r=>{var n;return new Date((n=r.event)==null?void 0:n.event_date)>new Date}),this.userStats.upcomingEvents=this.upcomingReservations.length,this.userStats.totalSpent=this.reservations.filter(r=>r.status==="paid").reduce((r,n)=>r+parseFloat(n.total_amount),0).toFixed(2);const s=await this.$http.get("/events?limit=6");s.data.data&&s.data.data.data?this.availableEvents=s.data.data.data:s.data.data?this.availableEvents=s.data.data:this.availableEvents=s.data}catch(e){console.error("Error fetching user data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},async updateProfile(){this.updating=!0;try{const e=await this.$http.put("/user/profile",this.profileForm);this.user=e.data.user,localStorage.setItem("user",JSON.stringify(this.user)),alert("Profil mis à jour avec succès !"),this.profileForm.password="",this.profileForm.password_confirmation=""}catch(e){console.error("Error updating profile:",e),alert("Erreur lors de la mise à jour du profil")}finally{this.updating=!1}},downloadTicket(e){console.log("Download ticket for reservation:",e),alert("Fonctionnalité de téléchargement à implémenter")},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})},getStatusClass(e){switch(e){case"paid":return"status-active";case"pending":return"bg-secondary-100 text-secondary-800";case"cancelled":return"status-inactive";default:return"bg-accent-100 text-accent-800"}},getStatusText(e){switch(e){case"paid":return"Payé";case"pending":return"En attente";case"cancelled":return"Annulé";default:return e}},getRoleClass(e){switch(e){case"admin":return"bg-danger-100 text-danger-800";case"organizer":return"bg-secondary-100 text-secondary-800";default:return"bg-primary-100 text-primary-800"}},getRoleText(e){switch(e){case"admin":return"Administrateur";case"organizer":return"Organisateur";default:return"Utilisateur"}}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchData()}},s1={class:"min-h-screen"},n1={class:"bg-white shadow-sm"},r1={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},o1={class:"flex justify-between items-center"},i1={class:"text-accent-600 mt-1"},l1={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},a1={class:"mb-8"},c1={class:"flex space-x-8"},u1={key:0,class:"space-y-8"},d1={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},f1={class:"card text-center"},p1={class:"text-3xl font-bold text-primary-600 mb-2"},m1={class:"card text-center"},h1={class:"text-3xl font-bold text-secondary-600 mb-2"},g1={class:"card text-center"},v1={class:"text-3xl font-bold text-success-600 mb-2"},x1={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},b1={class:"card"},y1={class:"flex justify-between items-center mb-6"},w1={key:0,class:"text-center py-8"},_1={class:"mt-6"},k1={key:1,class:"space-y-4"},E1={class:"w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center"},S1={class:"text-white font-bold"},C1={class:"flex-1"},R1={class:"font-semibold text-accent-900"},T1={class:"text-accent-600 text-sm"},A1={class:"text-accent-500 text-sm"},O1={class:"text-right"},$1={class:"text-accent-500 text-sm mt-1"},M1={key:1,class:"space-y-6"},P1={class:"flex justify-between items-center"},D1={key:0,class:"text-center py-16"},j1={key:1,class:"text-center py-16"},N1={class:"mt-6"},F1={key:2,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},I1={class:"flex items-start space-x-4"},U1={class:"w-16 h-16 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center flex-shrink-0"},V1={class:"text-white font-bold text-lg"},B1={class:"flex-1 min-w-0"},L1={class:"font-bold text-lg text-accent-900 mb-1"},z1={class:"text-accent-600 text-sm mb-2"},H1={class:"text-accent-500 text-sm mb-3"},q1={class:"flex items-center justify-between mb-4"},K1={class:"flex items-center space-x-4"},W1={class:"text-sm text-accent-600"},J1={class:"font-semibold text-accent-900"},G1={class:"flex space-x-2"},X1=["onClick"],Q1={key:2,class:"space-y-6"},Y1={class:"flex justify-between items-center"},Z1={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},eg={class:"event-image"},tg={class:"text-center"},sg={class:"text-white text-2xl font-bold"},ng={class:"mt-2 text-white/80 text-sm"},rg={class:"p-6"},og={class:"text-lg font-bold text-accent-900 mb-2"},ig={class:"text-accent-600 text-sm mb-4 line-clamp-2"},lg={class:"flex justify-between items-center mb-4"},ag={class:"price-tag"},cg={class:"text-sm text-accent-500"},ug={key:3,class:"space-y-6"},dg={class:"card max-w-2xl"},fg={class:"flex items-center space-x-6"},pg={class:"w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center"},mg={class:"text-white font-bold text-2xl"},hg={class:"text-lg font-semibold text-accent-900"},gg={class:"text-accent-600"},vg={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},xg={class:"flex justify-end"},bg=["disabled"],yg={key:0,class:"loading-spinner w-4 h-4 mr-2"};function wg(e,t,s,r,n,i){var l,c,d,u,f,g,v;const a=ct("router-link");return R(),T("div",s1,[o("div",n1,[o("div",r1,[o("div",o1,[o("div",null,[t[15]||(t[15]=o("h1",{class:"text-3xl font-bold text-gradient"},"Mon Dashboard",-1)),o("p",i1,"Bienvenue, "+y((l=n.user)==null?void 0:l.name),1)]),t[16]||(t[16]=o("div",{class:"flex items-center space-x-4"},[o("span",{class:"status-badge bg-primary-100 text-primary-800"}," Utilisateur ")],-1))])])]),o("div",l1,[o("div",a1,[o("nav",c1,[o("button",{onClick:t[0]||(t[0]=h=>n.activeTab="overview"),class:ae([n.activeTab==="overview"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Vue d'ensemble ",2),o("button",{onClick:t[1]||(t[1]=h=>n.activeTab="reservations"),class:ae([n.activeTab==="reservations"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Mes réservations ",2),o("button",{onClick:t[2]||(t[2]=h=>n.activeTab="events"),class:ae([n.activeTab==="events"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Événements disponibles ",2),o("button",{onClick:t[3]||(t[3]=h=>n.activeTab="profile"),class:ae([n.activeTab==="profile"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Mon profil ",2)])]),n.activeTab==="overview"?(R(),T("div",u1,[o("div",d1,[o("div",f1,[t[17]||(t[17]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",p1,y(n.userStats.totalReservations),1),t[18]||(t[18]=o("div",{class:"text-accent-600 font-medium"},"Réservations totales",-1))]),o("div",m1,[t[19]||(t[19]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",h1,y(n.userStats.upcomingEvents),1),t[20]||(t[20]=o("div",{class:"text-accent-600 font-medium"},"Événements à venir",-1))]),o("div",g1,[t[21]||(t[21]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",v1,"$"+y(n.userStats.totalSpent),1),t[22]||(t[22]=o("div",{class:"text-accent-600 font-medium"},"Total dépensé",-1))])]),o("div",x1,[o("div",{class:"card text-center group cursor-pointer",onClick:t[4]||(t[4]=h=>n.activeTab="events")},t[23]||(t[23]=[Be('<div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-c323b33e><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-c323b33e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" data-v-c323b33e></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-c323b33e>Découvrir des événements</h3><p class="text-accent-600 mb-6" data-v-c323b33e>Explorez les événements culturels kabyles disponibles</p><div class="btn-primary inline-block" data-v-c323b33e>Explorer</div>',4)])),o("div",{class:"card text-center group cursor-pointer",onClick:t[5]||(t[5]=h=>n.activeTab="reservations")},t[24]||(t[24]=[Be('<div class="w-20 h-20 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-c323b33e><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-c323b33e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" data-v-c323b33e></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-c323b33e>Mes réservations</h3><p class="text-accent-600 mb-6" data-v-c323b33e>Gérez vos billets et réservations d&#39;événements</p><div class="btn-outline inline-block" data-v-c323b33e>Voir mes billets</div>',4)]))]),o("div",b1,[o("div",y1,[t[25]||(t[25]=o("h3",{class:"text-xl font-bold text-accent-900"},"Mes prochains événements",-1)),o("button",{onClick:t[6]||(t[6]=h=>n.activeTab="reservations"),class:"text-primary-600 hover:text-primary-700 font-medium"}," Voir tous ")]),n.upcomingReservations.length===0?(R(),T("div",w1,[t[26]||(t[26]=o("svg",{class:"mx-auto h-12 w-12 text-accent-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),t[27]||(t[27]=o("h3",{class:"mt-2 text-sm font-medium text-accent-900"},"Aucun événement à venir",-1)),t[28]||(t[28]=o("p",{class:"mt-1 text-sm text-accent-500"},"Découvrez et réservez votre prochain événement culturel.",-1)),o("div",_1,[o("button",{onClick:t[7]||(t[7]=h=>n.activeTab="events"),class:"btn-primary"}," Découvrir des événements ")])])):(R(),T("div",k1,[(R(!0),T(ge,null,Te(n.upcomingReservations,h=>{var w,C,O,$;return R(),T("div",{key:h.id,class:"flex items-center space-x-4 p-4 bg-accent-50 rounded-lg hover:bg-accent-100 transition-colors duration-200"},[o("div",E1,[o("span",S1,y((w=h.event)==null?void 0:w.title.substring(0,2).toUpperCase()),1)]),o("div",C1,[o("h4",R1,y((C=h.event)==null?void 0:C.title),1),o("p",T1,y((O=h.event)==null?void 0:O.location),1),o("p",A1,y(i.formatDate(($=h.event)==null?void 0:$.event_date)),1)]),o("div",O1,[o("span",{class:ae([i.getStatusClass(h.status),"status-badge"])},y(i.getStatusText(h.status)),3),o("p",$1,y(h.quantity)+" billet(s)",1)])])}),128))]))])])):fe("",!0),n.activeTab==="reservations"?(R(),T("div",M1,[o("div",P1,[t[30]||(t[30]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Mes réservations",-1)),o("button",{onClick:t[8]||(t[8]=h=>n.activeTab="events"),class:"btn-primary"},t[29]||(t[29]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),H(" Nouvelle réservation ")]))]),n.loading?(R(),T("div",D1,t[31]||(t[31]=[o("div",{class:"loading-spinner w-12 h-12 mx-auto"},null,-1),o("p",{class:"text-accent-600 mt-4"},"Chargement de vos réservations...",-1)]))):n.reservations.length===0?(R(),T("div",j1,[t[32]||(t[32]=o("svg",{class:"mx-auto h-16 w-16 text-accent-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})],-1)),t[33]||(t[33]=o("h3",{class:"mt-4 text-lg font-medium text-accent-900"},"Aucune réservation",-1)),t[34]||(t[34]=o("p",{class:"mt-2 text-accent-500"},"Vous n'avez pas encore réservé d'événements.",-1)),o("div",N1,[o("button",{onClick:t[9]||(t[9]=h=>n.activeTab="events"),class:"btn-primary"}," Découvrir des événements ")])])):(R(),T("div",F1,[(R(!0),T(ge,null,Te(n.reservations,h=>{var w,C,O,$,j;return R(),T("div",{key:h.id,class:"card"},[o("div",I1,[o("div",U1,[o("span",V1,y((w=h.event)==null?void 0:w.title.substring(0,2).toUpperCase()),1)]),o("div",B1,[o("h3",L1,y((C=h.event)==null?void 0:C.title),1),o("p",z1,y((O=h.event)==null?void 0:O.location),1),o("p",H1,y(i.formatDate(($=h.event)==null?void 0:$.event_date)),1),o("div",q1,[o("div",K1,[o("span",W1,y(h.quantity)+" billet(s)",1),o("span",J1,"$"+y(h.total_amount),1)]),o("span",{class:ae([i.getStatusClass(h.status),"status-badge"])},y(i.getStatusText(h.status)),3)]),o("div",G1,[K(a,{to:`/events/${(j=h.event)==null?void 0:j.id}`,class:"btn-outline text-sm px-3 py-1 flex-1 text-center"},{default:ne(()=>t[35]||(t[35]=[H(" Voir l'événement ")])),_:2,__:[35]},1032,["to"]),h.status==="paid"?(R(),T("button",{key:0,onClick:I=>i.downloadTicket(h),class:"btn-primary text-sm px-3 py-1"}," Télécharger ",8,X1)):fe("",!0)])])])])}),128))]))])):fe("",!0),n.activeTab==="events"?(R(),T("div",Q1,[o("div",Y1,[t[37]||(t[37]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Événements disponibles",-1)),K(a,{to:"/events",class:"btn-outline"},{default:ne(()=>t[36]||(t[36]=[H(" Voir tous les événements ")])),_:1,__:[36]})]),o("div",Z1,[(R(!0),T(ge,null,Te(n.availableEvents,h=>(R(),T("div",{key:h.id,class:"event-card"},[o("div",eg,[o("div",tg,[o("span",sg,y(h.title.substring(0,2).toUpperCase()),1),o("div",ng,y(h.location),1)])]),o("div",rg,[o("h3",og,y(h.title),1),o("p",ig,y(h.description),1),o("div",lg,[o("span",ag,"$"+y(h.ticket_price),1),o("span",cg,y(i.formatDate(h.event_date)),1)]),K(a,{to:`/events/${h.id}`,class:"btn-primary w-full text-center"},{default:ne(()=>t[38]||(t[38]=[H(" Réserver ")])),_:2,__:[38]},1032,["to"])])]))),128))])])):fe("",!0),n.activeTab==="profile"?(R(),T("div",ug,[t[43]||(t[43]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Mon profil",-1)),o("div",dg,[o("form",{onSubmit:t[14]||(t[14]=En((...h)=>i.updateProfile&&i.updateProfile(...h),["prevent"])),class:"space-y-6"},[o("div",fg,[o("div",pg,[o("span",mg,y((d=(c=n.user)==null?void 0:c.name)==null?void 0:d.charAt(0).toUpperCase()),1)]),o("div",null,[o("h3",hg,y((u=n.user)==null?void 0:u.name),1),o("p",gg,y((f=n.user)==null?void 0:f.email),1),o("span",{class:ae([i.getRoleClass((g=n.user)==null?void 0:g.role),"status-badge mt-2"])},y(i.getRoleText((v=n.user)==null?void 0:v.role)),3)])]),o("div",vg,[o("div",null,[t[39]||(t[39]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Nom complet",-1)),X(o("input",{"onUpdate:modelValue":t[10]||(t[10]=h=>n.profileForm.name=h),type:"text",class:"input-field",required:""},null,512),[[he,n.profileForm.name]])]),o("div",null,[t[40]||(t[40]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Email",-1)),X(o("input",{"onUpdate:modelValue":t[11]||(t[11]=h=>n.profileForm.email=h),type:"email",class:"input-field",required:""},null,512),[[he,n.profileForm.email]])])]),o("div",null,[t[41]||(t[41]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Nouveau mot de passe (optionnel)",-1)),X(o("input",{"onUpdate:modelValue":t[12]||(t[12]=h=>n.profileForm.password=h),type:"password",class:"input-field",placeholder:"Laissez vide pour ne pas changer"},null,512),[[he,n.profileForm.password]])]),o("div",null,[t[42]||(t[42]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Confirmer le mot de passe",-1)),X(o("input",{"onUpdate:modelValue":t[13]||(t[13]=h=>n.profileForm.password_confirmation=h),type:"password",class:"input-field"},null,512),[[he,n.profileForm.password_confirmation]])]),o("div",xg,[o("button",{type:"submit",disabled:n.updating,class:"btn-primary"},[n.updating?(R(),T("span",yg)):fe("",!0),H(" "+y(n.updating?"Mise à jour...":"Mettre à jour"),1)],8,bg)])],32)])])):fe("",!0)])])}const _g=Ge(t1,[["render",wg],["__scopeId","data-v-c323b33e"]]),kg={name:"OrganizerDashboard",data(){return{user:null,activeTab:"overview",organizerStats:{total_events:0,total_reservations:0,total_revenue:0,active_events:0,upcoming_events:0},myEvents:[],recentEvents:[],popularEvents:[],newEvent:{title:"",description:"",location:"",event_date:"",ticket_price:"",max_attendees:""},creating:!1,loading:!0}},methods:{async fetchData(){try{const e=await this.$http.get("/me");this.user=e.data.user;const t=await this.$http.get("/organizer/stats");this.organizerStats=t.data;const s=await this.$http.get("/organizer/events");s.data.data&&s.data.data.data?this.myEvents=s.data.data.data:s.data.data?this.myEvents=s.data.data:this.myEvents=s.data,this.recentEvents=this.myEvents.slice(0,5)}catch(e){console.error("Error fetching organizer data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},async createEvent(){this.creating=!0;try{const e=await this.$http.post("/organizer/events",this.newEvent);this.myEvents.unshift(e.data.event),this.resetForm(),this.activeTab="events",alert("Événement créé avec succès !")}catch(e){console.error("Error creating event:",e),alert("Erreur lors de la création de l'événement")}finally{this.creating=!1}},resetForm(){this.newEvent={title:"",description:"",location:"",event_date:"",ticket_price:"",max_attendees:""}},editEvent(e){console.log("Edit event:",e)},viewEventStats(e){console.log("View event stats:",e)},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchData()}},Eg={class:"min-h-screen"},Sg={class:"bg-white shadow-sm"},Cg={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},Rg={class:"flex justify-between items-center"},Tg={class:"text-accent-600 mt-1"},Ag={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Og={class:"mb-8"},$g={class:"flex space-x-8"},Mg={key:0,class:"space-y-8"},Pg={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Dg={class:"card text-center"},jg={class:"text-3xl font-bold text-primary-600 mb-2"},Ng={class:"card text-center"},Fg={class:"text-3xl font-bold text-secondary-600 mb-2"},Ig={class:"card text-center"},Ug={class:"text-3xl font-bold text-success-600 mb-2"},Vg={class:"card text-center"},Bg={class:"text-3xl font-bold text-accent-600 mb-2"},Lg={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},zg={class:"card"},Hg={class:"flex justify-between items-center mb-6"},qg={class:"space-y-4"},Kg={class:"w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center"},Wg={class:"text-white font-bold"},Jg={class:"flex-1"},Gg={class:"font-semibold text-accent-900"},Xg={class:"text-accent-600 text-sm"},Qg={class:"text-right"},Yg={class:"font-semibold text-accent-900"},Zg={class:"text-accent-500 text-sm"},e2={key:1,class:"space-y-6"},t2={class:"flex justify-between items-center"},s2={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},n2={class:"event-image"},r2={class:"text-center"},o2={class:"text-white text-2xl font-bold"},i2={class:"mt-2 text-white/80 text-sm"},l2={class:"p-6"},a2={class:"flex justify-between items-start mb-3"},c2={class:"text-lg font-bold text-accent-900"},u2={class:"text-accent-600 text-sm mb-4 line-clamp-2"},d2={class:"flex justify-between items-center mb-4"},f2={class:"price-tag"},p2={class:"text-sm text-accent-500"},m2={class:"flex space-x-2"},h2=["onClick"],g2=["onClick"],v2={key:2,class:"space-y-6"},x2={class:"card max-w-4xl"},b2={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},y2={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},w2={class:"flex justify-end space-x-4"},_2=["disabled"],k2={key:0,class:"loading-spinner w-4 h-4 mr-2"},E2={key:3,class:"space-y-6"},S2={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},C2={class:"card"},R2={class:"space-y-3"},T2={class:"font-medium text-accent-900"},A2={class:"text-sm text-accent-500"},O2={class:"text-right"},$2={class:"font-semibold text-success-600"};function M2(e,t,s,r,n,i){var a;return R(),T("div",Eg,[o("div",Sg,[o("div",Cg,[o("div",Rg,[o("div",null,[t[16]||(t[16]=o("h1",{class:"text-3xl font-bold text-gradient"},"Dashboard Organisateur",-1)),o("p",Tg,"Bienvenue, "+y((a=n.user)==null?void 0:a.name),1)]),t[17]||(t[17]=o("div",{class:"flex items-center space-x-4"},[o("span",{class:"status-badge bg-secondary-100 text-secondary-800"}," Organisateur ")],-1))])])]),o("div",Ag,[o("div",Og,[o("nav",$g,[o("button",{onClick:t[0]||(t[0]=l=>n.activeTab="overview"),class:ae([n.activeTab==="overview"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Vue d'ensemble ",2),o("button",{onClick:t[1]||(t[1]=l=>n.activeTab="events"),class:ae([n.activeTab==="events"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Mes événements ",2),o("button",{onClick:t[2]||(t[2]=l=>n.activeTab="create"),class:ae([n.activeTab==="create"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Créer un événement ",2),o("button",{onClick:t[3]||(t[3]=l=>n.activeTab="analytics"),class:ae([n.activeTab==="analytics"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Statistiques ",2)])]),n.activeTab==="overview"?(R(),T("div",Mg,[o("div",Pg,[o("div",Dg,[t[18]||(t[18]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",jg,y(n.organizerStats.total_events),1),t[19]||(t[19]=o("div",{class:"text-accent-600 font-medium"},"Événements créés",-1))]),o("div",Ng,[t[20]||(t[20]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",Fg,y(n.organizerStats.total_reservations),1),t[21]||(t[21]=o("div",{class:"text-accent-600 font-medium"},"Réservations",-1))]),o("div",Ig,[t[22]||(t[22]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",Ug,"$"+y(n.organizerStats.total_revenue),1),t[23]||(t[23]=o("div",{class:"text-accent-600 font-medium"},"Revenus",-1))]),o("div",Vg,[t[24]||(t[24]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),o("div",Bg,y(n.organizerStats.upcoming_events),1),t[25]||(t[25]=o("div",{class:"text-accent-600 font-medium"},"Événements à venir",-1))])]),o("div",Lg,[o("div",{class:"card text-center group cursor-pointer",onClick:t[4]||(t[4]=l=>n.activeTab="create")},t[26]||(t[26]=[Be('<div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-6c3dd2e8><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-6c3dd2e8><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" data-v-6c3dd2e8></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-6c3dd2e8>Créer un événement</h3><p class="text-accent-600 mb-6" data-v-6c3dd2e8>Organisez votre prochain événement culturel kabyle</p><div class="btn-primary inline-block" data-v-6c3dd2e8>Commencer</div>',4)])),o("div",{class:"card text-center group cursor-pointer",onClick:t[5]||(t[5]=l=>n.activeTab="analytics")},t[27]||(t[27]=[Be('<div class="w-20 h-20 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-6c3dd2e8><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-6c3dd2e8><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" data-v-6c3dd2e8></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-6c3dd2e8>Voir les statistiques</h3><p class="text-accent-600 mb-6" data-v-6c3dd2e8>Analysez les performances de vos événements</p><div class="btn-outline inline-block" data-v-6c3dd2e8>Analyser</div>',4)]))]),o("div",zg,[o("div",Hg,[t[28]||(t[28]=o("h3",{class:"text-xl font-bold text-accent-900"},"Événements récents",-1)),o("button",{onClick:t[6]||(t[6]=l=>n.activeTab="events"),class:"text-primary-600 hover:text-primary-700 font-medium"}," Voir tous ")]),o("div",qg,[(R(!0),T(ge,null,Te(n.recentEvents,l=>(R(),T("div",{key:l.id,class:"flex items-center space-x-4 p-4 bg-accent-50 rounded-lg hover:bg-accent-100 transition-colors duration-200"},[o("div",Kg,[o("span",Wg,y(l.title.substring(0,2).toUpperCase()),1)]),o("div",Jg,[o("h4",Gg,y(l.title),1),o("p",Xg,y(i.formatDate(l.event_date)),1)]),o("div",Qg,[o("p",Yg,"$"+y(l.ticket_price),1),o("p",Zg,y(l.total_reservations||0)+" réservations",1)])]))),128))])])])):fe("",!0),n.activeTab==="events"?(R(),T("div",e2,[o("div",t2,[t[30]||(t[30]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Mes événements",-1)),o("button",{onClick:t[7]||(t[7]=l=>n.activeTab="create"),class:"btn-primary"},t[29]||(t[29]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),H(" Nouvel événement ")]))]),o("div",s2,[(R(!0),T(ge,null,Te(n.myEvents,l=>(R(),T("div",{key:l.id,class:"event-card"},[o("div",n2,[o("div",r2,[o("span",o2,y(l.title.substring(0,2).toUpperCase()),1),o("div",i2,y(l.location),1)])]),o("div",l2,[o("div",a2,[o("h3",c2,y(l.title),1),o("span",{class:ae([l.is_active?"status-active":"status-inactive","status-badge"])},y(l.is_active?"Actif":"Inactif"),3)]),o("p",u2,y(l.description),1),o("div",d2,[o("span",f2,"$"+y(l.ticket_price),1),o("span",p2,y(i.formatDate(l.event_date)),1)]),o("div",m2,[o("button",{onClick:c=>i.editEvent(l),class:"btn-primary text-sm px-3 py-1 flex-1"},"Modifier",8,h2),o("button",{onClick:c=>i.viewEventStats(l),class:"btn-outline text-sm px-3 py-1"},"Stats",8,g2)])])]))),128))])])):fe("",!0),n.activeTab==="create"?(R(),T("div",v2,[t[37]||(t[37]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Créer un nouvel événement",-1)),o("div",x2,[o("form",{onSubmit:t[15]||(t[15]=En((...l)=>i.createEvent&&i.createEvent(...l),["prevent"])),class:"space-y-6"},[o("div",b2,[o("div",null,[t[31]||(t[31]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Titre de l'événement",-1)),X(o("input",{"onUpdate:modelValue":t[8]||(t[8]=l=>n.newEvent.title=l),type:"text",class:"input-field",placeholder:"Ex: Festival de musique kabyle",required:""},null,512),[[he,n.newEvent.title]])]),o("div",null,[t[32]||(t[32]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Lieu",-1)),X(o("input",{"onUpdate:modelValue":t[9]||(t[9]=l=>n.newEvent.location=l),type:"text",class:"input-field",placeholder:"Ex: Centre culturel, Montréal",required:""},null,512),[[he,n.newEvent.location]])])]),o("div",null,[t[33]||(t[33]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Description",-1)),X(o("textarea",{"onUpdate:modelValue":t[10]||(t[10]=l=>n.newEvent.description=l),rows:"4",class:"input-field",placeholder:"Décrivez votre événement...",required:""},null,512),[[he,n.newEvent.description]])]),o("div",y2,[o("div",null,[t[34]||(t[34]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Date et heure",-1)),X(o("input",{"onUpdate:modelValue":t[11]||(t[11]=l=>n.newEvent.event_date=l),type:"datetime-local",class:"input-field",required:""},null,512),[[he,n.newEvent.event_date]])]),o("div",null,[t[35]||(t[35]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Prix du billet ($)",-1)),X(o("input",{"onUpdate:modelValue":t[12]||(t[12]=l=>n.newEvent.ticket_price=l),type:"number",step:"0.01",class:"input-field",placeholder:"25.00",required:""},null,512),[[he,n.newEvent.ticket_price]])]),o("div",null,[t[36]||(t[36]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Nombre de places",-1)),X(o("input",{"onUpdate:modelValue":t[13]||(t[13]=l=>n.newEvent.max_attendees=l),type:"number",class:"input-field",placeholder:"100",required:""},null,512),[[he,n.newEvent.max_attendees]])])]),o("div",w2,[o("button",{type:"button",onClick:t[14]||(t[14]=(...l)=>i.resetForm&&i.resetForm(...l)),class:"btn-outline"},"Annuler"),o("button",{type:"submit",disabled:n.creating,class:"btn-primary"},[n.creating?(R(),T("span",k2)):fe("",!0),H(" "+y(n.creating?"Création...":"Créer l'événement"),1)],8,_2)])],32)])])):fe("",!0),n.activeTab==="analytics"?(R(),T("div",E2,[t[40]||(t[40]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Statistiques et analyses",-1)),o("div",S2,[t[39]||(t[39]=o("div",{class:"card"},[o("h3",{class:"text-lg font-bold text-accent-900 mb-4"},"Revenus par mois"),o("div",{class:"h-64 flex items-center justify-center bg-accent-50 rounded-lg"},[o("p",{class:"text-accent-500"},"Graphique des revenus (à implémenter)")])],-1)),o("div",C2,[t[38]||(t[38]=o("h3",{class:"text-lg font-bold text-accent-900 mb-4"},"Événements populaires",-1)),o("div",R2,[(R(!0),T(ge,null,Te(n.popularEvents,l=>(R(),T("div",{key:l.id,class:"flex justify-between items-center p-3 bg-accent-50 rounded-lg"},[o("div",null,[o("p",T2,y(l.title),1),o("p",A2,y(l.reservations_count)+" réservations",1)]),o("div",O2,[o("p",$2,"$"+y(l.revenue),1)])]))),128))])])])])):fe("",!0)])])}const P2=Ge(kg,[["render",M2],["__scopeId","data-v-6c3dd2e8"]]),D2={name:"AdminDashboard",data(){return{user:null,activeTab:"overview",adminStats:{totalUsers:0,totalEvents:0,totalReservations:0,totalRevenue:0},users:[],events:[],reservations:[],recentActivity:[],loading:!0,showCreateUserModal:!1}},methods:{async fetchData(){try{const e=await this.$http.get("/me");this.user=e.data.user;const t=await this.$http.get("/admin/stats");this.adminStats=t.data;const s=await this.$http.get("/admin/users");s.data.data&&s.data.data.data?this.users=s.data.data.data:s.data.data?this.users=s.data.data:this.users=s.data;const r=await this.$http.get("/admin/events");r.data.data&&r.data.data.data?this.events=r.data.data.data:r.data.data?this.events=r.data.data:this.events=r.data;const n=await this.$http.get("/admin/reservations");n.data.data&&n.data.data.data?this.reservations=n.data.data.data:n.data.data?this.reservations=n.data.data:this.reservations=n.data}catch(e){console.error("Error fetching admin data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"short",day:"numeric"})},getRoleClass(e){switch(e){case"admin":return"bg-danger-100 text-danger-800";case"organizer":return"bg-secondary-100 text-secondary-800";default:return"bg-primary-100 text-primary-800"}},getRoleText(e){switch(e){case"admin":return"Administrateur";case"organizer":return"Organisateur";default:return"Utilisateur"}},getStatusClass(e){switch(e){case"paid":return"status-active";case"pending":return"bg-secondary-100 text-secondary-800";case"cancelled":return"status-inactive";default:return"bg-accent-100 text-accent-800"}},getStatusText(e){switch(e){case"paid":return"Payé";case"pending":return"En attente";case"cancelled":return"Annulé";default:return e}},editUser(e){console.log("Edit user:",e)},deleteUser(e){console.log("Delete user:",e)},editEvent(e){console.log("Edit event:",e)},deleteEvent(e){console.log("Delete event:",e)},viewReservation(e){console.log("View reservation:",e)}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchData()}},j2={class:"min-h-screen"},N2={class:"bg-white shadow-sm"},F2={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},I2={class:"flex justify-between items-center"},U2={class:"text-accent-600 mt-1"},V2={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},B2={class:"mb-8"},L2={class:"flex space-x-8"},z2={key:0,class:"space-y-8"},H2={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},q2={class:"card text-center"},K2={class:"text-3xl font-bold text-primary-600 mb-2"},W2={class:"card text-center"},J2={class:"text-3xl font-bold text-secondary-600 mb-2"},G2={class:"card text-center"},X2={class:"text-3xl font-bold text-success-600 mb-2"},Q2={class:"card text-center"},Y2={class:"text-3xl font-bold text-accent-600 mb-2"},Z2={class:"card"},ev={class:"space-y-4"},tv={class:"flex-1"},sv={class:"text-accent-900 font-medium"},nv={class:"text-accent-500 text-sm"},rv={key:1,class:"space-y-6"},ov={class:"flex justify-between items-center"},iv={class:"card"},lv={class:"overflow-x-auto"},av={class:"min-w-full divide-y divide-accent-200"},cv={class:"bg-white divide-y divide-accent-200"},uv={class:"px-6 py-4 whitespace-nowrap"},dv={class:"flex items-center"},fv={class:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center"},pv={class:"text-primary-600 font-medium"},mv={class:"ml-4"},hv={class:"text-sm font-medium text-accent-900"},gv={class:"text-sm text-accent-500"},vv={class:"px-6 py-4 whitespace-nowrap"},xv={class:"px-6 py-4 whitespace-nowrap"},bv={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-500"},yv={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},wv=["onClick"],_v=["onClick"],kv={key:2,class:"space-y-6"},Ev={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Sv={class:"event-image"},Cv={class:"text-center"},Rv={class:"text-white text-2xl font-bold"},Tv={class:"p-6"},Av={class:"text-lg font-bold text-accent-900 mb-2"},Ov={class:"text-accent-600 text-sm mb-4"},$v={class:"flex justify-between items-center mb-4"},Mv={class:"price-tag"},Pv={class:"flex space-x-2"},Dv=["onClick"],jv=["onClick"],Nv={key:3,class:"space-y-6"},Fv={class:"card"},Iv={class:"overflow-x-auto"},Uv={class:"min-w-full divide-y divide-accent-200"},Vv={class:"bg-white divide-y divide-accent-200"},Bv={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-accent-900"},Lv={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-900"},zv={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-900"},Hv={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-900"},qv={class:"px-6 py-4 whitespace-nowrap"},Kv={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Wv=["onClick"];function Jv(e,t,s,r,n,i){var a;return R(),T("div",j2,[o("div",N2,[o("div",F2,[o("div",I2,[o("div",null,[t[5]||(t[5]=o("h1",{class:"text-3xl font-bold text-gradient"},"Dashboard Administrateur",-1)),o("p",U2,"Bienvenue, "+y((a=n.user)==null?void 0:a.name),1)]),t[6]||(t[6]=o("div",{class:"flex items-center space-x-4"},[o("span",{class:"status-badge bg-danger-100 text-danger-800"}," Administrateur ")],-1))])])]),o("div",V2,[o("div",B2,[o("nav",L2,[o("button",{onClick:t[0]||(t[0]=l=>n.activeTab="overview"),class:ae([n.activeTab==="overview"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Vue d'ensemble ",2),o("button",{onClick:t[1]||(t[1]=l=>n.activeTab="users"),class:ae([n.activeTab==="users"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Utilisateurs ",2),o("button",{onClick:t[2]||(t[2]=l=>n.activeTab="events"),class:ae([n.activeTab==="events"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Événements ",2),o("button",{onClick:t[3]||(t[3]=l=>n.activeTab="reservations"),class:ae([n.activeTab==="reservations"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Réservations ",2)])]),n.activeTab==="overview"?(R(),T("div",z2,[o("div",H2,[o("div",q2,[t[7]||(t[7]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})])],-1)),o("div",K2,y(n.adminStats.totalUsers),1),t[8]||(t[8]=o("div",{class:"text-accent-600 font-medium"},"Utilisateurs",-1))]),o("div",W2,[t[9]||(t[9]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",J2,y(n.adminStats.totalEvents),1),t[10]||(t[10]=o("div",{class:"text-accent-600 font-medium"},"Événements",-1))]),o("div",G2,[t[11]||(t[11]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",X2,y(n.adminStats.totalReservations),1),t[12]||(t[12]=o("div",{class:"text-accent-600 font-medium"},"Réservations",-1))]),o("div",Q2,[t[13]||(t[13]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",Y2,"$"+y(n.adminStats.totalRevenue),1),t[14]||(t[14]=o("div",{class:"text-accent-600 font-medium"},"Revenus",-1))])]),o("div",Z2,[t[16]||(t[16]=o("h3",{class:"text-xl font-bold text-accent-900 mb-6"},"Activité récente",-1)),o("div",ev,[(R(!0),T(ge,null,Te(n.recentActivity,l=>(R(),T("div",{key:l.id,class:"flex items-center space-x-4 p-4 bg-accent-50 rounded-lg"},[t[15]||(t[15]=o("div",{class:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center"},[o("svg",{class:"w-5 h-5 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),o("div",tv,[o("p",sv,y(l.description),1),o("p",nv,y(i.formatDate(l.created_at)),1)])]))),128))])])])):fe("",!0),n.activeTab==="users"?(R(),T("div",rv,[o("div",ov,[t[18]||(t[18]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Gestion des utilisateurs",-1)),o("button",{onClick:t[4]||(t[4]=l=>n.showCreateUserModal=!0),class:"btn-primary"},t[17]||(t[17]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),H(" Nouvel utilisateur ")]))]),o("div",iv,[o("div",lv,[o("table",av,[t[19]||(t[19]=o("thead",{class:"bg-accent-50"},[o("tr",null,[o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Utilisateur"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Rôle"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Statut"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Inscrit le"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Actions")])],-1)),o("tbody",cv,[(R(!0),T(ge,null,Te(n.users,l=>(R(),T("tr",{key:l.id,class:"hover:bg-accent-50"},[o("td",uv,[o("div",dv,[o("div",fv,[o("span",pv,y(l.name.charAt(0).toUpperCase()),1)]),o("div",mv,[o("div",hv,y(l.name),1),o("div",gv,y(l.email),1)])])]),o("td",vv,[o("span",{class:ae([i.getRoleClass(l.role),"status-badge"])},y(i.getRoleText(l.role)),3)]),o("td",xv,[o("span",{class:ae([l.email_verified_at?"status-active":"status-inactive","status-badge"])},y(l.email_verified_at?"Vérifié":"Non vérifié"),3)]),o("td",bv,y(i.formatDate(l.created_at)),1),o("td",yv,[o("button",{onClick:c=>i.editUser(l),class:"text-primary-600 hover:text-primary-900 mr-3"},"Modifier",8,wv),o("button",{onClick:c=>i.deleteUser(l),class:"text-danger-600 hover:text-danger-900"},"Supprimer",8,_v)])]))),128))])])])])])):fe("",!0),n.activeTab==="events"?(R(),T("div",kv,[t[20]||(t[20]=o("div",{class:"flex justify-between items-center"},[o("h2",{class:"text-2xl font-bold text-accent-900"},"Gestion des événements")],-1)),o("div",Ev,[(R(!0),T(ge,null,Te(n.events,l=>(R(),T("div",{key:l.id,class:"event-card"},[o("div",Sv,[o("div",Cv,[o("span",Rv,y(l.title.substring(0,2).toUpperCase()),1)])]),o("div",Tv,[o("h3",Av,y(l.title),1),o("p",Ov,y(l.description.substring(0,100))+"...",1),o("div",$v,[o("span",Mv,"$"+y(l.ticket_price),1),o("span",{class:ae([l.status==="active"?"status-active":"status-inactive","status-badge"])},y(l.status==="active"?"Actif":"Inactif"),3)]),o("div",Pv,[o("button",{onClick:c=>i.editEvent(l),class:"btn-primary text-sm px-3 py-1 flex-1"},"Modifier",8,Dv),o("button",{onClick:c=>i.deleteEvent(l),class:"btn-outline text-sm px-3 py-1"},"Supprimer",8,jv)])])]))),128))])])):fe("",!0),n.activeTab==="reservations"?(R(),T("div",Nv,[t[22]||(t[22]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Gestion des réservations",-1)),o("div",Fv,[o("div",Iv,[o("table",Uv,[t[21]||(t[21]=o("thead",{class:"bg-accent-50"},[o("tr",null,[o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Réservation"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Utilisateur"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Événement"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Montant"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Statut"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Actions")])],-1)),o("tbody",Vv,[(R(!0),T(ge,null,Te(n.reservations,l=>{var c,d;return R(),T("tr",{key:l.id,class:"hover:bg-accent-50"},[o("td",Bv," #"+y(l.id),1),o("td",Lv,y((c=l.user)==null?void 0:c.name),1),o("td",zv,y((d=l.event)==null?void 0:d.title),1),o("td",Hv," $"+y(l.total_amount),1),o("td",qv,[o("span",{class:ae([i.getStatusClass(l.status),"status-badge"])},y(i.getStatusText(l.status)),3)]),o("td",Kv,[o("button",{onClick:u=>i.viewReservation(l),class:"text-primary-600 hover:text-primary-900"},"Voir",8,Wv)])])}),128))])])])])])):fe("",!0)])])}const Gv=Ge(D2,[["render",Jv]]);pe.defaults.baseURL="/api";pe.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";const zo=localStorage.getItem("auth_token");zo&&(pe.defaults.headers.common.Authorization=`Bearer ${zo}`);const Xv=[{path:"/",name:"home",component:Fp},{path:"/login",name:"login",component:Gp},{path:"/register",name:"register",component:im},{path:"/events",name:"events",component:Rm},{path:"/events/:id",name:"event-detail",component:f0,props:!0},{path:"/checkout/:eventId",name:"checkout",component:rh,props:!0,meta:{requiresAuth:!0}},{path:"/dashboard",name:"dashboard",component:e1,meta:{requiresAuth:!0}},{path:"/dashboard/user",name:"user-dashboard",component:_g,meta:{requiresAuth:!0,role:"user"}},{path:"/dashboard/organizer",name:"organizer-dashboard",component:P2,meta:{requiresAuth:!0,role:"organizer"}},{path:"/dashboard/admin",name:"admin-dashboard",component:Gv,meta:{requiresAuth:!0,role:"admin"}}],Dl=Nf({history:df(),routes:Xv});Dl.beforeEach((e,t,s)=>{const r=localStorage.getItem("auth_token"),n=localStorage.getItem("user"),i=n?JSON.parse(n):null;if(e.meta.requiresAuth&&!r){s("/login");return}if(e.path==="/dashboard")if(i)switch(i.role){case"admin":s("/dashboard/admin");return;case"organizer":s("/dashboard/organizer");return;case"user":s("/dashboard/user");return;default:s("/dashboard/user");return}else if(r){pe.get("/me",{headers:{Authorization:`Bearer ${r}`}}).then(a=>{const l=a.data.user;switch(localStorage.setItem("user",JSON.stringify(l)),l.role){case"admin":s("/dashboard/admin");return;case"organizer":s("/dashboard/organizer");return;case"user":s("/dashboard/user");return;default:s("/dashboard/user");return}}).catch(()=>{localStorage.removeItem("auth_token"),localStorage.removeItem("user"),s("/login")});return}else{s("/login");return}if(e.meta.role&&i&&i.role!==e.meta.role)switch(i.role){case"admin":s("/dashboard/admin");return;case"organizer":s("/dashboard/organizer");return;case"user":s("/dashboard/user");return;default:s("/");return}s()});const Or=$d(lp);Or.use(Dl);Or.config.globalProperties.$http=pe;Or.mount("#app");
