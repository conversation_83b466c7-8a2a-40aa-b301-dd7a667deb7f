function zo(e,t){return function(){return e.apply(t,arguments)}}const{toString:jl}=Object.prototype,{getPrototypeOf:ir}=Object,{iterator:on,toStringTag:Ho}=Symbol,ln=(e=>t=>{const s=jl.call(t);return e[s]||(e[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),We=e=>(e=e.toLowerCase(),t=>ln(t)===e),an=e=>t=>typeof t===e,{isArray:ts}=Array,ys=an("undefined");function Dl(e){return e!==null&&!ys(e)&&e.constructor!==null&&!ys(e.constructor)&&Me(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const qo=We("ArrayBuffer");function Fl(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&qo(e.buffer),t}const Nl=an("string"),Me=an("function"),Ko=an("number"),cn=e=>e!==null&&typeof e=="object",Il=e=>e===!0||e===!1,Us=e=>{if(ln(e)!=="object")return!1;const t=ir(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ho in e)&&!(on in e)},Ul=We("Date"),Bl=We("File"),Vl=We("Blob"),Ll=We("FileList"),zl=e=>cn(e)&&Me(e.pipe),Hl=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Me(e.append)&&((t=ln(e))==="formdata"||t==="object"&&Me(e.toString)&&e.toString()==="[object FormData]"))},ql=We("URLSearchParams"),[Kl,Wl,Jl,Gl]=["ReadableStream","Request","Response","Headers"].map(We),Xl=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function As(e,t,{allOwnKeys:s=!1}={}){if(e===null||typeof e>"u")return;let r,n;if(typeof e!="object"&&(e=[e]),ts(e))for(r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else{const i=s?Object.getOwnPropertyNames(e):Object.keys(e),a=i.length;let l;for(r=0;r<a;r++)l=i[r],t.call(null,e[l],l,e)}}function Wo(e,t){t=t.toLowerCase();const s=Object.keys(e);let r=s.length,n;for(;r-- >0;)if(n=s[r],t===n.toLowerCase())return n;return null}const jt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Jo=e=>!ys(e)&&e!==jt;function Vn(){const{caseless:e}=Jo(this)&&this||{},t={},s=(r,n)=>{const i=e&&Wo(t,n)||n;Us(t[i])&&Us(r)?t[i]=Vn(t[i],r):Us(r)?t[i]=Vn({},r):ts(r)?t[i]=r.slice():t[i]=r};for(let r=0,n=arguments.length;r<n;r++)arguments[r]&&As(arguments[r],s);return t}const Ql=(e,t,s,{allOwnKeys:r}={})=>(As(t,(n,i)=>{s&&Me(n)?e[i]=zo(n,s):e[i]=n},{allOwnKeys:r}),e),Yl=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Zl=(e,t,s,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),s&&Object.assign(e.prototype,s)},ea=(e,t,s,r)=>{let n,i,a;const l={};if(t=t||{},e==null)return t;do{for(n=Object.getOwnPropertyNames(e),i=n.length;i-- >0;)a=n[i],(!r||r(a,e,t))&&!l[a]&&(t[a]=e[a],l[a]=!0);e=s!==!1&&ir(e)}while(e&&(!s||s(e,t))&&e!==Object.prototype);return t},ta=(e,t,s)=>{e=String(e),(s===void 0||s>e.length)&&(s=e.length),s-=t.length;const r=e.indexOf(t,s);return r!==-1&&r===s},sa=e=>{if(!e)return null;if(ts(e))return e;let t=e.length;if(!Ko(t))return null;const s=new Array(t);for(;t-- >0;)s[t]=e[t];return s},na=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ir(Uint8Array)),ra=(e,t)=>{const r=(e&&e[on]).call(e);let n;for(;(n=r.next())&&!n.done;){const i=n.value;t.call(e,i[0],i[1])}},oa=(e,t)=>{let s;const r=[];for(;(s=e.exec(t))!==null;)r.push(s);return r},ia=We("HTMLFormElement"),la=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,r,n){return r.toUpperCase()+n}),Pr=(({hasOwnProperty:e})=>(t,s)=>e.call(t,s))(Object.prototype),aa=We("RegExp"),Go=(e,t)=>{const s=Object.getOwnPropertyDescriptors(e),r={};As(s,(n,i)=>{let a;(a=t(n,i,e))!==!1&&(r[i]=a||n)}),Object.defineProperties(e,r)},ca=e=>{Go(e,(t,s)=>{if(Me(e)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const r=e[s];if(Me(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},ua=(e,t)=>{const s={},r=n=>{n.forEach(i=>{s[i]=!0})};return ts(e)?r(e):r(String(e).split(t)),s},da=()=>{},fa=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function pa(e){return!!(e&&Me(e.append)&&e[Ho]==="FormData"&&e[on])}const ma=e=>{const t=new Array(10),s=(r,n)=>{if(cn(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[n]=r;const i=ts(r)?[]:{};return As(r,(a,l)=>{const c=s(a,n+1);!ys(c)&&(i[l]=c)}),t[n]=void 0,i}}return r};return s(e,0)},ha=We("AsyncFunction"),ga=e=>e&&(cn(e)||Me(e))&&Me(e.then)&&Me(e.catch),Xo=((e,t)=>e?setImmediate:t?((s,r)=>(jt.addEventListener("message",({source:n,data:i})=>{n===jt&&i===s&&r.length&&r.shift()()},!1),n=>{r.push(n),jt.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",Me(jt.postMessage)),va=typeof queueMicrotask<"u"?queueMicrotask.bind(jt):typeof process<"u"&&process.nextTick||Xo,xa=e=>e!=null&&Me(e[on]),b={isArray:ts,isArrayBuffer:qo,isBuffer:Dl,isFormData:Hl,isArrayBufferView:Fl,isString:Nl,isNumber:Ko,isBoolean:Il,isObject:cn,isPlainObject:Us,isReadableStream:Kl,isRequest:Wl,isResponse:Jl,isHeaders:Gl,isUndefined:ys,isDate:Ul,isFile:Bl,isBlob:Vl,isRegExp:aa,isFunction:Me,isStream:zl,isURLSearchParams:ql,isTypedArray:na,isFileList:Ll,forEach:As,merge:Vn,extend:Ql,trim:Xl,stripBOM:Yl,inherits:Zl,toFlatObject:ea,kindOf:ln,kindOfTest:We,endsWith:ta,toArray:sa,forEachEntry:ra,matchAll:oa,isHTMLForm:ia,hasOwnProperty:Pr,hasOwnProp:Pr,reduceDescriptors:Go,freezeMethods:ca,toObjectSet:ua,toCamelCase:la,noop:da,toFiniteNumber:fa,findKey:Wo,global:jt,isContextDefined:Jo,isSpecCompliantForm:pa,toJSONObject:ma,isAsyncFn:ha,isThenable:ga,setImmediate:Xo,asap:va,isIterable:xa};function G(e,t,s,r,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),s&&(this.config=s),r&&(this.request=r),n&&(this.response=n,this.status=n.status?n.status:null)}b.inherits(G,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:b.toJSONObject(this.config),code:this.code,status:this.status}}});const Qo=G.prototype,Yo={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Yo[e]={value:e}});Object.defineProperties(G,Yo);Object.defineProperty(Qo,"isAxiosError",{value:!0});G.from=(e,t,s,r,n,i)=>{const a=Object.create(Qo);return b.toFlatObject(e,a,function(c){return c!==Error.prototype},l=>l!=="isAxiosError"),G.call(a,e.message,t,s,r,n),a.cause=e,a.name=e.name,i&&Object.assign(a,i),a};const ba=null;function Ln(e){return b.isPlainObject(e)||b.isArray(e)}function Zo(e){return b.endsWith(e,"[]")?e.slice(0,-2):e}function jr(e,t,s){return e?e.concat(t).map(function(n,i){return n=Zo(n),!s&&i?"["+n+"]":n}).join(s?".":""):t}function ya(e){return b.isArray(e)&&!e.some(Ln)}const wa=b.toFlatObject(b,{},null,function(t){return/^is[A-Z]/.test(t)});function un(e,t,s){if(!b.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,s=b.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,C){return!b.isUndefined(C[w])});const r=s.metaTokens,n=s.visitor||u,i=s.dots,a=s.indexes,c=(s.Blob||typeof Blob<"u"&&Blob)&&b.isSpecCompliantForm(t);if(!b.isFunction(n))throw new TypeError("visitor must be a function");function d(g){if(g===null)return"";if(b.isDate(g))return g.toISOString();if(!c&&b.isBlob(g))throw new G("Blob is not supported. Use a Buffer instead.");return b.isArrayBuffer(g)||b.isTypedArray(g)?c&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function u(g,w,C){let O=g;if(g&&!C&&typeof g=="object"){if(b.endsWith(w,"{}"))w=r?w:w.slice(0,-2),g=JSON.stringify(g);else if(b.isArray(g)&&ya(g)||(b.isFileList(g)||b.endsWith(w,"[]"))&&(O=b.toArray(g)))return w=Zo(w),O.forEach(function(D,I){!(b.isUndefined(D)||D===null)&&t.append(a===!0?jr([w],I,i):a===null?w:w+"[]",d(D))}),!1}return Ln(g)?!0:(t.append(jr(C,w,i),d(g)),!1)}const f=[],h=Object.assign(wa,{defaultVisitor:u,convertValue:d,isVisitable:Ln});function v(g,w){if(!b.isUndefined(g)){if(f.indexOf(g)!==-1)throw Error("Circular reference detected in "+w.join("."));f.push(g),b.forEach(g,function(O,$){(!(b.isUndefined(O)||O===null)&&n.call(t,O,b.isString($)?$.trim():$,w,h))===!0&&v(O,w?w.concat($):[$])}),f.pop()}}if(!b.isObject(e))throw new TypeError("data must be an object");return v(e),t}function Dr(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function lr(e,t){this._pairs=[],e&&un(e,this,t)}const ei=lr.prototype;ei.append=function(t,s){this._pairs.push([t,s])};ei.toString=function(t){const s=t?function(r){return t.call(this,r,Dr)}:Dr;return this._pairs.map(function(n){return s(n[0])+"="+s(n[1])},"").join("&")};function _a(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ti(e,t,s){if(!t)return e;const r=s&&s.encode||_a;b.isFunction(s)&&(s={serialize:s});const n=s&&s.serialize;let i;if(n?i=n(t,s):i=b.isURLSearchParams(t)?t.toString():new lr(t,s).toString(r),i){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class Fr{constructor(){this.handlers=[]}use(t,s,r){return this.handlers.push({fulfilled:t,rejected:s,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){b.forEach(this.handlers,function(r){r!==null&&t(r)})}}const si={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ka=typeof URLSearchParams<"u"?URLSearchParams:lr,Ea=typeof FormData<"u"?FormData:null,Sa=typeof Blob<"u"?Blob:null,Ca={isBrowser:!0,classes:{URLSearchParams:ka,FormData:Ea,Blob:Sa},protocols:["http","https","file","blob","url","data"]},ar=typeof window<"u"&&typeof document<"u",zn=typeof navigator=="object"&&navigator||void 0,Ra=ar&&(!zn||["ReactNative","NativeScript","NS"].indexOf(zn.product)<0),Ta=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Aa=ar&&window.location.href||"http://localhost",Oa=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ar,hasStandardBrowserEnv:Ra,hasStandardBrowserWebWorkerEnv:Ta,navigator:zn,origin:Aa},Symbol.toStringTag,{value:"Module"})),we={...Oa,...Ca};function $a(e,t){return un(e,new we.classes.URLSearchParams,Object.assign({visitor:function(s,r,n,i){return we.isNode&&b.isBuffer(s)?(this.append(r,s.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function Ma(e){return b.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Pa(e){const t={},s=Object.keys(e);let r;const n=s.length;let i;for(r=0;r<n;r++)i=s[r],t[i]=e[i];return t}function ni(e){function t(s,r,n,i){let a=s[i++];if(a==="__proto__")return!0;const l=Number.isFinite(+a),c=i>=s.length;return a=!a&&b.isArray(n)?n.length:a,c?(b.hasOwnProp(n,a)?n[a]=[n[a],r]:n[a]=r,!l):((!n[a]||!b.isObject(n[a]))&&(n[a]=[]),t(s,r,n[a],i)&&b.isArray(n[a])&&(n[a]=Pa(n[a])),!l)}if(b.isFormData(e)&&b.isFunction(e.entries)){const s={};return b.forEachEntry(e,(r,n)=>{t(Ma(r),n,s,0)}),s}return null}function ja(e,t,s){if(b.isString(e))try{return(t||JSON.parse)(e),b.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(s||JSON.stringify)(e)}const Os={transitional:si,adapter:["xhr","http","fetch"],transformRequest:[function(t,s){const r=s.getContentType()||"",n=r.indexOf("application/json")>-1,i=b.isObject(t);if(i&&b.isHTMLForm(t)&&(t=new FormData(t)),b.isFormData(t))return n?JSON.stringify(ni(t)):t;if(b.isArrayBuffer(t)||b.isBuffer(t)||b.isStream(t)||b.isFile(t)||b.isBlob(t)||b.isReadableStream(t))return t;if(b.isArrayBufferView(t))return t.buffer;if(b.isURLSearchParams(t))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(i){if(r.indexOf("application/x-www-form-urlencoded")>-1)return $a(t,this.formSerializer).toString();if((l=b.isFileList(t))||r.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return un(l?{"files[]":t}:t,c&&new c,this.formSerializer)}}return i||n?(s.setContentType("application/json",!1),ja(t)):t}],transformResponse:[function(t){const s=this.transitional||Os.transitional,r=s&&s.forcedJSONParsing,n=this.responseType==="json";if(b.isResponse(t)||b.isReadableStream(t))return t;if(t&&b.isString(t)&&(r&&!this.responseType||n)){const a=!(s&&s.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(l){if(a)throw l.name==="SyntaxError"?G.from(l,G.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:we.classes.FormData,Blob:we.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};b.forEach(["delete","get","head","post","put","patch"],e=>{Os.headers[e]={}});const Da=b.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Fa=e=>{const t={};let s,r,n;return e&&e.split(`
`).forEach(function(a){n=a.indexOf(":"),s=a.substring(0,n).trim().toLowerCase(),r=a.substring(n+1).trim(),!(!s||t[s]&&Da[s])&&(s==="set-cookie"?t[s]?t[s].push(r):t[s]=[r]:t[s]=t[s]?t[s]+", "+r:r)}),t},Nr=Symbol("internals");function is(e){return e&&String(e).trim().toLowerCase()}function Bs(e){return e===!1||e==null?e:b.isArray(e)?e.map(Bs):String(e)}function Na(e){const t=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=s.exec(e);)t[r[1]]=r[2];return t}const Ia=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Cn(e,t,s,r,n){if(b.isFunction(r))return r.call(this,t,s);if(n&&(t=s),!!b.isString(t)){if(b.isString(r))return t.indexOf(r)!==-1;if(b.isRegExp(r))return r.test(t)}}function Ua(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,s,r)=>s.toUpperCase()+r)}function Ba(e,t){const s=b.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+s,{value:function(n,i,a){return this[r].call(this,t,n,i,a)},configurable:!0})})}let Pe=class{constructor(t){t&&this.set(t)}set(t,s,r){const n=this;function i(l,c,d){const u=is(c);if(!u)throw new Error("header name must be a non-empty string");const f=b.findKey(n,u);(!f||n[f]===void 0||d===!0||d===void 0&&n[f]!==!1)&&(n[f||c]=Bs(l))}const a=(l,c)=>b.forEach(l,(d,u)=>i(d,u,c));if(b.isPlainObject(t)||t instanceof this.constructor)a(t,s);else if(b.isString(t)&&(t=t.trim())&&!Ia(t))a(Fa(t),s);else if(b.isObject(t)&&b.isIterable(t)){let l={},c,d;for(const u of t){if(!b.isArray(u))throw TypeError("Object iterator must return a key-value pair");l[d=u[0]]=(c=l[d])?b.isArray(c)?[...c,u[1]]:[c,u[1]]:u[1]}a(l,s)}else t!=null&&i(s,t,r);return this}get(t,s){if(t=is(t),t){const r=b.findKey(this,t);if(r){const n=this[r];if(!s)return n;if(s===!0)return Na(n);if(b.isFunction(s))return s.call(this,n,r);if(b.isRegExp(s))return s.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,s){if(t=is(t),t){const r=b.findKey(this,t);return!!(r&&this[r]!==void 0&&(!s||Cn(this,this[r],r,s)))}return!1}delete(t,s){const r=this;let n=!1;function i(a){if(a=is(a),a){const l=b.findKey(r,a);l&&(!s||Cn(r,r[l],l,s))&&(delete r[l],n=!0)}}return b.isArray(t)?t.forEach(i):i(t),n}clear(t){const s=Object.keys(this);let r=s.length,n=!1;for(;r--;){const i=s[r];(!t||Cn(this,this[i],i,t,!0))&&(delete this[i],n=!0)}return n}normalize(t){const s=this,r={};return b.forEach(this,(n,i)=>{const a=b.findKey(r,i);if(a){s[a]=Bs(n),delete s[i];return}const l=t?Ua(i):String(i).trim();l!==i&&delete s[i],s[l]=Bs(n),r[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const s=Object.create(null);return b.forEach(this,(r,n)=>{r!=null&&r!==!1&&(s[n]=t&&b.isArray(r)?r.join(", "):r)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,s])=>t+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...s){const r=new this(t);return s.forEach(n=>r.set(n)),r}static accessor(t){const r=(this[Nr]=this[Nr]={accessors:{}}).accessors,n=this.prototype;function i(a){const l=is(a);r[l]||(Ba(n,a),r[l]=!0)}return b.isArray(t)?t.forEach(i):i(t),this}};Pe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);b.reduceDescriptors(Pe.prototype,({value:e},t)=>{let s=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[s]=r}}});b.freezeMethods(Pe);function Rn(e,t){const s=this||Os,r=t||s,n=Pe.from(r.headers);let i=r.data;return b.forEach(e,function(l){i=l.call(s,i,n.normalize(),t?t.status:void 0)}),n.normalize(),i}function ri(e){return!!(e&&e.__CANCEL__)}function ss(e,t,s){G.call(this,e??"canceled",G.ERR_CANCELED,t,s),this.name="CanceledError"}b.inherits(ss,G,{__CANCEL__:!0});function oi(e,t,s){const r=s.config.validateStatus;!s.status||!r||r(s.status)?e(s):t(new G("Request failed with status code "+s.status,[G.ERR_BAD_REQUEST,G.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function Va(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function La(e,t){e=e||10;const s=new Array(e),r=new Array(e);let n=0,i=0,a;return t=t!==void 0?t:1e3,function(c){const d=Date.now(),u=r[i];a||(a=d),s[n]=c,r[n]=d;let f=i,h=0;for(;f!==n;)h+=s[f++],f=f%e;if(n=(n+1)%e,n===i&&(i=(i+1)%e),d-a<t)return;const v=u&&d-u;return v?Math.round(h*1e3/v):void 0}}function za(e,t){let s=0,r=1e3/t,n,i;const a=(d,u=Date.now())=>{s=u,n=null,i&&(clearTimeout(i),i=null),e.apply(null,d)};return[(...d)=>{const u=Date.now(),f=u-s;f>=r?a(d,u):(n=d,i||(i=setTimeout(()=>{i=null,a(n)},r-f)))},()=>n&&a(n)]}const Js=(e,t,s=3)=>{let r=0;const n=La(50,250);return za(i=>{const a=i.loaded,l=i.lengthComputable?i.total:void 0,c=a-r,d=n(c),u=a<=l;r=a;const f={loaded:a,total:l,progress:l?a/l:void 0,bytes:c,rate:d||void 0,estimated:d&&l&&u?(l-a)/d:void 0,event:i,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},s)},Ir=(e,t)=>{const s=e!=null;return[r=>t[0]({lengthComputable:s,total:e,loaded:r}),t[1]]},Ur=e=>(...t)=>b.asap(()=>e(...t)),Ha=we.hasStandardBrowserEnv?((e,t)=>s=>(s=new URL(s,we.origin),e.protocol===s.protocol&&e.host===s.host&&(t||e.port===s.port)))(new URL(we.origin),we.navigator&&/(msie|trident)/i.test(we.navigator.userAgent)):()=>!0,qa=we.hasStandardBrowserEnv?{write(e,t,s,r,n,i){const a=[e+"="+encodeURIComponent(t)];b.isNumber(s)&&a.push("expires="+new Date(s).toGMTString()),b.isString(r)&&a.push("path="+r),b.isString(n)&&a.push("domain="+n),i===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ka(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Wa(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function ii(e,t,s){let r=!Ka(t);return e&&(r||s==!1)?Wa(e,t):t}const Br=e=>e instanceof Pe?{...e}:e;function Nt(e,t){t=t||{};const s={};function r(d,u,f,h){return b.isPlainObject(d)&&b.isPlainObject(u)?b.merge.call({caseless:h},d,u):b.isPlainObject(u)?b.merge({},u):b.isArray(u)?u.slice():u}function n(d,u,f,h){if(b.isUndefined(u)){if(!b.isUndefined(d))return r(void 0,d,f,h)}else return r(d,u,f,h)}function i(d,u){if(!b.isUndefined(u))return r(void 0,u)}function a(d,u){if(b.isUndefined(u)){if(!b.isUndefined(d))return r(void 0,d)}else return r(void 0,u)}function l(d,u,f){if(f in t)return r(d,u);if(f in e)return r(void 0,d)}const c={url:i,method:i,data:i,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:l,headers:(d,u,f)=>n(Br(d),Br(u),f,!0)};return b.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=c[u]||n,h=f(e[u],t[u],u);b.isUndefined(h)&&f!==l||(s[u]=h)}),s}const li=e=>{const t=Nt({},e);let{data:s,withXSRFToken:r,xsrfHeaderName:n,xsrfCookieName:i,headers:a,auth:l}=t;t.headers=a=Pe.from(a),t.url=ti(ii(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&a.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let c;if(b.isFormData(s)){if(we.hasStandardBrowserEnv||we.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((c=a.getContentType())!==!1){const[d,...u]=c?c.split(";").map(f=>f.trim()).filter(Boolean):[];a.setContentType([d||"multipart/form-data",...u].join("; "))}}if(we.hasStandardBrowserEnv&&(r&&b.isFunction(r)&&(r=r(t)),r||r!==!1&&Ha(t.url))){const d=n&&i&&qa.read(i);d&&a.set(n,d)}return t},Ja=typeof XMLHttpRequest<"u",Ga=Ja&&function(e){return new Promise(function(s,r){const n=li(e);let i=n.data;const a=Pe.from(n.headers).normalize();let{responseType:l,onUploadProgress:c,onDownloadProgress:d}=n,u,f,h,v,g;function w(){v&&v(),g&&g(),n.cancelToken&&n.cancelToken.unsubscribe(u),n.signal&&n.signal.removeEventListener("abort",u)}let C=new XMLHttpRequest;C.open(n.method.toUpperCase(),n.url,!0),C.timeout=n.timeout;function O(){if(!C)return;const D=Pe.from("getAllResponseHeaders"in C&&C.getAllResponseHeaders()),J={data:!l||l==="text"||l==="json"?C.responseText:C.response,status:C.status,statusText:C.statusText,headers:D,config:e,request:C};oi(function(Q){s(Q),w()},function(Q){r(Q),w()},J),C=null}"onloadend"in C?C.onloadend=O:C.onreadystatechange=function(){!C||C.readyState!==4||C.status===0&&!(C.responseURL&&C.responseURL.indexOf("file:")===0)||setTimeout(O)},C.onabort=function(){C&&(r(new G("Request aborted",G.ECONNABORTED,e,C)),C=null)},C.onerror=function(){r(new G("Network Error",G.ERR_NETWORK,e,C)),C=null},C.ontimeout=function(){let I=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const J=n.transitional||si;n.timeoutErrorMessage&&(I=n.timeoutErrorMessage),r(new G(I,J.clarifyTimeoutError?G.ETIMEDOUT:G.ECONNABORTED,e,C)),C=null},i===void 0&&a.setContentType(null),"setRequestHeader"in C&&b.forEach(a.toJSON(),function(I,J){C.setRequestHeader(J,I)}),b.isUndefined(n.withCredentials)||(C.withCredentials=!!n.withCredentials),l&&l!=="json"&&(C.responseType=n.responseType),d&&([h,g]=Js(d,!0),C.addEventListener("progress",h)),c&&C.upload&&([f,v]=Js(c),C.upload.addEventListener("progress",f),C.upload.addEventListener("loadend",v)),(n.cancelToken||n.signal)&&(u=D=>{C&&(r(!D||D.type?new ss(null,e,C):D),C.abort(),C=null)},n.cancelToken&&n.cancelToken.subscribe(u),n.signal&&(n.signal.aborted?u():n.signal.addEventListener("abort",u)));const $=Va(n.url);if($&&we.protocols.indexOf($)===-1){r(new G("Unsupported protocol "+$+":",G.ERR_BAD_REQUEST,e));return}C.send(i||null)})},Xa=(e,t)=>{const{length:s}=e=e?e.filter(Boolean):[];if(t||s){let r=new AbortController,n;const i=function(d){if(!n){n=!0,l();const u=d instanceof Error?d:this.reason;r.abort(u instanceof G?u:new ss(u instanceof Error?u.message:u))}};let a=t&&setTimeout(()=>{a=null,i(new G(`timeout ${t} of ms exceeded`,G.ETIMEDOUT))},t);const l=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(d=>{d.unsubscribe?d.unsubscribe(i):d.removeEventListener("abort",i)}),e=null)};e.forEach(d=>d.addEventListener("abort",i));const{signal:c}=r;return c.unsubscribe=()=>b.asap(l),c}},Qa=function*(e,t){let s=e.byteLength;if(s<t){yield e;return}let r=0,n;for(;r<s;)n=r+t,yield e.slice(r,n),r=n},Ya=async function*(e,t){for await(const s of Za(e))yield*Qa(s,t)},Za=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:s,value:r}=await t.read();if(s)break;yield r}}finally{await t.cancel()}},Vr=(e,t,s,r)=>{const n=Ya(e,t);let i=0,a,l=c=>{a||(a=!0,r&&r(c))};return new ReadableStream({async pull(c){try{const{done:d,value:u}=await n.next();if(d){l(),c.close();return}let f=u.byteLength;if(s){let h=i+=f;s(h)}c.enqueue(new Uint8Array(u))}catch(d){throw l(d),d}},cancel(c){return l(c),n.return()}},{highWaterMark:2})},dn=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ai=dn&&typeof ReadableStream=="function",ec=dn&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ci=(e,...t)=>{try{return!!e(...t)}catch{return!1}},tc=ai&&ci(()=>{let e=!1;const t=new Request(we.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Lr=64*1024,Hn=ai&&ci(()=>b.isReadableStream(new Response("").body)),Gs={stream:Hn&&(e=>e.body)};dn&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Gs[t]&&(Gs[t]=b.isFunction(e[t])?s=>s[t]():(s,r)=>{throw new G(`Response type '${t}' is not supported`,G.ERR_NOT_SUPPORT,r)})})})(new Response);const sc=async e=>{if(e==null)return 0;if(b.isBlob(e))return e.size;if(b.isSpecCompliantForm(e))return(await new Request(we.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(b.isArrayBufferView(e)||b.isArrayBuffer(e))return e.byteLength;if(b.isURLSearchParams(e)&&(e=e+""),b.isString(e))return(await ec(e)).byteLength},nc=async(e,t)=>{const s=b.toFiniteNumber(e.getContentLength());return s??sc(t)},rc=dn&&(async e=>{let{url:t,method:s,data:r,signal:n,cancelToken:i,timeout:a,onDownloadProgress:l,onUploadProgress:c,responseType:d,headers:u,withCredentials:f="same-origin",fetchOptions:h}=li(e);d=d?(d+"").toLowerCase():"text";let v=Xa([n,i&&i.toAbortSignal()],a),g;const w=v&&v.unsubscribe&&(()=>{v.unsubscribe()});let C;try{if(c&&tc&&s!=="get"&&s!=="head"&&(C=await nc(u,r))!==0){let J=new Request(t,{method:"POST",body:r,duplex:"half"}),le;if(b.isFormData(r)&&(le=J.headers.get("content-type"))&&u.setContentType(le),J.body){const[Q,Se]=Ir(C,Js(Ur(c)));r=Vr(J.body,Lr,Q,Se)}}b.isString(f)||(f=f?"include":"omit");const O="credentials"in Request.prototype;g=new Request(t,{...h,signal:v,method:s.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:O?f:void 0});let $=await fetch(g);const D=Hn&&(d==="stream"||d==="response");if(Hn&&(l||D&&w)){const J={};["status","statusText","headers"].forEach(Le=>{J[Le]=$[Le]});const le=b.toFiniteNumber($.headers.get("content-length")),[Q,Se]=l&&Ir(le,Js(Ur(l),!0))||[];$=new Response(Vr($.body,Lr,Q,()=>{Se&&Se(),w&&w()}),J)}d=d||"text";let I=await Gs[b.findKey(Gs,d)||"text"]($,e);return!D&&w&&w(),await new Promise((J,le)=>{oi(J,le,{data:I,headers:Pe.from($.headers),status:$.status,statusText:$.statusText,config:e,request:g})})}catch(O){throw w&&w(),O&&O.name==="TypeError"&&/Load failed|fetch/i.test(O.message)?Object.assign(new G("Network Error",G.ERR_NETWORK,e,g),{cause:O.cause||O}):G.from(O,O&&O.code,e,g)}}),qn={http:ba,xhr:Ga,fetch:rc};b.forEach(qn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const zr=e=>`- ${e}`,oc=e=>b.isFunction(e)||e===null||e===!1,ui={getAdapter:e=>{e=b.isArray(e)?e:[e];const{length:t}=e;let s,r;const n={};for(let i=0;i<t;i++){s=e[i];let a;if(r=s,!oc(s)&&(r=qn[(a=String(s)).toLowerCase()],r===void 0))throw new G(`Unknown adapter '${a}'`);if(r)break;n[a||"#"+i]=r}if(!r){const i=Object.entries(n).map(([l,c])=>`adapter ${l} `+(c===!1?"is not supported by the environment":"is not available in the build"));let a=t?i.length>1?`since :
`+i.map(zr).join(`
`):" "+zr(i[0]):"as no adapter specified";throw new G("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return r},adapters:qn};function Tn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ss(null,e)}function Hr(e){return Tn(e),e.headers=Pe.from(e.headers),e.data=Rn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ui.getAdapter(e.adapter||Os.adapter)(e).then(function(r){return Tn(e),r.data=Rn.call(e,e.transformResponse,r),r.headers=Pe.from(r.headers),r},function(r){return ri(r)||(Tn(e),r&&r.response&&(r.response.data=Rn.call(e,e.transformResponse,r.response),r.response.headers=Pe.from(r.response.headers))),Promise.reject(r)})}const di="1.9.0",fn={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{fn[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const qr={};fn.transitional=function(t,s,r){function n(i,a){return"[Axios v"+di+"] Transitional option '"+i+"'"+a+(r?". "+r:"")}return(i,a,l)=>{if(t===!1)throw new G(n(a," has been removed"+(s?" in "+s:"")),G.ERR_DEPRECATED);return s&&!qr[a]&&(qr[a]=!0,console.warn(n(a," has been deprecated since v"+s+" and will be removed in the near future"))),t?t(i,a,l):!0}};fn.spelling=function(t){return(s,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function ic(e,t,s){if(typeof e!="object")throw new G("options must be an object",G.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let n=r.length;for(;n-- >0;){const i=r[n],a=t[i];if(a){const l=e[i],c=l===void 0||a(l,i,e);if(c!==!0)throw new G("option "+i+" must be "+c,G.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new G("Unknown option "+i,G.ERR_BAD_OPTION)}}const Vs={assertOptions:ic,validators:fn},et=Vs.validators;let Dt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Fr,response:new Fr}}async request(t,s){try{return await this._request(t,s)}catch(r){if(r instanceof Error){let n={};Error.captureStackTrace?Error.captureStackTrace(n):n=new Error;const i=n.stack?n.stack.replace(/^.+\n/,""):"";try{r.stack?i&&!String(r.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+i):r.stack=i}catch{}}throw r}}_request(t,s){typeof t=="string"?(s=s||{},s.url=t):s=t||{},s=Nt(this.defaults,s);const{transitional:r,paramsSerializer:n,headers:i}=s;r!==void 0&&Vs.assertOptions(r,{silentJSONParsing:et.transitional(et.boolean),forcedJSONParsing:et.transitional(et.boolean),clarifyTimeoutError:et.transitional(et.boolean)},!1),n!=null&&(b.isFunction(n)?s.paramsSerializer={serialize:n}:Vs.assertOptions(n,{encode:et.function,serialize:et.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Vs.assertOptions(s,{baseUrl:et.spelling("baseURL"),withXsrfToken:et.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let a=i&&b.merge(i.common,i[s.method]);i&&b.forEach(["delete","get","head","post","put","patch","common"],g=>{delete i[g]}),s.headers=Pe.concat(a,i);const l=[];let c=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(s)===!1||(c=c&&w.synchronous,l.unshift(w.fulfilled,w.rejected))});const d=[];this.interceptors.response.forEach(function(w){d.push(w.fulfilled,w.rejected)});let u,f=0,h;if(!c){const g=[Hr.bind(this),void 0];for(g.unshift.apply(g,l),g.push.apply(g,d),h=g.length,u=Promise.resolve(s);f<h;)u=u.then(g[f++],g[f++]);return u}h=l.length;let v=s;for(f=0;f<h;){const g=l[f++],w=l[f++];try{v=g(v)}catch(C){w.call(this,C);break}}try{u=Hr.call(this,v)}catch(g){return Promise.reject(g)}for(f=0,h=d.length;f<h;)u=u.then(d[f++],d[f++]);return u}getUri(t){t=Nt(this.defaults,t);const s=ii(t.baseURL,t.url,t.allowAbsoluteUrls);return ti(s,t.params,t.paramsSerializer)}};b.forEach(["delete","get","head","options"],function(t){Dt.prototype[t]=function(s,r){return this.request(Nt(r||{},{method:t,url:s,data:(r||{}).data}))}});b.forEach(["post","put","patch"],function(t){function s(r){return function(i,a,l){return this.request(Nt(l||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:i,data:a}))}}Dt.prototype[t]=s(),Dt.prototype[t+"Form"]=s(!0)});let lc=class fi{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(i){s=i});const r=this;this.promise.then(n=>{if(!r._listeners)return;let i=r._listeners.length;for(;i-- >0;)r._listeners[i](n);r._listeners=null}),this.promise.then=n=>{let i;const a=new Promise(l=>{r.subscribe(l),i=l}).then(n);return a.cancel=function(){r.unsubscribe(i)},a},t(function(i,a,l){r.reason||(r.reason=new ss(i,a,l),s(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const s=this._listeners.indexOf(t);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const t=new AbortController,s=r=>{t.abort(r)};return this.subscribe(s),t.signal.unsubscribe=()=>this.unsubscribe(s),t.signal}static source(){let t;return{token:new fi(function(n){t=n}),cancel:t}}};function ac(e){return function(s){return e.apply(null,s)}}function cc(e){return b.isObject(e)&&e.isAxiosError===!0}const Kn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Kn).forEach(([e,t])=>{Kn[t]=e});function pi(e){const t=new Dt(e),s=zo(Dt.prototype.request,t);return b.extend(s,Dt.prototype,t,{allOwnKeys:!0}),b.extend(s,t,null,{allOwnKeys:!0}),s.create=function(n){return pi(Nt(e,n))},s}const de=pi(Os);de.Axios=Dt;de.CanceledError=ss;de.CancelToken=lc;de.isCancel=ri;de.VERSION=di;de.toFormData=un;de.AxiosError=G;de.Cancel=de.CanceledError;de.all=function(t){return Promise.all(t)};de.spread=ac;de.isAxiosError=cc;de.mergeConfig=Nt;de.AxiosHeaders=Pe;de.formToJSON=e=>ni(b.isHTMLForm(e)?new FormData(e):e);de.getAdapter=ui.getAdapter;de.HttpStatusCode=Kn;de.default=de;const{Axios:uv,AxiosError:dv,CanceledError:fv,isCancel:pv,CancelToken:mv,VERSION:hv,all:gv,Cancel:vv,isAxiosError:xv,spread:bv,toFormData:yv,AxiosHeaders:wv,HttpStatusCode:_v,formToJSON:kv,getAdapter:Ev,mergeConfig:Sv}=de;window.axios=de;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function cr(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const ie={},qt=[],rt=()=>{},uc=()=>!1,pn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ur=e=>e.startsWith("onUpdate:"),Ee=Object.assign,dr=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},dc=Object.prototype.hasOwnProperty,te=(e,t)=>dc.call(e,t),z=Array.isArray,Kt=e=>$s(e)==="[object Map]",ns=e=>$s(e)==="[object Set]",Kr=e=>$s(e)==="[object Date]",W=e=>typeof e=="function",ge=e=>typeof e=="string",ot=e=>typeof e=="symbol",ue=e=>e!==null&&typeof e=="object",mi=e=>(ue(e)||W(e))&&W(e.then)&&W(e.catch),hi=Object.prototype.toString,$s=e=>hi.call(e),fc=e=>$s(e).slice(8,-1),gi=e=>$s(e)==="[object Object]",fr=e=>ge(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ds=cr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),mn=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},pc=/-(\w)/g,Ve=mn(e=>e.replace(pc,(t,s)=>s?s.toUpperCase():"")),mc=/\B([A-Z])/g,It=mn(e=>e.replace(mc,"-$1").toLowerCase()),hn=mn(e=>e.charAt(0).toUpperCase()+e.slice(1)),An=mn(e=>e?`on${hn(e)}`:""),Rt=(e,t)=>!Object.is(e,t),Ls=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},vi=(e,t,s,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:s})},Xs=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Wr;const gn=()=>Wr||(Wr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function pr(e){if(z(e)){const t={};for(let s=0;s<e.length;s++){const r=e[s],n=ge(r)?xc(r):pr(r);if(n)for(const i in n)t[i]=n[i]}return t}else if(ge(e)||ue(e))return e}const hc=/;(?![^(]*\))/g,gc=/:([^]+)/,vc=/\/\*[^]*?\*\//g;function xc(e){const t={};return e.replace(vc,"").split(hc).forEach(s=>{if(s){const r=s.split(gc);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function fe(e){let t="";if(ge(e))t=e;else if(z(e))for(let s=0;s<e.length;s++){const r=fe(e[s]);r&&(t+=r+" ")}else if(ue(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const bc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",yc=cr(bc);function xi(e){return!!e||e===""}function wc(e,t){if(e.length!==t.length)return!1;let s=!0;for(let r=0;s&&r<e.length;r++)s=Ms(e[r],t[r]);return s}function Ms(e,t){if(e===t)return!0;let s=Kr(e),r=Kr(t);if(s||r)return s&&r?e.getTime()===t.getTime():!1;if(s=ot(e),r=ot(t),s||r)return e===t;if(s=z(e),r=z(t),s||r)return s&&r?wc(e,t):!1;if(s=ue(e),r=ue(t),s||r){if(!s||!r)return!1;const n=Object.keys(e).length,i=Object.keys(t).length;if(n!==i)return!1;for(const a in e){const l=e.hasOwnProperty(a),c=t.hasOwnProperty(a);if(l&&!c||!l&&c||!Ms(e[a],t[a]))return!1}}return String(e)===String(t)}function mr(e,t){return e.findIndex(s=>Ms(s,t))}const bi=e=>!!(e&&e.__v_isRef===!0),y=e=>ge(e)?e:e==null?"":z(e)||ue(e)&&(e.toString===hi||!W(e.toString))?bi(e)?y(e.value):JSON.stringify(e,yi,2):String(e),yi=(e,t)=>bi(t)?yi(e,t.value):Kt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[r,n],i)=>(s[On(r,i)+" =>"]=n,s),{})}:ns(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>On(s))}:ot(t)?On(t):ue(t)&&!z(t)&&!gi(t)?String(t):t,On=(e,t="")=>{var s;return ot(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Oe;class _c{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Oe,!t&&Oe&&(this.index=(Oe.scopes||(Oe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=Oe;try{return Oe=this,t()}finally{Oe=s}}}on(){++this._on===1&&(this.prevScope=Oe,Oe=this)}off(){this._on>0&&--this._on===0&&(Oe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,r;for(s=0,r=this.effects.length;s<r;s++)this.effects[s].stop();for(this.effects.length=0,s=0,r=this.cleanups.length;s<r;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,r=this.scopes.length;s<r;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function kc(){return Oe}let ce;const $n=new WeakSet;class wi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Oe&&Oe.active&&Oe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,$n.has(this)&&($n.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ki(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Jr(this),Ei(this);const t=ce,s=He;ce=this,He=!0;try{return this.fn()}finally{Si(this),ce=t,He=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)vr(t);this.deps=this.depsTail=void 0,Jr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?$n.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Wn(this)&&this.run()}get dirty(){return Wn(this)}}let _i=0,fs,ps;function ki(e,t=!1){if(e.flags|=8,t){e.next=ps,ps=e;return}e.next=fs,fs=e}function hr(){_i++}function gr(){if(--_i>0)return;if(ps){let t=ps;for(ps=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;fs;){let t=fs;for(fs=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=s}}if(e)throw e}function Ei(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Si(e){let t,s=e.depsTail,r=s;for(;r;){const n=r.prevDep;r.version===-1?(r===s&&(s=n),vr(r),Ec(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=n}e.deps=t,e.depsTail=s}function Wn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ci(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ci(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===ws)||(e.globalVersion=ws,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Wn(e))))return;e.flags|=2;const t=e.dep,s=ce,r=He;ce=e,He=!0;try{Ei(e);const n=e.fn(e._value);(t.version===0||Rt(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(n){throw t.version++,n}finally{ce=s,He=r,Si(e),e.flags&=-3}}function vr(e,t=!1){const{dep:s,prevSub:r,nextSub:n}=e;if(r&&(r.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=r,e.nextSub=void 0),s.subs===e&&(s.subs=r,!r&&s.computed)){s.computed.flags&=-5;for(let i=s.computed.deps;i;i=i.nextDep)vr(i,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function Ec(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let He=!0;const Ri=[];function gt(){Ri.push(He),He=!1}function vt(){const e=Ri.pop();He=e===void 0?!0:e}function Jr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=ce;ce=void 0;try{t()}finally{ce=s}}}let ws=0;class Sc{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class xr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ce||!He||ce===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==ce)s=this.activeLink=new Sc(ce,this),ce.deps?(s.prevDep=ce.depsTail,ce.depsTail.nextDep=s,ce.depsTail=s):ce.deps=ce.depsTail=s,Ti(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const r=s.nextDep;r.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=r),s.prevDep=ce.depsTail,s.nextDep=void 0,ce.depsTail.nextDep=s,ce.depsTail=s,ce.deps===s&&(ce.deps=r)}return s}trigger(t){this.version++,ws++,this.notify(t)}notify(t){hr();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{gr()}}}function Ti(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Ti(r)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const Jn=new WeakMap,Ft=Symbol(""),Gn=Symbol(""),_s=Symbol("");function ye(e,t,s){if(He&&ce){let r=Jn.get(e);r||Jn.set(e,r=new Map);let n=r.get(s);n||(r.set(s,n=new xr),n.map=r,n.key=s),n.track()}}function ft(e,t,s,r,n,i){const a=Jn.get(e);if(!a){ws++;return}const l=c=>{c&&c.trigger()};if(hr(),t==="clear")a.forEach(l);else{const c=z(e),d=c&&fr(s);if(c&&s==="length"){const u=Number(r);a.forEach((f,h)=>{(h==="length"||h===_s||!ot(h)&&h>=u)&&l(f)})}else switch((s!==void 0||a.has(void 0))&&l(a.get(s)),d&&l(a.get(_s)),t){case"add":c?d&&l(a.get("length")):(l(a.get(Ft)),Kt(e)&&l(a.get(Gn)));break;case"delete":c||(l(a.get(Ft)),Kt(e)&&l(a.get(Gn)));break;case"set":Kt(e)&&l(a.get(Ft));break}}gr()}function Lt(e){const t=ee(e);return t===e?t:(ye(t,"iterate",_s),Be(e)?t:t.map(xe))}function vn(e){return ye(e=ee(e),"iterate",_s),e}const Cc={__proto__:null,[Symbol.iterator](){return Mn(this,Symbol.iterator,xe)},concat(...e){return Lt(this).concat(...e.map(t=>z(t)?Lt(t):t))},entries(){return Mn(this,"entries",e=>(e[1]=xe(e[1]),e))},every(e,t){return ct(this,"every",e,t,void 0,arguments)},filter(e,t){return ct(this,"filter",e,t,s=>s.map(xe),arguments)},find(e,t){return ct(this,"find",e,t,xe,arguments)},findIndex(e,t){return ct(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ct(this,"findLast",e,t,xe,arguments)},findLastIndex(e,t){return ct(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ct(this,"forEach",e,t,void 0,arguments)},includes(...e){return Pn(this,"includes",e)},indexOf(...e){return Pn(this,"indexOf",e)},join(e){return Lt(this).join(e)},lastIndexOf(...e){return Pn(this,"lastIndexOf",e)},map(e,t){return ct(this,"map",e,t,void 0,arguments)},pop(){return ls(this,"pop")},push(...e){return ls(this,"push",e)},reduce(e,...t){return Gr(this,"reduce",e,t)},reduceRight(e,...t){return Gr(this,"reduceRight",e,t)},shift(){return ls(this,"shift")},some(e,t){return ct(this,"some",e,t,void 0,arguments)},splice(...e){return ls(this,"splice",e)},toReversed(){return Lt(this).toReversed()},toSorted(e){return Lt(this).toSorted(e)},toSpliced(...e){return Lt(this).toSpliced(...e)},unshift(...e){return ls(this,"unshift",e)},values(){return Mn(this,"values",xe)}};function Mn(e,t,s){const r=vn(e),n=r[t]();return r!==e&&!Be(e)&&(n._next=n.next,n.next=()=>{const i=n._next();return i.value&&(i.value=s(i.value)),i}),n}const Rc=Array.prototype;function ct(e,t,s,r,n,i){const a=vn(e),l=a!==e&&!Be(e),c=a[t];if(c!==Rc[t]){const f=c.apply(e,i);return l?xe(f):f}let d=s;a!==e&&(l?d=function(f,h){return s.call(this,xe(f),h,e)}:s.length>2&&(d=function(f,h){return s.call(this,f,h,e)}));const u=c.call(a,d,r);return l&&n?n(u):u}function Gr(e,t,s,r){const n=vn(e);let i=s;return n!==e&&(Be(e)?s.length>3&&(i=function(a,l,c){return s.call(this,a,l,c,e)}):i=function(a,l,c){return s.call(this,a,xe(l),c,e)}),n[t](i,...r)}function Pn(e,t,s){const r=ee(e);ye(r,"iterate",_s);const n=r[t](...s);return(n===-1||n===!1)&&wr(s[0])?(s[0]=ee(s[0]),r[t](...s)):n}function ls(e,t,s=[]){gt(),hr();const r=ee(e)[t].apply(e,s);return gr(),vt(),r}const Tc=cr("__proto__,__v_isRef,__isVue"),Ai=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ot));function Ac(e){ot(e)||(e=String(e));const t=ee(this);return ye(t,"has",e),t.hasOwnProperty(e)}class Oi{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,r){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,i=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return i;if(s==="__v_raw")return r===(n?i?Uc:ji:i?Pi:Mi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const a=z(t);if(!n){let c;if(a&&(c=Cc[s]))return c;if(s==="hasOwnProperty")return Ac}const l=Reflect.get(t,s,ke(t)?t:r);return(ot(s)?Ai.has(s):Tc(s))||(n||ye(t,"get",s),i)?l:ke(l)?a&&fr(s)?l:l.value:ue(l)?n?Fi(l):xn(l):l}}class $i extends Oi{constructor(t=!1){super(!1,t)}set(t,s,r,n){let i=t[s];if(!this._isShallow){const c=Tt(i);if(!Be(r)&&!Tt(r)&&(i=ee(i),r=ee(r)),!z(t)&&ke(i)&&!ke(r))return c?!1:(i.value=r,!0)}const a=z(t)&&fr(s)?Number(s)<t.length:te(t,s),l=Reflect.set(t,s,r,ke(t)?t:n);return t===ee(n)&&(a?Rt(r,i)&&ft(t,"set",s,r):ft(t,"add",s,r)),l}deleteProperty(t,s){const r=te(t,s);t[s];const n=Reflect.deleteProperty(t,s);return n&&r&&ft(t,"delete",s,void 0),n}has(t,s){const r=Reflect.has(t,s);return(!ot(s)||!Ai.has(s))&&ye(t,"has",s),r}ownKeys(t){return ye(t,"iterate",z(t)?"length":Ft),Reflect.ownKeys(t)}}class Oc extends Oi{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const $c=new $i,Mc=new Oc,Pc=new $i(!0);const Xn=e=>e,Fs=e=>Reflect.getPrototypeOf(e);function jc(e,t,s){return function(...r){const n=this.__v_raw,i=ee(n),a=Kt(i),l=e==="entries"||e===Symbol.iterator&&a,c=e==="keys"&&a,d=n[e](...r),u=s?Xn:t?Qs:xe;return!t&&ye(i,"iterate",c?Gn:Ft),{next(){const{value:f,done:h}=d.next();return h?{value:f,done:h}:{value:l?[u(f[0]),u(f[1])]:u(f),done:h}},[Symbol.iterator](){return this}}}}function Ns(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Dc(e,t){const s={get(n){const i=this.__v_raw,a=ee(i),l=ee(n);e||(Rt(n,l)&&ye(a,"get",n),ye(a,"get",l));const{has:c}=Fs(a),d=t?Xn:e?Qs:xe;if(c.call(a,n))return d(i.get(n));if(c.call(a,l))return d(i.get(l));i!==a&&i.get(n)},get size(){const n=this.__v_raw;return!e&&ye(ee(n),"iterate",Ft),Reflect.get(n,"size",n)},has(n){const i=this.__v_raw,a=ee(i),l=ee(n);return e||(Rt(n,l)&&ye(a,"has",n),ye(a,"has",l)),n===l?i.has(n):i.has(n)||i.has(l)},forEach(n,i){const a=this,l=a.__v_raw,c=ee(l),d=t?Xn:e?Qs:xe;return!e&&ye(c,"iterate",Ft),l.forEach((u,f)=>n.call(i,d(u),d(f),a))}};return Ee(s,e?{add:Ns("add"),set:Ns("set"),delete:Ns("delete"),clear:Ns("clear")}:{add(n){!t&&!Be(n)&&!Tt(n)&&(n=ee(n));const i=ee(this);return Fs(i).has.call(i,n)||(i.add(n),ft(i,"add",n,n)),this},set(n,i){!t&&!Be(i)&&!Tt(i)&&(i=ee(i));const a=ee(this),{has:l,get:c}=Fs(a);let d=l.call(a,n);d||(n=ee(n),d=l.call(a,n));const u=c.call(a,n);return a.set(n,i),d?Rt(i,u)&&ft(a,"set",n,i):ft(a,"add",n,i),this},delete(n){const i=ee(this),{has:a,get:l}=Fs(i);let c=a.call(i,n);c||(n=ee(n),c=a.call(i,n)),l&&l.call(i,n);const d=i.delete(n);return c&&ft(i,"delete",n,void 0),d},clear(){const n=ee(this),i=n.size!==0,a=n.clear();return i&&ft(n,"clear",void 0,void 0),a}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=jc(n,e,t)}),s}function br(e,t){const s=Dc(e,t);return(r,n,i)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?r:Reflect.get(te(s,n)&&n in r?s:r,n,i)}const Fc={get:br(!1,!1)},Nc={get:br(!1,!0)},Ic={get:br(!0,!1)};const Mi=new WeakMap,Pi=new WeakMap,ji=new WeakMap,Uc=new WeakMap;function Bc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Vc(e){return e.__v_skip||!Object.isExtensible(e)?0:Bc(fc(e))}function xn(e){return Tt(e)?e:yr(e,!1,$c,Fc,Mi)}function Di(e){return yr(e,!1,Pc,Nc,Pi)}function Fi(e){return yr(e,!0,Mc,Ic,ji)}function yr(e,t,s,r,n){if(!ue(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=Vc(e);if(i===0)return e;const a=n.get(e);if(a)return a;const l=new Proxy(e,i===2?r:s);return n.set(e,l),l}function Wt(e){return Tt(e)?Wt(e.__v_raw):!!(e&&e.__v_isReactive)}function Tt(e){return!!(e&&e.__v_isReadonly)}function Be(e){return!!(e&&e.__v_isShallow)}function wr(e){return e?!!e.__v_raw:!1}function ee(e){const t=e&&e.__v_raw;return t?ee(t):e}function Lc(e){return!te(e,"__v_skip")&&Object.isExtensible(e)&&vi(e,"__v_skip",!0),e}const xe=e=>ue(e)?xn(e):e,Qs=e=>ue(e)?Fi(e):e;function ke(e){return e?e.__v_isRef===!0:!1}function zc(e){return Ni(e,!1)}function Hc(e){return Ni(e,!0)}function Ni(e,t){return ke(e)?e:new qc(e,t)}class qc{constructor(t,s){this.dep=new xr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:ee(t),this._value=s?t:xe(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,r=this.__v_isShallow||Be(t)||Tt(t);t=r?t:ee(t),Rt(t,s)&&(this._rawValue=t,this._value=r?t:xe(t),this.dep.trigger())}}function Jt(e){return ke(e)?e.value:e}const Kc={get:(e,t,s)=>t==="__v_raw"?e:Jt(Reflect.get(e,t,s)),set:(e,t,s,r)=>{const n=e[t];return ke(n)&&!ke(s)?(n.value=s,!0):Reflect.set(e,t,s,r)}};function Ii(e){return Wt(e)?e:new Proxy(e,Kc)}class Wc{constructor(t,s,r){this.fn=t,this.setter=s,this._value=void 0,this.dep=new xr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=ws-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&ce!==this)return ki(this,!0),!0}get value(){const t=this.dep.track();return Ci(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Jc(e,t,s=!1){let r,n;return W(e)?r=e:(r=e.get,n=e.set),new Wc(r,n,s)}const Is={},Ys=new WeakMap;let Pt;function Gc(e,t=!1,s=Pt){if(s){let r=Ys.get(s);r||Ys.set(s,r=[]),r.push(e)}}function Xc(e,t,s=ie){const{immediate:r,deep:n,once:i,scheduler:a,augmentJob:l,call:c}=s,d=I=>n?I:Be(I)||n===!1||n===0?pt(I,1):pt(I);let u,f,h,v,g=!1,w=!1;if(ke(e)?(f=()=>e.value,g=Be(e)):Wt(e)?(f=()=>d(e),g=!0):z(e)?(w=!0,g=e.some(I=>Wt(I)||Be(I)),f=()=>e.map(I=>{if(ke(I))return I.value;if(Wt(I))return d(I);if(W(I))return c?c(I,2):I()})):W(e)?t?f=c?()=>c(e,2):e:f=()=>{if(h){gt();try{h()}finally{vt()}}const I=Pt;Pt=u;try{return c?c(e,3,[v]):e(v)}finally{Pt=I}}:f=rt,t&&n){const I=f,J=n===!0?1/0:n;f=()=>pt(I(),J)}const C=kc(),O=()=>{u.stop(),C&&C.active&&dr(C.effects,u)};if(i&&t){const I=t;t=(...J)=>{I(...J),O()}}let $=w?new Array(e.length).fill(Is):Is;const D=I=>{if(!(!(u.flags&1)||!u.dirty&&!I))if(t){const J=u.run();if(n||g||(w?J.some((le,Q)=>Rt(le,$[Q])):Rt(J,$))){h&&h();const le=Pt;Pt=u;try{const Q=[J,$===Is?void 0:w&&$[0]===Is?[]:$,v];$=J,c?c(t,3,Q):t(...Q)}finally{Pt=le}}}else u.run()};return l&&l(D),u=new wi(f),u.scheduler=a?()=>a(D,!1):D,v=I=>Gc(I,!1,u),h=u.onStop=()=>{const I=Ys.get(u);if(I){if(c)c(I,4);else for(const J of I)J();Ys.delete(u)}},t?r?D(!0):$=u.run():a?a(D.bind(null,!0),!0):u.run(),O.pause=u.pause.bind(u),O.resume=u.resume.bind(u),O.stop=O,O}function pt(e,t=1/0,s){if(t<=0||!ue(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,ke(e))pt(e.value,t,s);else if(z(e))for(let r=0;r<e.length;r++)pt(e[r],t,s);else if(ns(e)||Kt(e))e.forEach(r=>{pt(r,t,s)});else if(gi(e)){for(const r in e)pt(e[r],t,s);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&pt(e[r],t,s)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ps(e,t,s,r){try{return r?e(...r):e()}catch(n){bn(n,t,s)}}function it(e,t,s,r){if(W(e)){const n=Ps(e,t,s,r);return n&&mi(n)&&n.catch(i=>{bn(i,t,s)}),n}if(z(e)){const n=[];for(let i=0;i<e.length;i++)n.push(it(e[i],t,s,r));return n}}function bn(e,t,s,r=!0){const n=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:a}=t&&t.appContext.config||ie;if(t){let l=t.parent;const c=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const u=l.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,c,d)===!1)return}l=l.parent}if(i){gt(),Ps(i,null,10,[e,c,d]),vt();return}}Qc(e,s,n,r,a)}function Qc(e,t,s,r=!0,n=!1){if(n)throw e;console.error(e)}const Re=[];let st=-1;const Gt=[];let kt=null,zt=0;const Ui=Promise.resolve();let Zs=null;function _r(e){const t=Zs||Ui;return e?t.then(this?e.bind(this):e):t}function Yc(e){let t=st+1,s=Re.length;for(;t<s;){const r=t+s>>>1,n=Re[r],i=ks(n);i<e||i===e&&n.flags&2?t=r+1:s=r}return t}function kr(e){if(!(e.flags&1)){const t=ks(e),s=Re[Re.length-1];!s||!(e.flags&2)&&t>=ks(s)?Re.push(e):Re.splice(Yc(t),0,e),e.flags|=1,Bi()}}function Bi(){Zs||(Zs=Ui.then(Li))}function Zc(e){z(e)?Gt.push(...e):kt&&e.id===-1?kt.splice(zt+1,0,e):e.flags&1||(Gt.push(e),e.flags|=1),Bi()}function Xr(e,t,s=st+1){for(;s<Re.length;s++){const r=Re[s];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Re.splice(s,1),s--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Vi(e){if(Gt.length){const t=[...new Set(Gt)].sort((s,r)=>ks(s)-ks(r));if(Gt.length=0,kt){kt.push(...t);return}for(kt=t,zt=0;zt<kt.length;zt++){const s=kt[zt];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}kt=null,zt=0}}const ks=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Li(e){try{for(st=0;st<Re.length;st++){const t=Re[st];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ps(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;st<Re.length;st++){const t=Re[st];t&&(t.flags&=-2)}st=-1,Re.length=0,Vi(),Zs=null,(Re.length||Gt.length)&&Li()}}let Ne=null,zi=null;function en(e){const t=Ne;return Ne=e,zi=e&&e.type.__scopeId||null,t}function ne(e,t=Ne,s){if(!t||e._n)return e;const r=(...n)=>{r._d&&io(-1);const i=en(t);let a;try{a=e(...n)}finally{en(i),r._d&&io(1)}return a};return r._n=!0,r._c=!0,r._d=!0,r}function pe(e,t){if(Ne===null)return e;const s=kn(Ne),r=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[i,a,l,c=ie]=t[n];i&&(W(i)&&(i={mounted:i,updated:i}),i.deep&&pt(a),r.push({dir:i,instance:s,value:a,oldValue:void 0,arg:l,modifiers:c}))}return e}function $t(e,t,s,r){const n=e.dirs,i=t&&t.dirs;for(let a=0;a<n.length;a++){const l=n[a];i&&(l.oldValue=i[a].value);let c=l.dir[r];c&&(gt(),it(c,s,8,[e.el,l,e,t]),vt())}}const eu=Symbol("_vte"),tu=e=>e.__isTeleport;function Er(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Er(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Hi(e,t){return W(e)?Ee({name:e.name},t,{setup:e}):e}function qi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function tn(e,t,s,r,n=!1){if(z(e)){e.forEach((g,w)=>tn(g,t&&(z(t)?t[w]:t),s,r,n));return}if(ms(r)&&!n){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&tn(e,t,s,r.component.subTree);return}const i=r.shapeFlag&4?kn(r.component):r.el,a=n?null:i,{i:l,r:c}=e,d=t&&t.r,u=l.refs===ie?l.refs={}:l.refs,f=l.setupState,h=ee(f),v=f===ie?()=>!1:g=>te(h,g);if(d!=null&&d!==c&&(ge(d)?(u[d]=null,v(d)&&(f[d]=null)):ke(d)&&(d.value=null)),W(c))Ps(c,l,12,[a,u]);else{const g=ge(c),w=ke(c);if(g||w){const C=()=>{if(e.f){const O=g?v(c)?f[c]:u[c]:c.value;n?z(O)&&dr(O,i):z(O)?O.includes(i)||O.push(i):g?(u[c]=[i],v(c)&&(f[c]=u[c])):(c.value=[i],e.k&&(u[e.k]=c.value))}else g?(u[c]=a,v(c)&&(f[c]=a)):w&&(c.value=a,e.k&&(u[e.k]=a))};a?(C.id=-1,Fe(C,s)):C()}}}gn().requestIdleCallback;gn().cancelIdleCallback;const ms=e=>!!e.type.__asyncLoader,Ki=e=>e.type.__isKeepAlive;function su(e,t){Wi(e,"a",t)}function nu(e,t){Wi(e,"da",t)}function Wi(e,t,s=_e){const r=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(yn(t,r,s),s){let n=s.parent;for(;n&&n.parent;)Ki(n.parent.vnode)&&ru(r,t,s,n),n=n.parent}}function ru(e,t,s,r){const n=yn(t,e,r,!0);Ji(()=>{dr(r[t],n)},s)}function yn(e,t,s=_e,r=!1){if(s){const n=s[e]||(s[e]=[]),i=t.__weh||(t.__weh=(...a)=>{gt();const l=js(s),c=it(t,s,e,a);return l(),vt(),c});return r?n.unshift(i):n.push(i),i}}const bt=e=>(t,s=_e)=>{(!Ss||e==="sp")&&yn(e,(...r)=>t(...r),s)},ou=bt("bm"),iu=bt("m"),lu=bt("bu"),au=bt("u"),cu=bt("bum"),Ji=bt("um"),uu=bt("sp"),du=bt("rtg"),fu=bt("rtc");function pu(e,t=_e){yn("ec",e,t)}const mu="components";function xt(e,t){return gu(mu,e,!0,t)||e}const hu=Symbol.for("v-ndc");function gu(e,t,s=!0,r=!1){const n=Ne||_e;if(n){const i=n.type;{const l=nd(i,!1);if(l&&(l===t||l===Ve(t)||l===hn(Ve(t))))return i}const a=Qr(n[e]||i[e],t)||Qr(n.appContext[e],t);return!a&&r?i:a}}function Qr(e,t){return e&&(e[t]||e[Ve(t)]||e[hn(Ve(t))])}function $e(e,t,s,r){let n;const i=s,a=z(e);if(a||ge(e)){const l=a&&Wt(e);let c=!1,d=!1;l&&(c=!Be(e),d=Tt(e),e=vn(e)),n=new Array(e.length);for(let u=0,f=e.length;u<f;u++)n[u]=t(c?d?Qs(xe(e[u])):xe(e[u]):e[u],u,void 0,i)}else if(typeof e=="number"){n=new Array(e);for(let l=0;l<e;l++)n[l]=t(l+1,l,void 0,i)}else if(ue(e))if(e[Symbol.iterator])n=Array.from(e,(l,c)=>t(l,c,void 0,i));else{const l=Object.keys(e);n=new Array(l.length);for(let c=0,d=l.length;c<d;c++){const u=l[c];n[c]=t(e[u],u,c,i)}}else n=[];return n}const Qn=e=>e?ml(e)?kn(e):Qn(e.parent):null,hs=Ee(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Qn(e.parent),$root:e=>Qn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Xi(e),$forceUpdate:e=>e.f||(e.f=()=>{kr(e.update)}),$nextTick:e=>e.n||(e.n=_r.bind(e.proxy)),$watch:e=>Nu.bind(e)}),jn=(e,t)=>e!==ie&&!e.__isScriptSetup&&te(e,t),vu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:r,data:n,props:i,accessCache:a,type:l,appContext:c}=e;let d;if(t[0]!=="$"){const v=a[t];if(v!==void 0)switch(v){case 1:return r[t];case 2:return n[t];case 4:return s[t];case 3:return i[t]}else{if(jn(r,t))return a[t]=1,r[t];if(n!==ie&&te(n,t))return a[t]=2,n[t];if((d=e.propsOptions[0])&&te(d,t))return a[t]=3,i[t];if(s!==ie&&te(s,t))return a[t]=4,s[t];Yn&&(a[t]=0)}}const u=hs[t];let f,h;if(u)return t==="$attrs"&&ye(e.attrs,"get",""),u(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(s!==ie&&te(s,t))return a[t]=4,s[t];if(h=c.config.globalProperties,te(h,t))return h[t]},set({_:e},t,s){const{data:r,setupState:n,ctx:i}=e;return jn(n,t)?(n[t]=s,!0):r!==ie&&te(r,t)?(r[t]=s,!0):te(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:r,appContext:n,propsOptions:i}},a){let l;return!!s[a]||e!==ie&&te(e,a)||jn(t,a)||(l=i[0])&&te(l,a)||te(r,a)||te(hs,a)||te(n.config.globalProperties,a)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:te(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function Yr(e){return z(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let Yn=!0;function xu(e){const t=Xi(e),s=e.proxy,r=e.ctx;Yn=!1,t.beforeCreate&&Zr(t.beforeCreate,e,"bc");const{data:n,computed:i,methods:a,watch:l,provide:c,inject:d,created:u,beforeMount:f,mounted:h,beforeUpdate:v,updated:g,activated:w,deactivated:C,beforeDestroy:O,beforeUnmount:$,destroyed:D,unmounted:I,render:J,renderTracked:le,renderTriggered:Q,errorCaptured:Se,serverPrefetch:Le,expose:Je,inheritAttrs:yt,components:Ot,directives:Ge,filters:rs}=t;if(d&&bu(d,r,null),a)for(const re in a){const Y=a[re];W(Y)&&(r[re]=Y.bind(s))}if(n){const re=n.call(s,s);ue(re)&&(e.data=xn(re))}if(Yn=!0,i)for(const re in i){const Y=i[re],at=W(Y)?Y.bind(s,s):W(Y.get)?Y.get.bind(s,s):rt,wt=!W(Y)&&W(Y.set)?Y.set.bind(s):rt,Xe=ze({get:at,set:wt});Object.defineProperty(r,re,{enumerable:!0,configurable:!0,get:()=>Xe.value,set:Te=>Xe.value=Te})}if(l)for(const re in l)Gi(l[re],r,s,re);if(c){const re=W(c)?c.call(s):c;Reflect.ownKeys(re).forEach(Y=>{zs(Y,re[Y])})}u&&Zr(u,e,"c");function ve(re,Y){z(Y)?Y.forEach(at=>re(at.bind(s))):Y&&re(Y.bind(s))}if(ve(ou,f),ve(iu,h),ve(lu,v),ve(au,g),ve(su,w),ve(nu,C),ve(pu,Se),ve(fu,le),ve(du,Q),ve(cu,$),ve(Ji,I),ve(uu,Le),z(Je))if(Je.length){const re=e.exposed||(e.exposed={});Je.forEach(Y=>{Object.defineProperty(re,Y,{get:()=>s[Y],set:at=>s[Y]=at})})}else e.exposed||(e.exposed={});J&&e.render===rt&&(e.render=J),yt!=null&&(e.inheritAttrs=yt),Ot&&(e.components=Ot),Ge&&(e.directives=Ge),Le&&qi(e)}function bu(e,t,s=rt){z(e)&&(e=Zn(e));for(const r in e){const n=e[r];let i;ue(n)?"default"in n?i=mt(n.from||r,n.default,!0):i=mt(n.from||r):i=mt(n),ke(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:a=>i.value=a}):t[r]=i}}function Zr(e,t,s){it(z(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,s)}function Gi(e,t,s,r){let n=r.includes(".")?cl(s,r):()=>s[r];if(ge(e)){const i=t[e];W(i)&&Hs(n,i)}else if(W(e))Hs(n,e.bind(s));else if(ue(e))if(z(e))e.forEach(i=>Gi(i,t,s,r));else{const i=W(e.handler)?e.handler.bind(s):t[e.handler];W(i)&&Hs(n,i,e)}}function Xi(e){const t=e.type,{mixins:s,extends:r}=t,{mixins:n,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,l=i.get(t);let c;return l?c=l:!n.length&&!s&&!r?c=t:(c={},n.length&&n.forEach(d=>sn(c,d,a,!0)),sn(c,t,a)),ue(t)&&i.set(t,c),c}function sn(e,t,s,r=!1){const{mixins:n,extends:i}=t;i&&sn(e,i,s,!0),n&&n.forEach(a=>sn(e,a,s,!0));for(const a in t)if(!(r&&a==="expose")){const l=yu[a]||s&&s[a];e[a]=l?l(e[a],t[a]):t[a]}return e}const yu={data:eo,props:to,emits:to,methods:us,computed:us,beforeCreate:Ce,created:Ce,beforeMount:Ce,mounted:Ce,beforeUpdate:Ce,updated:Ce,beforeDestroy:Ce,beforeUnmount:Ce,destroyed:Ce,unmounted:Ce,activated:Ce,deactivated:Ce,errorCaptured:Ce,serverPrefetch:Ce,components:us,directives:us,watch:_u,provide:eo,inject:wu};function eo(e,t){return t?e?function(){return Ee(W(e)?e.call(this,this):e,W(t)?t.call(this,this):t)}:t:e}function wu(e,t){return us(Zn(e),Zn(t))}function Zn(e){if(z(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Ce(e,t){return e?[...new Set([].concat(e,t))]:t}function us(e,t){return e?Ee(Object.create(null),e,t):t}function to(e,t){return e?z(e)&&z(t)?[...new Set([...e,...t])]:Ee(Object.create(null),Yr(e),Yr(t??{})):t}function _u(e,t){if(!e)return t;if(!t)return e;const s=Ee(Object.create(null),e);for(const r in t)s[r]=Ce(e[r],t[r]);return s}function Qi(){return{app:null,config:{isNativeTag:uc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ku=0;function Eu(e,t){return function(r,n=null){W(r)||(r=Ee({},r)),n!=null&&!ue(n)&&(n=null);const i=Qi(),a=new WeakSet,l=[];let c=!1;const d=i.app={_uid:ku++,_component:r,_props:n,_container:null,_context:i,_instance:null,version:od,get config(){return i.config},set config(u){},use(u,...f){return a.has(u)||(u&&W(u.install)?(a.add(u),u.install(d,...f)):W(u)&&(a.add(u),u(d,...f))),d},mixin(u){return i.mixins.includes(u)||i.mixins.push(u),d},component(u,f){return f?(i.components[u]=f,d):i.components[u]},directive(u,f){return f?(i.directives[u]=f,d):i.directives[u]},mount(u,f,h){if(!c){const v=d._ceVNode||K(r,n);return v.appContext=i,h===!0?h="svg":h===!1&&(h=void 0),e(v,u,h),c=!0,d._container=u,u.__vue_app__=d,kn(v.component)}},onUnmount(u){l.push(u)},unmount(){c&&(it(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(u,f){return i.provides[u]=f,d},runWithContext(u){const f=Xt;Xt=d;try{return u()}finally{Xt=f}}};return d}}let Xt=null;function zs(e,t){if(_e){let s=_e.provides;const r=_e.parent&&_e.parent.provides;r===s&&(s=_e.provides=Object.create(r)),s[e]=t}}function mt(e,t,s=!1){const r=_e||Ne;if(r||Xt){let n=Xt?Xt._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&W(t)?t.call(r&&r.proxy):t}}const Yi={},Zi=()=>Object.create(Yi),el=e=>Object.getPrototypeOf(e)===Yi;function Su(e,t,s,r=!1){const n={},i=Zi();e.propsDefaults=Object.create(null),tl(e,t,n,i);for(const a in e.propsOptions[0])a in n||(n[a]=void 0);s?e.props=r?n:Di(n):e.type.props?e.props=n:e.props=i,e.attrs=i}function Cu(e,t,s,r){const{props:n,attrs:i,vnode:{patchFlag:a}}=e,l=ee(n),[c]=e.propsOptions;let d=!1;if((r||a>0)&&!(a&16)){if(a&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let h=u[f];if(wn(e.emitsOptions,h))continue;const v=t[h];if(c)if(te(i,h))v!==i[h]&&(i[h]=v,d=!0);else{const g=Ve(h);n[g]=er(c,l,g,v,e,!1)}else v!==i[h]&&(i[h]=v,d=!0)}}}else{tl(e,t,n,i)&&(d=!0);let u;for(const f in l)(!t||!te(t,f)&&((u=It(f))===f||!te(t,u)))&&(c?s&&(s[f]!==void 0||s[u]!==void 0)&&(n[f]=er(c,l,f,void 0,e,!0)):delete n[f]);if(i!==l)for(const f in i)(!t||!te(t,f))&&(delete i[f],d=!0)}d&&ft(e.attrs,"set","")}function tl(e,t,s,r){const[n,i]=e.propsOptions;let a=!1,l;if(t)for(let c in t){if(ds(c))continue;const d=t[c];let u;n&&te(n,u=Ve(c))?!i||!i.includes(u)?s[u]=d:(l||(l={}))[u]=d:wn(e.emitsOptions,c)||(!(c in r)||d!==r[c])&&(r[c]=d,a=!0)}if(i){const c=ee(s),d=l||ie;for(let u=0;u<i.length;u++){const f=i[u];s[f]=er(n,c,f,d[f],e,!te(d,f))}}return a}function er(e,t,s,r,n,i){const a=e[s];if(a!=null){const l=te(a,"default");if(l&&r===void 0){const c=a.default;if(a.type!==Function&&!a.skipFactory&&W(c)){const{propsDefaults:d}=n;if(s in d)r=d[s];else{const u=js(n);r=d[s]=c.call(null,t),u()}}else r=c;n.ce&&n.ce._setProp(s,r)}a[0]&&(i&&!l?r=!1:a[1]&&(r===""||r===It(s))&&(r=!0))}return r}const Ru=new WeakMap;function sl(e,t,s=!1){const r=s?Ru:t.propsCache,n=r.get(e);if(n)return n;const i=e.props,a={},l=[];let c=!1;if(!W(e)){const u=f=>{c=!0;const[h,v]=sl(f,t,!0);Ee(a,h),v&&l.push(...v)};!s&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!i&&!c)return ue(e)&&r.set(e,qt),qt;if(z(i))for(let u=0;u<i.length;u++){const f=Ve(i[u]);so(f)&&(a[f]=ie)}else if(i)for(const u in i){const f=Ve(u);if(so(f)){const h=i[u],v=a[f]=z(h)||W(h)?{type:h}:Ee({},h),g=v.type;let w=!1,C=!0;if(z(g))for(let O=0;O<g.length;++O){const $=g[O],D=W($)&&$.name;if(D==="Boolean"){w=!0;break}else D==="String"&&(C=!1)}else w=W(g)&&g.name==="Boolean";v[0]=w,v[1]=C,(w||te(v,"default"))&&l.push(f)}}const d=[a,l];return ue(e)&&r.set(e,d),d}function so(e){return e[0]!=="$"&&!ds(e)}const Sr=e=>e[0]==="_"||e==="$stable",Cr=e=>z(e)?e.map(nt):[nt(e)],Tu=(e,t,s)=>{if(t._n)return t;const r=ne((...n)=>Cr(t(...n)),s);return r._c=!1,r},nl=(e,t,s)=>{const r=e._ctx;for(const n in e){if(Sr(n))continue;const i=e[n];if(W(i))t[n]=Tu(n,i,r);else if(i!=null){const a=Cr(i);t[n]=()=>a}}},rl=(e,t)=>{const s=Cr(t);e.slots.default=()=>s},ol=(e,t,s)=>{for(const r in t)(s||!Sr(r))&&(e[r]=t[r])},Au=(e,t,s)=>{const r=e.slots=Zi();if(e.vnode.shapeFlag&32){const n=t._;n?(ol(r,t,s),s&&vi(r,"_",n,!0)):nl(t,r)}else t&&rl(e,t)},Ou=(e,t,s)=>{const{vnode:r,slots:n}=e;let i=!0,a=ie;if(r.shapeFlag&32){const l=t._;l?s&&l===1?i=!1:ol(n,t,s):(i=!t.$stable,nl(t,n)),a=t}else t&&(rl(e,t),a={default:1});if(i)for(const l in n)!Sr(l)&&a[l]==null&&delete n[l]},Fe=Hu;function $u(e){return Mu(e)}function Mu(e,t){const s=gn();s.__VUE__=!0;const{insert:r,remove:n,patchProp:i,createElement:a,createText:l,createComment:c,setText:d,setElementText:u,parentNode:f,nextSibling:h,setScopeId:v=rt,insertStaticContent:g}=e,w=(p,m,x,_=null,S=null,E=null,j=void 0,P=null,M=!!m.dynamicChildren)=>{if(p===m)return;p&&!as(p,m)&&(_=k(p),Te(p,S,E,!0),p=null),m.patchFlag===-2&&(M=!1,m.dynamicChildren=null);const{type:R,ref:L,shapeFlag:N}=m;switch(R){case _n:C(p,m,x,_);break;case At:O(p,m,x,_);break;case qs:p==null&&$(m,x,_,j);break;case he:Ot(p,m,x,_,S,E,j,P,M);break;default:N&1?J(p,m,x,_,S,E,j,P,M):N&6?Ge(p,m,x,_,S,E,j,P,M):(N&64||N&128)&&R.process(p,m,x,_,S,E,j,P,M,B)}L!=null&&S&&tn(L,p&&p.ref,E,m||p,!m)},C=(p,m,x,_)=>{if(p==null)r(m.el=l(m.children),x,_);else{const S=m.el=p.el;m.children!==p.children&&d(S,m.children)}},O=(p,m,x,_)=>{p==null?r(m.el=c(m.children||""),x,_):m.el=p.el},$=(p,m,x,_)=>{[p.el,p.anchor]=g(p.children,m,x,_,p.el,p.anchor)},D=({el:p,anchor:m},x,_)=>{let S;for(;p&&p!==m;)S=h(p),r(p,x,_),p=S;r(m,x,_)},I=({el:p,anchor:m})=>{let x;for(;p&&p!==m;)x=h(p),n(p),p=x;n(m)},J=(p,m,x,_,S,E,j,P,M)=>{m.type==="svg"?j="svg":m.type==="math"&&(j="mathml"),p==null?le(m,x,_,S,E,j,P,M):Le(p,m,S,E,j,P,M)},le=(p,m,x,_,S,E,j,P)=>{let M,R;const{props:L,shapeFlag:N,transition:V,dirs:q}=p;if(M=p.el=a(p.type,E,L&&L.is,L),N&8?u(M,p.children):N&16&&Se(p.children,M,null,_,S,Dn(p,E),j,P),q&&$t(p,null,_,"created"),Q(M,p,p.scopeId,j,_),L){for(const ae in L)ae!=="value"&&!ds(ae)&&i(M,ae,null,L[ae],E,_);"value"in L&&i(M,"value",null,L.value,E),(R=L.onVnodeBeforeMount)&&tt(R,_,p)}q&&$t(p,null,_,"beforeMount");const X=Pu(S,V);X&&V.beforeEnter(M),r(M,m,x),((R=L&&L.onVnodeMounted)||X||q)&&Fe(()=>{R&&tt(R,_,p),X&&V.enter(M),q&&$t(p,null,_,"mounted")},S)},Q=(p,m,x,_,S)=>{if(x&&v(p,x),_)for(let E=0;E<_.length;E++)v(p,_[E]);if(S){let E=S.subTree;if(m===E||dl(E.type)&&(E.ssContent===m||E.ssFallback===m)){const j=S.vnode;Q(p,j,j.scopeId,j.slotScopeIds,S.parent)}}},Se=(p,m,x,_,S,E,j,P,M=0)=>{for(let R=M;R<p.length;R++){const L=p[R]=P?Et(p[R]):nt(p[R]);w(null,L,m,x,_,S,E,j,P)}},Le=(p,m,x,_,S,E,j)=>{const P=m.el=p.el;let{patchFlag:M,dynamicChildren:R,dirs:L}=m;M|=p.patchFlag&16;const N=p.props||ie,V=m.props||ie;let q;if(x&&Mt(x,!1),(q=V.onVnodeBeforeUpdate)&&tt(q,x,m,p),L&&$t(m,p,x,"beforeUpdate"),x&&Mt(x,!0),(N.innerHTML&&V.innerHTML==null||N.textContent&&V.textContent==null)&&u(P,""),R?Je(p.dynamicChildren,R,P,x,_,Dn(m,S),E):j||Y(p,m,P,null,x,_,Dn(m,S),E,!1),M>0){if(M&16)yt(P,N,V,x,S);else if(M&2&&N.class!==V.class&&i(P,"class",null,V.class,S),M&4&&i(P,"style",N.style,V.style,S),M&8){const X=m.dynamicProps;for(let ae=0;ae<X.length;ae++){const se=X[ae],je=N[se],Ae=V[se];(Ae!==je||se==="value")&&i(P,se,je,Ae,S,x)}}M&1&&p.children!==m.children&&u(P,m.children)}else!j&&R==null&&yt(P,N,V,x,S);((q=V.onVnodeUpdated)||L)&&Fe(()=>{q&&tt(q,x,m,p),L&&$t(m,p,x,"updated")},_)},Je=(p,m,x,_,S,E,j)=>{for(let P=0;P<m.length;P++){const M=p[P],R=m[P],L=M.el&&(M.type===he||!as(M,R)||M.shapeFlag&198)?f(M.el):x;w(M,R,L,null,_,S,E,j,!0)}},yt=(p,m,x,_,S)=>{if(m!==x){if(m!==ie)for(const E in m)!ds(E)&&!(E in x)&&i(p,E,m[E],null,S,_);for(const E in x){if(ds(E))continue;const j=x[E],P=m[E];j!==P&&E!=="value"&&i(p,E,P,j,S,_)}"value"in x&&i(p,"value",m.value,x.value,S)}},Ot=(p,m,x,_,S,E,j,P,M)=>{const R=m.el=p?p.el:l(""),L=m.anchor=p?p.anchor:l("");let{patchFlag:N,dynamicChildren:V,slotScopeIds:q}=m;q&&(P=P?P.concat(q):q),p==null?(r(R,x,_),r(L,x,_),Se(m.children||[],x,L,S,E,j,P,M)):N>0&&N&64&&V&&p.dynamicChildren?(Je(p.dynamicChildren,V,x,S,E,j,P),(m.key!=null||S&&m===S.subTree)&&il(p,m,!0)):Y(p,m,x,L,S,E,j,P,M)},Ge=(p,m,x,_,S,E,j,P,M)=>{m.slotScopeIds=P,p==null?m.shapeFlag&512?S.ctx.activate(m,x,_,j,M):rs(m,x,_,S,E,j,M):Ut(p,m,M)},rs=(p,m,x,_,S,E,j)=>{const P=p.component=Yu(p,_,S);if(Ki(p)&&(P.ctx.renderer=B),Zu(P,!1,j),P.asyncDep){if(S&&S.registerDep(P,ve,j),!p.el){const M=P.subTree=K(At);O(null,M,m,x)}}else ve(P,p,m,x,S,E,j)},Ut=(p,m,x)=>{const _=m.component=p.component;if(Lu(p,m,x))if(_.asyncDep&&!_.asyncResolved){re(_,m,x);return}else _.next=m,_.update();else m.el=p.el,_.vnode=m},ve=(p,m,x,_,S,E,j)=>{const P=()=>{if(p.isMounted){let{next:N,bu:V,u:q,parent:X,vnode:ae}=p;{const Ye=ll(p);if(Ye){N&&(N.el=ae.el,re(p,N,j)),Ye.asyncDep.then(()=>{p.isUnmounted||P()});return}}let se=N,je;Mt(p,!1),N?(N.el=ae.el,re(p,N,j)):N=ae,V&&Ls(V),(je=N.props&&N.props.onVnodeBeforeUpdate)&&tt(je,X,N,ae),Mt(p,!0);const Ae=ro(p),Qe=p.subTree;p.subTree=Ae,w(Qe,Ae,f(Qe.el),k(Qe),p,S,E),N.el=Ae.el,se===null&&zu(p,Ae.el),q&&Fe(q,S),(je=N.props&&N.props.onVnodeUpdated)&&Fe(()=>tt(je,X,N,ae),S)}else{let N;const{el:V,props:q}=m,{bm:X,m:ae,parent:se,root:je,type:Ae}=p,Qe=ms(m);Mt(p,!1),X&&Ls(X),!Qe&&(N=q&&q.onVnodeBeforeMount)&&tt(N,se,m),Mt(p,!0);{je.ce&&je.ce._injectChildStyle(Ae);const Ye=p.subTree=ro(p);w(null,Ye,x,_,p,S,E),m.el=Ye.el}if(ae&&Fe(ae,S),!Qe&&(N=q&&q.onVnodeMounted)){const Ye=m;Fe(()=>tt(N,se,Ye),S)}(m.shapeFlag&256||se&&ms(se.vnode)&&se.vnode.shapeFlag&256)&&p.a&&Fe(p.a,S),p.isMounted=!0,m=x=_=null}};p.scope.on();const M=p.effect=new wi(P);p.scope.off();const R=p.update=M.run.bind(M),L=p.job=M.runIfDirty.bind(M);L.i=p,L.id=p.uid,M.scheduler=()=>kr(L),Mt(p,!0),R()},re=(p,m,x)=>{m.component=p;const _=p.vnode.props;p.vnode=m,p.next=null,Cu(p,m.props,_,x),Ou(p,m.children,x),gt(),Xr(p),vt()},Y=(p,m,x,_,S,E,j,P,M=!1)=>{const R=p&&p.children,L=p?p.shapeFlag:0,N=m.children,{patchFlag:V,shapeFlag:q}=m;if(V>0){if(V&128){wt(R,N,x,_,S,E,j,P,M);return}else if(V&256){at(R,N,x,_,S,E,j,P,M);return}}q&8?(L&16&&Ue(R,S,E),N!==R&&u(x,N)):L&16?q&16?wt(R,N,x,_,S,E,j,P,M):Ue(R,S,E,!0):(L&8&&u(x,""),q&16&&Se(N,x,_,S,E,j,P,M))},at=(p,m,x,_,S,E,j,P,M)=>{p=p||qt,m=m||qt;const R=p.length,L=m.length,N=Math.min(R,L);let V;for(V=0;V<N;V++){const q=m[V]=M?Et(m[V]):nt(m[V]);w(p[V],q,x,null,S,E,j,P,M)}R>L?Ue(p,S,E,!0,!1,N):Se(m,x,_,S,E,j,P,M,N)},wt=(p,m,x,_,S,E,j,P,M)=>{let R=0;const L=m.length;let N=p.length-1,V=L-1;for(;R<=N&&R<=V;){const q=p[R],X=m[R]=M?Et(m[R]):nt(m[R]);if(as(q,X))w(q,X,x,null,S,E,j,P,M);else break;R++}for(;R<=N&&R<=V;){const q=p[N],X=m[V]=M?Et(m[V]):nt(m[V]);if(as(q,X))w(q,X,x,null,S,E,j,P,M);else break;N--,V--}if(R>N){if(R<=V){const q=V+1,X=q<L?m[q].el:_;for(;R<=V;)w(null,m[R]=M?Et(m[R]):nt(m[R]),x,X,S,E,j,P,M),R++}}else if(R>V)for(;R<=N;)Te(p[R],S,E,!0),R++;else{const q=R,X=R,ae=new Map;for(R=X;R<=V;R++){const De=m[R]=M?Et(m[R]):nt(m[R]);De.key!=null&&ae.set(De.key,R)}let se,je=0;const Ae=V-X+1;let Qe=!1,Ye=0;const os=new Array(Ae);for(R=0;R<Ae;R++)os[R]=0;for(R=q;R<=N;R++){const De=p[R];if(je>=Ae){Te(De,S,E,!0);continue}let Ze;if(De.key!=null)Ze=ae.get(De.key);else for(se=X;se<=V;se++)if(os[se-X]===0&&as(De,m[se])){Ze=se;break}Ze===void 0?Te(De,S,E,!0):(os[Ze-X]=R+1,Ze>=Ye?Ye=Ze:Qe=!0,w(De,m[Ze],x,null,S,E,j,P,M),je++)}const $r=Qe?ju(os):qt;for(se=$r.length-1,R=Ae-1;R>=0;R--){const De=X+R,Ze=m[De],Mr=De+1<L?m[De+1].el:_;os[R]===0?w(null,Ze,x,Mr,S,E,j,P,M):Qe&&(se<0||R!==$r[se]?Xe(Ze,x,Mr,2):se--)}}},Xe=(p,m,x,_,S=null)=>{const{el:E,type:j,transition:P,children:M,shapeFlag:R}=p;if(R&6){Xe(p.component.subTree,m,x,_);return}if(R&128){p.suspense.move(m,x,_);return}if(R&64){j.move(p,m,x,B);return}if(j===he){r(E,m,x);for(let N=0;N<M.length;N++)Xe(M[N],m,x,_);r(p.anchor,m,x);return}if(j===qs){D(p,m,x);return}if(_!==2&&R&1&&P)if(_===0)P.beforeEnter(E),r(E,m,x),Fe(()=>P.enter(E),S);else{const{leave:N,delayLeave:V,afterLeave:q}=P,X=()=>{p.ctx.isUnmounted?n(E):r(E,m,x)},ae=()=>{N(E,()=>{X(),q&&q()})};V?V(E,X,ae):ae()}else r(E,m,x)},Te=(p,m,x,_=!1,S=!1)=>{const{type:E,props:j,ref:P,children:M,dynamicChildren:R,shapeFlag:L,patchFlag:N,dirs:V,cacheIndex:q}=p;if(N===-2&&(S=!1),P!=null&&(gt(),tn(P,null,x,p,!0),vt()),q!=null&&(m.renderCache[q]=void 0),L&256){m.ctx.deactivate(p);return}const X=L&1&&V,ae=!ms(p);let se;if(ae&&(se=j&&j.onVnodeBeforeUnmount)&&tt(se,m,p),L&6)Ds(p.component,x,_);else{if(L&128){p.suspense.unmount(x,_);return}X&&$t(p,null,m,"beforeUnmount"),L&64?p.type.remove(p,m,x,B,_):R&&!R.hasOnce&&(E!==he||N>0&&N&64)?Ue(R,m,x,!1,!0):(E===he&&N&384||!S&&L&16)&&Ue(M,m,x),_&&Bt(p)}(ae&&(se=j&&j.onVnodeUnmounted)||X)&&Fe(()=>{se&&tt(se,m,p),X&&$t(p,null,m,"unmounted")},x)},Bt=p=>{const{type:m,el:x,anchor:_,transition:S}=p;if(m===he){Vt(x,_);return}if(m===qs){I(p);return}const E=()=>{n(x),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(p.shapeFlag&1&&S&&!S.persisted){const{leave:j,delayLeave:P}=S,M=()=>j(x,E);P?P(p.el,E,M):M()}else E()},Vt=(p,m)=>{let x;for(;p!==m;)x=h(p),n(p),p=x;n(m)},Ds=(p,m,x)=>{const{bum:_,scope:S,job:E,subTree:j,um:P,m:M,a:R,parent:L,slots:{__:N}}=p;no(M),no(R),_&&Ls(_),L&&z(N)&&N.forEach(V=>{L.renderCache[V]=void 0}),S.stop(),E&&(E.flags|=8,Te(j,p,m,x)),P&&Fe(P,m),Fe(()=>{p.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},Ue=(p,m,x,_=!1,S=!1,E=0)=>{for(let j=E;j<p.length;j++)Te(p[j],m,x,_,S)},k=p=>{if(p.shapeFlag&6)return k(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const m=h(p.anchor||p.el),x=m&&m[eu];return x?h(x):m};let U=!1;const F=(p,m,x)=>{p==null?m._vnode&&Te(m._vnode,null,null,!0):w(m._vnode||null,p,m,null,null,null,x),m._vnode=p,U||(U=!0,Xr(),Vi(),U=!1)},B={p:w,um:Te,m:Xe,r:Bt,mt:rs,mc:Se,pc:Y,pbc:Je,n:k,o:e};return{render:F,hydrate:void 0,createApp:Eu(F)}}function Dn({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function Mt({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Pu(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function il(e,t,s=!1){const r=e.children,n=t.children;if(z(r)&&z(n))for(let i=0;i<r.length;i++){const a=r[i];let l=n[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=n[i]=Et(n[i]),l.el=a.el),!s&&l.patchFlag!==-2&&il(a,l)),l.type===_n&&(l.el=a.el),l.type===At&&!l.el&&(l.el=a.el)}}function ju(e){const t=e.slice(),s=[0];let r,n,i,a,l;const c=e.length;for(r=0;r<c;r++){const d=e[r];if(d!==0){if(n=s[s.length-1],e[n]<d){t[r]=n,s.push(r);continue}for(i=0,a=s.length-1;i<a;)l=i+a>>1,e[s[l]]<d?i=l+1:a=l;d<e[s[i]]&&(i>0&&(t[r]=s[i-1]),s[i]=r)}}for(i=s.length,a=s[i-1];i-- >0;)s[i]=a,a=t[a];return s}function ll(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ll(t)}function no(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Du=Symbol.for("v-scx"),Fu=()=>mt(Du);function Hs(e,t,s){return al(e,t,s)}function al(e,t,s=ie){const{immediate:r,deep:n,flush:i,once:a}=s,l=Ee({},s),c=t&&r||!t&&i!=="post";let d;if(Ss){if(i==="sync"){const v=Fu();d=v.__watcherHandles||(v.__watcherHandles=[])}else if(!c){const v=()=>{};return v.stop=rt,v.resume=rt,v.pause=rt,v}}const u=_e;l.call=(v,g,w)=>it(v,u,g,w);let f=!1;i==="post"?l.scheduler=v=>{Fe(v,u&&u.suspense)}:i!=="sync"&&(f=!0,l.scheduler=(v,g)=>{g?v():kr(v)}),l.augmentJob=v=>{t&&(v.flags|=4),f&&(v.flags|=2,u&&(v.id=u.uid,v.i=u))};const h=Xc(e,t,l);return Ss&&(d?d.push(h):c&&h()),h}function Nu(e,t,s){const r=this.proxy,n=ge(e)?e.includes(".")?cl(r,e):()=>r[e]:e.bind(r,r);let i;W(t)?i=t:(i=t.handler,s=t);const a=js(this),l=al(n,i.bind(r),s);return a(),l}function cl(e,t){const s=t.split(".");return()=>{let r=e;for(let n=0;n<s.length&&r;n++)r=r[s[n]];return r}}const Iu=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ve(t)}Modifiers`]||e[`${It(t)}Modifiers`];function Uu(e,t,...s){if(e.isUnmounted)return;const r=e.vnode.props||ie;let n=s;const i=t.startsWith("update:"),a=i&&Iu(r,t.slice(7));a&&(a.trim&&(n=s.map(u=>ge(u)?u.trim():u)),a.number&&(n=s.map(Xs)));let l,c=r[l=An(t)]||r[l=An(Ve(t))];!c&&i&&(c=r[l=An(It(t))]),c&&it(c,e,6,n);const d=r[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,it(d,e,6,n)}}function ul(e,t,s=!1){const r=t.emitsCache,n=r.get(e);if(n!==void 0)return n;const i=e.emits;let a={},l=!1;if(!W(e)){const c=d=>{const u=ul(d,t,!0);u&&(l=!0,Ee(a,u))};!s&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!l?(ue(e)&&r.set(e,null),null):(z(i)?i.forEach(c=>a[c]=null):Ee(a,i),ue(e)&&r.set(e,a),a)}function wn(e,t){return!e||!pn(t)?!1:(t=t.slice(2).replace(/Once$/,""),te(e,t[0].toLowerCase()+t.slice(1))||te(e,It(t))||te(e,t))}function ro(e){const{type:t,vnode:s,proxy:r,withProxy:n,propsOptions:[i],slots:a,attrs:l,emit:c,render:d,renderCache:u,props:f,data:h,setupState:v,ctx:g,inheritAttrs:w}=e,C=en(e);let O,$;try{if(s.shapeFlag&4){const I=n||r,J=I;O=nt(d.call(J,I,u,f,v,h,g)),$=l}else{const I=t;O=nt(I.length>1?I(f,{attrs:l,slots:a,emit:c}):I(f,null)),$=t.props?l:Bu(l)}}catch(I){gs.length=0,bn(I,e,1),O=K(At)}let D=O;if($&&w!==!1){const I=Object.keys($),{shapeFlag:J}=D;I.length&&J&7&&(i&&I.some(ur)&&($=Vu($,i)),D=Qt(D,$,!1,!0))}return s.dirs&&(D=Qt(D,null,!1,!0),D.dirs=D.dirs?D.dirs.concat(s.dirs):s.dirs),s.transition&&Er(D,s.transition),O=D,en(C),O}const Bu=e=>{let t;for(const s in e)(s==="class"||s==="style"||pn(s))&&((t||(t={}))[s]=e[s]);return t},Vu=(e,t)=>{const s={};for(const r in e)(!ur(r)||!(r.slice(9)in t))&&(s[r]=e[r]);return s};function Lu(e,t,s){const{props:r,children:n,component:i}=e,{props:a,children:l,patchFlag:c}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&c>=0){if(c&1024)return!0;if(c&16)return r?oo(r,a,d):!!a;if(c&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const h=u[f];if(a[h]!==r[h]&&!wn(d,h))return!0}}}else return(n||l)&&(!l||!l.$stable)?!0:r===a?!1:r?a?oo(r,a,d):!0:!!a;return!1}function oo(e,t,s){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let n=0;n<r.length;n++){const i=r[n];if(t[i]!==e[i]&&!wn(s,i))return!0}return!1}function zu({vnode:e,parent:t},s){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=s,t=t.parent;else break}}const dl=e=>e.__isSuspense;function Hu(e,t){t&&t.pendingBranch?z(e)?t.effects.push(...e):t.effects.push(e):Zc(e)}const he=Symbol.for("v-fgt"),_n=Symbol.for("v-txt"),At=Symbol.for("v-cmt"),qs=Symbol.for("v-stc"),gs=[];let Ie=null;function T(e=!1){gs.push(Ie=e?null:[])}function qu(){gs.pop(),Ie=gs[gs.length-1]||null}let Es=1;function io(e,t=!1){Es+=e,e<0&&Ie&&t&&(Ie.hasOnce=!0)}function fl(e){return e.dynamicChildren=Es>0?Ie||qt:null,qu(),Es>0&&Ie&&Ie.push(e),e}function A(e,t,s,r,n,i){return fl(o(e,t,s,r,n,i,!0))}function Ku(e,t,s,r,n){return fl(K(e,t,s,r,n,!0))}function nn(e){return e?e.__v_isVNode===!0:!1}function as(e,t){return e.type===t.type&&e.key===t.key}const pl=({key:e})=>e??null,Ks=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?ge(e)||ke(e)||W(e)?{i:Ne,r:e,k:t,f:!!s}:e:null);function o(e,t=null,s=null,r=0,n=null,i=e===he?0:1,a=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&pl(t),ref:t&&Ks(t),scopeId:zi,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:Ne};return l?(Rr(c,s),i&128&&e.normalize(c)):s&&(c.shapeFlag|=ge(s)?8:16),Es>0&&!a&&Ie&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&Ie.push(c),c}const K=Wu;function Wu(e,t=null,s=null,r=0,n=null,i=!1){if((!e||e===hu)&&(e=At),nn(e)){const l=Qt(e,t,!0);return s&&Rr(l,s),Es>0&&!i&&Ie&&(l.shapeFlag&6?Ie[Ie.indexOf(e)]=l:Ie.push(l)),l.patchFlag=-2,l}if(rd(e)&&(e=e.__vccOpts),t){t=Ju(t);let{class:l,style:c}=t;l&&!ge(l)&&(t.class=fe(l)),ue(c)&&(wr(c)&&!z(c)&&(c=Ee({},c)),t.style=pr(c))}const a=ge(e)?1:dl(e)?128:tu(e)?64:ue(e)?4:W(e)?2:0;return o(e,t,s,r,n,a,i,!0)}function Ju(e){return e?wr(e)||el(e)?Ee({},e):e:null}function Qt(e,t,s=!1,r=!1){const{props:n,ref:i,patchFlag:a,children:l,transition:c}=e,d=t?Gu(n||{},t):n,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&pl(d),ref:t&&t.ref?s&&i?z(i)?i.concat(Ks(t)):[i,Ks(t)]:Ks(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==he?a===-1?16:a|16:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Qt(e.ssContent),ssFallback:e.ssFallback&&Qt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&Er(u,c.clone(u)),u}function H(e=" ",t=0){return K(_n,null,e,t)}function qe(e,t){const s=K(qs,null,e);return s.staticCount=t,s}function me(e="",t=!1){return t?(T(),Ku(At,null,e)):K(At,null,e)}function nt(e){return e==null||typeof e=="boolean"?K(At):z(e)?K(he,null,e.slice()):nn(e)?Et(e):K(_n,null,String(e))}function Et(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Qt(e)}function Rr(e,t){let s=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(z(t))s=16;else if(typeof t=="object")if(r&65){const n=t.default;n&&(n._c&&(n._d=!1),Rr(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!el(t)?t._ctx=Ne:n===3&&Ne&&(Ne.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else W(t)?(t={default:t,_ctx:Ne},s=32):(t=String(t),r&64?(s=16,t=[H(t)]):s=8);e.children=t,e.shapeFlag|=s}function Gu(...e){const t={};for(let s=0;s<e.length;s++){const r=e[s];for(const n in r)if(n==="class")t.class!==r.class&&(t.class=fe([t.class,r.class]));else if(n==="style")t.style=pr([t.style,r.style]);else if(pn(n)){const i=t[n],a=r[n];a&&i!==a&&!(z(i)&&i.includes(a))&&(t[n]=i?[].concat(i,a):a)}else n!==""&&(t[n]=r[n])}return t}function tt(e,t,s,r=null){it(e,t,7,[s,r])}const Xu=Qi();let Qu=0;function Yu(e,t,s){const r=e.type,n=(t?t.appContext:e.appContext)||Xu,i={uid:Qu++,vnode:e,type:r,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new _c(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:sl(r,n),emitsOptions:ul(r,n),emit:null,emitted:null,propsDefaults:ie,inheritAttrs:r.inheritAttrs,ctx:ie,data:ie,props:ie,attrs:ie,slots:ie,refs:ie,setupState:ie,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Uu.bind(null,i),e.ce&&e.ce(i),i}let _e=null,rn,tr;{const e=gn(),t=(s,r)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(r),i=>{n.length>1?n.forEach(a=>a(i)):n[0](i)}};rn=t("__VUE_INSTANCE_SETTERS__",s=>_e=s),tr=t("__VUE_SSR_SETTERS__",s=>Ss=s)}const js=e=>{const t=_e;return rn(e),e.scope.on(),()=>{e.scope.off(),rn(t)}},lo=()=>{_e&&_e.scope.off(),rn(null)};function ml(e){return e.vnode.shapeFlag&4}let Ss=!1;function Zu(e,t=!1,s=!1){t&&tr(t);const{props:r,children:n}=e.vnode,i=ml(e);Su(e,r,i,t),Au(e,n,s||t);const a=i?ed(e,t):void 0;return t&&tr(!1),a}function ed(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,vu);const{setup:r}=s;if(r){gt();const n=e.setupContext=r.length>1?sd(e):null,i=js(e),a=Ps(r,e,0,[e.props,n]),l=mi(a);if(vt(),i(),(l||e.sp)&&!ms(e)&&qi(e),l){if(a.then(lo,lo),t)return a.then(c=>{ao(e,c)}).catch(c=>{bn(c,e,0)});e.asyncDep=a}else ao(e,a)}else hl(e)}function ao(e,t,s){W(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ue(t)&&(e.setupState=Ii(t)),hl(e)}function hl(e,t,s){const r=e.type;e.render||(e.render=r.render||rt);{const n=js(e);gt();try{xu(e)}finally{vt(),n()}}}const td={get(e,t){return ye(e,"get",""),e[t]}};function sd(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,td),slots:e.slots,emit:e.emit,expose:t}}function kn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Ii(Lc(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in hs)return hs[s](e)},has(t,s){return s in t||s in hs}})):e.proxy}function nd(e,t=!0){return W(e)?e.displayName||e.name:e.name||t&&e.__name}function rd(e){return W(e)&&"__vccOpts"in e}const ze=(e,t)=>Jc(e,t,Ss);function gl(e,t,s){const r=arguments.length;return r===2?ue(t)&&!z(t)?nn(t)?K(e,null,[t]):K(e,t):K(e,null,t):(r>3?s=Array.prototype.slice.call(arguments,2):r===3&&nn(s)&&(s=[s]),K(e,t,s))}const od="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let sr;const co=typeof window<"u"&&window.trustedTypes;if(co)try{sr=co.createPolicy("vue",{createHTML:e=>e})}catch{}const vl=sr?e=>sr.createHTML(e):e=>e,id="http://www.w3.org/2000/svg",ld="http://www.w3.org/1998/Math/MathML",dt=typeof document<"u"?document:null,uo=dt&&dt.createElement("template"),ad={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,r)=>{const n=t==="svg"?dt.createElementNS(id,e):t==="mathml"?dt.createElementNS(ld,e):s?dt.createElement(e,{is:s}):dt.createElement(e);return e==="select"&&r&&r.multiple!=null&&n.setAttribute("multiple",r.multiple),n},createText:e=>dt.createTextNode(e),createComment:e=>dt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>dt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,r,n,i){const a=s?s.previousSibling:t.lastChild;if(n&&(n===i||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===i||!(n=n.nextSibling)););else{uo.innerHTML=vl(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const l=uo.content;if(r==="svg"||r==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,s)}return[a?a.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},cd=Symbol("_vtc");function ud(e,t,s){const r=e[cd];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const fo=Symbol("_vod"),dd=Symbol("_vsh"),fd=Symbol(""),pd=/(^|;)\s*display\s*:/;function md(e,t,s){const r=e.style,n=ge(s);let i=!1;if(s&&!n){if(t)if(ge(t))for(const a of t.split(";")){const l=a.slice(0,a.indexOf(":")).trim();s[l]==null&&Ws(r,l,"")}else for(const a in t)s[a]==null&&Ws(r,a,"");for(const a in s)a==="display"&&(i=!0),Ws(r,a,s[a])}else if(n){if(t!==s){const a=r[fd];a&&(s+=";"+a),r.cssText=s,i=pd.test(s)}}else t&&e.removeAttribute("style");fo in e&&(e[fo]=i?r.display:"",e[dd]&&(r.display="none"))}const po=/\s*!important$/;function Ws(e,t,s){if(z(s))s.forEach(r=>Ws(e,t,r));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const r=hd(e,t);po.test(s)?e.setProperty(It(r),s.replace(po,""),"important"):e[r]=s}}const mo=["Webkit","Moz","ms"],Fn={};function hd(e,t){const s=Fn[t];if(s)return s;let r=Ve(t);if(r!=="filter"&&r in e)return Fn[t]=r;r=hn(r);for(let n=0;n<mo.length;n++){const i=mo[n]+r;if(i in e)return Fn[t]=i}return t}const ho="http://www.w3.org/1999/xlink";function go(e,t,s,r,n,i=yc(t)){r&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(ho,t.slice(6,t.length)):e.setAttributeNS(ho,t,s):s==null||i&&!xi(s)?e.removeAttribute(t):e.setAttribute(t,i?"":ot(s)?String(s):s)}function vo(e,t,s,r,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?vl(s):s);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,c=s==null?e.type==="checkbox"?"on":"":String(s);(l!==c||!("_value"in e))&&(e.value=c),s==null&&e.removeAttribute(t),e._value=s;return}let a=!1;if(s===""||s==null){const l=typeof e[t];l==="boolean"?s=xi(s):s==null&&l==="string"?(s="",a=!0):l==="number"&&(s=0,a=!0)}try{e[t]=s}catch{}a&&e.removeAttribute(n||t)}function Ct(e,t,s,r){e.addEventListener(t,s,r)}function gd(e,t,s,r){e.removeEventListener(t,s,r)}const xo=Symbol("_vei");function vd(e,t,s,r,n=null){const i=e[xo]||(e[xo]={}),a=i[t];if(r&&a)a.value=r;else{const[l,c]=xd(t);if(r){const d=i[t]=wd(r,n);Ct(e,l,d,c)}else a&&(gd(e,l,a,c),i[t]=void 0)}}const bo=/(?:Once|Passive|Capture)$/;function xd(e){let t;if(bo.test(e)){t={};let r;for(;r=e.match(bo);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):It(e.slice(2)),t]}let Nn=0;const bd=Promise.resolve(),yd=()=>Nn||(bd.then(()=>Nn=0),Nn=Date.now());function wd(e,t){const s=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=s.attached)return;it(_d(r,s.value),t,5,[r])};return s.value=e,s.attached=yd(),s}function _d(e,t){if(z(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(r=>n=>!n._stopped&&r&&r(n))}else return t}const yo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,kd=(e,t,s,r,n,i)=>{const a=n==="svg";t==="class"?ud(e,r,a):t==="style"?md(e,s,r):pn(t)?ur(t)||vd(e,t,s,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Ed(e,t,r,a))?(vo(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&go(e,t,r,a,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ge(r))?vo(e,Ve(t),r,i,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),go(e,t,r,a))};function Ed(e,t,s,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&yo(t)&&W(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return yo(t)&&ge(s)?!1:t in e}const Yt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return z(t)?s=>Ls(t,s):t};function Sd(e){e.target.composing=!0}function wo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ht=Symbol("_assign"),be={created(e,{modifiers:{lazy:t,trim:s,number:r}},n){e[ht]=Yt(n);const i=r||n.props&&n.props.type==="number";Ct(e,t?"change":"input",a=>{if(a.target.composing)return;let l=e.value;s&&(l=l.trim()),i&&(l=Xs(l)),e[ht](l)}),s&&Ct(e,"change",()=>{e.value=e.value.trim()}),t||(Ct(e,"compositionstart",Sd),Ct(e,"compositionend",wo),Ct(e,"change",wo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:r,trim:n,number:i}},a){if(e[ht]=Yt(a),e.composing)return;const l=(i||e.type==="number")&&!/^0\d/.test(e.value)?Xs(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(r&&t===s||n&&e.value.trim()===c)||(e.value=c))}},xl={deep:!0,created(e,t,s){e[ht]=Yt(s),Ct(e,"change",()=>{const r=e._modelValue,n=Cs(e),i=e.checked,a=e[ht];if(z(r)){const l=mr(r,n),c=l!==-1;if(i&&!c)a(r.concat(n));else if(!i&&c){const d=[...r];d.splice(l,1),a(d)}}else if(ns(r)){const l=new Set(r);i?l.add(n):l.delete(n),a(l)}else a(bl(e,i))})},mounted:_o,beforeUpdate(e,t,s){e[ht]=Yt(s),_o(e,t,s)}};function _o(e,{value:t,oldValue:s},r){e._modelValue=t;let n;if(z(t))n=mr(t,r.props.value)>-1;else if(ns(t))n=t.has(r.props.value);else{if(t===s)return;n=Ms(t,bl(e,!0))}e.checked!==n&&(e.checked=n)}const vs={deep:!0,created(e,{value:t,modifiers:{number:s}},r){const n=ns(t);Ct(e,"change",()=>{const i=Array.prototype.filter.call(e.options,a=>a.selected).map(a=>s?Xs(Cs(a)):Cs(a));e[ht](e.multiple?n?new Set(i):i:i[0]),e._assigning=!0,_r(()=>{e._assigning=!1})}),e[ht]=Yt(r)},mounted(e,{value:t}){ko(e,t)},beforeUpdate(e,t,s){e[ht]=Yt(s)},updated(e,{value:t}){e._assigning||ko(e,t)}};function ko(e,t){const s=e.multiple,r=z(t);if(!(s&&!r&&!ns(t))){for(let n=0,i=e.options.length;n<i;n++){const a=e.options[n],l=Cs(a);if(s)if(r){const c=typeof l;c==="string"||c==="number"?a.selected=t.some(d=>String(d)===String(l)):a.selected=mr(t,l)>-1}else a.selected=t.has(l);else if(Ms(Cs(a),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Cs(e){return"_value"in e?e._value:e.value}function bl(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const Cd=["ctrl","shift","alt","meta"],Rd={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Cd.some(s=>e[`${s}Key`]&&!t.includes(s))},En=(e,t)=>{const s=e._withMods||(e._withMods={}),r=t.join(".");return s[r]||(s[r]=(n,...i)=>{for(let a=0;a<t.length;a++){const l=Rd[t[a]];if(l&&l(n,t))return}return e(n,...i)})},Td=Ee({patchProp:kd},ad);let Eo;function Ad(){return Eo||(Eo=$u(Td))}const Od=(...e)=>{const t=Ad().createApp(...e),{mount:s}=t;return t.mount=r=>{const n=Md(r);if(!n)return;const i=t._component;!W(i)&&!i.render&&!i.template&&(i.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const a=s(n,!1,$d(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),a},t};function $d(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Md(e){return ge(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Ht=typeof document<"u";function yl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Pd(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&yl(e.default)}const Z=Object.assign;function In(e,t){const s={};for(const r in t){const n=t[r];s[r]=Ke(n)?n.map(e):e(n)}return s}const xs=()=>{},Ke=Array.isArray,wl=/#/g,jd=/&/g,Dd=/\//g,Fd=/=/g,Nd=/\?/g,_l=/\+/g,Id=/%5B/g,Ud=/%5D/g,kl=/%5E/g,Bd=/%60/g,El=/%7B/g,Vd=/%7C/g,Sl=/%7D/g,Ld=/%20/g;function Tr(e){return encodeURI(""+e).replace(Vd,"|").replace(Id,"[").replace(Ud,"]")}function zd(e){return Tr(e).replace(El,"{").replace(Sl,"}").replace(kl,"^")}function nr(e){return Tr(e).replace(_l,"%2B").replace(Ld,"+").replace(wl,"%23").replace(jd,"%26").replace(Bd,"`").replace(El,"{").replace(Sl,"}").replace(kl,"^")}function Hd(e){return nr(e).replace(Fd,"%3D")}function qd(e){return Tr(e).replace(wl,"%23").replace(Nd,"%3F")}function Kd(e){return e==null?"":qd(e).replace(Dd,"%2F")}function Rs(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Wd=/\/$/,Jd=e=>e.replace(Wd,"");function Un(e,t,s="/"){let r,n={},i="",a="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(r=t.slice(0,c),i=t.slice(c+1,l>-1?l:t.length),n=e(i)),l>-1&&(r=r||t.slice(0,l),a=t.slice(l,t.length)),r=Yd(r??t,s),{fullPath:r+(i&&"?")+i+a,path:r,query:n,hash:Rs(a)}}function Gd(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function So(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Xd(e,t,s){const r=t.matched.length-1,n=s.matched.length-1;return r>-1&&r===n&&Zt(t.matched[r],s.matched[n])&&Cl(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Zt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Cl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!Qd(e[s],t[s]))return!1;return!0}function Qd(e,t){return Ke(e)?Co(e,t):Ke(t)?Co(t,e):e===t}function Co(e,t){return Ke(t)?e.length===t.length&&e.every((s,r)=>s===t[r]):e.length===1&&e[0]===t}function Yd(e,t){if(e.startsWith("/"))return e;if(!e)return t;const s=t.split("/"),r=e.split("/"),n=r[r.length-1];(n===".."||n===".")&&r.push("");let i=s.length-1,a,l;for(a=0;a<r.length;a++)if(l=r[a],l!==".")if(l==="..")i>1&&i--;else break;return s.slice(0,i).join("/")+"/"+r.slice(a).join("/")}const _t={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ts;(function(e){e.pop="pop",e.push="push"})(Ts||(Ts={}));var bs;(function(e){e.back="back",e.forward="forward",e.unknown=""})(bs||(bs={}));function Zd(e){if(!e)if(Ht){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Jd(e)}const ef=/^[^#]+#/;function tf(e,t){return e.replace(ef,"#")+t}function sf(e,t){const s=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-s.left-(t.left||0),top:r.top-s.top-(t.top||0)}}const Sn=()=>({left:window.scrollX,top:window.scrollY});function nf(e){let t;if("el"in e){const s=e.el,r=typeof s=="string"&&s.startsWith("#"),n=typeof s=="string"?r?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n)return;t=sf(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ro(e,t){return(history.state?history.state.position-t:-1)+e}const rr=new Map;function rf(e,t){rr.set(e,t)}function of(e){const t=rr.get(e);return rr.delete(e),t}let lf=()=>location.protocol+"//"+location.host;function Rl(e,t){const{pathname:s,search:r,hash:n}=t,i=e.indexOf("#");if(i>-1){let l=n.includes(e.slice(i))?e.slice(i).length:1,c=n.slice(l);return c[0]!=="/"&&(c="/"+c),So(c,"")}return So(s,e)+r+n}function af(e,t,s,r){let n=[],i=[],a=null;const l=({state:h})=>{const v=Rl(e,location),g=s.value,w=t.value;let C=0;if(h){if(s.value=v,t.value=h,a&&a===g){a=null;return}C=w?h.position-w.position:0}else r(v);n.forEach(O=>{O(s.value,g,{delta:C,type:Ts.pop,direction:C?C>0?bs.forward:bs.back:bs.unknown})})};function c(){a=s.value}function d(h){n.push(h);const v=()=>{const g=n.indexOf(h);g>-1&&n.splice(g,1)};return i.push(v),v}function u(){const{history:h}=window;h.state&&h.replaceState(Z({},h.state,{scroll:Sn()}),"")}function f(){for(const h of i)h();i=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:d,destroy:f}}function To(e,t,s,r=!1,n=!1){return{back:e,current:t,forward:s,replaced:r,position:window.history.length,scroll:n?Sn():null}}function cf(e){const{history:t,location:s}=window,r={value:Rl(e,s)},n={value:t.state};n.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(c,d,u){const f=e.indexOf("#"),h=f>-1?(s.host&&document.querySelector("base")?e:e.slice(f))+c:lf()+e+c;try{t[u?"replaceState":"pushState"](d,"",h),n.value=d}catch(v){console.error(v),s[u?"replace":"assign"](h)}}function a(c,d){const u=Z({},t.state,To(n.value.back,c,n.value.forward,!0),d,{position:n.value.position});i(c,u,!0),r.value=c}function l(c,d){const u=Z({},n.value,t.state,{forward:c,scroll:Sn()});i(u.current,u,!0);const f=Z({},To(r.value,c,null),{position:u.position+1},d);i(c,f,!1),r.value=c}return{location:r,state:n,push:l,replace:a}}function uf(e){e=Zd(e);const t=cf(e),s=af(e,t.state,t.location,t.replace);function r(i,a=!0){a||s.pauseListeners(),history.go(i)}const n=Z({location:"",base:e,go:r,createHref:tf.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function df(e){return typeof e=="string"||e&&typeof e=="object"}function Tl(e){return typeof e=="string"||typeof e=="symbol"}const Al=Symbol("");var Ao;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ao||(Ao={}));function es(e,t){return Z(new Error,{type:e,[Al]:!0},t)}function ut(e,t){return e instanceof Error&&Al in e&&(t==null||!!(e.type&t))}const Oo="[^/]+?",ff={sensitive:!1,strict:!1,start:!0,end:!0},pf=/[.+*?^${}()[\]/\\]/g;function mf(e,t){const s=Z({},ff,t),r=[];let n=s.start?"^":"";const i=[];for(const d of e){const u=d.length?[]:[90];s.strict&&!d.length&&(n+="/");for(let f=0;f<d.length;f++){const h=d[f];let v=40+(s.sensitive?.25:0);if(h.type===0)f||(n+="/"),n+=h.value.replace(pf,"\\$&"),v+=40;else if(h.type===1){const{value:g,repeatable:w,optional:C,regexp:O}=h;i.push({name:g,repeatable:w,optional:C});const $=O||Oo;if($!==Oo){v+=10;try{new RegExp(`(${$})`)}catch(I){throw new Error(`Invalid custom RegExp for param "${g}" (${$}): `+I.message)}}let D=w?`((?:${$})(?:/(?:${$}))*)`:`(${$})`;f||(D=C&&d.length<2?`(?:/${D})`:"/"+D),C&&(D+="?"),n+=D,v+=20,C&&(v+=-8),w&&(v+=-20),$===".*"&&(v+=-50)}u.push(v)}r.push(u)}if(s.strict&&s.end){const d=r.length-1;r[d][r[d].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const a=new RegExp(n,s.sensitive?"":"i");function l(d){const u=d.match(a),f={};if(!u)return null;for(let h=1;h<u.length;h++){const v=u[h]||"",g=i[h-1];f[g.name]=v&&g.repeatable?v.split("/"):v}return f}function c(d){let u="",f=!1;for(const h of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const v of h)if(v.type===0)u+=v.value;else if(v.type===1){const{value:g,repeatable:w,optional:C}=v,O=g in d?d[g]:"";if(Ke(O)&&!w)throw new Error(`Provided param "${g}" is an array but it is not repeatable (* or + modifiers)`);const $=Ke(O)?O.join("/"):O;if(!$)if(C)h.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${g}"`);u+=$}}return u||"/"}return{re:a,score:r,keys:i,parse:l,stringify:c}}function hf(e,t){let s=0;for(;s<e.length&&s<t.length;){const r=t[s]-e[s];if(r)return r;s++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Ol(e,t){let s=0;const r=e.score,n=t.score;for(;s<r.length&&s<n.length;){const i=hf(r[s],n[s]);if(i)return i;s++}if(Math.abs(n.length-r.length)===1){if($o(r))return 1;if($o(n))return-1}return n.length-r.length}function $o(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const gf={type:0,value:""},vf=/[a-zA-Z0-9_]/;function xf(e){if(!e)return[[]];if(e==="/")return[[gf]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(v){throw new Error(`ERR (${s})/"${d}": ${v}`)}let s=0,r=s;const n=[];let i;function a(){i&&n.push(i),i=[]}let l=0,c,d="",u="";function f(){d&&(s===0?i.push({type:0,value:d}):s===1||s===2||s===3?(i.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:d,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),d="")}function h(){d+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&s!==2){r=s,s=4;continue}switch(s){case 0:c==="/"?(d&&f(),a()):c===":"?(f(),s=1):h();break;case 4:h(),s=r;break;case 1:c==="("?s=2:vf.test(c)?h():(f(),s=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:s=3:u+=c;break;case 3:f(),s=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,u="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${d}"`),f(),a(),n}function bf(e,t,s){const r=mf(xf(e.path),s),n=Z(r,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function yf(e,t){const s=[],r=new Map;t=Do({strict:!1,end:!0,sensitive:!1},t);function n(f){return r.get(f)}function i(f,h,v){const g=!v,w=Po(f);w.aliasOf=v&&v.record;const C=Do(t,f),O=[w];if("alias"in f){const I=typeof f.alias=="string"?[f.alias]:f.alias;for(const J of I)O.push(Po(Z({},w,{components:v?v.record.components:w.components,path:J,aliasOf:v?v.record:w})))}let $,D;for(const I of O){const{path:J}=I;if(h&&J[0]!=="/"){const le=h.record.path,Q=le[le.length-1]==="/"?"":"/";I.path=h.record.path+(J&&Q+J)}if($=bf(I,h,C),v?v.alias.push($):(D=D||$,D!==$&&D.alias.push($),g&&f.name&&!jo($)&&a(f.name)),$l($)&&c($),w.children){const le=w.children;for(let Q=0;Q<le.length;Q++)i(le[Q],$,v&&v.children[Q])}v=v||$}return D?()=>{a(D)}:xs}function a(f){if(Tl(f)){const h=r.get(f);h&&(r.delete(f),s.splice(s.indexOf(h),1),h.children.forEach(a),h.alias.forEach(a))}else{const h=s.indexOf(f);h>-1&&(s.splice(h,1),f.record.name&&r.delete(f.record.name),f.children.forEach(a),f.alias.forEach(a))}}function l(){return s}function c(f){const h=kf(f,s);s.splice(h,0,f),f.record.name&&!jo(f)&&r.set(f.record.name,f)}function d(f,h){let v,g={},w,C;if("name"in f&&f.name){if(v=r.get(f.name),!v)throw es(1,{location:f});C=v.record.name,g=Z(Mo(h.params,v.keys.filter(D=>!D.optional).concat(v.parent?v.parent.keys.filter(D=>D.optional):[]).map(D=>D.name)),f.params&&Mo(f.params,v.keys.map(D=>D.name))),w=v.stringify(g)}else if(f.path!=null)w=f.path,v=s.find(D=>D.re.test(w)),v&&(g=v.parse(w),C=v.record.name);else{if(v=h.name?r.get(h.name):s.find(D=>D.re.test(h.path)),!v)throw es(1,{location:f,currentLocation:h});C=v.record.name,g=Z({},h.params,f.params),w=v.stringify(g)}const O=[];let $=v;for(;$;)O.unshift($.record),$=$.parent;return{name:C,path:w,params:g,matched:O,meta:_f(O)}}e.forEach(f=>i(f));function u(){s.length=0,r.clear()}return{addRoute:i,resolve:d,removeRoute:a,clearRoutes:u,getRoutes:l,getRecordMatcher:n}}function Mo(e,t){const s={};for(const r of t)r in e&&(s[r]=e[r]);return s}function Po(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:wf(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function wf(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const r in e.components)t[r]=typeof s=="object"?s[r]:s;return t}function jo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function _f(e){return e.reduce((t,s)=>Z(t,s.meta),{})}function Do(e,t){const s={};for(const r in e)s[r]=r in t?t[r]:e[r];return s}function kf(e,t){let s=0,r=t.length;for(;s!==r;){const i=s+r>>1;Ol(e,t[i])<0?r=i:s=i+1}const n=Ef(e);return n&&(r=t.lastIndexOf(n,r-1)),r}function Ef(e){let t=e;for(;t=t.parent;)if($l(t)&&Ol(e,t)===0)return t}function $l({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Sf(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<r.length;++n){const i=r[n].replace(_l," "),a=i.indexOf("="),l=Rs(a<0?i:i.slice(0,a)),c=a<0?null:Rs(i.slice(a+1));if(l in t){let d=t[l];Ke(d)||(d=t[l]=[d]),d.push(c)}else t[l]=c}return t}function Fo(e){let t="";for(let s in e){const r=e[s];if(s=Hd(s),r==null){r!==void 0&&(t+=(t.length?"&":"")+s);continue}(Ke(r)?r.map(i=>i&&nr(i)):[r&&nr(r)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+s,i!=null&&(t+="="+i))})}return t}function Cf(e){const t={};for(const s in e){const r=e[s];r!==void 0&&(t[s]=Ke(r)?r.map(n=>n==null?null:""+n):r==null?r:""+r)}return t}const Rf=Symbol(""),No=Symbol(""),Ar=Symbol(""),Ml=Symbol(""),or=Symbol("");function cs(){let e=[];function t(r){return e.push(r),()=>{const n=e.indexOf(r);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function St(e,t,s,r,n,i=a=>a()){const a=r&&(r.enterCallbacks[n]=r.enterCallbacks[n]||[]);return()=>new Promise((l,c)=>{const d=h=>{h===!1?c(es(4,{from:s,to:t})):h instanceof Error?c(h):df(h)?c(es(2,{from:t,to:h})):(a&&r.enterCallbacks[n]===a&&typeof h=="function"&&a.push(h),l())},u=i(()=>e.call(r&&r.instances[n],t,s,d));let f=Promise.resolve(u);e.length<3&&(f=f.then(d)),f.catch(h=>c(h))})}function Bn(e,t,s,r,n=i=>i()){const i=[];for(const a of e)for(const l in a.components){let c=a.components[l];if(!(t!=="beforeRouteEnter"&&!a.instances[l]))if(yl(c)){const u=(c.__vccOpts||c)[t];u&&i.push(St(u,s,r,a,l,n))}else{let d=c();i.push(()=>d.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${l}" at "${a.path}"`);const f=Pd(u)?u.default:u;a.mods[l]=u,a.components[l]=f;const v=(f.__vccOpts||f)[t];return v&&St(v,s,r,a,l,n)()}))}}return i}function Io(e){const t=mt(Ar),s=mt(Ml),r=ze(()=>{const c=Jt(e.to);return t.resolve(c)}),n=ze(()=>{const{matched:c}=r.value,{length:d}=c,u=c[d-1],f=s.matched;if(!u||!f.length)return-1;const h=f.findIndex(Zt.bind(null,u));if(h>-1)return h;const v=Uo(c[d-2]);return d>1&&Uo(u)===v&&f[f.length-1].path!==v?f.findIndex(Zt.bind(null,c[d-2])):h}),i=ze(()=>n.value>-1&&Mf(s.params,r.value.params)),a=ze(()=>n.value>-1&&n.value===s.matched.length-1&&Cl(s.params,r.value.params));function l(c={}){if($f(c)){const d=t[Jt(e.replace)?"replace":"push"](Jt(e.to)).catch(xs);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:r,href:ze(()=>r.value.href),isActive:i,isExactActive:a,navigate:l}}function Tf(e){return e.length===1?e[0]:e}const Af=Hi({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Io,setup(e,{slots:t}){const s=xn(Io(e)),{options:r}=mt(Ar),n=ze(()=>({[Bo(e.activeClass,r.linkActiveClass,"router-link-active")]:s.isActive,[Bo(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const i=t.default&&Tf(t.default(s));return e.custom?i:gl("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},i)}}}),Of=Af;function $f(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Mf(e,t){for(const s in t){const r=t[s],n=e[s];if(typeof r=="string"){if(r!==n)return!1}else if(!Ke(n)||n.length!==r.length||r.some((i,a)=>i!==n[a]))return!1}return!0}function Uo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Bo=(e,t,s)=>e??t??s,Pf=Hi({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){const r=mt(or),n=ze(()=>e.route||r.value),i=mt(No,0),a=ze(()=>{let d=Jt(i);const{matched:u}=n.value;let f;for(;(f=u[d])&&!f.components;)d++;return d}),l=ze(()=>n.value.matched[a.value]);zs(No,ze(()=>a.value+1)),zs(Rf,l),zs(or,n);const c=zc();return Hs(()=>[c.value,l.value,e.name],([d,u,f],[h,v,g])=>{u&&(u.instances[f]=d,v&&v!==u&&d&&d===h&&(u.leaveGuards.size||(u.leaveGuards=v.leaveGuards),u.updateGuards.size||(u.updateGuards=v.updateGuards))),d&&u&&(!v||!Zt(u,v)||!h)&&(u.enterCallbacks[f]||[]).forEach(w=>w(d))},{flush:"post"}),()=>{const d=n.value,u=e.name,f=l.value,h=f&&f.components[u];if(!h)return Vo(s.default,{Component:h,route:d});const v=f.props[u],g=v?v===!0?d.params:typeof v=="function"?v(d):v:null,C=gl(h,Z({},g,t,{onVnodeUnmounted:O=>{O.component.isUnmounted&&(f.instances[u]=null)},ref:c}));return Vo(s.default,{Component:C,route:d})||C}}});function Vo(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const jf=Pf;function Df(e){const t=yf(e.routes,e),s=e.parseQuery||Sf,r=e.stringifyQuery||Fo,n=e.history,i=cs(),a=cs(),l=cs(),c=Hc(_t);let d=_t;Ht&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=In.bind(null,k=>""+k),f=In.bind(null,Kd),h=In.bind(null,Rs);function v(k,U){let F,B;return Tl(k)?(F=t.getRecordMatcher(k),B=U):B=k,t.addRoute(B,F)}function g(k){const U=t.getRecordMatcher(k);U&&t.removeRoute(U)}function w(){return t.getRoutes().map(k=>k.record)}function C(k){return!!t.getRecordMatcher(k)}function O(k,U){if(U=Z({},U||c.value),typeof k=="string"){const x=Un(s,k,U.path),_=t.resolve({path:x.path},U),S=n.createHref(x.fullPath);return Z(x,_,{params:h(_.params),hash:Rs(x.hash),redirectedFrom:void 0,href:S})}let F;if(k.path!=null)F=Z({},k,{path:Un(s,k.path,U.path).path});else{const x=Z({},k.params);for(const _ in x)x[_]==null&&delete x[_];F=Z({},k,{params:f(x)}),U.params=f(U.params)}const B=t.resolve(F,U),oe=k.hash||"";B.params=u(h(B.params));const p=Gd(r,Z({},k,{hash:zd(oe),path:B.path})),m=n.createHref(p);return Z({fullPath:p,hash:oe,query:r===Fo?Cf(k.query):k.query||{}},B,{redirectedFrom:void 0,href:m})}function $(k){return typeof k=="string"?Un(s,k,c.value.path):Z({},k)}function D(k,U){if(d!==k)return es(8,{from:U,to:k})}function I(k){return Q(k)}function J(k){return I(Z($(k),{replace:!0}))}function le(k){const U=k.matched[k.matched.length-1];if(U&&U.redirect){const{redirect:F}=U;let B=typeof F=="function"?F(k):F;return typeof B=="string"&&(B=B.includes("?")||B.includes("#")?B=$(B):{path:B},B.params={}),Z({query:k.query,hash:k.hash,params:B.path!=null?{}:k.params},B)}}function Q(k,U){const F=d=O(k),B=c.value,oe=k.state,p=k.force,m=k.replace===!0,x=le(F);if(x)return Q(Z($(x),{state:typeof x=="object"?Z({},oe,x.state):oe,force:p,replace:m}),U||F);const _=F;_.redirectedFrom=U;let S;return!p&&Xd(r,B,F)&&(S=es(16,{to:_,from:B}),Xe(B,B,!0,!1)),(S?Promise.resolve(S):Je(_,B)).catch(E=>ut(E)?ut(E,2)?E:wt(E):Y(E,_,B)).then(E=>{if(E){if(ut(E,2))return Q(Z({replace:m},$(E.to),{state:typeof E.to=="object"?Z({},oe,E.to.state):oe,force:p}),U||_)}else E=Ot(_,B,!0,m,oe);return yt(_,B,E),E})}function Se(k,U){const F=D(k,U);return F?Promise.reject(F):Promise.resolve()}function Le(k){const U=Vt.values().next().value;return U&&typeof U.runWithContext=="function"?U.runWithContext(k):k()}function Je(k,U){let F;const[B,oe,p]=Ff(k,U);F=Bn(B.reverse(),"beforeRouteLeave",k,U);for(const x of B)x.leaveGuards.forEach(_=>{F.push(St(_,k,U))});const m=Se.bind(null,k,U);return F.push(m),Ue(F).then(()=>{F=[];for(const x of i.list())F.push(St(x,k,U));return F.push(m),Ue(F)}).then(()=>{F=Bn(oe,"beforeRouteUpdate",k,U);for(const x of oe)x.updateGuards.forEach(_=>{F.push(St(_,k,U))});return F.push(m),Ue(F)}).then(()=>{F=[];for(const x of p)if(x.beforeEnter)if(Ke(x.beforeEnter))for(const _ of x.beforeEnter)F.push(St(_,k,U));else F.push(St(x.beforeEnter,k,U));return F.push(m),Ue(F)}).then(()=>(k.matched.forEach(x=>x.enterCallbacks={}),F=Bn(p,"beforeRouteEnter",k,U,Le),F.push(m),Ue(F))).then(()=>{F=[];for(const x of a.list())F.push(St(x,k,U));return F.push(m),Ue(F)}).catch(x=>ut(x,8)?x:Promise.reject(x))}function yt(k,U,F){l.list().forEach(B=>Le(()=>B(k,U,F)))}function Ot(k,U,F,B,oe){const p=D(k,U);if(p)return p;const m=U===_t,x=Ht?history.state:{};F&&(B||m?n.replace(k.fullPath,Z({scroll:m&&x&&x.scroll},oe)):n.push(k.fullPath,oe)),c.value=k,Xe(k,U,F,m),wt()}let Ge;function rs(){Ge||(Ge=n.listen((k,U,F)=>{if(!Ds.listening)return;const B=O(k),oe=le(B);if(oe){Q(Z(oe,{replace:!0,force:!0}),B).catch(xs);return}d=B;const p=c.value;Ht&&rf(Ro(p.fullPath,F.delta),Sn()),Je(B,p).catch(m=>ut(m,12)?m:ut(m,2)?(Q(Z($(m.to),{force:!0}),B).then(x=>{ut(x,20)&&!F.delta&&F.type===Ts.pop&&n.go(-1,!1)}).catch(xs),Promise.reject()):(F.delta&&n.go(-F.delta,!1),Y(m,B,p))).then(m=>{m=m||Ot(B,p,!1),m&&(F.delta&&!ut(m,8)?n.go(-F.delta,!1):F.type===Ts.pop&&ut(m,20)&&n.go(-1,!1)),yt(B,p,m)}).catch(xs)}))}let Ut=cs(),ve=cs(),re;function Y(k,U,F){wt(k);const B=ve.list();return B.length?B.forEach(oe=>oe(k,U,F)):console.error(k),Promise.reject(k)}function at(){return re&&c.value!==_t?Promise.resolve():new Promise((k,U)=>{Ut.add([k,U])})}function wt(k){return re||(re=!k,rs(),Ut.list().forEach(([U,F])=>k?F(k):U()),Ut.reset()),k}function Xe(k,U,F,B){const{scrollBehavior:oe}=e;if(!Ht||!oe)return Promise.resolve();const p=!F&&of(Ro(k.fullPath,0))||(B||!F)&&history.state&&history.state.scroll||null;return _r().then(()=>oe(k,U,p)).then(m=>m&&nf(m)).catch(m=>Y(m,k,U))}const Te=k=>n.go(k);let Bt;const Vt=new Set,Ds={currentRoute:c,listening:!0,addRoute:v,removeRoute:g,clearRoutes:t.clearRoutes,hasRoute:C,getRoutes:w,resolve:O,options:e,push:I,replace:J,go:Te,back:()=>Te(-1),forward:()=>Te(1),beforeEach:i.add,beforeResolve:a.add,afterEach:l.add,onError:ve.add,isReady:at,install(k){const U=this;k.component("RouterLink",Of),k.component("RouterView",jf),k.config.globalProperties.$router=U,Object.defineProperty(k.config.globalProperties,"$route",{enumerable:!0,get:()=>Jt(c)}),Ht&&!Bt&&c.value===_t&&(Bt=!0,I(n.location).catch(oe=>{}));const F={};for(const oe in _t)Object.defineProperty(F,oe,{get:()=>c.value[oe],enumerable:!0});k.provide(Ar,U),k.provide(Ml,Di(F)),k.provide(or,c);const B=k.unmount;Vt.add(k),k.unmount=function(){Vt.delete(k),Vt.size<1&&(d=_t,Ge&&Ge(),Ge=null,c.value=_t,Bt=!1,re=!1),B()}}};function Ue(k){return k.reduce((U,F)=>U.then(()=>Le(F)),Promise.resolve())}return Ds}function Ff(e,t){const s=[],r=[],n=[],i=Math.max(t.matched.length,e.matched.length);for(let a=0;a<i;a++){const l=t.matched[a];l&&(e.matched.find(d=>Zt(d,l))?r.push(l):s.push(l));const c=e.matched[a];c&&(t.matched.find(d=>Zt(d,c))||n.push(c))}return[s,r,n]}const lt=(e,t)=>{const s=e.__vccOpts||e;for(const[r,n]of t)s[r]=n;return s},Nf={name:"App",data(){return{user:null,mobileMenuOpen:!1,isAuthenticated:!1}},mounted(){this.checkAuthStatus(),window.addEventListener("storage",this.handleStorageChange),this.authCheckInterval=setInterval(()=>{this.checkAuthStatus()},5e3)},beforeUnmount(){window.removeEventListener("storage",this.handleStorageChange),this.authCheckInterval&&clearInterval(this.authCheckInterval)},methods:{async logout(){try{await this.$http.post("/logout"),this.clearAuthData()}catch(e){console.error("Logout error:",e),this.clearAuthData()}},clearAuthData(){localStorage.removeItem("auth_token"),localStorage.removeItem("user"),delete this.$http.defaults.headers.common.Authorization,this.user=null,this.isAuthenticated=!1,this.mobileMenuOpen=!1,this.$forceUpdate(),this.$router.push("/").then(()=>{this.$nextTick(()=>{this.checkAuthStatus()})})},checkAuthStatus(){const e=localStorage.getItem("auth_token"),t=localStorage.getItem("user");e&&t?(this.isAuthenticated=!0,this.user=JSON.parse(t),this.$http.defaults.headers.common.Authorization=`Bearer ${e}`):(this.isAuthenticated=!1,this.user=null)},handleStorageChange(e){(e.key==="auth_token"||e.key==="user")&&this.checkAuthStatus()}},watch:{$route(){this.checkAuthStatus()}}},If={id:"app",class:"min-h-screen"},Uf={class:"glass-effect fixed w-full z-50 backdrop-blur-md"},Bf={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Vf={class:"flex justify-between h-20"},Lf={class:"flex items-center"},zf={class:"hidden md:flex items-center space-x-6"},Hf={key:0,class:"flex items-center space-x-4"},qf={key:1,class:"flex items-center space-x-4"},Kf={class:"md:hidden flex items-center"},Wf={class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Jf={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"},Gf={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},Xf={key:0,class:"md:hidden py-4 border-t border-white/20"},Qf={class:"flex flex-col space-y-2"},Yf={key:0,class:"flex flex-col space-y-2"},Zf={key:1,class:"flex flex-col space-y-2"},ep={class:"flex-1"},tp={class:"bg-gradient-to-br from-accent-900 via-accent-800 to-primary-900 text-white relative overflow-hidden"},sp={class:"relative max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8"},np={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},rp={class:"space-y-3"};function op(e,t,s,r,n,i){const a=xt("router-link"),l=xt("router-view");return T(),A("div",If,[o("nav",Uf,[o("div",Bf,[o("div",Vf,[o("div",Lf,[K(a,{to:"/",class:"flex items-center space-x-3"},{default:ne(()=>t[7]||(t[7]=[o("div",{class:"w-12 h-12 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200"},[o("span",{class:"text-white font-bold text-xl"},"K")],-1),o("span",{class:"text-2xl font-bold text-gradient"},"KabEvents",-1)])),_:1,__:[7]})]),o("div",zf,[K(a,{to:"/events",class:"nav-link"},{default:ne(()=>t[8]||(t[8]=[H(" Événements ")])),_:1,__:[8]}),n.isAuthenticated?(T(),A("div",qf,[K(a,{to:"/dashboard",class:"nav-link"},{default:ne(()=>t[11]||(t[11]=[H(" Dashboard ")])),_:1,__:[11]}),o("button",{onClick:t[0]||(t[0]=(...c)=>i.logout&&i.logout(...c)),class:"nav-link"}," Déconnexion ")])):(T(),A("div",Hf,[K(a,{to:"/login",class:"nav-link"},{default:ne(()=>t[9]||(t[9]=[H(" Connexion ")])),_:1,__:[9]}),K(a,{to:"/register",class:"btn-primary"},{default:ne(()=>t[10]||(t[10]=[H(" S'inscrire ")])),_:1,__:[10]})]))]),o("div",Kf,[o("button",{onClick:t[1]||(t[1]=c=>n.mobileMenuOpen=!n.mobileMenuOpen),class:"text-accent-700 hover:text-primary-600 p-2 rounded-lg transition-colors duration-200"},[(T(),A("svg",Wf,[n.mobileMenuOpen?(T(),A("path",Gf)):(T(),A("path",Jf))]))])])]),n.mobileMenuOpen?(T(),A("div",Xf,[o("div",Qf,[K(a,{to:"/events",class:"nav-link",onClick:t[2]||(t[2]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[12]||(t[12]=[H(" Événements ")])),_:1,__:[12]}),n.isAuthenticated?(T(),A("div",Zf,[K(a,{to:"/dashboard",class:"nav-link",onClick:t[5]||(t[5]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[15]||(t[15]=[H(" Dashboard ")])),_:1,__:[15]}),o("button",{onClick:t[6]||(t[6]=c=>{i.logout,n.mobileMenuOpen=!1}),class:"nav-link text-left"}," Déconnexion ")])):(T(),A("div",Yf,[K(a,{to:"/login",class:"nav-link",onClick:t[3]||(t[3]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[13]||(t[13]=[H(" Connexion ")])),_:1,__:[13]}),K(a,{to:"/register",class:"btn-primary text-center",onClick:t[4]||(t[4]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[14]||(t[14]=[H(" S'inscrire ")])),_:1,__:[14]})]))])])):me("",!0)])]),o("main",ep,[K(l)]),o("footer",tp,[t[23]||(t[23]=o("div",{class:"absolute inset-0 bg-hero-pattern opacity-10"},null,-1)),o("div",sp,[o("div",np,[t[20]||(t[20]=qe('<div class="md:col-span-2"><div class="flex items-center space-x-3 mb-6"><div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-lg"><span class="text-white font-bold text-xl">K</span></div><span class="text-2xl font-bold">KabEvents</span></div><p class="text-accent-200 text-lg leading-relaxed mb-6"> La plateforme de référence pour les événements culturels kabyles au Canada. Connectons notre communauté à travers la culture et les traditions. </p><div class="flex space-x-4"><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg></a><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"></path></svg></a><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"></path></svg></a></div></div>',1)),o("div",null,[t[19]||(t[19]=o("h3",{class:"text-xl font-semibold mb-6 text-white"},"Liens rapides",-1)),o("ul",rp,[o("li",null,[K(a,{to:"/events",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},{default:ne(()=>t[16]||(t[16]=[o("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"},null,-1),H(" Événements ")])),_:1,__:[16]})]),o("li",null,[K(a,{to:"/register",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},{default:ne(()=>t[17]||(t[17]=[o("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"},null,-1),H(" Devenir organisateur ")])),_:1,__:[17]})]),t[18]||(t[18]=o("li",null,[o("a",{href:"#",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},[o("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"}),H(" À propos ")])],-1))])]),t[21]||(t[21]=qe('<div><h3 class="text-xl font-semibold mb-6 text-white">Contact</h3><div class="space-y-4"><div class="flex items-start space-x-3"><svg class="w-5 h-5 text-secondary-400 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg><div><p class="text-accent-200"><EMAIL></p></div></div><div class="flex items-start space-x-3"><svg class="w-5 h-5 text-secondary-400 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg><div><p class="text-accent-200">+1 (555) 123-4567</p></div></div></div></div>',1))]),t[22]||(t[22]=qe('<div class="mt-12 pt-8 border-t border-white/20"><div class="flex flex-col md:flex-row justify-between items-center"><p class="text-accent-200 text-sm"> © 2024 KabEvents. Tous droits réservés. </p><div class="flex space-x-6 mt-4 md:mt-0"><a href="#" class="text-accent-200 hover:text-white text-sm transition-colors duration-200">Politique de confidentialité</a><a href="#" class="text-accent-200 hover:text-white text-sm transition-colors duration-200">Conditions d&#39;utilisation</a></div></div></div>',1))])])])}const ip=lt(Nf,[["render",op]]),lp={name:"Home",data(){return{featuredEvents:[],loading:!0}},methods:{async fetchFeaturedEvents(){try{const e=await this.$http.get("/events?limit=3");this.featuredEvents=e.data.data||e.data}catch(e){console.error("Error fetching events:",e)}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric"})}},mounted(){this.fetchFeaturedEvents()}},ap={class:"relative min-h-screen flex items-center justify-center overflow-hidden"},cp={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 text-center"},up={class:"animate-fade-in"},dp={class:"flex flex-col sm:flex-row gap-6 justify-center items-center animate-slide-up"},fp={class:"py-24 relative"},pp={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},mp={key:0,class:"text-center py-16"},hp={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},gp={class:"event-image"},vp={class:"text-center"},xp={class:"text-white text-3xl font-bold"},bp={class:"mt-2 text-white/80 text-sm font-medium"},yp={class:"p-6"},wp={class:"flex items-center justify-between mb-3"},_p={class:"price-tag"},kp={class:"text-sm text-accent-500 font-medium"},Ep={class:"text-xl font-bold text-accent-900 mb-3 line-clamp-2"},Sp={class:"text-accent-600 mb-6 line-clamp-3 leading-relaxed"},Cp={class:"flex items-center justify-between"},Rp={class:"flex items-center space-x-2"},Tp={class:"text-sm text-accent-500"},Ap={class:"text-center mt-16"},Op={class:"py-24 relative overflow-hidden"},$p={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Mp={class:"text-center mt-20"},Pp={class:"card max-w-4xl mx-auto"},jp={class:"flex flex-col sm:flex-row gap-4 justify-center"};function Dp(e,t,s,r,n,i){const a=xt("router-link");return T(),A("div",null,[o("section",ap,[t[5]||(t[5]=qe('<div class="absolute inset-0 hero-gradient" data-v-3683f6a2></div><div class="absolute inset-0 bg-hero-pattern opacity-20" data-v-3683f6a2></div><div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-bounce-gentle" data-v-3683f6a2></div><div class="absolute top-40 right-20 w-16 h-16 bg-secondary-400/20 rounded-full animate-bounce-gentle" style="animation-delay:1s;" data-v-3683f6a2></div><div class="absolute bottom-40 left-20 w-12 h-12 bg-primary-400/20 rounded-full animate-bounce-gentle" style="animation-delay:2s;" data-v-3683f6a2></div>',5)),o("div",cp,[o("div",up,[t[2]||(t[2]=o("h1",{class:"text-5xl md:text-7xl font-bold mb-8 text-white leading-tight"},[H(" Découvrez la "),o("span",{class:"bg-gradient-to-r from-secondary-300 to-secondary-100 bg-clip-text text-transparent"}," Culture Kabyle ")],-1)),t[3]||(t[3]=o("p",{class:"text-xl md:text-2xl mb-12 text-white/90 max-w-3xl mx-auto leading-relaxed"}," Participez aux plus beaux événements culturels kabyles au Canada. Connectez-vous avec votre communauté et célébrez nos traditions ancestrales. ",-1)),o("div",dp,[K(a,{to:"/events",class:"btn-secondary text-lg px-8 py-4"},{default:ne(()=>t[0]||(t[0]=[o("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),H(" Voir les événements ")])),_:1,__:[0]}),K(a,{to:"/register",class:"btn-outline text-lg px-8 py-4 bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-primary-600"},{default:ne(()=>t[1]||(t[1]=[o("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),H(" Devenir organisateur ")])),_:1,__:[1]})])]),t[4]||(t[4]=o("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"},[o("svg",{class:"w-6 h-6 text-white/70",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))])]),o("section",fp,[t[11]||(t[11]=o("div",{class:"absolute inset-0 bg-gradient-to-b from-white via-accent-50/30 to-white"},null,-1)),o("div",pp,[t[10]||(t[10]=o("div",{class:"text-center mb-16"},[o("h2",{class:"text-4xl md:text-5xl font-bold text-gradient mb-6"},"Événements à venir"),o("p",{class:"text-xl text-accent-600 max-w-2xl mx-auto leading-relaxed"}," Ne manquez pas ces événements exceptionnels qui célèbrent notre riche patrimoine culturel ")],-1)),n.loading?(T(),A("div",mp,t[6]||(t[6]=[o("div",{class:"loading-spinner w-12 h-12 mx-auto"},null,-1),o("p",{class:"text-accent-600 mt-4"},"Chargement des événements...",-1)]))):(T(),A("div",hp,[(T(!0),A(he,null,$e(n.featuredEvents,l=>(T(),A("div",{key:l.id,class:"event-card"},[o("div",gp,[o("div",vp,[o("span",xp,y(l.title.substring(0,2).toUpperCase()),1),o("div",bp,y(l.location||"Canada"),1)])]),o("div",yp,[o("div",wp,[o("span",_p,"$"+y(l.ticket_price),1),o("span",kp,y(i.formatDate(l.event_date)),1)]),o("h3",Ep,y(l.title),1),o("p",Sp,y(l.description),1),o("div",Cp,[o("div",Rp,[t[7]||(t[7]=o("svg",{class:"w-4 h-4 text-accent-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),o("span",Tp,y(l.location||"Canada"),1)]),K(a,{to:`/events/${l.id}`,class:"btn-primary text-sm px-4 py-2"},{default:ne(()=>t[8]||(t[8]=[H(" Voir détails ")])),_:2,__:[8]},1032,["to"])])])]))),128))])),o("div",Ap,[K(a,{to:"/events",class:"btn-outline text-lg px-8 py-4"},{default:ne(()=>t[9]||(t[9]=[o("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1),H(" Voir tous les événements ")])),_:1,__:[9]})])])]),o("section",Op,[t[17]||(t[17]=o("div",{class:"absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-secondary-50"},null,-1)),t[18]||(t[18]=o("div",{class:"absolute top-0 left-0 w-full h-full bg-hero-pattern opacity-5"},null,-1)),o("div",$p,[t[16]||(t[16]=qe('<div class="text-center mb-20" data-v-3683f6a2><h2 class="text-4xl md:text-5xl font-bold text-gradient mb-6" data-v-3683f6a2>Pourquoi choisir KabEvents ?</h2><p class="text-xl text-accent-600 max-w-3xl mx-auto leading-relaxed" data-v-3683f6a2> Une plateforme moderne et sécurisée dédiée à la préservation et à la célébration de notre patrimoine culturel </p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-12" data-v-3683f6a2><div class="text-center group" data-v-3683f6a2><div class="relative mb-8" data-v-3683f6a2><div class="w-24 h-24 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-3683f6a2><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-3683f6a2><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-3683f6a2></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-secondary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-3683f6a2></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-3683f6a2>Événements authentiques</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-3683f6a2> Des événements culturels kabyles authentiques organisés par et pour notre communauté, préservant nos traditions ancestrales </p></div><div class="text-center group" data-v-3683f6a2><div class="relative mb-8" data-v-3683f6a2><div class="w-24 h-24 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-3683f6a2><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-3683f6a2><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" data-v-3683f6a2></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-primary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-3683f6a2></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-3683f6a2>Réservation sécurisée</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-3683f6a2> Système de paiement sécurisé avec billets électroniques, QR codes et protection complète de vos données personnelles </p></div><div class="text-center group" data-v-3683f6a2><div class="relative mb-8" data-v-3683f6a2><div class="w-24 h-24 bg-gradient-to-br from-success-500 to-success-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-3683f6a2><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-3683f6a2><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" data-v-3683f6a2></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-secondary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-3683f6a2></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-3683f6a2>Communauté unie</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-3683f6a2> Rassemblez-vous avec la communauté kabyle du Canada et créez des liens durables autour de notre culture commune </p></div></div>',2)),o("div",Mp,[o("div",Pp,[t[14]||(t[14]=o("h3",{class:"text-3xl font-bold text-accent-900 mb-4"},"Prêt à rejoindre notre communauté ?",-1)),t[15]||(t[15]=o("p",{class:"text-xl text-accent-600 mb-8 leading-relaxed"}," Découvrez des événements exceptionnels et connectez-vous avec des milliers de membres de notre communauté ",-1)),o("div",jp,[K(a,{to:"/register",class:"btn-primary text-lg px-8 py-4"},{default:ne(()=>t[12]||(t[12]=[H(" Créer un compte gratuit ")])),_:1,__:[12]}),K(a,{to:"/events",class:"btn-outline text-lg px-8 py-4"},{default:ne(()=>t[13]||(t[13]=[H(" Explorer les événements ")])),_:1,__:[13]})])])])])])])}const Fp=lt(lp,[["render",Dp],["__scopeId","data-v-3683f6a2"]]),Np={name:"Login",data(){return{form:{email:"",password:"",remember:!1},loading:!1,error:null}},methods:{async login(){this.loading=!0,this.error=null;try{const e=await this.$http.post("/login",{email:this.form.email,password:this.form.password});localStorage.setItem("auth_token",e.data.token),localStorage.setItem("user",JSON.stringify(e.data.user)),this.$http.defaults.headers.common.Authorization=`Bearer ${e.data.token}`;const t=this.$route.query.redirect||"/dashboard";this.$router.push(t)}catch(e){e.response&&e.response.data?this.error=e.response.data.message||"Erreur de connexion":this.error="Erreur de connexion. Veuillez réessayer."}finally{this.loading=!1}}},mounted(){localStorage.getItem("auth_token")&&this.$router.push("/dashboard")}},Ip={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Up={class:"max-w-md w-full space-y-8"},Bp={class:"mt-2 text-center text-sm text-gray-600"},Vp={key:0,class:"bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded"},Lp={class:"space-y-4"},zp={class:"flex items-center justify-between"},Hp={class:"flex items-center"},qp=["disabled"],Kp={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function Wp(e,t,s,r,n,i){const a=xt("router-link");return T(),A("div",Ip,[o("div",Up,[o("div",null,[t[6]||(t[6]=o("div",{class:"mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-blue-500 rounded-lg flex items-center justify-center"},[o("span",{class:"text-white font-bold text-xl"},"K")],-1)),t[7]||(t[7]=o("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Connexion à votre compte ",-1)),o("p",Bp,[t[5]||(t[5]=H(" Ou ")),K(a,{to:"/register",class:"font-medium text-orange-600 hover:text-orange-500"},{default:ne(()=>t[4]||(t[4]=[H(" créez un nouveau compte ")])),_:1,__:[4]})])]),o("form",{class:"mt-8 space-y-6",onSubmit:t[3]||(t[3]=En((...l)=>i.login&&i.login(...l),["prevent"]))},[n.error?(T(),A("div",Vp,y(n.error),1)):me("",!0),o("div",Lp,[o("div",null,[t[8]||(t[8]=o("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Adresse email ",-1)),pe(o("input",{id:"email","onUpdate:modelValue":t[0]||(t[0]=l=>n.form.email=l),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"<EMAIL>"},null,512),[[be,n.form.email]])]),o("div",null,[t[9]||(t[9]=o("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Mot de passe ",-1)),pe(o("input",{id:"password","onUpdate:modelValue":t[1]||(t[1]=l=>n.form.password=l),name:"password",type:"password",autocomplete:"current-password",required:"",class:"input-field mt-1",placeholder:"Votre mot de passe"},null,512),[[be,n.form.password]])])]),o("div",zp,[o("div",Hp,[pe(o("input",{id:"remember-me","onUpdate:modelValue":t[2]||(t[2]=l=>n.form.remember=l),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"},null,512),[[xl,n.form.remember]]),t[10]||(t[10]=o("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Se souvenir de moi ",-1))]),t[11]||(t[11]=o("div",{class:"text-sm"},[o("a",{href:"#",class:"font-medium text-orange-600 hover:text-orange-500"}," Mot de passe oublié ? ")],-1))]),o("div",null,[o("button",{type:"submit",disabled:n.loading,class:"btn-primary w-full flex justify-center items-center"},[n.loading?(T(),A("svg",Kp,t[12]||(t[12]=[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):me("",!0),H(" "+y(n.loading?"Connexion...":"Se connecter"),1)],8,qp)])],32)])])}const Jp=lt(Np,[["render",Wp]]),Gp={name:"Register",data(){return{form:{name:"",email:"",password:"",password_confirmation:"",role:"user",terms:!1},loading:!1,error:null}},methods:{async register(){this.loading=!0,this.error=null;try{const e=await this.$http.post("/register",{name:this.form.name,email:this.form.email,password:this.form.password,password_confirmation:this.form.password_confirmation,role:this.form.role});localStorage.setItem("auth_token",e.data.token),localStorage.setItem("user",JSON.stringify(e.data.user)),this.$http.defaults.headers.common.Authorization=`Bearer ${e.data.token}`,this.$router.push("/dashboard")}catch(e){if(e.response&&e.response.data)if(e.response.data.errors){const t=Object.values(e.response.data.errors).flat();this.error=t.join(", ")}else this.error=e.response.data.message||"Erreur lors de l'inscription";else this.error="Erreur lors de l'inscription. Veuillez réessayer."}finally{this.loading=!1}}},mounted(){localStorage.getItem("auth_token")&&this.$router.push("/dashboard")}},Xp={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Qp={class:"max-w-md w-full space-y-8"},Yp={class:"mt-2 text-center text-sm text-gray-600"},Zp={key:0,class:"bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded"},em={class:"space-y-4"},tm={class:"flex items-center"},sm=["disabled"],nm={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function rm(e,t,s,r,n,i){const a=xt("router-link");return T(),A("div",Xp,[o("div",Qp,[o("div",null,[t[9]||(t[9]=o("div",{class:"mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-blue-500 rounded-lg flex items-center justify-center"},[o("span",{class:"text-white font-bold text-xl"},"K")],-1)),t[10]||(t[10]=o("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Créer votre compte ",-1)),o("p",Yp,[t[8]||(t[8]=H(" Ou ")),K(a,{to:"/login",class:"font-medium text-orange-600 hover:text-orange-500"},{default:ne(()=>t[7]||(t[7]=[H(" connectez-vous à votre compte existant ")])),_:1,__:[7]})])]),o("form",{class:"mt-8 space-y-6",onSubmit:t[6]||(t[6]=En((...l)=>i.register&&i.register(...l),["prevent"]))},[n.error?(T(),A("div",Zp,y(n.error),1)):me("",!0),o("div",em,[o("div",null,[t[11]||(t[11]=o("label",{for:"name",class:"block text-sm font-medium text-gray-700"}," Nom complet ",-1)),pe(o("input",{id:"name","onUpdate:modelValue":t[0]||(t[0]=l=>n.form.name=l),name:"name",type:"text",autocomplete:"name",required:"",class:"input-field mt-1",placeholder:"Votre nom complet"},null,512),[[be,n.form.name]])]),o("div",null,[t[12]||(t[12]=o("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Adresse email ",-1)),pe(o("input",{id:"email","onUpdate:modelValue":t[1]||(t[1]=l=>n.form.email=l),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"<EMAIL>"},null,512),[[be,n.form.email]])]),o("div",null,[t[14]||(t[14]=o("label",{for:"role",class:"block text-sm font-medium text-gray-700"}," Type de compte ",-1)),pe(o("select",{id:"role","onUpdate:modelValue":t[2]||(t[2]=l=>n.form.role=l),name:"role",class:"input-field mt-1"},t[13]||(t[13]=[o("option",{value:"user"},"Utilisateur (participer aux événements)",-1),o("option",{value:"organizer"},"Organisateur (créer des événements)",-1)]),512),[[vs,n.form.role]])]),o("div",null,[t[15]||(t[15]=o("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Mot de passe ",-1)),pe(o("input",{id:"password","onUpdate:modelValue":t[3]||(t[3]=l=>n.form.password=l),name:"password",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Minimum 8 caractères"},null,512),[[be,n.form.password]])]),o("div",null,[t[16]||(t[16]=o("label",{for:"password_confirmation",class:"block text-sm font-medium text-gray-700"}," Confirmer le mot de passe ",-1)),pe(o("input",{id:"password_confirmation","onUpdate:modelValue":t[4]||(t[4]=l=>n.form.password_confirmation=l),name:"password_confirmation",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Répétez votre mot de passe"},null,512),[[be,n.form.password_confirmation]])])]),o("div",tm,[pe(o("input",{id:"terms","onUpdate:modelValue":t[5]||(t[5]=l=>n.form.terms=l),name:"terms",type:"checkbox",required:"",class:"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"},null,512),[[xl,n.form.terms]]),t[17]||(t[17]=o("label",{for:"terms",class:"ml-2 block text-sm text-gray-900"},[H(" J'accepte les "),o("a",{href:"#",class:"text-orange-600 hover:text-orange-500"},"conditions d'utilisation"),H(" et la "),o("a",{href:"#",class:"text-orange-600 hover:text-orange-500"},"politique de confidentialité")],-1))]),o("div",null,[o("button",{type:"submit",disabled:n.loading,class:"btn-primary w-full flex justify-center items-center"},[n.loading?(T(),A("svg",nm,t[18]||(t[18]=[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):me("",!0),H(" "+y(n.loading?"Création...":"Créer mon compte"),1)],8,sm)])],32)])])}const om=lt(Gp,[["render",rm]]),im={name:"Events",data(){return{events:[],filteredEvents:[],loading:!0,filters:{search:"",city:"",maxPrice:"",sortBy:"date"}}},methods:{async fetchEvents(){try{const e=await this.$http.get("/events");this.events=e.data.data||e.data,this.filteredEvents=[...this.events],this.filterEvents()}catch(e){console.error("Error fetching events:",e)}finally{this.loading=!1}},filterEvents(){let e=[...this.events];this.filters.search&&(e=e.filter(t=>t.title.toLowerCase().includes(this.filters.search.toLowerCase())||t.description.toLowerCase().includes(this.filters.search.toLowerCase()))),this.filters.city&&(e=e.filter(t=>t.location.includes(this.filters.city))),this.filters.maxPrice&&(e=e.filter(t=>parseFloat(t.ticket_price)<=parseFloat(this.filters.maxPrice))),e.sort((t,s)=>{switch(this.filters.sortBy){case"price":return parseFloat(t.ticket_price)-parseFloat(s.ticket_price);case"title":return t.title.localeCompare(s.title);case"date":default:return new Date(t.event_date)-new Date(s.event_date)}}),this.filteredEvents=e},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvents()}},lm={class:"min-h-screen bg-gray-50"},am={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},cm={class:"bg-white rounded-lg shadow p-6"},um={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},dm={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12"},fm={key:0,class:"text-center py-12"},pm={key:1,class:"text-center py-12"},mm={key:2,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},hm={class:"h-48 bg-gradient-to-r from-orange-400 to-blue-400 rounded-lg mb-4 flex items-center justify-center"},gm={class:"text-white text-2xl font-bold"},vm={class:"space-y-3"},xm={class:"text-xl font-semibold text-gray-900"},bm={class:"text-gray-600 text-sm line-clamp-3"},ym={class:"flex items-center text-sm text-gray-500"},wm={class:"flex items-center text-sm text-gray-500"},_m={class:"flex items-center justify-between"},km={class:"flex items-center text-sm text-gray-500"},Em={class:"text-2xl font-bold text-orange-600"};function Sm(e,t,s,r,n,i){const a=xt("router-link");return T(),A("div",lm,[t[21]||(t[21]=o("div",{class:"bg-white shadow"},[o("div",{class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},[o("h1",{class:"text-3xl font-bold text-gray-900"},"Événements culturels kabyles"),o("p",{class:"mt-2 text-gray-600"},"Découvrez tous les événements à venir")])],-1)),o("div",am,[o("div",cm,[o("div",um,[o("div",null,[t[8]||(t[8]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Rechercher",-1)),pe(o("input",{"onUpdate:modelValue":t[0]||(t[0]=l=>n.filters.search=l),type:"text",placeholder:"Nom de l'événement...",class:"input-field",onInput:t[1]||(t[1]=(...l)=>i.filterEvents&&i.filterEvents(...l))},null,544),[[be,n.filters.search]])]),o("div",null,[t[10]||(t[10]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Ville",-1)),pe(o("select",{"onUpdate:modelValue":t[2]||(t[2]=l=>n.filters.city=l),class:"input-field",onChange:t[3]||(t[3]=(...l)=>i.filterEvents&&i.filterEvents(...l))},t[9]||(t[9]=[qe('<option value="" data-v-a8083e47>Toutes les villes</option><option value="Toronto" data-v-a8083e47>Toronto</option><option value="Montréal" data-v-a8083e47>Montréal</option><option value="Vancouver" data-v-a8083e47>Vancouver</option><option value="Calgary" data-v-a8083e47>Calgary</option>',5)]),544),[[vs,n.filters.city]])]),o("div",null,[t[12]||(t[12]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Prix max",-1)),pe(o("select",{"onUpdate:modelValue":t[4]||(t[4]=l=>n.filters.maxPrice=l),class:"input-field",onChange:t[5]||(t[5]=(...l)=>i.filterEvents&&i.filterEvents(...l))},t[11]||(t[11]=[o("option",{value:""},"Tous les prix",-1),o("option",{value:"50"},"Moins de 50$",-1),o("option",{value:"75"},"Moins de 75$",-1),o("option",{value:"100"},"Moins de 100$",-1)]),544),[[vs,n.filters.maxPrice]])]),o("div",null,[t[14]||(t[14]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Trier par",-1)),pe(o("select",{"onUpdate:modelValue":t[6]||(t[6]=l=>n.filters.sortBy=l),class:"input-field",onChange:t[7]||(t[7]=(...l)=>i.filterEvents&&i.filterEvents(...l))},t[13]||(t[13]=[o("option",{value:"date"},"Date",-1),o("option",{value:"price"},"Prix",-1),o("option",{value:"title"},"Nom",-1)]),544),[[vs,n.filters.sortBy]])])])])]),o("div",dm,[n.loading?(T(),A("div",fm,t[15]||(t[15]=[o("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"},null,-1),o("p",{class:"mt-2 text-gray-600"},"Chargement des événements...",-1)]))):n.filteredEvents.length===0?(T(),A("div",pm,t[16]||(t[16]=[o("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),o("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Aucun événement trouvé",-1),o("p",{class:"mt-1 text-sm text-gray-500"},"Essayez de modifier vos critères de recherche.",-1)]))):(T(),A("div",mm,[(T(!0),A(he,null,$e(n.filteredEvents,l=>(T(),A("div",{key:l.id,class:"card hover:shadow-xl transition duration-300"},[o("div",hm,[o("span",gm,y(l.title.substring(0,2).toUpperCase()),1)]),o("div",vm,[o("h3",xm,y(l.title),1),o("p",bm,y(l.description),1),o("div",ym,[t[17]||(t[17]=o("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),H(" "+y(i.formatDate(l.event_date)),1)]),o("div",wm,[t[18]||(t[18]=o("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),H(" "+y(l.location),1)]),o("div",_m,[o("div",km,[t[19]||(t[19]=o("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1)),H(" "+y(l.available_tickets)+" places disponibles ",1)]),o("span",Em,"$"+y(l.ticket_price),1)]),K(a,{to:`/events/${l.id}`,class:"btn-primary w-full text-center block"},{default:ne(()=>t[20]||(t[20]=[H(" Voir détails ")])),_:2,__:[20]},1032,["to"])])]))),128))]))])])}const Cm=lt(im,[["render",Sm],["__scopeId","data-v-a8083e47"]]),Rm={name:"EventDetail",props:["id"],data(){return{event:null,loading:!0,quantity:1,reserving:!1,error:null,success:null}},computed:{isAuthenticated(){return!!localStorage.getItem("auth_token")}},methods:{async fetchEvent(){try{const e=await this.$http.get(`/events/${this.id}`);this.event=e.data.data||e.data}catch(e){console.error("Error fetching event:",e),this.event=null}finally{this.loading=!1}},async reserveTickets(){this.reserving=!0,this.error=null,this.success=null;try{const e=await this.$http.post(`/events/${this.id}/reserve`,{quantity:this.quantity});this.success="Réservation effectuée avec succès ! Vous recevrez un email de confirmation.",this.event.available_tickets-=this.quantity,setTimeout(()=>{this.$router.push("/dashboard")},2e3)}catch(e){e.response&&e.response.data?this.error=e.response.data.message||"Erreur lors de la réservation":this.error="Erreur lors de la réservation. Veuillez réessayer."}finally{this.reserving=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric"})},formatTime(e){return new Date(e).toLocaleTimeString("fr-CA",{hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvent()},watch:{id(){this.fetchEvent()}}},Tm={class:"min-h-screen bg-gray-50"},Am={key:0,class:"flex items-center justify-center min-h-screen"},Om={key:1,class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},$m={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Mm={class:"lg:col-span-2"},Pm={class:"h-64 md:h-96 bg-gradient-to-r from-orange-400 to-blue-400 rounded-xl mb-6 flex items-center justify-center"},jm={class:"text-white text-4xl font-bold"},Dm={class:"bg-white rounded-xl shadow-lg p-6"},Fm={class:"text-3xl font-bold text-gray-900 mb-4"},Nm={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"},Im={class:"flex items-center text-gray-600"},Um={class:"font-medium"},Bm={class:"text-sm"},Vm={class:"flex items-center text-gray-600"},Lm={class:"font-medium"},zm={class:"flex items-center text-gray-600"},Hm={class:"font-medium"},qm={class:"text-sm"},Km={class:"flex items-center text-gray-600"},Wm={class:"text-sm"},Jm={class:"border-t pt-6"},Gm={class:"text-gray-700 leading-relaxed"},Xm={class:"lg:col-span-1"},Qm={class:"bg-white rounded-xl shadow-lg p-6 sticky top-8"},Ym={class:"text-center mb-6"},Zm={class:"text-3xl font-bold text-orange-600"},e0={key:0,class:"space-y-4"},t0={key:1,class:"space-y-4"},s0=["value"],n0={class:"border-t pt-4"},r0={class:"flex justify-between mb-2"},o0={class:"font-semibold"},i0={class:"flex justify-between text-lg font-bold"},l0=["disabled"],a0={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},c0={key:2,class:"text-center"},u0={key:3,class:"mt-4 bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded"},d0={key:4,class:"mt-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded"},f0={key:2,class:"flex items-center justify-center min-h-screen"},p0={class:"text-center"};function m0(e,t,s,r,n,i){var l;const a=xt("router-link");return T(),A("div",Tm,[n.loading?(T(),A("div",Am,t[3]||(t[3]=[o("div",{class:"text-center"},[o("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"}),o("p",{class:"mt-2 text-gray-600"},"Chargement de l'événement...")],-1)]))):n.event?(T(),A("div",Om,[o("button",{onClick:t[0]||(t[0]=c=>e.$router.go(-1)),class:"mb-6 flex items-center text-orange-600 hover:text-orange-700"},t[4]||(t[4]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1),H(" Retour ")])),o("div",$m,[o("div",Mm,[o("div",Pm,[o("span",jm,y(n.event.title.substring(0,2).toUpperCase()),1)]),o("div",Dm,[o("h1",Fm,y(n.event.title),1),o("div",Nm,[o("div",Im,[t[5]||(t[5]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),o("div",null,[o("p",Um,y(i.formatDate(n.event.event_date)),1),o("p",Bm,y(i.formatTime(n.event.event_date)),1)])]),o("div",Vm,[t[6]||(t[6]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),o("div",null,[o("p",Lm,y(n.event.location),1)])]),o("div",zm,[t[7]||(t[7]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"})],-1)),o("div",null,[o("p",Hm,y(n.event.available_tickets)+" places disponibles",1),o("p",qm,"sur "+y(n.event.total_tickets)+" places",1)])]),o("div",Km,[t[9]||(t[9]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"})],-1)),o("div",null,[t[8]||(t[8]=o("p",{class:"font-medium"},"Organisé par",-1)),o("p",Wm,y(((l=n.event.organizer)==null?void 0:l.name)||"Organisateur"),1)])])]),o("div",Jm,[t[10]||(t[10]=o("h2",{class:"text-xl font-semibold mb-3"},"Description",-1)),o("p",Gm,y(n.event.description),1)])])]),o("div",Xm,[o("div",Qm,[o("div",Ym,[o("p",Zm,"$"+y(n.event.ticket_price),1),t[11]||(t[11]=o("p",{class:"text-gray-600"},"par billet",-1))]),i.isAuthenticated?n.event.available_tickets>0?(T(),A("div",t0,[o("div",null,[t[15]||(t[15]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Nombre de billets ",-1)),pe(o("select",{"onUpdate:modelValue":t[1]||(t[1]=c=>n.quantity=c),class:"input-field"},[(T(!0),A(he,null,$e(Math.min(10,n.event.available_tickets),c=>(T(),A("option",{key:c,value:c},y(c)+" billet"+y(c>1?"s":""),9,s0))),128))],512),[[vs,n.quantity]])]),o("div",n0,[o("div",r0,[o("span",null,y(n.quantity)+" × $"+y(n.event.ticket_price),1),o("span",o0,"$"+y((n.quantity*parseFloat(n.event.ticket_price)).toFixed(2)),1)]),o("div",i0,[t[16]||(t[16]=o("span",null,"Total",-1)),o("span",null,"$"+y((n.quantity*parseFloat(n.event.ticket_price)).toFixed(2)),1)])]),o("button",{onClick:t[2]||(t[2]=(...c)=>i.reserveTickets&&i.reserveTickets(...c)),disabled:n.reserving,class:"btn-primary w-full flex justify-center items-center"},[n.reserving?(T(),A("svg",a0,t[17]||(t[17]=[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):me("",!0),H(" "+y(n.reserving?"Réservation...":"Réserver maintenant"),1)],8,l0)])):(T(),A("div",c0,t[18]||(t[18]=[o("p",{class:"text-orange-600 font-semibold mb-4"},"Événement complet",-1),o("p",{class:"text-gray-600 text-sm"}," Cet événement n'a plus de places disponibles. ",-1)]))):(T(),A("div",e0,[t[14]||(t[14]=o("p",{class:"text-center text-gray-600 mb-4"}," Connectez-vous pour réserver vos billets ",-1)),K(a,{to:"/login",class:"btn-primary w-full text-center block"},{default:ne(()=>t[12]||(t[12]=[H(" Se connecter ")])),_:1,__:[12]}),K(a,{to:"/register",class:"btn-secondary w-full text-center block"},{default:ne(()=>t[13]||(t[13]=[H(" Créer un compte ")])),_:1,__:[13]})])),n.error?(T(),A("div",u0,y(n.error),1)):me("",!0),n.success?(T(),A("div",d0,y(n.success),1)):me("",!0)])])])])):(T(),A("div",f0,[o("div",p0,[t[20]||(t[20]=o("h2",{class:"text-2xl font-bold text-gray-900 mb-2"},"Événement non trouvé",-1)),t[21]||(t[21]=o("p",{class:"text-gray-600 mb-4"},"L'événement que vous recherchez n'existe pas.",-1)),K(a,{to:"/events",class:"btn-primary"},{default:ne(()=>t[19]||(t[19]=[H(" Voir tous les événements ")])),_:1,__:[19]})])]))])}const h0=lt(Rm,[["render",m0]]),g0={name:"Dashboard",data(){return{user:null,reservations:[],userStats:{totalReservations:0,upcomingEvents:0,totalSpent:0},adminStats:{totalUsers:0,totalEvents:0,totalReservations:0,totalRevenue:0},loading:!0}},methods:{async fetchUserData(){try{const e=await this.$http.get("/me");if(this.user=e.data.user,this.user.role==="user"){const t=await this.$http.get("/user/reservations");this.reservations=t.data.data||t.data,this.userStats.totalReservations=this.reservations.length,this.userStats.upcomingEvents=this.reservations.filter(s=>{var r;return new Date((r=s.event)==null?void 0:r.event_date)>new Date}).length,this.userStats.totalSpent=this.reservations.filter(s=>s.status==="paid").reduce((s,r)=>s+parseFloat(r.total_amount),0).toFixed(2)}}catch(e){console.error("Error fetching user data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})},getStatusClass(e){switch(e){case"paid":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},getStatusText(e){switch(e){case"paid":return"Payé";case"pending":return"En attente";case"cancelled":return"Annulé";default:return e}}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchUserData()}},v0={class:"min-h-screen bg-gray-50"},x0={class:"bg-white shadow"},b0={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},y0={class:"flex justify-between items-center"},w0={class:"text-gray-600"},_0={class:"flex items-center space-x-2"},k0={class:"px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium"},E0={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},S0={key:0,class:"space-y-8"},C0={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},R0={class:"card"},T0={class:"flex items-center"},A0={class:"ml-4"},O0={class:"text-2xl font-bold text-gray-900"},$0={class:"card"},M0={class:"flex items-center"},P0={class:"ml-4"},j0={class:"text-2xl font-bold text-gray-900"},D0={class:"card"},F0={class:"flex items-center"},N0={class:"ml-4"},I0={class:"text-2xl font-bold text-gray-900"},U0={class:"card"},B0={class:"flex justify-between items-center mb-6"},V0={key:0,class:"text-center py-8"},L0={key:1,class:"text-center py-8"},z0={key:2,class:"space-y-4"},H0={class:"flex justify-between items-start"},q0={class:"flex-1"},K0={class:"font-semibold text-lg"},W0={class:"text-gray-600 text-sm mb-2"},J0={class:"text-gray-600 text-sm"},G0={class:"mt-2 flex items-center space-x-4"},X0={class:"text-sm text-gray-500"},Q0={class:"text-sm font-medium"},Y0={class:"ml-4"},Z0={key:1,class:"space-y-8"},eh={key:2,class:"space-y-8"},th={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},sh={class:"card text-center"},nh={class:"text-3xl font-bold text-orange-600 mb-2"},rh={class:"card text-center"},oh={class:"text-3xl font-bold text-blue-600 mb-2"},ih={class:"card text-center"},lh={class:"text-3xl font-bold text-green-600 mb-2"},ah={class:"card text-center"},ch={class:"text-3xl font-bold text-purple-600 mb-2"};function uh(e,t,s,r,n,i){var l,c,d,u,f,h;const a=xt("router-link");return T(),A("div",v0,[o("div",x0,[o("div",b0,[o("div",y0,[o("div",null,[t[0]||(t[0]=o("h1",{class:"text-2xl font-bold text-gray-900"},"Dashboard",-1)),o("p",w0,"Bienvenue, "+y((l=n.user)==null?void 0:l.name),1)]),o("div",_0,[o("span",k0,y(((c=n.user)==null?void 0:c.role)==="admin"?"Administrateur":((d=n.user)==null?void 0:d.role)==="organizer"?"Organisateur":"Utilisateur"),1)])])])]),o("div",E0,[((u=n.user)==null?void 0:u.role)==="user"?(T(),A("div",S0,[o("div",C0,[o("div",R0,[o("div",T0,[t[2]||(t[2]=o("div",{class:"p-3 bg-orange-100 rounded-lg"},[o("svg",{class:"w-6 h-6 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",A0,[t[1]||(t[1]=o("p",{class:"text-sm font-medium text-gray-600"},"Réservations totales",-1)),o("p",O0,y(n.userStats.totalReservations),1)])])]),o("div",$0,[o("div",M0,[t[4]||(t[4]=o("div",{class:"p-3 bg-blue-100 rounded-lg"},[o("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",P0,[t[3]||(t[3]=o("p",{class:"text-sm font-medium text-gray-600"},"Événements à venir",-1)),o("p",j0,y(n.userStats.upcomingEvents),1)])])]),o("div",D0,[o("div",F0,[t[6]||(t[6]=o("div",{class:"p-3 bg-green-100 rounded-lg"},[o("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",N0,[t[5]||(t[5]=o("p",{class:"text-sm font-medium text-gray-600"},"Total dépensé",-1)),o("p",I0,"$"+y(n.userStats.totalSpent),1)])])])]),o("div",U0,[o("div",B0,[t[8]||(t[8]=o("h2",{class:"text-xl font-semibold"},"Mes réservations",-1)),K(a,{to:"/events",class:"btn-primary"},{default:ne(()=>t[7]||(t[7]=[H(" Réserver un événement ")])),_:1,__:[7]})]),n.loading?(T(),A("div",V0,t[9]||(t[9]=[o("div",{class:"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-orange-600"},null,-1)]))):n.reservations.length===0?(T(),A("div",L0,t[10]||(t[10]=[o("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})],-1),o("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Aucune réservation",-1),o("p",{class:"mt-1 text-sm text-gray-500"},"Commencez par réserver votre premier événement.",-1)]))):(T(),A("div",z0,[(T(!0),A(he,null,$e(n.reservations,v=>{var g,w,C,O;return T(),A("div",{key:v.id,class:"border rounded-lg p-4 hover:bg-gray-50"},[o("div",H0,[o("div",q0,[o("h3",K0,y((g=v.event)==null?void 0:g.title),1),o("p",W0,y((w=v.event)==null?void 0:w.location),1),o("p",J0,y(i.formatDate((C=v.event)==null?void 0:C.event_date)),1),o("div",G0,[o("span",X0,y(v.quantity)+" billet(s)",1),o("span",Q0,"$"+y(v.total_amount),1),o("span",{class:fe([i.getStatusClass(v.status),"px-2 py-1 rounded-full text-xs font-medium"])},y(i.getStatusText(v.status)),3)])]),o("div",Y0,[K(a,{to:`/events/${(O=v.event)==null?void 0:O.id}`,class:"text-orange-600 hover:text-orange-700 text-sm"},{default:ne(()=>t[11]||(t[11]=[H(" Voir l'événement ")])),_:2,__:[11]},1032,["to"])])])])}),128))]))])])):((f=n.user)==null?void 0:f.role)==="organizer"?(T(),A("div",Z0,t[12]||(t[12]=[qe('<div class="text-center"><h2 class="text-2xl font-bold text-gray-900 mb-4">Dashboard Organisateur</h2><p class="text-gray-600">Gérez vos événements et suivez vos ventes</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="card text-center"><svg class="mx-auto h-12 w-12 text-orange-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg><h3 class="text-lg font-semibold mb-2">Créer un événement</h3><p class="text-gray-600 mb-4">Organisez votre prochain événement culturel</p><button class="btn-primary">Créer un événement</button></div><div class="card text-center"><svg class="mx-auto h-12 w-12 text-blue-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg><h3 class="text-lg font-semibold mb-2">Voir les statistiques</h3><p class="text-gray-600 mb-4">Analysez les performances de vos événements</p><button class="btn-secondary">Voir les stats</button></div></div>',2)]))):((h=n.user)==null?void 0:h.role)==="admin"?(T(),A("div",eh,[t[17]||(t[17]=o("div",{class:"text-center"},[o("h2",{class:"text-2xl font-bold text-gray-900 mb-4"},"Dashboard Administrateur"),o("p",{class:"text-gray-600"},"Gérez la plateforme KabEvents")],-1)),o("div",th,[o("div",sh,[o("div",nh,y(n.adminStats.totalUsers),1),t[13]||(t[13]=o("div",{class:"text-gray-600"},"Utilisateurs",-1))]),o("div",rh,[o("div",oh,y(n.adminStats.totalEvents),1),t[14]||(t[14]=o("div",{class:"text-gray-600"},"Événements",-1))]),o("div",ih,[o("div",lh,y(n.adminStats.totalReservations),1),t[15]||(t[15]=o("div",{class:"text-gray-600"},"Réservations",-1))]),o("div",ah,[o("div",ch,"$"+y(n.adminStats.totalRevenue),1),t[16]||(t[16]=o("div",{class:"text-gray-600"},"Revenus",-1))])])])):me("",!0)])])}const dh=lt(g0,[["render",uh]]),fh={name:"UserDashboard",data(){return{user:null,activeTab:"overview",userStats:{totalReservations:0,upcomingEvents:0,totalSpent:0},reservations:[],upcomingReservations:[],availableEvents:[],profileForm:{name:"",email:"",password:"",password_confirmation:""},loading:!0,updating:!1}},methods:{async fetchData(){try{const e=await this.$http.get("/me");this.user=e.data.user,this.profileForm.name=this.user.name,this.profileForm.email=this.user.email;const t=await this.$http.get("/user/reservations");this.reservations=t.data.data||t.data,this.userStats.totalReservations=this.reservations.length,this.upcomingReservations=this.reservations.filter(r=>{var n;return new Date((n=r.event)==null?void 0:n.event_date)>new Date}),this.userStats.upcomingEvents=this.upcomingReservations.length,this.userStats.totalSpent=this.reservations.filter(r=>r.status==="paid").reduce((r,n)=>r+parseFloat(n.total_amount),0).toFixed(2);const s=await this.$http.get("/events?limit=6");this.availableEvents=s.data.data||s.data}catch(e){console.error("Error fetching user data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},async updateProfile(){this.updating=!0;try{const e=await this.$http.put("/user/profile",this.profileForm);this.user=e.data.user,localStorage.setItem("user",JSON.stringify(this.user)),alert("Profil mis à jour avec succès !"),this.profileForm.password="",this.profileForm.password_confirmation=""}catch(e){console.error("Error updating profile:",e),alert("Erreur lors de la mise à jour du profil")}finally{this.updating=!1}},downloadTicket(e){console.log("Download ticket for reservation:",e),alert("Fonctionnalité de téléchargement à implémenter")},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})},getStatusClass(e){switch(e){case"paid":return"status-active";case"pending":return"bg-secondary-100 text-secondary-800";case"cancelled":return"status-inactive";default:return"bg-accent-100 text-accent-800"}},getStatusText(e){switch(e){case"paid":return"Payé";case"pending":return"En attente";case"cancelled":return"Annulé";default:return e}},getRoleClass(e){switch(e){case"admin":return"bg-danger-100 text-danger-800";case"organizer":return"bg-secondary-100 text-secondary-800";default:return"bg-primary-100 text-primary-800"}},getRoleText(e){switch(e){case"admin":return"Administrateur";case"organizer":return"Organisateur";default:return"Utilisateur"}}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchData()}},ph={class:"min-h-screen"},mh={class:"bg-white shadow-sm"},hh={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},gh={class:"flex justify-between items-center"},vh={class:"text-accent-600 mt-1"},xh={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},bh={class:"mb-8"},yh={class:"flex space-x-8"},wh={key:0,class:"space-y-8"},_h={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},kh={class:"card text-center"},Eh={class:"text-3xl font-bold text-primary-600 mb-2"},Sh={class:"card text-center"},Ch={class:"text-3xl font-bold text-secondary-600 mb-2"},Rh={class:"card text-center"},Th={class:"text-3xl font-bold text-success-600 mb-2"},Ah={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Oh={class:"card"},$h={class:"flex justify-between items-center mb-6"},Mh={key:0,class:"text-center py-8"},Ph={class:"mt-6"},jh={key:1,class:"space-y-4"},Dh={class:"w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center"},Fh={class:"text-white font-bold"},Nh={class:"flex-1"},Ih={class:"font-semibold text-accent-900"},Uh={class:"text-accent-600 text-sm"},Bh={class:"text-accent-500 text-sm"},Vh={class:"text-right"},Lh={class:"text-accent-500 text-sm mt-1"},zh={key:1,class:"space-y-6"},Hh={class:"flex justify-between items-center"},qh={key:0,class:"text-center py-16"},Kh={key:1,class:"text-center py-16"},Wh={class:"mt-6"},Jh={key:2,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Gh={class:"flex items-start space-x-4"},Xh={class:"w-16 h-16 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center flex-shrink-0"},Qh={class:"text-white font-bold text-lg"},Yh={class:"flex-1 min-w-0"},Zh={class:"font-bold text-lg text-accent-900 mb-1"},e1={class:"text-accent-600 text-sm mb-2"},t1={class:"text-accent-500 text-sm mb-3"},s1={class:"flex items-center justify-between mb-4"},n1={class:"flex items-center space-x-4"},r1={class:"text-sm text-accent-600"},o1={class:"font-semibold text-accent-900"},i1={class:"flex space-x-2"},l1=["onClick"],a1={key:2,class:"space-y-6"},c1={class:"flex justify-between items-center"},u1={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},d1={class:"event-image"},f1={class:"text-center"},p1={class:"text-white text-2xl font-bold"},m1={class:"mt-2 text-white/80 text-sm"},h1={class:"p-6"},g1={class:"text-lg font-bold text-accent-900 mb-2"},v1={class:"text-accent-600 text-sm mb-4 line-clamp-2"},x1={class:"flex justify-between items-center mb-4"},b1={class:"price-tag"},y1={class:"text-sm text-accent-500"},w1={key:3,class:"space-y-6"},_1={class:"card max-w-2xl"},k1={class:"flex items-center space-x-6"},E1={class:"w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center"},S1={class:"text-white font-bold text-2xl"},C1={class:"text-lg font-semibold text-accent-900"},R1={class:"text-accent-600"},T1={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},A1={class:"flex justify-end"},O1=["disabled"],$1={key:0,class:"loading-spinner w-4 h-4 mr-2"};function M1(e,t,s,r,n,i){var l,c,d,u,f,h,v;const a=xt("router-link");return T(),A("div",ph,[o("div",mh,[o("div",hh,[o("div",gh,[o("div",null,[t[15]||(t[15]=o("h1",{class:"text-3xl font-bold text-gradient"},"Mon Dashboard",-1)),o("p",vh,"Bienvenue, "+y((l=n.user)==null?void 0:l.name),1)]),t[16]||(t[16]=o("div",{class:"flex items-center space-x-4"},[o("span",{class:"status-badge bg-primary-100 text-primary-800"}," Utilisateur ")],-1))])])]),o("div",xh,[o("div",bh,[o("nav",yh,[o("button",{onClick:t[0]||(t[0]=g=>n.activeTab="overview"),class:fe([n.activeTab==="overview"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Vue d'ensemble ",2),o("button",{onClick:t[1]||(t[1]=g=>n.activeTab="reservations"),class:fe([n.activeTab==="reservations"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Mes réservations ",2),o("button",{onClick:t[2]||(t[2]=g=>n.activeTab="events"),class:fe([n.activeTab==="events"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Événements disponibles ",2),o("button",{onClick:t[3]||(t[3]=g=>n.activeTab="profile"),class:fe([n.activeTab==="profile"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Mon profil ",2)])]),n.activeTab==="overview"?(T(),A("div",wh,[o("div",_h,[o("div",kh,[t[17]||(t[17]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",Eh,y(n.userStats.totalReservations),1),t[18]||(t[18]=o("div",{class:"text-accent-600 font-medium"},"Réservations totales",-1))]),o("div",Sh,[t[19]||(t[19]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",Ch,y(n.userStats.upcomingEvents),1),t[20]||(t[20]=o("div",{class:"text-accent-600 font-medium"},"Événements à venir",-1))]),o("div",Rh,[t[21]||(t[21]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",Th,"$"+y(n.userStats.totalSpent),1),t[22]||(t[22]=o("div",{class:"text-accent-600 font-medium"},"Total dépensé",-1))])]),o("div",Ah,[o("div",{class:"card text-center group cursor-pointer",onClick:t[4]||(t[4]=g=>n.activeTab="events")},t[23]||(t[23]=[qe('<div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-1a8905d9><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-1a8905d9><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" data-v-1a8905d9></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-1a8905d9>Découvrir des événements</h3><p class="text-accent-600 mb-6" data-v-1a8905d9>Explorez les événements culturels kabyles disponibles</p><div class="btn-primary inline-block" data-v-1a8905d9>Explorer</div>',4)])),o("div",{class:"card text-center group cursor-pointer",onClick:t[5]||(t[5]=g=>n.activeTab="reservations")},t[24]||(t[24]=[qe('<div class="w-20 h-20 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-1a8905d9><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-1a8905d9><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" data-v-1a8905d9></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-1a8905d9>Mes réservations</h3><p class="text-accent-600 mb-6" data-v-1a8905d9>Gérez vos billets et réservations d&#39;événements</p><div class="btn-outline inline-block" data-v-1a8905d9>Voir mes billets</div>',4)]))]),o("div",Oh,[o("div",$h,[t[25]||(t[25]=o("h3",{class:"text-xl font-bold text-accent-900"},"Mes prochains événements",-1)),o("button",{onClick:t[6]||(t[6]=g=>n.activeTab="reservations"),class:"text-primary-600 hover:text-primary-700 font-medium"}," Voir tous ")]),n.upcomingReservations.length===0?(T(),A("div",Mh,[t[26]||(t[26]=o("svg",{class:"mx-auto h-12 w-12 text-accent-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),t[27]||(t[27]=o("h3",{class:"mt-2 text-sm font-medium text-accent-900"},"Aucun événement à venir",-1)),t[28]||(t[28]=o("p",{class:"mt-1 text-sm text-accent-500"},"Découvrez et réservez votre prochain événement culturel.",-1)),o("div",Ph,[o("button",{onClick:t[7]||(t[7]=g=>n.activeTab="events"),class:"btn-primary"}," Découvrir des événements ")])])):(T(),A("div",jh,[(T(!0),A(he,null,$e(n.upcomingReservations,g=>{var w,C,O,$;return T(),A("div",{key:g.id,class:"flex items-center space-x-4 p-4 bg-accent-50 rounded-lg hover:bg-accent-100 transition-colors duration-200"},[o("div",Dh,[o("span",Fh,y((w=g.event)==null?void 0:w.title.substring(0,2).toUpperCase()),1)]),o("div",Nh,[o("h4",Ih,y((C=g.event)==null?void 0:C.title),1),o("p",Uh,y((O=g.event)==null?void 0:O.location),1),o("p",Bh,y(i.formatDate(($=g.event)==null?void 0:$.event_date)),1)]),o("div",Vh,[o("span",{class:fe([i.getStatusClass(g.status),"status-badge"])},y(i.getStatusText(g.status)),3),o("p",Lh,y(g.quantity)+" billet(s)",1)])])}),128))]))])])):me("",!0),n.activeTab==="reservations"?(T(),A("div",zh,[o("div",Hh,[t[30]||(t[30]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Mes réservations",-1)),o("button",{onClick:t[8]||(t[8]=g=>n.activeTab="events"),class:"btn-primary"},t[29]||(t[29]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),H(" Nouvelle réservation ")]))]),n.loading?(T(),A("div",qh,t[31]||(t[31]=[o("div",{class:"loading-spinner w-12 h-12 mx-auto"},null,-1),o("p",{class:"text-accent-600 mt-4"},"Chargement de vos réservations...",-1)]))):n.reservations.length===0?(T(),A("div",Kh,[t[32]||(t[32]=o("svg",{class:"mx-auto h-16 w-16 text-accent-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})],-1)),t[33]||(t[33]=o("h3",{class:"mt-4 text-lg font-medium text-accent-900"},"Aucune réservation",-1)),t[34]||(t[34]=o("p",{class:"mt-2 text-accent-500"},"Vous n'avez pas encore réservé d'événements.",-1)),o("div",Wh,[o("button",{onClick:t[9]||(t[9]=g=>n.activeTab="events"),class:"btn-primary"}," Découvrir des événements ")])])):(T(),A("div",Jh,[(T(!0),A(he,null,$e(n.reservations,g=>{var w,C,O,$,D;return T(),A("div",{key:g.id,class:"card"},[o("div",Gh,[o("div",Xh,[o("span",Qh,y((w=g.event)==null?void 0:w.title.substring(0,2).toUpperCase()),1)]),o("div",Yh,[o("h3",Zh,y((C=g.event)==null?void 0:C.title),1),o("p",e1,y((O=g.event)==null?void 0:O.location),1),o("p",t1,y(i.formatDate(($=g.event)==null?void 0:$.event_date)),1),o("div",s1,[o("div",n1,[o("span",r1,y(g.quantity)+" billet(s)",1),o("span",o1,"$"+y(g.total_amount),1)]),o("span",{class:fe([i.getStatusClass(g.status),"status-badge"])},y(i.getStatusText(g.status)),3)]),o("div",i1,[K(a,{to:`/events/${(D=g.event)==null?void 0:D.id}`,class:"btn-outline text-sm px-3 py-1 flex-1 text-center"},{default:ne(()=>t[35]||(t[35]=[H(" Voir l'événement ")])),_:2,__:[35]},1032,["to"]),g.status==="paid"?(T(),A("button",{key:0,onClick:I=>i.downloadTicket(g),class:"btn-primary text-sm px-3 py-1"}," Télécharger ",8,l1)):me("",!0)])])])])}),128))]))])):me("",!0),n.activeTab==="events"?(T(),A("div",a1,[o("div",c1,[t[37]||(t[37]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Événements disponibles",-1)),K(a,{to:"/events",class:"btn-outline"},{default:ne(()=>t[36]||(t[36]=[H(" Voir tous les événements ")])),_:1,__:[36]})]),o("div",u1,[(T(!0),A(he,null,$e(n.availableEvents,g=>(T(),A("div",{key:g.id,class:"event-card"},[o("div",d1,[o("div",f1,[o("span",p1,y(g.title.substring(0,2).toUpperCase()),1),o("div",m1,y(g.location),1)])]),o("div",h1,[o("h3",g1,y(g.title),1),o("p",v1,y(g.description),1),o("div",x1,[o("span",b1,"$"+y(g.ticket_price),1),o("span",y1,y(i.formatDate(g.event_date)),1)]),K(a,{to:`/events/${g.id}`,class:"btn-primary w-full text-center"},{default:ne(()=>t[38]||(t[38]=[H(" Réserver ")])),_:2,__:[38]},1032,["to"])])]))),128))])])):me("",!0),n.activeTab==="profile"?(T(),A("div",w1,[t[43]||(t[43]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Mon profil",-1)),o("div",_1,[o("form",{onSubmit:t[14]||(t[14]=En((...g)=>i.updateProfile&&i.updateProfile(...g),["prevent"])),class:"space-y-6"},[o("div",k1,[o("div",E1,[o("span",S1,y((d=(c=n.user)==null?void 0:c.name)==null?void 0:d.charAt(0).toUpperCase()),1)]),o("div",null,[o("h3",C1,y((u=n.user)==null?void 0:u.name),1),o("p",R1,y((f=n.user)==null?void 0:f.email),1),o("span",{class:fe([i.getRoleClass((h=n.user)==null?void 0:h.role),"status-badge mt-2"])},y(i.getRoleText((v=n.user)==null?void 0:v.role)),3)])]),o("div",T1,[o("div",null,[t[39]||(t[39]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Nom complet",-1)),pe(o("input",{"onUpdate:modelValue":t[10]||(t[10]=g=>n.profileForm.name=g),type:"text",class:"input-field",required:""},null,512),[[be,n.profileForm.name]])]),o("div",null,[t[40]||(t[40]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Email",-1)),pe(o("input",{"onUpdate:modelValue":t[11]||(t[11]=g=>n.profileForm.email=g),type:"email",class:"input-field",required:""},null,512),[[be,n.profileForm.email]])])]),o("div",null,[t[41]||(t[41]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Nouveau mot de passe (optionnel)",-1)),pe(o("input",{"onUpdate:modelValue":t[12]||(t[12]=g=>n.profileForm.password=g),type:"password",class:"input-field",placeholder:"Laissez vide pour ne pas changer"},null,512),[[be,n.profileForm.password]])]),o("div",null,[t[42]||(t[42]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Confirmer le mot de passe",-1)),pe(o("input",{"onUpdate:modelValue":t[13]||(t[13]=g=>n.profileForm.password_confirmation=g),type:"password",class:"input-field"},null,512),[[be,n.profileForm.password_confirmation]])]),o("div",A1,[o("button",{type:"submit",disabled:n.updating,class:"btn-primary"},[n.updating?(T(),A("span",$1)):me("",!0),H(" "+y(n.updating?"Mise à jour...":"Mettre à jour"),1)],8,O1)])],32)])])):me("",!0)])])}const P1=lt(fh,[["render",M1],["__scopeId","data-v-1a8905d9"]]),j1={name:"OrganizerDashboard",data(){return{user:null,activeTab:"overview",organizerStats:{totalEvents:0,totalReservations:0,totalRevenue:0,averageAttendance:0},myEvents:[],recentEvents:[],popularEvents:[],newEvent:{title:"",description:"",location:"",event_date:"",ticket_price:"",max_attendees:""},creating:!1,loading:!0}},methods:{async fetchData(){try{const e=await this.$http.get("/me");this.user=e.data.user;const t=await this.$http.get("/organizer/stats");this.organizerStats=t.data;const s=await this.$http.get("/organizer/events");this.myEvents=s.data.data||s.data,this.recentEvents=this.myEvents.slice(0,5)}catch(e){console.error("Error fetching organizer data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},async createEvent(){this.creating=!0;try{const e=await this.$http.post("/organizer/events",this.newEvent);this.myEvents.unshift(e.data.event),this.resetForm(),this.activeTab="events",alert("Événement créé avec succès !")}catch(e){console.error("Error creating event:",e),alert("Erreur lors de la création de l'événement")}finally{this.creating=!1}},resetForm(){this.newEvent={title:"",description:"",location:"",event_date:"",ticket_price:"",max_attendees:""}},editEvent(e){console.log("Edit event:",e)},viewEventStats(e){console.log("View event stats:",e)},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchData()}},D1={class:"min-h-screen"},F1={class:"bg-white shadow-sm"},N1={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},I1={class:"flex justify-between items-center"},U1={class:"text-accent-600 mt-1"},B1={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},V1={class:"mb-8"},L1={class:"flex space-x-8"},z1={key:0,class:"space-y-8"},H1={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},q1={class:"card text-center"},K1={class:"text-3xl font-bold text-primary-600 mb-2"},W1={class:"card text-center"},J1={class:"text-3xl font-bold text-secondary-600 mb-2"},G1={class:"card text-center"},X1={class:"text-3xl font-bold text-success-600 mb-2"},Q1={class:"card text-center"},Y1={class:"text-3xl font-bold text-accent-600 mb-2"},Z1={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},eg={class:"card"},tg={class:"flex justify-between items-center mb-6"},sg={class:"space-y-4"},ng={class:"w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center"},rg={class:"text-white font-bold"},og={class:"flex-1"},ig={class:"font-semibold text-accent-900"},lg={class:"text-accent-600 text-sm"},ag={class:"text-right"},cg={class:"font-semibold text-accent-900"},ug={class:"text-accent-500 text-sm"},dg={key:1,class:"space-y-6"},fg={class:"flex justify-between items-center"},pg={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},mg={class:"event-image"},hg={class:"text-center"},gg={class:"text-white text-2xl font-bold"},vg={class:"mt-2 text-white/80 text-sm"},xg={class:"p-6"},bg={class:"flex justify-between items-start mb-3"},yg={class:"text-lg font-bold text-accent-900"},wg={class:"text-accent-600 text-sm mb-4 line-clamp-2"},_g={class:"flex justify-between items-center mb-4"},kg={class:"price-tag"},Eg={class:"text-sm text-accent-500"},Sg={class:"flex space-x-2"},Cg=["onClick"],Rg=["onClick"],Tg={key:2,class:"space-y-6"},Ag={class:"card max-w-4xl"},Og={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},$g={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Mg={class:"flex justify-end space-x-4"},Pg=["disabled"],jg={key:0,class:"loading-spinner w-4 h-4 mr-2"},Dg={key:3,class:"space-y-6"},Fg={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Ng={class:"card"},Ig={class:"space-y-3"},Ug={class:"font-medium text-accent-900"},Bg={class:"text-sm text-accent-500"},Vg={class:"text-right"},Lg={class:"font-semibold text-success-600"};function zg(e,t,s,r,n,i){var a;return T(),A("div",D1,[o("div",F1,[o("div",N1,[o("div",I1,[o("div",null,[t[16]||(t[16]=o("h1",{class:"text-3xl font-bold text-gradient"},"Dashboard Organisateur",-1)),o("p",U1,"Bienvenue, "+y((a=n.user)==null?void 0:a.name),1)]),t[17]||(t[17]=o("div",{class:"flex items-center space-x-4"},[o("span",{class:"status-badge bg-secondary-100 text-secondary-800"}," Organisateur ")],-1))])])]),o("div",B1,[o("div",V1,[o("nav",L1,[o("button",{onClick:t[0]||(t[0]=l=>n.activeTab="overview"),class:fe([n.activeTab==="overview"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Vue d'ensemble ",2),o("button",{onClick:t[1]||(t[1]=l=>n.activeTab="events"),class:fe([n.activeTab==="events"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Mes événements ",2),o("button",{onClick:t[2]||(t[2]=l=>n.activeTab="create"),class:fe([n.activeTab==="create"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Créer un événement ",2),o("button",{onClick:t[3]||(t[3]=l=>n.activeTab="analytics"),class:fe([n.activeTab==="analytics"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Statistiques ",2)])]),n.activeTab==="overview"?(T(),A("div",z1,[o("div",H1,[o("div",q1,[t[18]||(t[18]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",K1,y(n.organizerStats.totalEvents),1),t[19]||(t[19]=o("div",{class:"text-accent-600 font-medium"},"Événements créés",-1))]),o("div",W1,[t[20]||(t[20]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",J1,y(n.organizerStats.totalReservations),1),t[21]||(t[21]=o("div",{class:"text-accent-600 font-medium"},"Réservations",-1))]),o("div",G1,[t[22]||(t[22]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",X1,"$"+y(n.organizerStats.totalRevenue),1),t[23]||(t[23]=o("div",{class:"text-accent-600 font-medium"},"Revenus",-1))]),o("div",Q1,[t[24]||(t[24]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),o("div",Y1,y(n.organizerStats.averageAttendance)+"%",1),t[25]||(t[25]=o("div",{class:"text-accent-600 font-medium"},"Taux de participation",-1))])]),o("div",Z1,[o("div",{class:"card text-center group cursor-pointer",onClick:t[4]||(t[4]=l=>n.activeTab="create")},t[26]||(t[26]=[qe('<div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-c8d51439><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-c8d51439><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" data-v-c8d51439></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-c8d51439>Créer un événement</h3><p class="text-accent-600 mb-6" data-v-c8d51439>Organisez votre prochain événement culturel kabyle</p><div class="btn-primary inline-block" data-v-c8d51439>Commencer</div>',4)])),o("div",{class:"card text-center group cursor-pointer",onClick:t[5]||(t[5]=l=>n.activeTab="analytics")},t[27]||(t[27]=[qe('<div class="w-20 h-20 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-c8d51439><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-c8d51439><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" data-v-c8d51439></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-c8d51439>Voir les statistiques</h3><p class="text-accent-600 mb-6" data-v-c8d51439>Analysez les performances de vos événements</p><div class="btn-outline inline-block" data-v-c8d51439>Analyser</div>',4)]))]),o("div",eg,[o("div",tg,[t[28]||(t[28]=o("h3",{class:"text-xl font-bold text-accent-900"},"Événements récents",-1)),o("button",{onClick:t[6]||(t[6]=l=>n.activeTab="events"),class:"text-primary-600 hover:text-primary-700 font-medium"}," Voir tous ")]),o("div",sg,[(T(!0),A(he,null,$e(n.recentEvents,l=>(T(),A("div",{key:l.id,class:"flex items-center space-x-4 p-4 bg-accent-50 rounded-lg hover:bg-accent-100 transition-colors duration-200"},[o("div",ng,[o("span",rg,y(l.title.substring(0,2).toUpperCase()),1)]),o("div",og,[o("h4",ig,y(l.title),1),o("p",lg,y(i.formatDate(l.event_date)),1)]),o("div",ag,[o("p",cg,"$"+y(l.ticket_price),1),o("p",ug,y(l.reservations_count||0)+" réservations",1)])]))),128))])])])):me("",!0),n.activeTab==="events"?(T(),A("div",dg,[o("div",fg,[t[30]||(t[30]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Mes événements",-1)),o("button",{onClick:t[7]||(t[7]=l=>n.activeTab="create"),class:"btn-primary"},t[29]||(t[29]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),H(" Nouvel événement ")]))]),o("div",pg,[(T(!0),A(he,null,$e(n.myEvents,l=>(T(),A("div",{key:l.id,class:"event-card"},[o("div",mg,[o("div",hg,[o("span",gg,y(l.title.substring(0,2).toUpperCase()),1),o("div",vg,y(l.location),1)])]),o("div",xg,[o("div",bg,[o("h3",yg,y(l.title),1),o("span",{class:fe([l.status==="active"?"status-active":"status-inactive","status-badge"])},y(l.status==="active"?"Actif":"Inactif"),3)]),o("p",wg,y(l.description),1),o("div",_g,[o("span",kg,"$"+y(l.ticket_price),1),o("span",Eg,y(i.formatDate(l.event_date)),1)]),o("div",Sg,[o("button",{onClick:c=>i.editEvent(l),class:"btn-primary text-sm px-3 py-1 flex-1"},"Modifier",8,Cg),o("button",{onClick:c=>i.viewEventStats(l),class:"btn-outline text-sm px-3 py-1"},"Stats",8,Rg)])])]))),128))])])):me("",!0),n.activeTab==="create"?(T(),A("div",Tg,[t[37]||(t[37]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Créer un nouvel événement",-1)),o("div",Ag,[o("form",{onSubmit:t[15]||(t[15]=En((...l)=>i.createEvent&&i.createEvent(...l),["prevent"])),class:"space-y-6"},[o("div",Og,[o("div",null,[t[31]||(t[31]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Titre de l'événement",-1)),pe(o("input",{"onUpdate:modelValue":t[8]||(t[8]=l=>n.newEvent.title=l),type:"text",class:"input-field",placeholder:"Ex: Festival de musique kabyle",required:""},null,512),[[be,n.newEvent.title]])]),o("div",null,[t[32]||(t[32]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Lieu",-1)),pe(o("input",{"onUpdate:modelValue":t[9]||(t[9]=l=>n.newEvent.location=l),type:"text",class:"input-field",placeholder:"Ex: Centre culturel, Montréal",required:""},null,512),[[be,n.newEvent.location]])])]),o("div",null,[t[33]||(t[33]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Description",-1)),pe(o("textarea",{"onUpdate:modelValue":t[10]||(t[10]=l=>n.newEvent.description=l),rows:"4",class:"input-field",placeholder:"Décrivez votre événement...",required:""},null,512),[[be,n.newEvent.description]])]),o("div",$g,[o("div",null,[t[34]||(t[34]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Date et heure",-1)),pe(o("input",{"onUpdate:modelValue":t[11]||(t[11]=l=>n.newEvent.event_date=l),type:"datetime-local",class:"input-field",required:""},null,512),[[be,n.newEvent.event_date]])]),o("div",null,[t[35]||(t[35]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Prix du billet ($)",-1)),pe(o("input",{"onUpdate:modelValue":t[12]||(t[12]=l=>n.newEvent.ticket_price=l),type:"number",step:"0.01",class:"input-field",placeholder:"25.00",required:""},null,512),[[be,n.newEvent.ticket_price]])]),o("div",null,[t[36]||(t[36]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Nombre de places",-1)),pe(o("input",{"onUpdate:modelValue":t[13]||(t[13]=l=>n.newEvent.max_attendees=l),type:"number",class:"input-field",placeholder:"100",required:""},null,512),[[be,n.newEvent.max_attendees]])])]),o("div",Mg,[o("button",{type:"button",onClick:t[14]||(t[14]=(...l)=>i.resetForm&&i.resetForm(...l)),class:"btn-outline"},"Annuler"),o("button",{type:"submit",disabled:n.creating,class:"btn-primary"},[n.creating?(T(),A("span",jg)):me("",!0),H(" "+y(n.creating?"Création...":"Créer l'événement"),1)],8,Pg)])],32)])])):me("",!0),n.activeTab==="analytics"?(T(),A("div",Dg,[t[40]||(t[40]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Statistiques et analyses",-1)),o("div",Fg,[t[39]||(t[39]=o("div",{class:"card"},[o("h3",{class:"text-lg font-bold text-accent-900 mb-4"},"Revenus par mois"),o("div",{class:"h-64 flex items-center justify-center bg-accent-50 rounded-lg"},[o("p",{class:"text-accent-500"},"Graphique des revenus (à implémenter)")])],-1)),o("div",Ng,[t[38]||(t[38]=o("h3",{class:"text-lg font-bold text-accent-900 mb-4"},"Événements populaires",-1)),o("div",Ig,[(T(!0),A(he,null,$e(n.popularEvents,l=>(T(),A("div",{key:l.id,class:"flex justify-between items-center p-3 bg-accent-50 rounded-lg"},[o("div",null,[o("p",Ug,y(l.title),1),o("p",Bg,y(l.reservations_count)+" réservations",1)]),o("div",Vg,[o("p",Lg,"$"+y(l.revenue),1)])]))),128))])])])])):me("",!0)])])}const Hg=lt(j1,[["render",zg],["__scopeId","data-v-c8d51439"]]),qg={name:"AdminDashboard",data(){return{user:null,activeTab:"overview",adminStats:{totalUsers:0,totalEvents:0,totalReservations:0,totalRevenue:0},users:[],events:[],reservations:[],recentActivity:[],loading:!0,showCreateUserModal:!1}},methods:{async fetchData(){try{const e=await this.$http.get("/me");this.user=e.data.user;const t=await this.$http.get("/admin/stats");this.adminStats=t.data;const s=await this.$http.get("/admin/users");this.users=s.data.data||s.data;const r=await this.$http.get("/admin/events");this.events=r.data.data||r.data;const n=await this.$http.get("/admin/reservations");this.reservations=n.data.data||n.data}catch(e){console.error("Error fetching admin data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"short",day:"numeric"})},getRoleClass(e){switch(e){case"admin":return"bg-danger-100 text-danger-800";case"organizer":return"bg-secondary-100 text-secondary-800";default:return"bg-primary-100 text-primary-800"}},getRoleText(e){switch(e){case"admin":return"Administrateur";case"organizer":return"Organisateur";default:return"Utilisateur"}},getStatusClass(e){switch(e){case"paid":return"status-active";case"pending":return"bg-secondary-100 text-secondary-800";case"cancelled":return"status-inactive";default:return"bg-accent-100 text-accent-800"}},getStatusText(e){switch(e){case"paid":return"Payé";case"pending":return"En attente";case"cancelled":return"Annulé";default:return e}},editUser(e){console.log("Edit user:",e)},deleteUser(e){console.log("Delete user:",e)},editEvent(e){console.log("Edit event:",e)},deleteEvent(e){console.log("Delete event:",e)},viewReservation(e){console.log("View reservation:",e)}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchData()}},Kg={class:"min-h-screen"},Wg={class:"bg-white shadow-sm"},Jg={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},Gg={class:"flex justify-between items-center"},Xg={class:"text-accent-600 mt-1"},Qg={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Yg={class:"mb-8"},Zg={class:"flex space-x-8"},e2={key:0,class:"space-y-8"},t2={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},s2={class:"card text-center"},n2={class:"text-3xl font-bold text-primary-600 mb-2"},r2={class:"card text-center"},o2={class:"text-3xl font-bold text-secondary-600 mb-2"},i2={class:"card text-center"},l2={class:"text-3xl font-bold text-success-600 mb-2"},a2={class:"card text-center"},c2={class:"text-3xl font-bold text-accent-600 mb-2"},u2={class:"card"},d2={class:"space-y-4"},f2={class:"flex-1"},p2={class:"text-accent-900 font-medium"},m2={class:"text-accent-500 text-sm"},h2={key:1,class:"space-y-6"},g2={class:"flex justify-between items-center"},v2={class:"card"},x2={class:"overflow-x-auto"},b2={class:"min-w-full divide-y divide-accent-200"},y2={class:"bg-white divide-y divide-accent-200"},w2={class:"px-6 py-4 whitespace-nowrap"},_2={class:"flex items-center"},k2={class:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center"},E2={class:"text-primary-600 font-medium"},S2={class:"ml-4"},C2={class:"text-sm font-medium text-accent-900"},R2={class:"text-sm text-accent-500"},T2={class:"px-6 py-4 whitespace-nowrap"},A2={class:"px-6 py-4 whitespace-nowrap"},O2={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-500"},$2={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},M2=["onClick"],P2=["onClick"],j2={key:2,class:"space-y-6"},D2={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},F2={class:"event-image"},N2={class:"text-center"},I2={class:"text-white text-2xl font-bold"},U2={class:"p-6"},B2={class:"text-lg font-bold text-accent-900 mb-2"},V2={class:"text-accent-600 text-sm mb-4"},L2={class:"flex justify-between items-center mb-4"},z2={class:"price-tag"},H2={class:"flex space-x-2"},q2=["onClick"],K2=["onClick"],W2={key:3,class:"space-y-6"},J2={class:"card"},G2={class:"overflow-x-auto"},X2={class:"min-w-full divide-y divide-accent-200"},Q2={class:"bg-white divide-y divide-accent-200"},Y2={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-accent-900"},Z2={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-900"},ev={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-900"},tv={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-900"},sv={class:"px-6 py-4 whitespace-nowrap"},nv={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},rv=["onClick"];function ov(e,t,s,r,n,i){var a;return T(),A("div",Kg,[o("div",Wg,[o("div",Jg,[o("div",Gg,[o("div",null,[t[5]||(t[5]=o("h1",{class:"text-3xl font-bold text-gradient"},"Dashboard Administrateur",-1)),o("p",Xg,"Bienvenue, "+y((a=n.user)==null?void 0:a.name),1)]),t[6]||(t[6]=o("div",{class:"flex items-center space-x-4"},[o("span",{class:"status-badge bg-danger-100 text-danger-800"}," Administrateur ")],-1))])])]),o("div",Qg,[o("div",Yg,[o("nav",Zg,[o("button",{onClick:t[0]||(t[0]=l=>n.activeTab="overview"),class:fe([n.activeTab==="overview"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Vue d'ensemble ",2),o("button",{onClick:t[1]||(t[1]=l=>n.activeTab="users"),class:fe([n.activeTab==="users"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Utilisateurs ",2),o("button",{onClick:t[2]||(t[2]=l=>n.activeTab="events"),class:fe([n.activeTab==="events"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Événements ",2),o("button",{onClick:t[3]||(t[3]=l=>n.activeTab="reservations"),class:fe([n.activeTab==="reservations"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Réservations ",2)])]),n.activeTab==="overview"?(T(),A("div",e2,[o("div",t2,[o("div",s2,[t[7]||(t[7]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})])],-1)),o("div",n2,y(n.adminStats.totalUsers),1),t[8]||(t[8]=o("div",{class:"text-accent-600 font-medium"},"Utilisateurs",-1))]),o("div",r2,[t[9]||(t[9]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",o2,y(n.adminStats.totalEvents),1),t[10]||(t[10]=o("div",{class:"text-accent-600 font-medium"},"Événements",-1))]),o("div",i2,[t[11]||(t[11]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",l2,y(n.adminStats.totalReservations),1),t[12]||(t[12]=o("div",{class:"text-accent-600 font-medium"},"Réservations",-1))]),o("div",a2,[t[13]||(t[13]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",c2,"$"+y(n.adminStats.totalRevenue),1),t[14]||(t[14]=o("div",{class:"text-accent-600 font-medium"},"Revenus",-1))])]),o("div",u2,[t[16]||(t[16]=o("h3",{class:"text-xl font-bold text-accent-900 mb-6"},"Activité récente",-1)),o("div",d2,[(T(!0),A(he,null,$e(n.recentActivity,l=>(T(),A("div",{key:l.id,class:"flex items-center space-x-4 p-4 bg-accent-50 rounded-lg"},[t[15]||(t[15]=o("div",{class:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center"},[o("svg",{class:"w-5 h-5 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),o("div",f2,[o("p",p2,y(l.description),1),o("p",m2,y(i.formatDate(l.created_at)),1)])]))),128))])])])):me("",!0),n.activeTab==="users"?(T(),A("div",h2,[o("div",g2,[t[18]||(t[18]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Gestion des utilisateurs",-1)),o("button",{onClick:t[4]||(t[4]=l=>n.showCreateUserModal=!0),class:"btn-primary"},t[17]||(t[17]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),H(" Nouvel utilisateur ")]))]),o("div",v2,[o("div",x2,[o("table",b2,[t[19]||(t[19]=o("thead",{class:"bg-accent-50"},[o("tr",null,[o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Utilisateur"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Rôle"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Statut"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Inscrit le"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Actions")])],-1)),o("tbody",y2,[(T(!0),A(he,null,$e(n.users,l=>(T(),A("tr",{key:l.id,class:"hover:bg-accent-50"},[o("td",w2,[o("div",_2,[o("div",k2,[o("span",E2,y(l.name.charAt(0).toUpperCase()),1)]),o("div",S2,[o("div",C2,y(l.name),1),o("div",R2,y(l.email),1)])])]),o("td",T2,[o("span",{class:fe([i.getRoleClass(l.role),"status-badge"])},y(i.getRoleText(l.role)),3)]),o("td",A2,[o("span",{class:fe([l.email_verified_at?"status-active":"status-inactive","status-badge"])},y(l.email_verified_at?"Vérifié":"Non vérifié"),3)]),o("td",O2,y(i.formatDate(l.created_at)),1),o("td",$2,[o("button",{onClick:c=>i.editUser(l),class:"text-primary-600 hover:text-primary-900 mr-3"},"Modifier",8,M2),o("button",{onClick:c=>i.deleteUser(l),class:"text-danger-600 hover:text-danger-900"},"Supprimer",8,P2)])]))),128))])])])])])):me("",!0),n.activeTab==="events"?(T(),A("div",j2,[t[20]||(t[20]=o("div",{class:"flex justify-between items-center"},[o("h2",{class:"text-2xl font-bold text-accent-900"},"Gestion des événements")],-1)),o("div",D2,[(T(!0),A(he,null,$e(n.events,l=>(T(),A("div",{key:l.id,class:"event-card"},[o("div",F2,[o("div",N2,[o("span",I2,y(l.title.substring(0,2).toUpperCase()),1)])]),o("div",U2,[o("h3",B2,y(l.title),1),o("p",V2,y(l.description.substring(0,100))+"...",1),o("div",L2,[o("span",z2,"$"+y(l.ticket_price),1),o("span",{class:fe([l.status==="active"?"status-active":"status-inactive","status-badge"])},y(l.status==="active"?"Actif":"Inactif"),3)]),o("div",H2,[o("button",{onClick:c=>i.editEvent(l),class:"btn-primary text-sm px-3 py-1 flex-1"},"Modifier",8,q2),o("button",{onClick:c=>i.deleteEvent(l),class:"btn-outline text-sm px-3 py-1"},"Supprimer",8,K2)])])]))),128))])])):me("",!0),n.activeTab==="reservations"?(T(),A("div",W2,[t[22]||(t[22]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Gestion des réservations",-1)),o("div",J2,[o("div",G2,[o("table",X2,[t[21]||(t[21]=o("thead",{class:"bg-accent-50"},[o("tr",null,[o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Réservation"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Utilisateur"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Événement"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Montant"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Statut"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Actions")])],-1)),o("tbody",Q2,[(T(!0),A(he,null,$e(n.reservations,l=>{var c,d;return T(),A("tr",{key:l.id,class:"hover:bg-accent-50"},[o("td",Y2," #"+y(l.id),1),o("td",Z2,y((c=l.user)==null?void 0:c.name),1),o("td",ev,y((d=l.event)==null?void 0:d.title),1),o("td",tv," $"+y(l.total_amount),1),o("td",sv,[o("span",{class:fe([i.getStatusClass(l.status),"status-badge"])},y(i.getStatusText(l.status)),3)]),o("td",nv,[o("button",{onClick:u=>i.viewReservation(l),class:"text-primary-600 hover:text-primary-900"},"Voir",8,rv)])])}),128))])])])])])):me("",!0)])])}const iv=lt(qg,[["render",ov]]);de.defaults.baseURL="/api";de.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";const Lo=localStorage.getItem("auth_token");Lo&&(de.defaults.headers.common.Authorization=`Bearer ${Lo}`);const lv=[{path:"/",name:"home",component:Fp},{path:"/login",name:"login",component:Jp},{path:"/register",name:"register",component:om},{path:"/events",name:"events",component:Cm},{path:"/events/:id",name:"event-detail",component:h0,props:!0},{path:"/dashboard",name:"dashboard",component:dh,meta:{requiresAuth:!0}},{path:"/dashboard/user",name:"user-dashboard",component:P1,meta:{requiresAuth:!0,role:"user"}},{path:"/dashboard/organizer",name:"organizer-dashboard",component:Hg,meta:{requiresAuth:!0,role:"organizer"}},{path:"/dashboard/admin",name:"admin-dashboard",component:iv,meta:{requiresAuth:!0,role:"admin"}}],Pl=Df({history:uf(),routes:lv});Pl.beforeEach((e,t,s)=>{const r=localStorage.getItem("auth_token"),n=localStorage.getItem("user"),i=n?JSON.parse(n):null;if(e.meta.requiresAuth&&!r){s("/login");return}if(e.path==="/dashboard"&&i)switch(i.role){case"admin":s("/dashboard/admin");return;case"organizer":s("/dashboard/organizer");return;case"user":s("/dashboard/user");return;default:s("/dashboard/user");return}if(e.meta.role&&i&&i.role!==e.meta.role)switch(i.role){case"admin":s("/dashboard/admin");return;case"organizer":s("/dashboard/organizer");return;case"user":s("/dashboard/user");return;default:s("/");return}s()});const Or=Od(ip);Or.use(Pl);Or.config.globalProperties.$http=de;Or.mount("#app");
