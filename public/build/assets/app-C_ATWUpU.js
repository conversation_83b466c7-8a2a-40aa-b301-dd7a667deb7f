function Vo(e,t){return function(){return e.apply(t,arguments)}}const{toString:jl}=Object.prototype,{getPrototypeOf:or}=Object,{iterator:nn,toStringTag:Ho}=Symbol,rn=(e=>t=>{const s=jl.call(t);return e[s]||(e[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Ve=e=>(e=e.toLowerCase(),t=>rn(t)===e),on=e=>t=>typeof t===e,{isArray:Zt}=Array,xs=on("undefined");function Dl(e){return e!==null&&!xs(e)&&e.constructor!==null&&!xs(e.constructor)&&ke(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const zo=Ve("ArrayBuffer");function Nl(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&zo(e.buffer),t}const Fl=on("string"),ke=on("function"),qo=on("number"),ln=e=>e!==null&&typeof e=="object",Il=e=>e===!0||e===!1,Fs=e=>{if(rn(e)!=="object")return!1;const t=or(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ho in e)&&!(nn in e)},Ll=Ve("Date"),Bl=Ve("File"),Ul=Ve("Blob"),Vl=Ve("FileList"),Hl=e=>ln(e)&&ke(e.pipe),zl=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||ke(e.append)&&((t=rn(e))==="formdata"||t==="object"&&ke(e.toString)&&e.toString()==="[object FormData]"))},ql=Ve("URLSearchParams"),[Kl,Wl,Jl,Gl]=["ReadableStream","Request","Response","Headers"].map(Ve),Xl=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ks(e,t,{allOwnKeys:s=!1}={}){if(e===null||typeof e>"u")return;let n,r;if(typeof e!="object"&&(e=[e]),Zt(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const o=s?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(n=0;n<i;n++)l=o[n],t.call(null,e[l],l,e)}}function Ko(e,t){t=t.toLowerCase();const s=Object.keys(e);let n=s.length,r;for(;n-- >0;)if(r=s[n],t===r.toLowerCase())return r;return null}const Ot=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Wo=e=>!xs(e)&&e!==Ot;function Bn(){const{caseless:e}=Wo(this)&&this||{},t={},s=(n,r)=>{const o=e&&Ko(t,r)||r;Fs(t[o])&&Fs(n)?t[o]=Bn(t[o],n):Fs(n)?t[o]=Bn({},n):Zt(n)?t[o]=n.slice():t[o]=n};for(let n=0,r=arguments.length;n<r;n++)arguments[n]&&ks(arguments[n],s);return t}const Ql=(e,t,s,{allOwnKeys:n}={})=>(ks(t,(r,o)=>{s&&ke(r)?e[o]=Vo(r,s):e[o]=r},{allOwnKeys:n}),e),Yl=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Zl=(e,t,s,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),s&&Object.assign(e.prototype,s)},ea=(e,t,s,n)=>{let r,o,i;const l={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),o=r.length;o-- >0;)i=r[o],(!n||n(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=s!==!1&&or(e)}while(e&&(!s||s(e,t))&&e!==Object.prototype);return t},ta=(e,t,s)=>{e=String(e),(s===void 0||s>e.length)&&(s=e.length),s-=t.length;const n=e.indexOf(t,s);return n!==-1&&n===s},sa=e=>{if(!e)return null;if(Zt(e))return e;let t=e.length;if(!qo(t))return null;const s=new Array(t);for(;t-- >0;)s[t]=e[t];return s},na=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&or(Uint8Array)),ra=(e,t)=>{const n=(e&&e[nn]).call(e);let r;for(;(r=n.next())&&!r.done;){const o=r.value;t.call(e,o[0],o[1])}},oa=(e,t)=>{let s;const n=[];for(;(s=e.exec(t))!==null;)n.push(s);return n},ia=Ve("HTMLFormElement"),la=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,n,r){return n.toUpperCase()+r}),Mr=(({hasOwnProperty:e})=>(t,s)=>e.call(t,s))(Object.prototype),aa=Ve("RegExp"),Jo=(e,t)=>{const s=Object.getOwnPropertyDescriptors(e),n={};ks(s,(r,o)=>{let i;(i=t(r,o,e))!==!1&&(n[o]=i||r)}),Object.defineProperties(e,n)},ca=e=>{Jo(e,(t,s)=>{if(ke(e)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const n=e[s];if(ke(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},ua=(e,t)=>{const s={},n=r=>{r.forEach(o=>{s[o]=!0})};return Zt(e)?n(e):n(String(e).split(t)),s},fa=()=>{},da=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function pa(e){return!!(e&&ke(e.append)&&e[Ho]==="FormData"&&e[nn])}const ha=e=>{const t=new Array(10),s=(n,r)=>{if(ln(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[r]=n;const o=Zt(n)?[]:{};return ks(n,(i,l)=>{const a=s(i,r+1);!xs(a)&&(o[l]=a)}),t[r]=void 0,o}}return n};return s(e,0)},ma=Ve("AsyncFunction"),ga=e=>e&&(ln(e)||ke(e))&&ke(e.then)&&ke(e.catch),Go=((e,t)=>e?setImmediate:t?((s,n)=>(Ot.addEventListener("message",({source:r,data:o})=>{r===Ot&&o===s&&n.length&&n.shift()()},!1),r=>{n.push(r),Ot.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",ke(Ot.postMessage)),va=typeof queueMicrotask<"u"?queueMicrotask.bind(Ot):typeof process<"u"&&process.nextTick||Go,xa=e=>e!=null&&ke(e[nn]),x={isArray:Zt,isArrayBuffer:zo,isBuffer:Dl,isFormData:zl,isArrayBufferView:Nl,isString:Fl,isNumber:qo,isBoolean:Il,isObject:ln,isPlainObject:Fs,isReadableStream:Kl,isRequest:Wl,isResponse:Jl,isHeaders:Gl,isUndefined:xs,isDate:Ll,isFile:Bl,isBlob:Ul,isRegExp:aa,isFunction:ke,isStream:Hl,isURLSearchParams:ql,isTypedArray:na,isFileList:Vl,forEach:ks,merge:Bn,extend:Ql,trim:Xl,stripBOM:Yl,inherits:Zl,toFlatObject:ea,kindOf:rn,kindOfTest:Ve,endsWith:ta,toArray:sa,forEachEntry:ra,matchAll:oa,isHTMLForm:ia,hasOwnProperty:Mr,hasOwnProp:Mr,reduceDescriptors:Jo,freezeMethods:ca,toObjectSet:ua,toCamelCase:la,noop:fa,toFiniteNumber:da,findKey:Ko,global:Ot,isContextDefined:Wo,isSpecCompliantForm:pa,toJSONObject:ha,isAsyncFn:ma,isThenable:ga,setImmediate:Go,asap:va,isIterable:xa};function W(e,t,s,n,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),s&&(this.config=s),n&&(this.request=n),r&&(this.response=r,this.status=r.status?r.status:null)}x.inherits(W,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:x.toJSONObject(this.config),code:this.code,status:this.status}}});const Xo=W.prototype,Qo={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Qo[e]={value:e}});Object.defineProperties(W,Qo);Object.defineProperty(Xo,"isAxiosError",{value:!0});W.from=(e,t,s,n,r,o)=>{const i=Object.create(Xo);return x.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),W.call(i,e.message,t,s,n,r),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const ya=null;function Un(e){return x.isPlainObject(e)||x.isArray(e)}function Yo(e){return x.endsWith(e,"[]")?e.slice(0,-2):e}function $r(e,t,s){return e?e.concat(t).map(function(r,o){return r=Yo(r),!s&&o?"["+r+"]":r}).join(s?".":""):t}function ba(e){return x.isArray(e)&&!e.some(Un)}const wa=x.toFlatObject(x,{},null,function(t){return/^is[A-Z]/.test(t)});function an(e,t,s){if(!x.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,s=x.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(E,R){return!x.isUndefined(R[E])});const n=s.metaTokens,r=s.visitor||u,o=s.dots,i=s.indexes,a=(s.Blob||typeof Blob<"u"&&Blob)&&x.isSpecCompliantForm(t);if(!x.isFunction(r))throw new TypeError("visitor must be a function");function f(y){if(y===null)return"";if(x.isDate(y))return y.toISOString();if(!a&&x.isBlob(y))throw new W("Blob is not supported. Use a Buffer instead.");return x.isArrayBuffer(y)||x.isTypedArray(y)?a&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function u(y,E,R){let O=y;if(y&&!R&&typeof y=="object"){if(x.endsWith(E,"{}"))E=n?E:E.slice(0,-2),y=JSON.stringify(y);else if(x.isArray(y)&&ba(y)||(x.isFileList(y)||x.endsWith(E,"[]"))&&(O=x.toArray(y)))return E=Yo(E),O.forEach(function(j,D){!(x.isUndefined(j)||j===null)&&t.append(i===!0?$r([E],D,o):i===null?E:E+"[]",f(j))}),!1}return Un(y)?!0:(t.append($r(R,E,o),f(y)),!1)}const d=[],m=Object.assign(wa,{defaultVisitor:u,convertValue:f,isVisitable:Un});function g(y,E){if(!x.isUndefined(y)){if(d.indexOf(y)!==-1)throw Error("Circular reference detected in "+E.join("."));d.push(y),x.forEach(y,function(O,T){(!(x.isUndefined(O)||O===null)&&r.call(t,O,x.isString(T)?T.trim():T,E,m))===!0&&g(O,E?E.concat(T):[T])}),d.pop()}}if(!x.isObject(e))throw new TypeError("data must be an object");return g(e),t}function jr(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function ir(e,t){this._pairs=[],e&&an(e,this,t)}const Zo=ir.prototype;Zo.append=function(t,s){this._pairs.push([t,s])};Zo.toString=function(t){const s=t?function(n){return t.call(this,n,jr)}:jr;return this._pairs.map(function(r){return s(r[0])+"="+s(r[1])},"").join("&")};function _a(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ei(e,t,s){if(!t)return e;const n=s&&s.encode||_a;x.isFunction(s)&&(s={serialize:s});const r=s&&s.serialize;let o;if(r?o=r(t,s):o=x.isURLSearchParams(t)?t.toString():new ir(t,s).toString(n),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Dr{constructor(){this.handlers=[]}use(t,s,n){return this.handlers.push({fulfilled:t,rejected:s,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){x.forEach(this.handlers,function(n){n!==null&&t(n)})}}const ti={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ea=typeof URLSearchParams<"u"?URLSearchParams:ir,Sa=typeof FormData<"u"?FormData:null,Ra=typeof Blob<"u"?Blob:null,Ca={isBrowser:!0,classes:{URLSearchParams:Ea,FormData:Sa,Blob:Ra},protocols:["http","https","file","blob","url","data"]},lr=typeof window<"u"&&typeof document<"u",Vn=typeof navigator=="object"&&navigator||void 0,ka=lr&&(!Vn||["ReactNative","NativeScript","NS"].indexOf(Vn.product)<0),Aa=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Oa=lr&&window.location.href||"http://localhost",Ta=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:lr,hasStandardBrowserEnv:ka,hasStandardBrowserWebWorkerEnv:Aa,navigator:Vn,origin:Oa},Symbol.toStringTag,{value:"Module"})),ge={...Ta,...Ca};function Pa(e,t){return an(e,new ge.classes.URLSearchParams,Object.assign({visitor:function(s,n,r,o){return ge.isNode&&x.isBuffer(s)?(this.append(n,s.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Ma(e){return x.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function $a(e){const t={},s=Object.keys(e);let n;const r=s.length;let o;for(n=0;n<r;n++)o=s[n],t[o]=e[o];return t}function si(e){function t(s,n,r,o){let i=s[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=o>=s.length;return i=!i&&x.isArray(r)?r.length:i,a?(x.hasOwnProp(r,i)?r[i]=[r[i],n]:r[i]=n,!l):((!r[i]||!x.isObject(r[i]))&&(r[i]=[]),t(s,n,r[i],o)&&x.isArray(r[i])&&(r[i]=$a(r[i])),!l)}if(x.isFormData(e)&&x.isFunction(e.entries)){const s={};return x.forEachEntry(e,(n,r)=>{t(Ma(n),r,s,0)}),s}return null}function ja(e,t,s){if(x.isString(e))try{return(t||JSON.parse)(e),x.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(s||JSON.stringify)(e)}const As={transitional:ti,adapter:["xhr","http","fetch"],transformRequest:[function(t,s){const n=s.getContentType()||"",r=n.indexOf("application/json")>-1,o=x.isObject(t);if(o&&x.isHTMLForm(t)&&(t=new FormData(t)),x.isFormData(t))return r?JSON.stringify(si(t)):t;if(x.isArrayBuffer(t)||x.isBuffer(t)||x.isStream(t)||x.isFile(t)||x.isBlob(t)||x.isReadableStream(t))return t;if(x.isArrayBufferView(t))return t.buffer;if(x.isURLSearchParams(t))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Pa(t,this.formSerializer).toString();if((l=x.isFileList(t))||n.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return an(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return o||r?(s.setContentType("application/json",!1),ja(t)):t}],transformResponse:[function(t){const s=this.transitional||As.transitional,n=s&&s.forcedJSONParsing,r=this.responseType==="json";if(x.isResponse(t)||x.isReadableStream(t))return t;if(t&&x.isString(t)&&(n&&!this.responseType||r)){const i=!(s&&s.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?W.from(l,W.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ge.classes.FormData,Blob:ge.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};x.forEach(["delete","get","head","post","put","patch"],e=>{As.headers[e]={}});const Da=x.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Na=e=>{const t={};let s,n,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),s=i.substring(0,r).trim().toLowerCase(),n=i.substring(r+1).trim(),!(!s||t[s]&&Da[s])&&(s==="set-cookie"?t[s]?t[s].push(n):t[s]=[n]:t[s]=t[s]?t[s]+", "+n:n)}),t},Nr=Symbol("internals");function rs(e){return e&&String(e).trim().toLowerCase()}function Is(e){return e===!1||e==null?e:x.isArray(e)?e.map(Is):String(e)}function Fa(e){const t=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=s.exec(e);)t[n[1]]=n[2];return t}const Ia=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Rn(e,t,s,n,r){if(x.isFunction(n))return n.call(this,t,s);if(r&&(t=s),!!x.isString(t)){if(x.isString(n))return t.indexOf(n)!==-1;if(x.isRegExp(n))return n.test(t)}}function La(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,s,n)=>s.toUpperCase()+n)}function Ba(e,t){const s=x.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+s,{value:function(r,o,i){return this[n].call(this,t,r,o,i)},configurable:!0})})}let Ae=class{constructor(t){t&&this.set(t)}set(t,s,n){const r=this;function o(l,a,f){const u=rs(a);if(!u)throw new Error("header name must be a non-empty string");const d=x.findKey(r,u);(!d||r[d]===void 0||f===!0||f===void 0&&r[d]!==!1)&&(r[d||a]=Is(l))}const i=(l,a)=>x.forEach(l,(f,u)=>o(f,u,a));if(x.isPlainObject(t)||t instanceof this.constructor)i(t,s);else if(x.isString(t)&&(t=t.trim())&&!Ia(t))i(Na(t),s);else if(x.isObject(t)&&x.isIterable(t)){let l={},a,f;for(const u of t){if(!x.isArray(u))throw TypeError("Object iterator must return a key-value pair");l[f=u[0]]=(a=l[f])?x.isArray(a)?[...a,u[1]]:[a,u[1]]:u[1]}i(l,s)}else t!=null&&o(s,t,n);return this}get(t,s){if(t=rs(t),t){const n=x.findKey(this,t);if(n){const r=this[n];if(!s)return r;if(s===!0)return Fa(r);if(x.isFunction(s))return s.call(this,r,n);if(x.isRegExp(s))return s.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,s){if(t=rs(t),t){const n=x.findKey(this,t);return!!(n&&this[n]!==void 0&&(!s||Rn(this,this[n],n,s)))}return!1}delete(t,s){const n=this;let r=!1;function o(i){if(i=rs(i),i){const l=x.findKey(n,i);l&&(!s||Rn(n,n[l],l,s))&&(delete n[l],r=!0)}}return x.isArray(t)?t.forEach(o):o(t),r}clear(t){const s=Object.keys(this);let n=s.length,r=!1;for(;n--;){const o=s[n];(!t||Rn(this,this[o],o,t,!0))&&(delete this[o],r=!0)}return r}normalize(t){const s=this,n={};return x.forEach(this,(r,o)=>{const i=x.findKey(n,o);if(i){s[i]=Is(r),delete s[o];return}const l=t?La(o):String(o).trim();l!==o&&delete s[o],s[l]=Is(r),n[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const s=Object.create(null);return x.forEach(this,(n,r)=>{n!=null&&n!==!1&&(s[r]=t&&x.isArray(n)?n.join(", "):n)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,s])=>t+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...s){const n=new this(t);return s.forEach(r=>n.set(r)),n}static accessor(t){const n=(this[Nr]=this[Nr]={accessors:{}}).accessors,r=this.prototype;function o(i){const l=rs(i);n[l]||(Ba(r,i),n[l]=!0)}return x.isArray(t)?t.forEach(o):o(t),this}};Ae.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);x.reduceDescriptors(Ae.prototype,({value:e},t)=>{let s=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[s]=n}}});x.freezeMethods(Ae);function Cn(e,t){const s=this||As,n=t||s,r=Ae.from(n.headers);let o=n.data;return x.forEach(e,function(l){o=l.call(s,o,r.normalize(),t?t.status:void 0)}),r.normalize(),o}function ni(e){return!!(e&&e.__CANCEL__)}function es(e,t,s){W.call(this,e??"canceled",W.ERR_CANCELED,t,s),this.name="CanceledError"}x.inherits(es,W,{__CANCEL__:!0});function ri(e,t,s){const n=s.config.validateStatus;!s.status||!n||n(s.status)?e(s):t(new W("Request failed with status code "+s.status,[W.ERR_BAD_REQUEST,W.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function Ua(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Va(e,t){e=e||10;const s=new Array(e),n=new Array(e);let r=0,o=0,i;return t=t!==void 0?t:1e3,function(a){const f=Date.now(),u=n[o];i||(i=f),s[r]=a,n[r]=f;let d=o,m=0;for(;d!==r;)m+=s[d++],d=d%e;if(r=(r+1)%e,r===o&&(o=(o+1)%e),f-i<t)return;const g=u&&f-u;return g?Math.round(m*1e3/g):void 0}}function Ha(e,t){let s=0,n=1e3/t,r,o;const i=(f,u=Date.now())=>{s=u,r=null,o&&(clearTimeout(o),o=null),e.apply(null,f)};return[(...f)=>{const u=Date.now(),d=u-s;d>=n?i(f,u):(r=f,o||(o=setTimeout(()=>{o=null,i(r)},n-d)))},()=>r&&i(r)]}const Ks=(e,t,s=3)=>{let n=0;const r=Va(50,250);return Ha(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,a=i-n,f=r(a),u=i<=l;n=i;const d={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:f||void 0,estimated:f&&l&&u?(l-i)/f:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(d)},s)},Fr=(e,t)=>{const s=e!=null;return[n=>t[0]({lengthComputable:s,total:e,loaded:n}),t[1]]},Ir=e=>(...t)=>x.asap(()=>e(...t)),za=ge.hasStandardBrowserEnv?((e,t)=>s=>(s=new URL(s,ge.origin),e.protocol===s.protocol&&e.host===s.host&&(t||e.port===s.port)))(new URL(ge.origin),ge.navigator&&/(msie|trident)/i.test(ge.navigator.userAgent)):()=>!0,qa=ge.hasStandardBrowserEnv?{write(e,t,s,n,r,o){const i=[e+"="+encodeURIComponent(t)];x.isNumber(s)&&i.push("expires="+new Date(s).toGMTString()),x.isString(n)&&i.push("path="+n),x.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ka(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Wa(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function oi(e,t,s){let n=!Ka(t);return e&&(n||s==!1)?Wa(e,t):t}const Lr=e=>e instanceof Ae?{...e}:e;function jt(e,t){t=t||{};const s={};function n(f,u,d,m){return x.isPlainObject(f)&&x.isPlainObject(u)?x.merge.call({caseless:m},f,u):x.isPlainObject(u)?x.merge({},u):x.isArray(u)?u.slice():u}function r(f,u,d,m){if(x.isUndefined(u)){if(!x.isUndefined(f))return n(void 0,f,d,m)}else return n(f,u,d,m)}function o(f,u){if(!x.isUndefined(u))return n(void 0,u)}function i(f,u){if(x.isUndefined(u)){if(!x.isUndefined(f))return n(void 0,f)}else return n(void 0,u)}function l(f,u,d){if(d in t)return n(f,u);if(d in e)return n(void 0,f)}const a={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(f,u,d)=>r(Lr(f),Lr(u),d,!0)};return x.forEach(Object.keys(Object.assign({},e,t)),function(u){const d=a[u]||r,m=d(e[u],t[u],u);x.isUndefined(m)&&d!==l||(s[u]=m)}),s}const ii=e=>{const t=jt({},e);let{data:s,withXSRFToken:n,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=Ae.from(i),t.url=ei(oi(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(x.isFormData(s)){if(ge.hasStandardBrowserEnv||ge.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[f,...u]=a?a.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([f||"multipart/form-data",...u].join("; "))}}if(ge.hasStandardBrowserEnv&&(n&&x.isFunction(n)&&(n=n(t)),n||n!==!1&&za(t.url))){const f=r&&o&&qa.read(o);f&&i.set(r,f)}return t},Ja=typeof XMLHttpRequest<"u",Ga=Ja&&function(e){return new Promise(function(s,n){const r=ii(e);let o=r.data;const i=Ae.from(r.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:f}=r,u,d,m,g,y;function E(){g&&g(),y&&y(),r.cancelToken&&r.cancelToken.unsubscribe(u),r.signal&&r.signal.removeEventListener("abort",u)}let R=new XMLHttpRequest;R.open(r.method.toUpperCase(),r.url,!0),R.timeout=r.timeout;function O(){if(!R)return;const j=Ae.from("getAllResponseHeaders"in R&&R.getAllResponseHeaders()),K={data:!l||l==="text"||l==="json"?R.responseText:R.response,status:R.status,statusText:R.statusText,headers:j,config:e,request:R};ri(function(Q){s(Q),E()},function(Q){n(Q),E()},K),R=null}"onloadend"in R?R.onloadend=O:R.onreadystatechange=function(){!R||R.readyState!==4||R.status===0&&!(R.responseURL&&R.responseURL.indexOf("file:")===0)||setTimeout(O)},R.onabort=function(){R&&(n(new W("Request aborted",W.ECONNABORTED,e,R)),R=null)},R.onerror=function(){n(new W("Network Error",W.ERR_NETWORK,e,R)),R=null},R.ontimeout=function(){let D=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const K=r.transitional||ti;r.timeoutErrorMessage&&(D=r.timeoutErrorMessage),n(new W(D,K.clarifyTimeoutError?W.ETIMEDOUT:W.ECONNABORTED,e,R)),R=null},o===void 0&&i.setContentType(null),"setRequestHeader"in R&&x.forEach(i.toJSON(),function(D,K){R.setRequestHeader(K,D)}),x.isUndefined(r.withCredentials)||(R.withCredentials=!!r.withCredentials),l&&l!=="json"&&(R.responseType=r.responseType),f&&([m,y]=Ks(f,!0),R.addEventListener("progress",m)),a&&R.upload&&([d,g]=Ks(a),R.upload.addEventListener("progress",d),R.upload.addEventListener("loadend",g)),(r.cancelToken||r.signal)&&(u=j=>{R&&(n(!j||j.type?new es(null,e,R):j),R.abort(),R=null)},r.cancelToken&&r.cancelToken.subscribe(u),r.signal&&(r.signal.aborted?u():r.signal.addEventListener("abort",u)));const T=Ua(r.url);if(T&&ge.protocols.indexOf(T)===-1){n(new W("Unsupported protocol "+T+":",W.ERR_BAD_REQUEST,e));return}R.send(o||null)})},Xa=(e,t)=>{const{length:s}=e=e?e.filter(Boolean):[];if(t||s){let n=new AbortController,r;const o=function(f){if(!r){r=!0,l();const u=f instanceof Error?f:this.reason;n.abort(u instanceof W?u:new es(u instanceof Error?u.message:u))}};let i=t&&setTimeout(()=>{i=null,o(new W(`timeout ${t} of ms exceeded`,W.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(f=>{f.unsubscribe?f.unsubscribe(o):f.removeEventListener("abort",o)}),e=null)};e.forEach(f=>f.addEventListener("abort",o));const{signal:a}=n;return a.unsubscribe=()=>x.asap(l),a}},Qa=function*(e,t){let s=e.byteLength;if(s<t){yield e;return}let n=0,r;for(;n<s;)r=n+t,yield e.slice(n,r),n=r},Ya=async function*(e,t){for await(const s of Za(e))yield*Qa(s,t)},Za=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:s,value:n}=await t.read();if(s)break;yield n}}finally{await t.cancel()}},Br=(e,t,s,n)=>{const r=Ya(e,t);let o=0,i,l=a=>{i||(i=!0,n&&n(a))};return new ReadableStream({async pull(a){try{const{done:f,value:u}=await r.next();if(f){l(),a.close();return}let d=u.byteLength;if(s){let m=o+=d;s(m)}a.enqueue(new Uint8Array(u))}catch(f){throw l(f),f}},cancel(a){return l(a),r.return()}},{highWaterMark:2})},cn=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",li=cn&&typeof ReadableStream=="function",ec=cn&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ai=(e,...t)=>{try{return!!e(...t)}catch{return!1}},tc=li&&ai(()=>{let e=!1;const t=new Request(ge.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ur=64*1024,Hn=li&&ai(()=>x.isReadableStream(new Response("").body)),Ws={stream:Hn&&(e=>e.body)};cn&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Ws[t]&&(Ws[t]=x.isFunction(e[t])?s=>s[t]():(s,n)=>{throw new W(`Response type '${t}' is not supported`,W.ERR_NOT_SUPPORT,n)})})})(new Response);const sc=async e=>{if(e==null)return 0;if(x.isBlob(e))return e.size;if(x.isSpecCompliantForm(e))return(await new Request(ge.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(x.isArrayBufferView(e)||x.isArrayBuffer(e))return e.byteLength;if(x.isURLSearchParams(e)&&(e=e+""),x.isString(e))return(await ec(e)).byteLength},nc=async(e,t)=>{const s=x.toFiniteNumber(e.getContentLength());return s??sc(t)},rc=cn&&(async e=>{let{url:t,method:s,data:n,signal:r,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:f,headers:u,withCredentials:d="same-origin",fetchOptions:m}=ii(e);f=f?(f+"").toLowerCase():"text";let g=Xa([r,o&&o.toAbortSignal()],i),y;const E=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let R;try{if(a&&tc&&s!=="get"&&s!=="head"&&(R=await nc(u,n))!==0){let K=new Request(t,{method:"POST",body:n,duplex:"half"}),ie;if(x.isFormData(n)&&(ie=K.headers.get("content-type"))&&u.setContentType(ie),K.body){const[Q,be]=Fr(R,Ks(Ir(a)));n=Br(K.body,Ur,Q,be)}}x.isString(d)||(d=d?"include":"omit");const O="credentials"in Request.prototype;y=new Request(t,{...m,signal:g,method:s.toUpperCase(),headers:u.normalize().toJSON(),body:n,duplex:"half",credentials:O?d:void 0});let T=await fetch(y);const j=Hn&&(f==="stream"||f==="response");if(Hn&&(l||j&&E)){const K={};["status","statusText","headers"].forEach(Ie=>{K[Ie]=T[Ie]});const ie=x.toFiniteNumber(T.headers.get("content-length")),[Q,be]=l&&Fr(ie,Ks(Ir(l),!0))||[];T=new Response(Br(T.body,Ur,Q,()=>{be&&be(),E&&E()}),K)}f=f||"text";let D=await Ws[x.findKey(Ws,f)||"text"](T,e);return!j&&E&&E(),await new Promise((K,ie)=>{ri(K,ie,{data:D,headers:Ae.from(T.headers),status:T.status,statusText:T.statusText,config:e,request:y})})}catch(O){throw E&&E(),O&&O.name==="TypeError"&&/Load failed|fetch/i.test(O.message)?Object.assign(new W("Network Error",W.ERR_NETWORK,e,y),{cause:O.cause||O}):W.from(O,O&&O.code,e,y)}}),zn={http:ya,xhr:Ga,fetch:rc};x.forEach(zn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Vr=e=>`- ${e}`,oc=e=>x.isFunction(e)||e===null||e===!1,ci={getAdapter:e=>{e=x.isArray(e)?e:[e];const{length:t}=e;let s,n;const r={};for(let o=0;o<t;o++){s=e[o];let i;if(n=s,!oc(s)&&(n=zn[(i=String(s)).toLowerCase()],n===void 0))throw new W(`Unknown adapter '${i}'`);if(n)break;r[i||"#"+o]=n}if(!n){const o=Object.entries(r).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Vr).join(`
`):" "+Vr(o[0]):"as no adapter specified";throw new W("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:zn};function kn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new es(null,e)}function Hr(e){return kn(e),e.headers=Ae.from(e.headers),e.data=Cn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ci.getAdapter(e.adapter||As.adapter)(e).then(function(n){return kn(e),n.data=Cn.call(e,e.transformResponse,n),n.headers=Ae.from(n.headers),n},function(n){return ni(n)||(kn(e),n&&n.response&&(n.response.data=Cn.call(e,e.transformResponse,n.response),n.response.headers=Ae.from(n.response.headers))),Promise.reject(n)})}const ui="1.9.0",un={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{un[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const zr={};un.transitional=function(t,s,n){function r(o,i){return"[Axios v"+ui+"] Transitional option '"+o+"'"+i+(n?". "+n:"")}return(o,i,l)=>{if(t===!1)throw new W(r(i," has been removed"+(s?" in "+s:"")),W.ERR_DEPRECATED);return s&&!zr[i]&&(zr[i]=!0,console.warn(r(i," has been deprecated since v"+s+" and will be removed in the near future"))),t?t(o,i,l):!0}};un.spelling=function(t){return(s,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function ic(e,t,s){if(typeof e!="object")throw new W("options must be an object",W.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let r=n.length;for(;r-- >0;){const o=n[r],i=t[o];if(i){const l=e[o],a=l===void 0||i(l,o,e);if(a!==!0)throw new W("option "+o+" must be "+a,W.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new W("Unknown option "+o,W.ERR_BAD_OPTION)}}const Ls={assertOptions:ic,validators:un},Ge=Ls.validators;let Pt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Dr,response:new Dr}}async request(t,s){try{return await this._request(t,s)}catch(n){if(n instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(t,s){typeof t=="string"?(s=s||{},s.url=t):s=t||{},s=jt(this.defaults,s);const{transitional:n,paramsSerializer:r,headers:o}=s;n!==void 0&&Ls.assertOptions(n,{silentJSONParsing:Ge.transitional(Ge.boolean),forcedJSONParsing:Ge.transitional(Ge.boolean),clarifyTimeoutError:Ge.transitional(Ge.boolean)},!1),r!=null&&(x.isFunction(r)?s.paramsSerializer={serialize:r}:Ls.assertOptions(r,{encode:Ge.function,serialize:Ge.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Ls.assertOptions(s,{baseUrl:Ge.spelling("baseURL"),withXsrfToken:Ge.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let i=o&&x.merge(o.common,o[s.method]);o&&x.forEach(["delete","get","head","post","put","patch","common"],y=>{delete o[y]}),s.headers=Ae.concat(i,o);const l=[];let a=!0;this.interceptors.request.forEach(function(E){typeof E.runWhen=="function"&&E.runWhen(s)===!1||(a=a&&E.synchronous,l.unshift(E.fulfilled,E.rejected))});const f=[];this.interceptors.response.forEach(function(E){f.push(E.fulfilled,E.rejected)});let u,d=0,m;if(!a){const y=[Hr.bind(this),void 0];for(y.unshift.apply(y,l),y.push.apply(y,f),m=y.length,u=Promise.resolve(s);d<m;)u=u.then(y[d++],y[d++]);return u}m=l.length;let g=s;for(d=0;d<m;){const y=l[d++],E=l[d++];try{g=y(g)}catch(R){E.call(this,R);break}}try{u=Hr.call(this,g)}catch(y){return Promise.reject(y)}for(d=0,m=f.length;d<m;)u=u.then(f[d++],f[d++]);return u}getUri(t){t=jt(this.defaults,t);const s=oi(t.baseURL,t.url,t.allowAbsoluteUrls);return ei(s,t.params,t.paramsSerializer)}};x.forEach(["delete","get","head","options"],function(t){Pt.prototype[t]=function(s,n){return this.request(jt(n||{},{method:t,url:s,data:(n||{}).data}))}});x.forEach(["post","put","patch"],function(t){function s(n){return function(o,i,l){return this.request(jt(l||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Pt.prototype[t]=s(),Pt.prototype[t+"Form"]=s(!0)});let lc=class fi{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(o){s=o});const n=this;this.promise.then(r=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](r);n._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(l=>{n.subscribe(l),o=l}).then(r);return i.cancel=function(){n.unsubscribe(o)},i},t(function(o,i,l){n.reason||(n.reason=new es(o,i,l),s(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const s=this._listeners.indexOf(t);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const t=new AbortController,s=n=>{t.abort(n)};return this.subscribe(s),t.signal.unsubscribe=()=>this.unsubscribe(s),t.signal}static source(){let t;return{token:new fi(function(r){t=r}),cancel:t}}};function ac(e){return function(s){return e.apply(null,s)}}function cc(e){return x.isObject(e)&&e.isAxiosError===!0}const qn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(qn).forEach(([e,t])=>{qn[t]=e});function di(e){const t=new Pt(e),s=Vo(Pt.prototype.request,t);return x.extend(s,Pt.prototype,t,{allOwnKeys:!0}),x.extend(s,t,null,{allOwnKeys:!0}),s.create=function(r){return di(jt(e,r))},s}const fe=di(As);fe.Axios=Pt;fe.CanceledError=es;fe.CancelToken=lc;fe.isCancel=ni;fe.VERSION=ui;fe.toFormData=an;fe.AxiosError=W;fe.Cancel=fe.CanceledError;fe.all=function(t){return Promise.all(t)};fe.spread=ac;fe.isAxiosError=cc;fe.mergeConfig=jt;fe.AxiosHeaders=Ae;fe.formToJSON=e=>si(x.isHTMLForm(e)?new FormData(e):e);fe.getAdapter=ci.getAdapter;fe.HttpStatusCode=qn;fe.default=fe;const{Axios:m0,AxiosError:g0,CanceledError:v0,isCancel:x0,CancelToken:y0,VERSION:b0,all:w0,Cancel:_0,isAxiosError:E0,spread:S0,toFormData:R0,AxiosHeaders:C0,HttpStatusCode:k0,formToJSON:A0,getAdapter:O0,mergeConfig:T0}=fe;window.axios=fe;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ar(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const oe={},Ht=[],Ze=()=>{},uc=()=>!1,fn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),cr=e=>e.startsWith("onUpdate:"),ye=Object.assign,ur=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},fc=Object.prototype.hasOwnProperty,te=(e,t)=>fc.call(e,t),U=Array.isArray,zt=e=>Os(e)==="[object Map]",ts=e=>Os(e)==="[object Set]",qr=e=>Os(e)==="[object Date]",q=e=>typeof e=="function",de=e=>typeof e=="string",et=e=>typeof e=="symbol",ue=e=>e!==null&&typeof e=="object",pi=e=>(ue(e)||q(e))&&q(e.then)&&q(e.catch),hi=Object.prototype.toString,Os=e=>hi.call(e),dc=e=>Os(e).slice(8,-1),mi=e=>Os(e)==="[object Object]",fr=e=>de(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,cs=ar(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),dn=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},pc=/-(\w)/g,Fe=dn(e=>e.replace(pc,(t,s)=>s?s.toUpperCase():"")),hc=/\B([A-Z])/g,Dt=dn(e=>e.replace(hc,"-$1").toLowerCase()),pn=dn(e=>e.charAt(0).toUpperCase()+e.slice(1)),An=dn(e=>e?`on${pn(e)}`:""),wt=(e,t)=>!Object.is(e,t),Bs=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},gi=(e,t,s,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:s})},Js=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Kr;const hn=()=>Kr||(Kr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function dr(e){if(U(e)){const t={};for(let s=0;s<e.length;s++){const n=e[s],r=de(n)?xc(n):dr(n);if(r)for(const o in r)t[o]=r[o]}return t}else if(de(e)||ue(e))return e}const mc=/;(?![^(]*\))/g,gc=/:([^]+)/,vc=/\/\*[^]*?\*\//g;function xc(e){const t={};return e.replace(vc,"").split(mc).forEach(s=>{if(s){const n=s.split(gc);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function mn(e){let t="";if(de(e))t=e;else if(U(e))for(let s=0;s<e.length;s++){const n=mn(e[s]);n&&(t+=n+" ")}else if(ue(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const yc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",bc=ar(yc);function vi(e){return!!e||e===""}function wc(e,t){if(e.length!==t.length)return!1;let s=!0;for(let n=0;s&&n<e.length;n++)s=Ts(e[n],t[n]);return s}function Ts(e,t){if(e===t)return!0;let s=qr(e),n=qr(t);if(s||n)return s&&n?e.getTime()===t.getTime():!1;if(s=et(e),n=et(t),s||n)return e===t;if(s=U(e),n=U(t),s||n)return s&&n?wc(e,t):!1;if(s=ue(e),n=ue(t),s||n){if(!s||!n)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),a=t.hasOwnProperty(i);if(l&&!a||!l&&a||!Ts(e[i],t[i]))return!1}}return String(e)===String(t)}function pr(e,t){return e.findIndex(s=>Ts(s,t))}const xi=e=>!!(e&&e.__v_isRef===!0),L=e=>de(e)?e:e==null?"":U(e)||ue(e)&&(e.toString===hi||!q(e.toString))?xi(e)?L(e.value):JSON.stringify(e,yi,2):String(e),yi=(e,t)=>xi(t)?yi(e,t.value):zt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[n,r],o)=>(s[On(n,o)+" =>"]=r,s),{})}:ts(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>On(s))}:et(t)?On(t):ue(t)&&!U(t)&&!mi(t)?String(t):t,On=(e,t="")=>{var s;return et(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Re;class _c{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Re,!t&&Re&&(this.index=(Re.scopes||(Re.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=Re;try{return Re=this,t()}finally{Re=s}}}on(){++this._on===1&&(this.prevScope=Re,Re=this)}off(){this._on>0&&--this._on===0&&(Re=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Ec(){return Re}let ae;const Tn=new WeakSet;class bi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Re&&Re.active&&Re.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Tn.has(this)&&(Tn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||_i(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Wr(this),Ei(this);const t=ae,s=Be;ae=this,Be=!0;try{return this.fn()}finally{Si(this),ae=t,Be=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)gr(t);this.deps=this.depsTail=void 0,Wr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Tn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Kn(this)&&this.run()}get dirty(){return Kn(this)}}let wi=0,us,fs;function _i(e,t=!1){if(e.flags|=8,t){e.next=fs,fs=e;return}e.next=us,us=e}function hr(){wi++}function mr(){if(--wi>0)return;if(fs){let t=fs;for(fs=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;us;){let t=us;for(us=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=s}}if(e)throw e}function Ei(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Si(e){let t,s=e.depsTail,n=s;for(;n;){const r=n.prevDep;n.version===-1?(n===s&&(s=r),gr(n),Sc(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=r}e.deps=t,e.depsTail=s}function Kn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ri(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ri(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===ys)||(e.globalVersion=ys,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Kn(e))))return;e.flags|=2;const t=e.dep,s=ae,n=Be;ae=e,Be=!0;try{Ei(e);const r=e.fn(e._value);(t.version===0||wt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ae=s,Be=n,Si(e),e.flags&=-3}}function gr(e,t=!1){const{dep:s,prevSub:n,nextSub:r}=e;if(n&&(n.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=n,e.nextSub=void 0),s.subs===e&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let o=s.computed.deps;o;o=o.nextDep)gr(o,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function Sc(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Be=!0;const Ci=[];function ft(){Ci.push(Be),Be=!1}function dt(){const e=Ci.pop();Be=e===void 0?!0:e}function Wr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=ae;ae=void 0;try{t()}finally{ae=s}}}let ys=0;class Rc{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class vr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ae||!Be||ae===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==ae)s=this.activeLink=new Rc(ae,this),ae.deps?(s.prevDep=ae.depsTail,ae.depsTail.nextDep=s,ae.depsTail=s):ae.deps=ae.depsTail=s,ki(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=ae.depsTail,s.nextDep=void 0,ae.depsTail.nextDep=s,ae.depsTail=s,ae.deps===s&&(ae.deps=n)}return s}trigger(t){this.version++,ys++,this.notify(t)}notify(t){hr();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{mr()}}}function ki(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)ki(n)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const Wn=new WeakMap,Mt=Symbol(""),Jn=Symbol(""),bs=Symbol("");function me(e,t,s){if(Be&&ae){let n=Wn.get(e);n||Wn.set(e,n=new Map);let r=n.get(s);r||(n.set(s,r=new vr),r.map=n,r.key=s),r.track()}}function it(e,t,s,n,r,o){const i=Wn.get(e);if(!i){ys++;return}const l=a=>{a&&a.trigger()};if(hr(),t==="clear")i.forEach(l);else{const a=U(e),f=a&&fr(s);if(a&&s==="length"){const u=Number(n);i.forEach((d,m)=>{(m==="length"||m===bs||!et(m)&&m>=u)&&l(d)})}else switch((s!==void 0||i.has(void 0))&&l(i.get(s)),f&&l(i.get(bs)),t){case"add":a?f&&l(i.get("length")):(l(i.get(Mt)),zt(e)&&l(i.get(Jn)));break;case"delete":a||(l(i.get(Mt)),zt(e)&&l(i.get(Jn)));break;case"set":zt(e)&&l(i.get(Mt));break}}mr()}function Bt(e){const t=ee(e);return t===e?t:(me(t,"iterate",bs),Ne(e)?t:t.map(he))}function gn(e){return me(e=ee(e),"iterate",bs),e}const Cc={__proto__:null,[Symbol.iterator](){return Pn(this,Symbol.iterator,he)},concat(...e){return Bt(this).concat(...e.map(t=>U(t)?Bt(t):t))},entries(){return Pn(this,"entries",e=>(e[1]=he(e[1]),e))},every(e,t){return nt(this,"every",e,t,void 0,arguments)},filter(e,t){return nt(this,"filter",e,t,s=>s.map(he),arguments)},find(e,t){return nt(this,"find",e,t,he,arguments)},findIndex(e,t){return nt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return nt(this,"findLast",e,t,he,arguments)},findLastIndex(e,t){return nt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return nt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Mn(this,"includes",e)},indexOf(...e){return Mn(this,"indexOf",e)},join(e){return Bt(this).join(e)},lastIndexOf(...e){return Mn(this,"lastIndexOf",e)},map(e,t){return nt(this,"map",e,t,void 0,arguments)},pop(){return os(this,"pop")},push(...e){return os(this,"push",e)},reduce(e,...t){return Jr(this,"reduce",e,t)},reduceRight(e,...t){return Jr(this,"reduceRight",e,t)},shift(){return os(this,"shift")},some(e,t){return nt(this,"some",e,t,void 0,arguments)},splice(...e){return os(this,"splice",e)},toReversed(){return Bt(this).toReversed()},toSorted(e){return Bt(this).toSorted(e)},toSpliced(...e){return Bt(this).toSpliced(...e)},unshift(...e){return os(this,"unshift",e)},values(){return Pn(this,"values",he)}};function Pn(e,t,s){const n=gn(e),r=n[t]();return n!==e&&!Ne(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=s(o.value)),o}),r}const kc=Array.prototype;function nt(e,t,s,n,r,o){const i=gn(e),l=i!==e&&!Ne(e),a=i[t];if(a!==kc[t]){const d=a.apply(e,o);return l?he(d):d}let f=s;i!==e&&(l?f=function(d,m){return s.call(this,he(d),m,e)}:s.length>2&&(f=function(d,m){return s.call(this,d,m,e)}));const u=a.call(i,f,n);return l&&r?r(u):u}function Jr(e,t,s,n){const r=gn(e);let o=s;return r!==e&&(Ne(e)?s.length>3&&(o=function(i,l,a){return s.call(this,i,l,a,e)}):o=function(i,l,a){return s.call(this,i,he(l),a,e)}),r[t](o,...n)}function Mn(e,t,s){const n=ee(e);me(n,"iterate",bs);const r=n[t](...s);return(r===-1||r===!1)&&br(s[0])?(s[0]=ee(s[0]),n[t](...s)):r}function os(e,t,s=[]){ft(),hr();const n=ee(e)[t].apply(e,s);return mr(),dt(),n}const Ac=ar("__proto__,__v_isRef,__isVue"),Ai=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(et));function Oc(e){et(e)||(e=String(e));const t=ee(this);return me(t,"has",e),t.hasOwnProperty(e)}class Oi{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,n){if(s==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(s==="__v_isReactive")return!r;if(s==="__v_isReadonly")return r;if(s==="__v_isShallow")return o;if(s==="__v_raw")return n===(r?o?Lc:$i:o?Mi:Pi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const i=U(t);if(!r){let a;if(i&&(a=Cc[s]))return a;if(s==="hasOwnProperty")return Oc}const l=Reflect.get(t,s,xe(t)?t:n);return(et(s)?Ai.has(s):Ac(s))||(r||me(t,"get",s),o)?l:xe(l)?i&&fr(s)?l:l.value:ue(l)?r?Di(l):vn(l):l}}class Ti extends Oi{constructor(t=!1){super(!1,t)}set(t,s,n,r){let o=t[s];if(!this._isShallow){const a=_t(o);if(!Ne(n)&&!_t(n)&&(o=ee(o),n=ee(n)),!U(t)&&xe(o)&&!xe(n))return a?!1:(o.value=n,!0)}const i=U(t)&&fr(s)?Number(s)<t.length:te(t,s),l=Reflect.set(t,s,n,xe(t)?t:r);return t===ee(r)&&(i?wt(n,o)&&it(t,"set",s,n):it(t,"add",s,n)),l}deleteProperty(t,s){const n=te(t,s);t[s];const r=Reflect.deleteProperty(t,s);return r&&n&&it(t,"delete",s,void 0),r}has(t,s){const n=Reflect.has(t,s);return(!et(s)||!Ai.has(s))&&me(t,"has",s),n}ownKeys(t){return me(t,"iterate",U(t)?"length":Mt),Reflect.ownKeys(t)}}class Tc extends Oi{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const Pc=new Ti,Mc=new Tc,$c=new Ti(!0);const Gn=e=>e,js=e=>Reflect.getPrototypeOf(e);function jc(e,t,s){return function(...n){const r=this.__v_raw,o=ee(r),i=zt(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,f=r[e](...n),u=s?Gn:t?Gs:he;return!t&&me(o,"iterate",a?Jn:Mt),{next(){const{value:d,done:m}=f.next();return m?{value:d,done:m}:{value:l?[u(d[0]),u(d[1])]:u(d),done:m}},[Symbol.iterator](){return this}}}}function Ds(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Dc(e,t){const s={get(r){const o=this.__v_raw,i=ee(o),l=ee(r);e||(wt(r,l)&&me(i,"get",r),me(i,"get",l));const{has:a}=js(i),f=t?Gn:e?Gs:he;if(a.call(i,r))return f(o.get(r));if(a.call(i,l))return f(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&me(ee(r),"iterate",Mt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=ee(o),l=ee(r);return e||(wt(r,l)&&me(i,"has",r),me(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,a=ee(l),f=t?Gn:e?Gs:he;return!e&&me(a,"iterate",Mt),l.forEach((u,d)=>r.call(o,f(u),f(d),i))}};return ye(s,e?{add:Ds("add"),set:Ds("set"),delete:Ds("delete"),clear:Ds("clear")}:{add(r){!t&&!Ne(r)&&!_t(r)&&(r=ee(r));const o=ee(this);return js(o).has.call(o,r)||(o.add(r),it(o,"add",r,r)),this},set(r,o){!t&&!Ne(o)&&!_t(o)&&(o=ee(o));const i=ee(this),{has:l,get:a}=js(i);let f=l.call(i,r);f||(r=ee(r),f=l.call(i,r));const u=a.call(i,r);return i.set(r,o),f?wt(o,u)&&it(i,"set",r,o):it(i,"add",r,o),this},delete(r){const o=ee(this),{has:i,get:l}=js(o);let a=i.call(o,r);a||(r=ee(r),a=i.call(o,r)),l&&l.call(o,r);const f=o.delete(r);return a&&it(o,"delete",r,void 0),f},clear(){const r=ee(this),o=r.size!==0,i=r.clear();return o&&it(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{s[r]=jc(r,e,t)}),s}function xr(e,t){const s=Dc(e,t);return(n,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?n:Reflect.get(te(s,r)&&r in n?s:n,r,o)}const Nc={get:xr(!1,!1)},Fc={get:xr(!1,!0)},Ic={get:xr(!0,!1)};const Pi=new WeakMap,Mi=new WeakMap,$i=new WeakMap,Lc=new WeakMap;function Bc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Uc(e){return e.__v_skip||!Object.isExtensible(e)?0:Bc(dc(e))}function vn(e){return _t(e)?e:yr(e,!1,Pc,Nc,Pi)}function ji(e){return yr(e,!1,$c,Fc,Mi)}function Di(e){return yr(e,!0,Mc,Ic,$i)}function yr(e,t,s,n,r){if(!ue(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Uc(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?n:s);return r.set(e,l),l}function qt(e){return _t(e)?qt(e.__v_raw):!!(e&&e.__v_isReactive)}function _t(e){return!!(e&&e.__v_isReadonly)}function Ne(e){return!!(e&&e.__v_isShallow)}function br(e){return e?!!e.__v_raw:!1}function ee(e){const t=e&&e.__v_raw;return t?ee(t):e}function Vc(e){return!te(e,"__v_skip")&&Object.isExtensible(e)&&gi(e,"__v_skip",!0),e}const he=e=>ue(e)?vn(e):e,Gs=e=>ue(e)?Di(e):e;function xe(e){return e?e.__v_isRef===!0:!1}function Hc(e){return Ni(e,!1)}function zc(e){return Ni(e,!0)}function Ni(e,t){return xe(e)?e:new qc(e,t)}class qc{constructor(t,s){this.dep=new vr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:ee(t),this._value=s?t:he(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,n=this.__v_isShallow||Ne(t)||_t(t);t=n?t:ee(t),wt(t,s)&&(this._rawValue=t,this._value=n?t:he(t),this.dep.trigger())}}function Kt(e){return xe(e)?e.value:e}const Kc={get:(e,t,s)=>t==="__v_raw"?e:Kt(Reflect.get(e,t,s)),set:(e,t,s,n)=>{const r=e[t];return xe(r)&&!xe(s)?(r.value=s,!0):Reflect.set(e,t,s,n)}};function Fi(e){return qt(e)?e:new Proxy(e,Kc)}class Wc{constructor(t,s,n){this.fn=t,this.setter=s,this._value=void 0,this.dep=new vr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=ys-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&ae!==this)return _i(this,!0),!0}get value(){const t=this.dep.track();return Ri(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Jc(e,t,s=!1){let n,r;return q(e)?n=e:(n=e.get,r=e.set),new Wc(n,r,s)}const Ns={},Xs=new WeakMap;let At;function Gc(e,t=!1,s=At){if(s){let n=Xs.get(s);n||Xs.set(s,n=[]),n.push(e)}}function Xc(e,t,s=oe){const{immediate:n,deep:r,once:o,scheduler:i,augmentJob:l,call:a}=s,f=D=>r?D:Ne(D)||r===!1||r===0?lt(D,1):lt(D);let u,d,m,g,y=!1,E=!1;if(xe(e)?(d=()=>e.value,y=Ne(e)):qt(e)?(d=()=>f(e),y=!0):U(e)?(E=!0,y=e.some(D=>qt(D)||Ne(D)),d=()=>e.map(D=>{if(xe(D))return D.value;if(qt(D))return f(D);if(q(D))return a?a(D,2):D()})):q(e)?t?d=a?()=>a(e,2):e:d=()=>{if(m){ft();try{m()}finally{dt()}}const D=At;At=u;try{return a?a(e,3,[g]):e(g)}finally{At=D}}:d=Ze,t&&r){const D=d,K=r===!0?1/0:r;d=()=>lt(D(),K)}const R=Ec(),O=()=>{u.stop(),R&&R.active&&ur(R.effects,u)};if(o&&t){const D=t;t=(...K)=>{D(...K),O()}}let T=E?new Array(e.length).fill(Ns):Ns;const j=D=>{if(!(!(u.flags&1)||!u.dirty&&!D))if(t){const K=u.run();if(r||y||(E?K.some((ie,Q)=>wt(ie,T[Q])):wt(K,T))){m&&m();const ie=At;At=u;try{const Q=[K,T===Ns?void 0:E&&T[0]===Ns?[]:T,g];T=K,a?a(t,3,Q):t(...Q)}finally{At=ie}}}else u.run()};return l&&l(j),u=new bi(d),u.scheduler=i?()=>i(j,!1):j,g=D=>Gc(D,!1,u),m=u.onStop=()=>{const D=Xs.get(u);if(D){if(a)a(D,4);else for(const K of D)K();Xs.delete(u)}},t?n?j(!0):T=u.run():i?i(j.bind(null,!0),!0):u.run(),O.pause=u.pause.bind(u),O.resume=u.resume.bind(u),O.stop=O,O}function lt(e,t=1/0,s){if(t<=0||!ue(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,xe(e))lt(e.value,t,s);else if(U(e))for(let n=0;n<e.length;n++)lt(e[n],t,s);else if(ts(e)||zt(e))e.forEach(n=>{lt(n,t,s)});else if(mi(e)){for(const n in e)lt(e[n],t,s);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&lt(e[n],t,s)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ps(e,t,s,n){try{return n?e(...n):e()}catch(r){xn(r,t,s)}}function tt(e,t,s,n){if(q(e)){const r=Ps(e,t,s,n);return r&&pi(r)&&r.catch(o=>{xn(o,t,s)}),r}if(U(e)){const r=[];for(let o=0;o<e.length;o++)r.push(tt(e[o],t,s,n));return r}}function xn(e,t,s,n=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||oe;if(t){let l=t.parent;const a=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const u=l.ec;if(u){for(let d=0;d<u.length;d++)if(u[d](e,a,f)===!1)return}l=l.parent}if(o){ft(),Ps(o,null,10,[e,a,f]),dt();return}}Qc(e,s,r,n,i)}function Qc(e,t,s,n=!0,r=!1){if(r)throw e;console.error(e)}const _e=[];let Qe=-1;const Wt=[];let vt=null,Ut=0;const Ii=Promise.resolve();let Qs=null;function wr(e){const t=Qs||Ii;return e?t.then(this?e.bind(this):e):t}function Yc(e){let t=Qe+1,s=_e.length;for(;t<s;){const n=t+s>>>1,r=_e[n],o=ws(r);o<e||o===e&&r.flags&2?t=n+1:s=n}return t}function _r(e){if(!(e.flags&1)){const t=ws(e),s=_e[_e.length-1];!s||!(e.flags&2)&&t>=ws(s)?_e.push(e):_e.splice(Yc(t),0,e),e.flags|=1,Li()}}function Li(){Qs||(Qs=Ii.then(Ui))}function Zc(e){U(e)?Wt.push(...e):vt&&e.id===-1?vt.splice(Ut+1,0,e):e.flags&1||(Wt.push(e),e.flags|=1),Li()}function Gr(e,t,s=Qe+1){for(;s<_e.length;s++){const n=_e[s];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;_e.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function Bi(e){if(Wt.length){const t=[...new Set(Wt)].sort((s,n)=>ws(s)-ws(n));if(Wt.length=0,vt){vt.push(...t);return}for(vt=t,Ut=0;Ut<vt.length;Ut++){const s=vt[Ut];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}vt=null,Ut=0}}const ws=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ui(e){try{for(Qe=0;Qe<_e.length;Qe++){const t=_e[Qe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ps(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Qe<_e.length;Qe++){const t=_e[Qe];t&&(t.flags&=-2)}Qe=-1,_e.length=0,Bi(),Qs=null,(_e.length||Wt.length)&&Ui()}}let $e=null,Vi=null;function Ys(e){const t=$e;return $e=e,Vi=e&&e.type.__scopeId||null,t}function ce(e,t=$e,s){if(!t||e._n)return e;const n=(...r)=>{n._d&&oo(-1);const o=Ys(t);let i;try{i=e(...r)}finally{Ys(o),n._d&&oo(1)}return i};return n._n=!0,n._c=!0,n._d=!0,n}function Ce(e,t){if($e===null)return e;const s=En($e),n=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,a=oe]=t[r];o&&(q(o)&&(o={mounted:o,updated:o}),o.deep&&lt(i),n.push({dir:o,instance:s,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function Ct(e,t,s,n){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let a=l.dir[n];a&&(ft(),tt(a,s,8,[e.el,l,e,t]),dt())}}const eu=Symbol("_vte"),tu=e=>e.__isTeleport;function Er(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Er(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Hi(e,t){return q(e)?ye({name:e.name},t,{setup:e}):e}function zi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Zs(e,t,s,n,r=!1){if(U(e)){e.forEach((y,E)=>Zs(y,t&&(U(t)?t[E]:t),s,n,r));return}if(ds(n)&&!r){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&Zs(e,t,s,n.component.subTree);return}const o=n.shapeFlag&4?En(n.component):n.el,i=r?null:o,{i:l,r:a}=e,f=t&&t.r,u=l.refs===oe?l.refs={}:l.refs,d=l.setupState,m=ee(d),g=d===oe?()=>!1:y=>te(m,y);if(f!=null&&f!==a&&(de(f)?(u[f]=null,g(f)&&(d[f]=null)):xe(f)&&(f.value=null)),q(a))Ps(a,l,12,[i,u]);else{const y=de(a),E=xe(a);if(y||E){const R=()=>{if(e.f){const O=y?g(a)?d[a]:u[a]:a.value;r?U(O)&&ur(O,o):U(O)?O.includes(o)||O.push(o):y?(u[a]=[o],g(a)&&(d[a]=u[a])):(a.value=[o],e.k&&(u[e.k]=a.value))}else y?(u[a]=i,g(a)&&(d[a]=i)):E&&(a.value=i,e.k&&(u[e.k]=i))};i?(R.id=-1,Pe(R,s)):R()}}}hn().requestIdleCallback;hn().cancelIdleCallback;const ds=e=>!!e.type.__asyncLoader,qi=e=>e.type.__isKeepAlive;function su(e,t){Ki(e,"a",t)}function nu(e,t){Ki(e,"da",t)}function Ki(e,t,s=ve){const n=e.__wdc||(e.__wdc=()=>{let r=s;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(yn(t,n,s),s){let r=s.parent;for(;r&&r.parent;)qi(r.parent.vnode)&&ru(n,t,s,r),r=r.parent}}function ru(e,t,s,n){const r=yn(t,e,n,!0);Wi(()=>{ur(n[t],r)},s)}function yn(e,t,s=ve,n=!1){if(s){const r=s[e]||(s[e]=[]),o=t.__weh||(t.__weh=(...i)=>{ft();const l=Ms(s),a=tt(t,s,e,i);return l(),dt(),a});return n?r.unshift(o):r.push(o),o}}const pt=e=>(t,s=ve)=>{(!Es||e==="sp")&&yn(e,(...n)=>t(...n),s)},ou=pt("bm"),iu=pt("m"),lu=pt("bu"),au=pt("u"),cu=pt("bum"),Wi=pt("um"),uu=pt("sp"),fu=pt("rtg"),du=pt("rtc");function pu(e,t=ve){yn("ec",e,t)}const hu="components";function Et(e,t){return gu(hu,e,!0,t)||e}const mu=Symbol.for("v-ndc");function gu(e,t,s=!0,n=!1){const r=$e||ve;if(r){const o=r.type;{const l=nf(o,!1);if(l&&(l===t||l===Fe(t)||l===pn(Fe(t))))return o}const i=Xr(r[e]||o[e],t)||Xr(r.appContext[e],t);return!i&&n?o:i}}function Xr(e,t){return e&&(e[t]||e[Fe(t)]||e[pn(Fe(t))])}function bn(e,t,s,n){let r;const o=s,i=U(e);if(i||de(e)){const l=i&&qt(e);let a=!1,f=!1;l&&(a=!Ne(e),f=_t(e),e=gn(e)),r=new Array(e.length);for(let u=0,d=e.length;u<d;u++)r[u]=t(a?f?Gs(he(e[u])):he(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(ue(e))if(e[Symbol.iterator])r=Array.from(e,(l,a)=>t(l,a,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let a=0,f=l.length;a<f;a++){const u=l[a];r[a]=t(e[u],u,a,o)}}else r=[];return r}const Xn=e=>e?pl(e)?En(e):Xn(e.parent):null,ps=ye(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Xn(e.parent),$root:e=>Xn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Gi(e),$forceUpdate:e=>e.f||(e.f=()=>{_r(e.update)}),$nextTick:e=>e.n||(e.n=wr.bind(e.proxy)),$watch:e=>Fu.bind(e)}),$n=(e,t)=>e!==oe&&!e.__isScriptSetup&&te(e,t),vu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:n,data:r,props:o,accessCache:i,type:l,appContext:a}=e;let f;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return n[t];case 2:return r[t];case 4:return s[t];case 3:return o[t]}else{if($n(n,t))return i[t]=1,n[t];if(r!==oe&&te(r,t))return i[t]=2,r[t];if((f=e.propsOptions[0])&&te(f,t))return i[t]=3,o[t];if(s!==oe&&te(s,t))return i[t]=4,s[t];Qn&&(i[t]=0)}}const u=ps[t];let d,m;if(u)return t==="$attrs"&&me(e.attrs,"get",""),u(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(s!==oe&&te(s,t))return i[t]=4,s[t];if(m=a.config.globalProperties,te(m,t))return m[t]},set({_:e},t,s){const{data:n,setupState:r,ctx:o}=e;return $n(r,t)?(r[t]=s,!0):n!==oe&&te(n,t)?(n[t]=s,!0):te(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:n,appContext:r,propsOptions:o}},i){let l;return!!s[i]||e!==oe&&te(e,i)||$n(t,i)||(l=o[0])&&te(l,i)||te(n,i)||te(ps,i)||te(r.config.globalProperties,i)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:te(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function Qr(e){return U(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let Qn=!0;function xu(e){const t=Gi(e),s=e.proxy,n=e.ctx;Qn=!1,t.beforeCreate&&Yr(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:a,inject:f,created:u,beforeMount:d,mounted:m,beforeUpdate:g,updated:y,activated:E,deactivated:R,beforeDestroy:O,beforeUnmount:T,destroyed:j,unmounted:D,render:K,renderTracked:ie,renderTriggered:Q,errorCaptured:be,serverPrefetch:Ie,expose:He,inheritAttrs:ht,components:Rt,directives:ze,filters:ss}=t;if(f&&yu(f,n,null),i)for(const ne in i){const Y=i[ne];q(Y)&&(n[ne]=Y.bind(s))}if(r){const ne=r.call(s,s);ue(ne)&&(e.data=vn(ne))}if(Qn=!0,o)for(const ne in o){const Y=o[ne],st=q(Y)?Y.bind(s,s):q(Y.get)?Y.get.bind(s,s):Ze,mt=!q(Y)&&q(Y.set)?Y.set.bind(s):Ze,qe=Le({get:st,set:mt});Object.defineProperty(n,ne,{enumerable:!0,configurable:!0,get:()=>qe.value,set:Ee=>qe.value=Ee})}if(l)for(const ne in l)Ji(l[ne],n,s,ne);if(a){const ne=q(a)?a.call(s):a;Reflect.ownKeys(ne).forEach(Y=>{Us(Y,ne[Y])})}u&&Yr(u,e,"c");function pe(ne,Y){U(Y)?Y.forEach(st=>ne(st.bind(s))):Y&&ne(Y.bind(s))}if(pe(ou,d),pe(iu,m),pe(lu,g),pe(au,y),pe(su,E),pe(nu,R),pe(pu,be),pe(du,ie),pe(fu,Q),pe(cu,T),pe(Wi,D),pe(uu,Ie),U(He))if(He.length){const ne=e.exposed||(e.exposed={});He.forEach(Y=>{Object.defineProperty(ne,Y,{get:()=>s[Y],set:st=>s[Y]=st})})}else e.exposed||(e.exposed={});K&&e.render===Ze&&(e.render=K),ht!=null&&(e.inheritAttrs=ht),Rt&&(e.components=Rt),ze&&(e.directives=ze),Ie&&zi(e)}function yu(e,t,s=Ze){U(e)&&(e=Yn(e));for(const n in e){const r=e[n];let o;ue(r)?"default"in r?o=at(r.from||n,r.default,!0):o=at(r.from||n):o=at(r),xe(o)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[n]=o}}function Yr(e,t,s){tt(U(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,s)}function Ji(e,t,s,n){let r=n.includes(".")?al(s,n):()=>s[n];if(de(e)){const o=t[e];q(o)&&Vs(r,o)}else if(q(e))Vs(r,e.bind(s));else if(ue(e))if(U(e))e.forEach(o=>Ji(o,t,s,n));else{const o=q(e.handler)?e.handler.bind(s):t[e.handler];q(o)&&Vs(r,o,e)}}function Gi(e){const t=e.type,{mixins:s,extends:n}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!r.length&&!s&&!n?a=t:(a={},r.length&&r.forEach(f=>en(a,f,i,!0)),en(a,t,i)),ue(t)&&o.set(t,a),a}function en(e,t,s,n=!1){const{mixins:r,extends:o}=t;o&&en(e,o,s,!0),r&&r.forEach(i=>en(e,i,s,!0));for(const i in t)if(!(n&&i==="expose")){const l=bu[i]||s&&s[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const bu={data:Zr,props:eo,emits:eo,methods:as,computed:as,beforeCreate:we,created:we,beforeMount:we,mounted:we,beforeUpdate:we,updated:we,beforeDestroy:we,beforeUnmount:we,destroyed:we,unmounted:we,activated:we,deactivated:we,errorCaptured:we,serverPrefetch:we,components:as,directives:as,watch:_u,provide:Zr,inject:wu};function Zr(e,t){return t?e?function(){return ye(q(e)?e.call(this,this):e,q(t)?t.call(this,this):t)}:t:e}function wu(e,t){return as(Yn(e),Yn(t))}function Yn(e){if(U(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function we(e,t){return e?[...new Set([].concat(e,t))]:t}function as(e,t){return e?ye(Object.create(null),e,t):t}function eo(e,t){return e?U(e)&&U(t)?[...new Set([...e,...t])]:ye(Object.create(null),Qr(e),Qr(t??{})):t}function _u(e,t){if(!e)return t;if(!t)return e;const s=ye(Object.create(null),e);for(const n in t)s[n]=we(e[n],t[n]);return s}function Xi(){return{app:null,config:{isNativeTag:uc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Eu=0;function Su(e,t){return function(n,r=null){q(n)||(n=ye({},n)),r!=null&&!ue(r)&&(r=null);const o=Xi(),i=new WeakSet,l=[];let a=!1;const f=o.app={_uid:Eu++,_component:n,_props:r,_container:null,_context:o,_instance:null,version:of,get config(){return o.config},set config(u){},use(u,...d){return i.has(u)||(u&&q(u.install)?(i.add(u),u.install(f,...d)):q(u)&&(i.add(u),u(f,...d))),f},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),f},component(u,d){return d?(o.components[u]=d,f):o.components[u]},directive(u,d){return d?(o.directives[u]=d,f):o.directives[u]},mount(u,d,m){if(!a){const g=f._ceVNode||J(n,r);return g.appContext=o,m===!0?m="svg":m===!1&&(m=void 0),e(g,u,m),a=!0,f._container=u,u.__vue_app__=f,En(g.component)}},onUnmount(u){l.push(u)},unmount(){a&&(tt(l,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(u,d){return o.provides[u]=d,f},runWithContext(u){const d=Jt;Jt=f;try{return u()}finally{Jt=d}}};return f}}let Jt=null;function Us(e,t){if(ve){let s=ve.provides;const n=ve.parent&&ve.parent.provides;n===s&&(s=ve.provides=Object.create(n)),s[e]=t}}function at(e,t,s=!1){const n=ve||$e;if(n||Jt){let r=Jt?Jt._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return s&&q(t)?t.call(n&&n.proxy):t}}const Qi={},Yi=()=>Object.create(Qi),Zi=e=>Object.getPrototypeOf(e)===Qi;function Ru(e,t,s,n=!1){const r={},o=Yi();e.propsDefaults=Object.create(null),el(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);s?e.props=n?r:ji(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Cu(e,t,s,n){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=ee(r),[a]=e.propsOptions;let f=!1;if((n||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let d=0;d<u.length;d++){let m=u[d];if(wn(e.emitsOptions,m))continue;const g=t[m];if(a)if(te(o,m))g!==o[m]&&(o[m]=g,f=!0);else{const y=Fe(m);r[y]=Zn(a,l,y,g,e,!1)}else g!==o[m]&&(o[m]=g,f=!0)}}}else{el(e,t,r,o)&&(f=!0);let u;for(const d in l)(!t||!te(t,d)&&((u=Dt(d))===d||!te(t,u)))&&(a?s&&(s[d]!==void 0||s[u]!==void 0)&&(r[d]=Zn(a,l,d,void 0,e,!0)):delete r[d]);if(o!==l)for(const d in o)(!t||!te(t,d))&&(delete o[d],f=!0)}f&&it(e.attrs,"set","")}function el(e,t,s,n){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(cs(a))continue;const f=t[a];let u;r&&te(r,u=Fe(a))?!o||!o.includes(u)?s[u]=f:(l||(l={}))[u]=f:wn(e.emitsOptions,a)||(!(a in n)||f!==n[a])&&(n[a]=f,i=!0)}if(o){const a=ee(s),f=l||oe;for(let u=0;u<o.length;u++){const d=o[u];s[d]=Zn(r,a,d,f[d],e,!te(f,d))}}return i}function Zn(e,t,s,n,r,o){const i=e[s];if(i!=null){const l=te(i,"default");if(l&&n===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&q(a)){const{propsDefaults:f}=r;if(s in f)n=f[s];else{const u=Ms(r);n=f[s]=a.call(null,t),u()}}else n=a;r.ce&&r.ce._setProp(s,n)}i[0]&&(o&&!l?n=!1:i[1]&&(n===""||n===Dt(s))&&(n=!0))}return n}const ku=new WeakMap;function tl(e,t,s=!1){const n=s?ku:t.propsCache,r=n.get(e);if(r)return r;const o=e.props,i={},l=[];let a=!1;if(!q(e)){const u=d=>{a=!0;const[m,g]=tl(d,t,!0);ye(i,m),g&&l.push(...g)};!s&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!a)return ue(e)&&n.set(e,Ht),Ht;if(U(o))for(let u=0;u<o.length;u++){const d=Fe(o[u]);to(d)&&(i[d]=oe)}else if(o)for(const u in o){const d=Fe(u);if(to(d)){const m=o[u],g=i[d]=U(m)||q(m)?{type:m}:ye({},m),y=g.type;let E=!1,R=!0;if(U(y))for(let O=0;O<y.length;++O){const T=y[O],j=q(T)&&T.name;if(j==="Boolean"){E=!0;break}else j==="String"&&(R=!1)}else E=q(y)&&y.name==="Boolean";g[0]=E,g[1]=R,(E||te(g,"default"))&&l.push(d)}}const f=[i,l];return ue(e)&&n.set(e,f),f}function to(e){return e[0]!=="$"&&!cs(e)}const Sr=e=>e[0]==="_"||e==="$stable",Rr=e=>U(e)?e.map(Ye):[Ye(e)],Au=(e,t,s)=>{if(t._n)return t;const n=ce((...r)=>Rr(t(...r)),s);return n._c=!1,n},sl=(e,t,s)=>{const n=e._ctx;for(const r in e){if(Sr(r))continue;const o=e[r];if(q(o))t[r]=Au(r,o,n);else if(o!=null){const i=Rr(o);t[r]=()=>i}}},nl=(e,t)=>{const s=Rr(t);e.slots.default=()=>s},rl=(e,t,s)=>{for(const n in t)(s||!Sr(n))&&(e[n]=t[n])},Ou=(e,t,s)=>{const n=e.slots=Yi();if(e.vnode.shapeFlag&32){const r=t._;r?(rl(n,t,s),s&&gi(n,"_",r,!0)):sl(t,n)}else t&&nl(e,t)},Tu=(e,t,s)=>{const{vnode:n,slots:r}=e;let o=!0,i=oe;if(n.shapeFlag&32){const l=t._;l?s&&l===1?o=!1:rl(r,t,s):(o=!t.$stable,sl(t,r)),i=t}else t&&(nl(e,t),i={default:1});if(o)for(const l in r)!Sr(l)&&i[l]==null&&delete r[l]},Pe=zu;function Pu(e){return Mu(e)}function Mu(e,t){const s=hn();s.__VUE__=!0;const{insert:n,remove:r,patchProp:o,createElement:i,createText:l,createComment:a,setText:f,setElementText:u,parentNode:d,nextSibling:m,setScopeId:g=Ze,insertStaticContent:y}=e,E=(p,h,v,b=null,S=null,_=null,P=void 0,A=null,k=!!h.dynamicChildren)=>{if(p===h)return;p&&!is(p,h)&&(b=w(p),Ee(p,S,_,!0),p=null),h.patchFlag===-2&&(k=!1,h.dynamicChildren=null);const{type:C,ref:B,shapeFlag:$}=h;switch(C){case _n:R(p,h,v,b);break;case St:O(p,h,v,b);break;case Hs:p==null&&T(h,v,b,P);break;case Me:Rt(p,h,v,b,S,_,P,A,k);break;default:$&1?K(p,h,v,b,S,_,P,A,k):$&6?ze(p,h,v,b,S,_,P,A,k):($&64||$&128)&&C.process(p,h,v,b,S,_,P,A,k,F)}B!=null&&S&&Zs(B,p&&p.ref,_,h||p,!h)},R=(p,h,v,b)=>{if(p==null)n(h.el=l(h.children),v,b);else{const S=h.el=p.el;h.children!==p.children&&f(S,h.children)}},O=(p,h,v,b)=>{p==null?n(h.el=a(h.children||""),v,b):h.el=p.el},T=(p,h,v,b)=>{[p.el,p.anchor]=y(p.children,h,v,b,p.el,p.anchor)},j=({el:p,anchor:h},v,b)=>{let S;for(;p&&p!==h;)S=m(p),n(p,v,b),p=S;n(h,v,b)},D=({el:p,anchor:h})=>{let v;for(;p&&p!==h;)v=m(p),r(p),p=v;r(h)},K=(p,h,v,b,S,_,P,A,k)=>{h.type==="svg"?P="svg":h.type==="math"&&(P="mathml"),p==null?ie(h,v,b,S,_,P,A,k):Ie(p,h,S,_,P,A,k)},ie=(p,h,v,b,S,_,P,A)=>{let k,C;const{props:B,shapeFlag:$,transition:I,dirs:z}=p;if(k=p.el=i(p.type,_,B&&B.is,B),$&8?u(k,p.children):$&16&&be(p.children,k,null,b,S,jn(p,_),P,A),z&&Ct(p,null,b,"created"),Q(k,p,p.scopeId,P,b),B){for(const le in B)le!=="value"&&!cs(le)&&o(k,le,null,B[le],_,b);"value"in B&&o(k,"value",null,B.value,_),(C=B.onVnodeBeforeMount)&&Xe(C,b,p)}z&&Ct(p,null,b,"beforeMount");const X=$u(S,I);X&&I.beforeEnter(k),n(k,h,v),((C=B&&B.onVnodeMounted)||X||z)&&Pe(()=>{C&&Xe(C,b,p),X&&I.enter(k),z&&Ct(p,null,b,"mounted")},S)},Q=(p,h,v,b,S)=>{if(v&&g(p,v),b)for(let _=0;_<b.length;_++)g(p,b[_]);if(S){let _=S.subTree;if(h===_||ul(_.type)&&(_.ssContent===h||_.ssFallback===h)){const P=S.vnode;Q(p,P,P.scopeId,P.slotScopeIds,S.parent)}}},be=(p,h,v,b,S,_,P,A,k=0)=>{for(let C=k;C<p.length;C++){const B=p[C]=A?xt(p[C]):Ye(p[C]);E(null,B,h,v,b,S,_,P,A)}},Ie=(p,h,v,b,S,_,P)=>{const A=h.el=p.el;let{patchFlag:k,dynamicChildren:C,dirs:B}=h;k|=p.patchFlag&16;const $=p.props||oe,I=h.props||oe;let z;if(v&&kt(v,!1),(z=I.onVnodeBeforeUpdate)&&Xe(z,v,h,p),B&&Ct(h,p,v,"beforeUpdate"),v&&kt(v,!0),($.innerHTML&&I.innerHTML==null||$.textContent&&I.textContent==null)&&u(A,""),C?He(p.dynamicChildren,C,A,v,b,jn(h,S),_):P||Y(p,h,A,null,v,b,jn(h,S),_,!1),k>0){if(k&16)ht(A,$,I,v,S);else if(k&2&&$.class!==I.class&&o(A,"class",null,I.class,S),k&4&&o(A,"style",$.style,I.style,S),k&8){const X=h.dynamicProps;for(let le=0;le<X.length;le++){const se=X[le],Oe=$[se],Se=I[se];(Se!==Oe||se==="value")&&o(A,se,Oe,Se,S,v)}}k&1&&p.children!==h.children&&u(A,h.children)}else!P&&C==null&&ht(A,$,I,v,S);((z=I.onVnodeUpdated)||B)&&Pe(()=>{z&&Xe(z,v,h,p),B&&Ct(h,p,v,"updated")},b)},He=(p,h,v,b,S,_,P)=>{for(let A=0;A<h.length;A++){const k=p[A],C=h[A],B=k.el&&(k.type===Me||!is(k,C)||k.shapeFlag&198)?d(k.el):v;E(k,C,B,null,b,S,_,P,!0)}},ht=(p,h,v,b,S)=>{if(h!==v){if(h!==oe)for(const _ in h)!cs(_)&&!(_ in v)&&o(p,_,h[_],null,S,b);for(const _ in v){if(cs(_))continue;const P=v[_],A=h[_];P!==A&&_!=="value"&&o(p,_,A,P,S,b)}"value"in v&&o(p,"value",h.value,v.value,S)}},Rt=(p,h,v,b,S,_,P,A,k)=>{const C=h.el=p?p.el:l(""),B=h.anchor=p?p.anchor:l("");let{patchFlag:$,dynamicChildren:I,slotScopeIds:z}=h;z&&(A=A?A.concat(z):z),p==null?(n(C,v,b),n(B,v,b),be(h.children||[],v,B,S,_,P,A,k)):$>0&&$&64&&I&&p.dynamicChildren?(He(p.dynamicChildren,I,v,S,_,P,A),(h.key!=null||S&&h===S.subTree)&&ol(p,h,!0)):Y(p,h,v,B,S,_,P,A,k)},ze=(p,h,v,b,S,_,P,A,k)=>{h.slotScopeIds=A,p==null?h.shapeFlag&512?S.ctx.activate(h,v,b,P,k):ss(h,v,b,S,_,P,k):Ft(p,h,k)},ss=(p,h,v,b,S,_,P)=>{const A=p.component=Yu(p,b,S);if(qi(p)&&(A.ctx.renderer=F),Zu(A,!1,P),A.asyncDep){if(S&&S.registerDep(A,pe,P),!p.el){const k=A.subTree=J(St);O(null,k,h,v)}}else pe(A,p,h,v,S,_,P)},Ft=(p,h,v)=>{const b=h.component=p.component;if(Vu(p,h,v))if(b.asyncDep&&!b.asyncResolved){ne(b,h,v);return}else b.next=h,b.update();else h.el=p.el,b.vnode=h},pe=(p,h,v,b,S,_,P)=>{const A=()=>{if(p.isMounted){let{next:$,bu:I,u:z,parent:X,vnode:le}=p;{const We=il(p);if(We){$&&($.el=le.el,ne(p,$,P)),We.asyncDep.then(()=>{p.isUnmounted||A()});return}}let se=$,Oe;kt(p,!1),$?($.el=le.el,ne(p,$,P)):$=le,I&&Bs(I),(Oe=$.props&&$.props.onVnodeBeforeUpdate)&&Xe(Oe,X,$,le),kt(p,!0);const Se=no(p),Ke=p.subTree;p.subTree=Se,E(Ke,Se,d(Ke.el),w(Ke),p,S,_),$.el=Se.el,se===null&&Hu(p,Se.el),z&&Pe(z,S),(Oe=$.props&&$.props.onVnodeUpdated)&&Pe(()=>Xe(Oe,X,$,le),S)}else{let $;const{el:I,props:z}=h,{bm:X,m:le,parent:se,root:Oe,type:Se}=p,Ke=ds(h);kt(p,!1),X&&Bs(X),!Ke&&($=z&&z.onVnodeBeforeMount)&&Xe($,se,h),kt(p,!0);{Oe.ce&&Oe.ce._injectChildStyle(Se);const We=p.subTree=no(p);E(null,We,v,b,p,S,_),h.el=We.el}if(le&&Pe(le,S),!Ke&&($=z&&z.onVnodeMounted)){const We=h;Pe(()=>Xe($,se,We),S)}(h.shapeFlag&256||se&&ds(se.vnode)&&se.vnode.shapeFlag&256)&&p.a&&Pe(p.a,S),p.isMounted=!0,h=v=b=null}};p.scope.on();const k=p.effect=new bi(A);p.scope.off();const C=p.update=k.run.bind(k),B=p.job=k.runIfDirty.bind(k);B.i=p,B.id=p.uid,k.scheduler=()=>_r(B),kt(p,!0),C()},ne=(p,h,v)=>{h.component=p;const b=p.vnode.props;p.vnode=h,p.next=null,Cu(p,h.props,b,v),Tu(p,h.children,v),ft(),Gr(p),dt()},Y=(p,h,v,b,S,_,P,A,k=!1)=>{const C=p&&p.children,B=p?p.shapeFlag:0,$=h.children,{patchFlag:I,shapeFlag:z}=h;if(I>0){if(I&128){mt(C,$,v,b,S,_,P,A,k);return}else if(I&256){st(C,$,v,b,S,_,P,A,k);return}}z&8?(B&16&&De(C,S,_),$!==C&&u(v,$)):B&16?z&16?mt(C,$,v,b,S,_,P,A,k):De(C,S,_,!0):(B&8&&u(v,""),z&16&&be($,v,b,S,_,P,A,k))},st=(p,h,v,b,S,_,P,A,k)=>{p=p||Ht,h=h||Ht;const C=p.length,B=h.length,$=Math.min(C,B);let I;for(I=0;I<$;I++){const z=h[I]=k?xt(h[I]):Ye(h[I]);E(p[I],z,v,null,S,_,P,A,k)}C>B?De(p,S,_,!0,!1,$):be(h,v,b,S,_,P,A,k,$)},mt=(p,h,v,b,S,_,P,A,k)=>{let C=0;const B=h.length;let $=p.length-1,I=B-1;for(;C<=$&&C<=I;){const z=p[C],X=h[C]=k?xt(h[C]):Ye(h[C]);if(is(z,X))E(z,X,v,null,S,_,P,A,k);else break;C++}for(;C<=$&&C<=I;){const z=p[$],X=h[I]=k?xt(h[I]):Ye(h[I]);if(is(z,X))E(z,X,v,null,S,_,P,A,k);else break;$--,I--}if(C>$){if(C<=I){const z=I+1,X=z<B?h[z].el:b;for(;C<=I;)E(null,h[C]=k?xt(h[C]):Ye(h[C]),v,X,S,_,P,A,k),C++}}else if(C>I)for(;C<=$;)Ee(p[C],S,_,!0),C++;else{const z=C,X=C,le=new Map;for(C=X;C<=I;C++){const Te=h[C]=k?xt(h[C]):Ye(h[C]);Te.key!=null&&le.set(Te.key,C)}let se,Oe=0;const Se=I-X+1;let Ke=!1,We=0;const ns=new Array(Se);for(C=0;C<Se;C++)ns[C]=0;for(C=z;C<=$;C++){const Te=p[C];if(Oe>=Se){Ee(Te,S,_,!0);continue}let Je;if(Te.key!=null)Je=le.get(Te.key);else for(se=X;se<=I;se++)if(ns[se-X]===0&&is(Te,h[se])){Je=se;break}Je===void 0?Ee(Te,S,_,!0):(ns[Je-X]=C+1,Je>=We?We=Je:Ke=!0,E(Te,h[Je],v,null,S,_,P,A,k),Oe++)}const Tr=Ke?ju(ns):Ht;for(se=Tr.length-1,C=Se-1;C>=0;C--){const Te=X+C,Je=h[Te],Pr=Te+1<B?h[Te+1].el:b;ns[C]===0?E(null,Je,v,Pr,S,_,P,A,k):Ke&&(se<0||C!==Tr[se]?qe(Je,v,Pr,2):se--)}}},qe=(p,h,v,b,S=null)=>{const{el:_,type:P,transition:A,children:k,shapeFlag:C}=p;if(C&6){qe(p.component.subTree,h,v,b);return}if(C&128){p.suspense.move(h,v,b);return}if(C&64){P.move(p,h,v,F);return}if(P===Me){n(_,h,v);for(let $=0;$<k.length;$++)qe(k[$],h,v,b);n(p.anchor,h,v);return}if(P===Hs){j(p,h,v);return}if(b!==2&&C&1&&A)if(b===0)A.beforeEnter(_),n(_,h,v),Pe(()=>A.enter(_),S);else{const{leave:$,delayLeave:I,afterLeave:z}=A,X=()=>{p.ctx.isUnmounted?r(_):n(_,h,v)},le=()=>{$(_,()=>{X(),z&&z()})};I?I(_,X,le):le()}else n(_,h,v)},Ee=(p,h,v,b=!1,S=!1)=>{const{type:_,props:P,ref:A,children:k,dynamicChildren:C,shapeFlag:B,patchFlag:$,dirs:I,cacheIndex:z}=p;if($===-2&&(S=!1),A!=null&&(ft(),Zs(A,null,v,p,!0),dt()),z!=null&&(h.renderCache[z]=void 0),B&256){h.ctx.deactivate(p);return}const X=B&1&&I,le=!ds(p);let se;if(le&&(se=P&&P.onVnodeBeforeUnmount)&&Xe(se,h,p),B&6)$s(p.component,v,b);else{if(B&128){p.suspense.unmount(v,b);return}X&&Ct(p,null,h,"beforeUnmount"),B&64?p.type.remove(p,h,v,F,b):C&&!C.hasOnce&&(_!==Me||$>0&&$&64)?De(C,h,v,!1,!0):(_===Me&&$&384||!S&&B&16)&&De(k,h,v),b&&It(p)}(le&&(se=P&&P.onVnodeUnmounted)||X)&&Pe(()=>{se&&Xe(se,h,p),X&&Ct(p,null,h,"unmounted")},v)},It=p=>{const{type:h,el:v,anchor:b,transition:S}=p;if(h===Me){Lt(v,b);return}if(h===Hs){D(p);return}const _=()=>{r(v),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(p.shapeFlag&1&&S&&!S.persisted){const{leave:P,delayLeave:A}=S,k=()=>P(v,_);A?A(p.el,_,k):k()}else _()},Lt=(p,h)=>{let v;for(;p!==h;)v=m(p),r(p),p=v;r(h)},$s=(p,h,v)=>{const{bum:b,scope:S,job:_,subTree:P,um:A,m:k,a:C,parent:B,slots:{__:$}}=p;so(k),so(C),b&&Bs(b),B&&U($)&&$.forEach(I=>{B.renderCache[I]=void 0}),S.stop(),_&&(_.flags|=8,Ee(P,p,h,v)),A&&Pe(A,h),Pe(()=>{p.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},De=(p,h,v,b=!1,S=!1,_=0)=>{for(let P=_;P<p.length;P++)Ee(p[P],h,v,b,S)},w=p=>{if(p.shapeFlag&6)return w(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const h=m(p.anchor||p.el),v=h&&h[eu];return v?m(v):h};let N=!1;const M=(p,h,v)=>{p==null?h._vnode&&Ee(h._vnode,null,null,!0):E(h._vnode||null,p,h,null,null,null,v),h._vnode=p,N||(N=!0,Gr(),Bi(),N=!1)},F={p:E,um:Ee,m:qe,r:It,mt:ss,mc:be,pc:Y,pbc:He,n:w,o:e};return{render:M,hydrate:void 0,createApp:Su(M)}}function jn({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function kt({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function $u(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ol(e,t,s=!1){const n=e.children,r=t.children;if(U(n)&&U(r))for(let o=0;o<n.length;o++){const i=n[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=xt(r[o]),l.el=i.el),!s&&l.patchFlag!==-2&&ol(i,l)),l.type===_n&&(l.el=i.el),l.type===St&&!l.el&&(l.el=i.el)}}function ju(e){const t=e.slice(),s=[0];let n,r,o,i,l;const a=e.length;for(n=0;n<a;n++){const f=e[n];if(f!==0){if(r=s[s.length-1],e[r]<f){t[n]=r,s.push(n);continue}for(o=0,i=s.length-1;o<i;)l=o+i>>1,e[s[l]]<f?o=l+1:i=l;f<e[s[o]]&&(o>0&&(t[n]=s[o-1]),s[o]=n)}}for(o=s.length,i=s[o-1];o-- >0;)s[o]=i,i=t[i];return s}function il(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:il(t)}function so(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Du=Symbol.for("v-scx"),Nu=()=>at(Du);function Vs(e,t,s){return ll(e,t,s)}function ll(e,t,s=oe){const{immediate:n,deep:r,flush:o,once:i}=s,l=ye({},s),a=t&&n||!t&&o!=="post";let f;if(Es){if(o==="sync"){const g=Nu();f=g.__watcherHandles||(g.__watcherHandles=[])}else if(!a){const g=()=>{};return g.stop=Ze,g.resume=Ze,g.pause=Ze,g}}const u=ve;l.call=(g,y,E)=>tt(g,u,y,E);let d=!1;o==="post"?l.scheduler=g=>{Pe(g,u&&u.suspense)}:o!=="sync"&&(d=!0,l.scheduler=(g,y)=>{y?g():_r(g)}),l.augmentJob=g=>{t&&(g.flags|=4),d&&(g.flags|=2,u&&(g.id=u.uid,g.i=u))};const m=Xc(e,t,l);return Es&&(f?f.push(m):a&&m()),m}function Fu(e,t,s){const n=this.proxy,r=de(e)?e.includes(".")?al(n,e):()=>n[e]:e.bind(n,n);let o;q(t)?o=t:(o=t.handler,s=t);const i=Ms(this),l=ll(r,o.bind(n),s);return i(),l}function al(e,t){const s=t.split(".");return()=>{let n=e;for(let r=0;r<s.length&&n;r++)n=n[s[r]];return n}}const Iu=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Fe(t)}Modifiers`]||e[`${Dt(t)}Modifiers`];function Lu(e,t,...s){if(e.isUnmounted)return;const n=e.vnode.props||oe;let r=s;const o=t.startsWith("update:"),i=o&&Iu(n,t.slice(7));i&&(i.trim&&(r=s.map(u=>de(u)?u.trim():u)),i.number&&(r=s.map(Js)));let l,a=n[l=An(t)]||n[l=An(Fe(t))];!a&&o&&(a=n[l=An(Dt(t))]),a&&tt(a,e,6,r);const f=n[l+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,tt(f,e,6,r)}}function cl(e,t,s=!1){const n=t.emitsCache,r=n.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!q(e)){const a=f=>{const u=cl(f,t,!0);u&&(l=!0,ye(i,u))};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(ue(e)&&n.set(e,null),null):(U(o)?o.forEach(a=>i[a]=null):ye(i,o),ue(e)&&n.set(e,i),i)}function wn(e,t){return!e||!fn(t)?!1:(t=t.slice(2).replace(/Once$/,""),te(e,t[0].toLowerCase()+t.slice(1))||te(e,Dt(t))||te(e,t))}function no(e){const{type:t,vnode:s,proxy:n,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:a,render:f,renderCache:u,props:d,data:m,setupState:g,ctx:y,inheritAttrs:E}=e,R=Ys(e);let O,T;try{if(s.shapeFlag&4){const D=r||n,K=D;O=Ye(f.call(K,D,u,d,g,m,y)),T=l}else{const D=t;O=Ye(D.length>1?D(d,{attrs:l,slots:i,emit:a}):D(d,null)),T=t.props?l:Bu(l)}}catch(D){hs.length=0,xn(D,e,1),O=J(St)}let j=O;if(T&&E!==!1){const D=Object.keys(T),{shapeFlag:K}=j;D.length&&K&7&&(o&&D.some(cr)&&(T=Uu(T,o)),j=Gt(j,T,!1,!0))}return s.dirs&&(j=Gt(j,null,!1,!0),j.dirs=j.dirs?j.dirs.concat(s.dirs):s.dirs),s.transition&&Er(j,s.transition),O=j,Ys(R),O}const Bu=e=>{let t;for(const s in e)(s==="class"||s==="style"||fn(s))&&((t||(t={}))[s]=e[s]);return t},Uu=(e,t)=>{const s={};for(const n in e)(!cr(n)||!(n.slice(9)in t))&&(s[n]=e[n]);return s};function Vu(e,t,s){const{props:n,children:r,component:o}=e,{props:i,children:l,patchFlag:a}=t,f=o.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&a>=0){if(a&1024)return!0;if(a&16)return n?ro(n,i,f):!!i;if(a&8){const u=t.dynamicProps;for(let d=0;d<u.length;d++){const m=u[d];if(i[m]!==n[m]&&!wn(f,m))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:n===i?!1:n?i?ro(n,i,f):!0:!!i;return!1}function ro(e,t,s){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let r=0;r<n.length;r++){const o=n[r];if(t[o]!==e[o]&&!wn(s,o))return!0}return!1}function Hu({vnode:e,parent:t},s){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=s,t=t.parent;else break}}const ul=e=>e.__isSuspense;function zu(e,t){t&&t.pendingBranch?U(e)?t.effects.push(...e):t.effects.push(e):Zc(e)}const Me=Symbol.for("v-fgt"),_n=Symbol.for("v-txt"),St=Symbol.for("v-cmt"),Hs=Symbol.for("v-stc"),hs=[];let je=null;function V(e=!1){hs.push(je=e?null:[])}function qu(){hs.pop(),je=hs[hs.length-1]||null}let _s=1;function oo(e,t=!1){_s+=e,e<0&&je&&t&&(je.hasOnce=!0)}function fl(e){return e.dynamicChildren=_s>0?je||Ht:null,qu(),_s>0&&je&&je.push(e),e}function H(e,t,s,n,r,o){return fl(c(e,t,s,n,r,o,!0))}function Ku(e,t,s,n,r){return fl(J(e,t,s,n,r,!0))}function tn(e){return e?e.__v_isVNode===!0:!1}function is(e,t){return e.type===t.type&&e.key===t.key}const dl=({key:e})=>e??null,zs=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?de(e)||xe(e)||q(e)?{i:$e,r:e,k:t,f:!!s}:e:null);function c(e,t=null,s=null,n=0,r=null,o=e===Me?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&dl(t),ref:t&&zs(t),scopeId:Vi,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:n,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:$e};return l?(Cr(a,s),o&128&&e.normalize(a)):s&&(a.shapeFlag|=de(s)?8:16),_s>0&&!i&&je&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&je.push(a),a}const J=Wu;function Wu(e,t=null,s=null,n=0,r=null,o=!1){if((!e||e===mu)&&(e=St),tn(e)){const l=Gt(e,t,!0);return s&&Cr(l,s),_s>0&&!o&&je&&(l.shapeFlag&6?je[je.indexOf(e)]=l:je.push(l)),l.patchFlag=-2,l}if(rf(e)&&(e=e.__vccOpts),t){t=Ju(t);let{class:l,style:a}=t;l&&!de(l)&&(t.class=mn(l)),ue(a)&&(br(a)&&!U(a)&&(a=ye({},a)),t.style=dr(a))}const i=de(e)?1:ul(e)?128:tu(e)?64:ue(e)?4:q(e)?2:0;return c(e,t,s,n,r,i,o,!0)}function Ju(e){return e?br(e)||Zi(e)?ye({},e):e:null}function Gt(e,t,s=!1,n=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:a}=e,f=t?Gu(r||{},t):r,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&dl(f),ref:t&&t.ref?s&&o?U(o)?o.concat(zs(t)):[o,zs(t)]:zs(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Me?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Gt(e.ssContent),ssFallback:e.ssFallback&&Gt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&n&&Er(u,a.clone(u)),u}function G(e=" ",t=0){return J(_n,null,e,t)}function $t(e,t){const s=J(Hs,null,e);return s.staticCount=t,s}function ct(e="",t=!1){return t?(V(),Ku(St,null,e)):J(St,null,e)}function Ye(e){return e==null||typeof e=="boolean"?J(St):U(e)?J(Me,null,e.slice()):tn(e)?xt(e):J(_n,null,String(e))}function xt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Gt(e)}function Cr(e,t){let s=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(U(t))s=16;else if(typeof t=="object")if(n&65){const r=t.default;r&&(r._c&&(r._d=!1),Cr(e,r()),r._c&&(r._d=!0));return}else{s=32;const r=t._;!r&&!Zi(t)?t._ctx=$e:r===3&&$e&&($e.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else q(t)?(t={default:t,_ctx:$e},s=32):(t=String(t),n&64?(s=16,t=[G(t)]):s=8);e.children=t,e.shapeFlag|=s}function Gu(...e){const t={};for(let s=0;s<e.length;s++){const n=e[s];for(const r in n)if(r==="class")t.class!==n.class&&(t.class=mn([t.class,n.class]));else if(r==="style")t.style=dr([t.style,n.style]);else if(fn(r)){const o=t[r],i=n[r];i&&o!==i&&!(U(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=n[r])}return t}function Xe(e,t,s,n=null){tt(e,t,7,[s,n])}const Xu=Xi();let Qu=0;function Yu(e,t,s){const n=e.type,r=(t?t.appContext:e.appContext)||Xu,o={uid:Qu++,vnode:e,type:n,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new _c(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:tl(n,r),emitsOptions:cl(n,r),emit:null,emitted:null,propsDefaults:oe,inheritAttrs:n.inheritAttrs,ctx:oe,data:oe,props:oe,attrs:oe,slots:oe,refs:oe,setupState:oe,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Lu.bind(null,o),e.ce&&e.ce(o),o}let ve=null,sn,er;{const e=hn(),t=(s,n)=>{let r;return(r=e[s])||(r=e[s]=[]),r.push(n),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};sn=t("__VUE_INSTANCE_SETTERS__",s=>ve=s),er=t("__VUE_SSR_SETTERS__",s=>Es=s)}const Ms=e=>{const t=ve;return sn(e),e.scope.on(),()=>{e.scope.off(),sn(t)}},io=()=>{ve&&ve.scope.off(),sn(null)};function pl(e){return e.vnode.shapeFlag&4}let Es=!1;function Zu(e,t=!1,s=!1){t&&er(t);const{props:n,children:r}=e.vnode,o=pl(e);Ru(e,n,o,t),Ou(e,r,s||t);const i=o?ef(e,t):void 0;return t&&er(!1),i}function ef(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,vu);const{setup:n}=s;if(n){ft();const r=e.setupContext=n.length>1?sf(e):null,o=Ms(e),i=Ps(n,e,0,[e.props,r]),l=pi(i);if(dt(),o(),(l||e.sp)&&!ds(e)&&zi(e),l){if(i.then(io,io),t)return i.then(a=>{lo(e,a)}).catch(a=>{xn(a,e,0)});e.asyncDep=i}else lo(e,i)}else hl(e)}function lo(e,t,s){q(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ue(t)&&(e.setupState=Fi(t)),hl(e)}function hl(e,t,s){const n=e.type;e.render||(e.render=n.render||Ze);{const r=Ms(e);ft();try{xu(e)}finally{dt(),r()}}}const tf={get(e,t){return me(e,"get",""),e[t]}};function sf(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,tf),slots:e.slots,emit:e.emit,expose:t}}function En(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Fi(Vc(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in ps)return ps[s](e)},has(t,s){return s in t||s in ps}})):e.proxy}function nf(e,t=!0){return q(e)?e.displayName||e.name:e.name||t&&e.__name}function rf(e){return q(e)&&"__vccOpts"in e}const Le=(e,t)=>Jc(e,t,Es);function ml(e,t,s){const n=arguments.length;return n===2?ue(t)&&!U(t)?tn(t)?J(e,null,[t]):J(e,t):J(e,null,t):(n>3?s=Array.prototype.slice.call(arguments,2):n===3&&tn(s)&&(s=[s]),J(e,t,s))}const of="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let tr;const ao=typeof window<"u"&&window.trustedTypes;if(ao)try{tr=ao.createPolicy("vue",{createHTML:e=>e})}catch{}const gl=tr?e=>tr.createHTML(e):e=>e,lf="http://www.w3.org/2000/svg",af="http://www.w3.org/1998/Math/MathML",ot=typeof document<"u"?document:null,co=ot&&ot.createElement("template"),cf={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,n)=>{const r=t==="svg"?ot.createElementNS(lf,e):t==="mathml"?ot.createElementNS(af,e):s?ot.createElement(e,{is:s}):ot.createElement(e);return e==="select"&&n&&n.multiple!=null&&r.setAttribute("multiple",n.multiple),r},createText:e=>ot.createTextNode(e),createComment:e=>ot.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ot.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,n,r,o){const i=s?s.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),s),!(r===o||!(r=r.nextSibling)););else{co.innerHTML=gl(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const l=co.content;if(n==="svg"||n==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,s)}return[i?i.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},uf=Symbol("_vtc");function ff(e,t,s){const n=e[uf];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const uo=Symbol("_vod"),df=Symbol("_vsh"),pf=Symbol(""),hf=/(^|;)\s*display\s*:/;function mf(e,t,s){const n=e.style,r=de(s);let o=!1;if(s&&!r){if(t)if(de(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();s[l]==null&&qs(n,l,"")}else for(const i in t)s[i]==null&&qs(n,i,"");for(const i in s)i==="display"&&(o=!0),qs(n,i,s[i])}else if(r){if(t!==s){const i=n[pf];i&&(s+=";"+i),n.cssText=s,o=hf.test(s)}}else t&&e.removeAttribute("style");uo in e&&(e[uo]=o?n.display:"",e[df]&&(n.display="none"))}const fo=/\s*!important$/;function qs(e,t,s){if(U(s))s.forEach(n=>qs(e,t,n));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const n=gf(e,t);fo.test(s)?e.setProperty(Dt(n),s.replace(fo,""),"important"):e[n]=s}}const po=["Webkit","Moz","ms"],Dn={};function gf(e,t){const s=Dn[t];if(s)return s;let n=Fe(t);if(n!=="filter"&&n in e)return Dn[t]=n;n=pn(n);for(let r=0;r<po.length;r++){const o=po[r]+n;if(o in e)return Dn[t]=o}return t}const ho="http://www.w3.org/1999/xlink";function mo(e,t,s,n,r,o=bc(t)){n&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(ho,t.slice(6,t.length)):e.setAttributeNS(ho,t,s):s==null||o&&!vi(s)?e.removeAttribute(t):e.setAttribute(t,o?"":et(s)?String(s):s)}function go(e,t,s,n,r){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?gl(s):s);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=s==null?e.type==="checkbox"?"on":"":String(s);(l!==a||!("_value"in e))&&(e.value=a),s==null&&e.removeAttribute(t),e._value=s;return}let i=!1;if(s===""||s==null){const l=typeof e[t];l==="boolean"?s=vi(s):s==null&&l==="string"?(s="",i=!0):l==="number"&&(s=0,i=!0)}try{e[t]=s}catch{}i&&e.removeAttribute(r||t)}function bt(e,t,s,n){e.addEventListener(t,s,n)}function vf(e,t,s,n){e.removeEventListener(t,s,n)}const vo=Symbol("_vei");function xf(e,t,s,n,r=null){const o=e[vo]||(e[vo]={}),i=o[t];if(n&&i)i.value=n;else{const[l,a]=yf(t);if(n){const f=o[t]=_f(n,r);bt(e,l,f,a)}else i&&(vf(e,l,i,a),o[t]=void 0)}}const xo=/(?:Once|Passive|Capture)$/;function yf(e){let t;if(xo.test(e)){t={};let n;for(;n=e.match(xo);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Dt(e.slice(2)),t]}let Nn=0;const bf=Promise.resolve(),wf=()=>Nn||(bf.then(()=>Nn=0),Nn=Date.now());function _f(e,t){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;tt(Ef(n,s.value),t,5,[n])};return s.value=e,s.attached=wf(),s}function Ef(e,t){if(U(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(n=>r=>!r._stopped&&n&&n(r))}else return t}const yo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Sf=(e,t,s,n,r,o)=>{const i=r==="svg";t==="class"?ff(e,n,i):t==="style"?mf(e,s,n):fn(t)?cr(t)||xf(e,t,s,n,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Rf(e,t,n,i))?(go(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&mo(e,t,n,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!de(n))?go(e,Fe(t),n,o,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),mo(e,t,n,i))};function Rf(e,t,s,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&yo(t)&&q(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return yo(t)&&de(s)?!1:t in e}const Xt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return U(t)?s=>Bs(t,s):t};function Cf(e){e.target.composing=!0}function bo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ut=Symbol("_assign"),Tt={created(e,{modifiers:{lazy:t,trim:s,number:n}},r){e[ut]=Xt(r);const o=n||r.props&&r.props.type==="number";bt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;s&&(l=l.trim()),o&&(l=Js(l)),e[ut](l)}),s&&bt(e,"change",()=>{e.value=e.value.trim()}),t||(bt(e,"compositionstart",Cf),bt(e,"compositionend",bo),bt(e,"change",bo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:n,trim:r,number:o}},i){if(e[ut]=Xt(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?Js(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(n&&t===s||r&&e.value.trim()===a)||(e.value=a))}},vl={deep:!0,created(e,t,s){e[ut]=Xt(s),bt(e,"change",()=>{const n=e._modelValue,r=Ss(e),o=e.checked,i=e[ut];if(U(n)){const l=pr(n,r),a=l!==-1;if(o&&!a)i(n.concat(r));else if(!o&&a){const f=[...n];f.splice(l,1),i(f)}}else if(ts(n)){const l=new Set(n);o?l.add(r):l.delete(r),i(l)}else i(xl(e,o))})},mounted:wo,beforeUpdate(e,t,s){e[ut]=Xt(s),wo(e,t,s)}};function wo(e,{value:t,oldValue:s},n){e._modelValue=t;let r;if(U(t))r=pr(t,n.props.value)>-1;else if(ts(t))r=t.has(n.props.value);else{if(t===s)return;r=Ts(t,xl(e,!0))}e.checked!==r&&(e.checked=r)}const ms={deep:!0,created(e,{value:t,modifiers:{number:s}},n){const r=ts(t);bt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>s?Js(Ss(i)):Ss(i));e[ut](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,wr(()=>{e._assigning=!1})}),e[ut]=Xt(n)},mounted(e,{value:t}){_o(e,t)},beforeUpdate(e,t,s){e[ut]=Xt(s)},updated(e,{value:t}){e._assigning||_o(e,t)}};function _o(e,t){const s=e.multiple,n=U(t);if(!(s&&!n&&!ts(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],l=Ss(i);if(s)if(n){const a=typeof l;a==="string"||a==="number"?i.selected=t.some(f=>String(f)===String(l)):i.selected=pr(t,l)>-1}else i.selected=t.has(l);else if(Ts(Ss(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Ss(e){return"_value"in e?e._value:e.value}function xl(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const kf=["ctrl","shift","alt","meta"],Af={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>kf.some(s=>e[`${s}Key`]&&!t.includes(s))},yl=(e,t)=>{const s=e._withMods||(e._withMods={}),n=t.join(".");return s[n]||(s[n]=(r,...o)=>{for(let i=0;i<t.length;i++){const l=Af[t[i]];if(l&&l(r,t))return}return e(r,...o)})},Of=ye({patchProp:Sf},cf);let Eo;function Tf(){return Eo||(Eo=Pu(Of))}const Pf=(...e)=>{const t=Tf().createApp(...e),{mount:s}=t;return t.mount=n=>{const r=$f(n);if(!r)return;const o=t._component;!q(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=s(r,!1,Mf(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Mf(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function $f(e){return de(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Vt=typeof document<"u";function bl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function jf(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&bl(e.default)}const Z=Object.assign;function Fn(e,t){const s={};for(const n in t){const r=t[n];s[n]=Ue(r)?r.map(e):e(r)}return s}const gs=()=>{},Ue=Array.isArray,wl=/#/g,Df=/&/g,Nf=/\//g,Ff=/=/g,If=/\?/g,_l=/\+/g,Lf=/%5B/g,Bf=/%5D/g,El=/%5E/g,Uf=/%60/g,Sl=/%7B/g,Vf=/%7C/g,Rl=/%7D/g,Hf=/%20/g;function kr(e){return encodeURI(""+e).replace(Vf,"|").replace(Lf,"[").replace(Bf,"]")}function zf(e){return kr(e).replace(Sl,"{").replace(Rl,"}").replace(El,"^")}function sr(e){return kr(e).replace(_l,"%2B").replace(Hf,"+").replace(wl,"%23").replace(Df,"%26").replace(Uf,"`").replace(Sl,"{").replace(Rl,"}").replace(El,"^")}function qf(e){return sr(e).replace(Ff,"%3D")}function Kf(e){return kr(e).replace(wl,"%23").replace(If,"%3F")}function Wf(e){return e==null?"":Kf(e).replace(Nf,"%2F")}function Rs(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Jf=/\/$/,Gf=e=>e.replace(Jf,"");function In(e,t,s="/"){let n,r={},o="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(n=t.slice(0,a),o=t.slice(a+1,l>-1?l:t.length),r=e(o)),l>-1&&(n=n||t.slice(0,l),i=t.slice(l,t.length)),n=Zf(n??t,s),{fullPath:n+(o&&"?")+o+i,path:n,query:r,hash:Rs(i)}}function Xf(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function So(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Qf(e,t,s){const n=t.matched.length-1,r=s.matched.length-1;return n>-1&&n===r&&Qt(t.matched[n],s.matched[r])&&Cl(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Qt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Cl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!Yf(e[s],t[s]))return!1;return!0}function Yf(e,t){return Ue(e)?Ro(e,t):Ue(t)?Ro(t,e):e===t}function Ro(e,t){return Ue(t)?e.length===t.length&&e.every((s,n)=>s===t[n]):e.length===1&&e[0]===t}function Zf(e,t){if(e.startsWith("/"))return e;if(!e)return t;const s=t.split("/"),n=e.split("/"),r=n[n.length-1];(r===".."||r===".")&&n.push("");let o=s.length-1,i,l;for(i=0;i<n.length;i++)if(l=n[i],l!==".")if(l==="..")o>1&&o--;else break;return s.slice(0,o).join("/")+"/"+n.slice(i).join("/")}const gt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Cs;(function(e){e.pop="pop",e.push="push"})(Cs||(Cs={}));var vs;(function(e){e.back="back",e.forward="forward",e.unknown=""})(vs||(vs={}));function ed(e){if(!e)if(Vt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Gf(e)}const td=/^[^#]+#/;function sd(e,t){return e.replace(td,"#")+t}function nd(e,t){const s=document.documentElement.getBoundingClientRect(),n=e.getBoundingClientRect();return{behavior:t.behavior,left:n.left-s.left-(t.left||0),top:n.top-s.top-(t.top||0)}}const Sn=()=>({left:window.scrollX,top:window.scrollY});function rd(e){let t;if("el"in e){const s=e.el,n=typeof s=="string"&&s.startsWith("#"),r=typeof s=="string"?n?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!r)return;t=nd(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Co(e,t){return(history.state?history.state.position-t:-1)+e}const nr=new Map;function od(e,t){nr.set(e,t)}function id(e){const t=nr.get(e);return nr.delete(e),t}let ld=()=>location.protocol+"//"+location.host;function kl(e,t){const{pathname:s,search:n,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,a=r.slice(l);return a[0]!=="/"&&(a="/"+a),So(a,"")}return So(s,e)+n+r}function ad(e,t,s,n){let r=[],o=[],i=null;const l=({state:m})=>{const g=kl(e,location),y=s.value,E=t.value;let R=0;if(m){if(s.value=g,t.value=m,i&&i===y){i=null;return}R=E?m.position-E.position:0}else n(g);r.forEach(O=>{O(s.value,y,{delta:R,type:Cs.pop,direction:R?R>0?vs.forward:vs.back:vs.unknown})})};function a(){i=s.value}function f(m){r.push(m);const g=()=>{const y=r.indexOf(m);y>-1&&r.splice(y,1)};return o.push(g),g}function u(){const{history:m}=window;m.state&&m.replaceState(Z({},m.state,{scroll:Sn()}),"")}function d(){for(const m of o)m();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:a,listen:f,destroy:d}}function ko(e,t,s,n=!1,r=!1){return{back:e,current:t,forward:s,replaced:n,position:window.history.length,scroll:r?Sn():null}}function cd(e){const{history:t,location:s}=window,n={value:kl(e,s)},r={value:t.state};r.value||o(n.value,{back:null,current:n.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,f,u){const d=e.indexOf("#"),m=d>-1?(s.host&&document.querySelector("base")?e:e.slice(d))+a:ld()+e+a;try{t[u?"replaceState":"pushState"](f,"",m),r.value=f}catch(g){console.error(g),s[u?"replace":"assign"](m)}}function i(a,f){const u=Z({},t.state,ko(r.value.back,a,r.value.forward,!0),f,{position:r.value.position});o(a,u,!0),n.value=a}function l(a,f){const u=Z({},r.value,t.state,{forward:a,scroll:Sn()});o(u.current,u,!0);const d=Z({},ko(n.value,a,null),{position:u.position+1},f);o(a,d,!1),n.value=a}return{location:n,state:r,push:l,replace:i}}function ud(e){e=ed(e);const t=cd(e),s=ad(e,t.state,t.location,t.replace);function n(o,i=!0){i||s.pauseListeners(),history.go(o)}const r=Z({location:"",base:e,go:n,createHref:sd.bind(null,e)},t,s);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function fd(e){return typeof e=="string"||e&&typeof e=="object"}function Al(e){return typeof e=="string"||typeof e=="symbol"}const Ol=Symbol("");var Ao;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ao||(Ao={}));function Yt(e,t){return Z(new Error,{type:e,[Ol]:!0},t)}function rt(e,t){return e instanceof Error&&Ol in e&&(t==null||!!(e.type&t))}const Oo="[^/]+?",dd={sensitive:!1,strict:!1,start:!0,end:!0},pd=/[.+*?^${}()[\]/\\]/g;function hd(e,t){const s=Z({},dd,t),n=[];let r=s.start?"^":"";const o=[];for(const f of e){const u=f.length?[]:[90];s.strict&&!f.length&&(r+="/");for(let d=0;d<f.length;d++){const m=f[d];let g=40+(s.sensitive?.25:0);if(m.type===0)d||(r+="/"),r+=m.value.replace(pd,"\\$&"),g+=40;else if(m.type===1){const{value:y,repeatable:E,optional:R,regexp:O}=m;o.push({name:y,repeatable:E,optional:R});const T=O||Oo;if(T!==Oo){g+=10;try{new RegExp(`(${T})`)}catch(D){throw new Error(`Invalid custom RegExp for param "${y}" (${T}): `+D.message)}}let j=E?`((?:${T})(?:/(?:${T}))*)`:`(${T})`;d||(j=R&&f.length<2?`(?:/${j})`:"/"+j),R&&(j+="?"),r+=j,g+=20,R&&(g+=-8),E&&(g+=-20),T===".*"&&(g+=-50)}u.push(g)}n.push(u)}if(s.strict&&s.end){const f=n.length-1;n[f][n[f].length-1]+=.7000000000000001}s.strict||(r+="/?"),s.end?r+="$":s.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,s.sensitive?"":"i");function l(f){const u=f.match(i),d={};if(!u)return null;for(let m=1;m<u.length;m++){const g=u[m]||"",y=o[m-1];d[y.name]=g&&y.repeatable?g.split("/"):g}return d}function a(f){let u="",d=!1;for(const m of e){(!d||!u.endsWith("/"))&&(u+="/"),d=!1;for(const g of m)if(g.type===0)u+=g.value;else if(g.type===1){const{value:y,repeatable:E,optional:R}=g,O=y in f?f[y]:"";if(Ue(O)&&!E)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const T=Ue(O)?O.join("/"):O;if(!T)if(R)m.length<2&&(u.endsWith("/")?u=u.slice(0,-1):d=!0);else throw new Error(`Missing required param "${y}"`);u+=T}}return u||"/"}return{re:i,score:n,keys:o,parse:l,stringify:a}}function md(e,t){let s=0;for(;s<e.length&&s<t.length;){const n=t[s]-e[s];if(n)return n;s++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Tl(e,t){let s=0;const n=e.score,r=t.score;for(;s<n.length&&s<r.length;){const o=md(n[s],r[s]);if(o)return o;s++}if(Math.abs(r.length-n.length)===1){if(To(n))return 1;if(To(r))return-1}return r.length-n.length}function To(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const gd={type:0,value:""},vd=/[a-zA-Z0-9_]/;function xd(e){if(!e)return[[]];if(e==="/")return[[gd]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${s})/"${f}": ${g}`)}let s=0,n=s;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,a,f="",u="";function d(){f&&(s===0?o.push({type:0,value:f}):s===1||s===2||s===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:f,regexp:u,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),f="")}function m(){f+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&s!==2){n=s,s=4;continue}switch(s){case 0:a==="/"?(f&&d(),i()):a===":"?(d(),s=1):m();break;case 4:m(),s=n;break;case 1:a==="("?s=2:vd.test(a)?m():(d(),s=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+a:s=3:u+=a;break;case 3:d(),s=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,u="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${f}"`),d(),i(),r}function yd(e,t,s){const n=hd(xd(e.path),s),r=Z(n,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function bd(e,t){const s=[],n=new Map;t=jo({strict:!1,end:!0,sensitive:!1},t);function r(d){return n.get(d)}function o(d,m,g){const y=!g,E=Mo(d);E.aliasOf=g&&g.record;const R=jo(t,d),O=[E];if("alias"in d){const D=typeof d.alias=="string"?[d.alias]:d.alias;for(const K of D)O.push(Mo(Z({},E,{components:g?g.record.components:E.components,path:K,aliasOf:g?g.record:E})))}let T,j;for(const D of O){const{path:K}=D;if(m&&K[0]!=="/"){const ie=m.record.path,Q=ie[ie.length-1]==="/"?"":"/";D.path=m.record.path+(K&&Q+K)}if(T=yd(D,m,R),g?g.alias.push(T):(j=j||T,j!==T&&j.alias.push(T),y&&d.name&&!$o(T)&&i(d.name)),Pl(T)&&a(T),E.children){const ie=E.children;for(let Q=0;Q<ie.length;Q++)o(ie[Q],T,g&&g.children[Q])}g=g||T}return j?()=>{i(j)}:gs}function i(d){if(Al(d)){const m=n.get(d);m&&(n.delete(d),s.splice(s.indexOf(m),1),m.children.forEach(i),m.alias.forEach(i))}else{const m=s.indexOf(d);m>-1&&(s.splice(m,1),d.record.name&&n.delete(d.record.name),d.children.forEach(i),d.alias.forEach(i))}}function l(){return s}function a(d){const m=Ed(d,s);s.splice(m,0,d),d.record.name&&!$o(d)&&n.set(d.record.name,d)}function f(d,m){let g,y={},E,R;if("name"in d&&d.name){if(g=n.get(d.name),!g)throw Yt(1,{location:d});R=g.record.name,y=Z(Po(m.params,g.keys.filter(j=>!j.optional).concat(g.parent?g.parent.keys.filter(j=>j.optional):[]).map(j=>j.name)),d.params&&Po(d.params,g.keys.map(j=>j.name))),E=g.stringify(y)}else if(d.path!=null)E=d.path,g=s.find(j=>j.re.test(E)),g&&(y=g.parse(E),R=g.record.name);else{if(g=m.name?n.get(m.name):s.find(j=>j.re.test(m.path)),!g)throw Yt(1,{location:d,currentLocation:m});R=g.record.name,y=Z({},m.params,d.params),E=g.stringify(y)}const O=[];let T=g;for(;T;)O.unshift(T.record),T=T.parent;return{name:R,path:E,params:y,matched:O,meta:_d(O)}}e.forEach(d=>o(d));function u(){s.length=0,n.clear()}return{addRoute:o,resolve:f,removeRoute:i,clearRoutes:u,getRoutes:l,getRecordMatcher:r}}function Po(e,t){const s={};for(const n of t)n in e&&(s[n]=e[n]);return s}function Mo(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:wd(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function wd(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const n in e.components)t[n]=typeof s=="object"?s[n]:s;return t}function $o(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function _d(e){return e.reduce((t,s)=>Z(t,s.meta),{})}function jo(e,t){const s={};for(const n in e)s[n]=n in t?t[n]:e[n];return s}function Ed(e,t){let s=0,n=t.length;for(;s!==n;){const o=s+n>>1;Tl(e,t[o])<0?n=o:s=o+1}const r=Sd(e);return r&&(n=t.lastIndexOf(r,n-1)),n}function Sd(e){let t=e;for(;t=t.parent;)if(Pl(t)&&Tl(e,t)===0)return t}function Pl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Rd(e){const t={};if(e===""||e==="?")return t;const n=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<n.length;++r){const o=n[r].replace(_l," "),i=o.indexOf("="),l=Rs(i<0?o:o.slice(0,i)),a=i<0?null:Rs(o.slice(i+1));if(l in t){let f=t[l];Ue(f)||(f=t[l]=[f]),f.push(a)}else t[l]=a}return t}function Do(e){let t="";for(let s in e){const n=e[s];if(s=qf(s),n==null){n!==void 0&&(t+=(t.length?"&":"")+s);continue}(Ue(n)?n.map(o=>o&&sr(o)):[n&&sr(n)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+s,o!=null&&(t+="="+o))})}return t}function Cd(e){const t={};for(const s in e){const n=e[s];n!==void 0&&(t[s]=Ue(n)?n.map(r=>r==null?null:""+r):n==null?n:""+n)}return t}const kd=Symbol(""),No=Symbol(""),Ar=Symbol(""),Ml=Symbol(""),rr=Symbol("");function ls(){let e=[];function t(n){return e.push(n),()=>{const r=e.indexOf(n);r>-1&&e.splice(r,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function yt(e,t,s,n,r,o=i=>i()){const i=n&&(n.enterCallbacks[r]=n.enterCallbacks[r]||[]);return()=>new Promise((l,a)=>{const f=m=>{m===!1?a(Yt(4,{from:s,to:t})):m instanceof Error?a(m):fd(m)?a(Yt(2,{from:t,to:m})):(i&&n.enterCallbacks[r]===i&&typeof m=="function"&&i.push(m),l())},u=o(()=>e.call(n&&n.instances[r],t,s,f));let d=Promise.resolve(u);e.length<3&&(d=d.then(f)),d.catch(m=>a(m))})}function Ln(e,t,s,n,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(bl(a)){const u=(a.__vccOpts||a)[t];u&&o.push(yt(u,s,n,i,l,r))}else{let f=a();o.push(()=>f.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const d=jf(u)?u.default:u;i.mods[l]=u,i.components[l]=d;const g=(d.__vccOpts||d)[t];return g&&yt(g,s,n,i,l,r)()}))}}return o}function Fo(e){const t=at(Ar),s=at(Ml),n=Le(()=>{const a=Kt(e.to);return t.resolve(a)}),r=Le(()=>{const{matched:a}=n.value,{length:f}=a,u=a[f-1],d=s.matched;if(!u||!d.length)return-1;const m=d.findIndex(Qt.bind(null,u));if(m>-1)return m;const g=Io(a[f-2]);return f>1&&Io(u)===g&&d[d.length-1].path!==g?d.findIndex(Qt.bind(null,a[f-2])):m}),o=Le(()=>r.value>-1&&Md(s.params,n.value.params)),i=Le(()=>r.value>-1&&r.value===s.matched.length-1&&Cl(s.params,n.value.params));function l(a={}){if(Pd(a)){const f=t[Kt(e.replace)?"replace":"push"](Kt(e.to)).catch(gs);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>f),f}return Promise.resolve()}return{route:n,href:Le(()=>n.value.href),isActive:o,isExactActive:i,navigate:l}}function Ad(e){return e.length===1?e[0]:e}const Od=Hi({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Fo,setup(e,{slots:t}){const s=vn(Fo(e)),{options:n}=at(Ar),r=Le(()=>({[Lo(e.activeClass,n.linkActiveClass,"router-link-active")]:s.isActive,[Lo(e.exactActiveClass,n.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const o=t.default&&Ad(t.default(s));return e.custom?o:ml("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:r.value},o)}}}),Td=Od;function Pd(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Md(e,t){for(const s in t){const n=t[s],r=e[s];if(typeof n=="string"){if(n!==r)return!1}else if(!Ue(r)||r.length!==n.length||n.some((o,i)=>o!==r[i]))return!1}return!0}function Io(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Lo=(e,t,s)=>e??t??s,$d=Hi({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){const n=at(rr),r=Le(()=>e.route||n.value),o=at(No,0),i=Le(()=>{let f=Kt(o);const{matched:u}=r.value;let d;for(;(d=u[f])&&!d.components;)f++;return f}),l=Le(()=>r.value.matched[i.value]);Us(No,Le(()=>i.value+1)),Us(kd,l),Us(rr,r);const a=Hc();return Vs(()=>[a.value,l.value,e.name],([f,u,d],[m,g,y])=>{u&&(u.instances[d]=f,g&&g!==u&&f&&f===m&&(u.leaveGuards.size||(u.leaveGuards=g.leaveGuards),u.updateGuards.size||(u.updateGuards=g.updateGuards))),f&&u&&(!g||!Qt(u,g)||!m)&&(u.enterCallbacks[d]||[]).forEach(E=>E(f))},{flush:"post"}),()=>{const f=r.value,u=e.name,d=l.value,m=d&&d.components[u];if(!m)return Bo(s.default,{Component:m,route:f});const g=d.props[u],y=g?g===!0?f.params:typeof g=="function"?g(f):g:null,R=ml(m,Z({},y,t,{onVnodeUnmounted:O=>{O.component.isUnmounted&&(d.instances[u]=null)},ref:a}));return Bo(s.default,{Component:R,route:f})||R}}});function Bo(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const jd=$d;function Dd(e){const t=bd(e.routes,e),s=e.parseQuery||Rd,n=e.stringifyQuery||Do,r=e.history,o=ls(),i=ls(),l=ls(),a=zc(gt);let f=gt;Vt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Fn.bind(null,w=>""+w),d=Fn.bind(null,Wf),m=Fn.bind(null,Rs);function g(w,N){let M,F;return Al(w)?(M=t.getRecordMatcher(w),F=N):F=w,t.addRoute(F,M)}function y(w){const N=t.getRecordMatcher(w);N&&t.removeRoute(N)}function E(){return t.getRoutes().map(w=>w.record)}function R(w){return!!t.getRecordMatcher(w)}function O(w,N){if(N=Z({},N||a.value),typeof w=="string"){const v=In(s,w,N.path),b=t.resolve({path:v.path},N),S=r.createHref(v.fullPath);return Z(v,b,{params:m(b.params),hash:Rs(v.hash),redirectedFrom:void 0,href:S})}let M;if(w.path!=null)M=Z({},w,{path:In(s,w.path,N.path).path});else{const v=Z({},w.params);for(const b in v)v[b]==null&&delete v[b];M=Z({},w,{params:d(v)}),N.params=d(N.params)}const F=t.resolve(M,N),re=w.hash||"";F.params=u(m(F.params));const p=Xf(n,Z({},w,{hash:zf(re),path:F.path})),h=r.createHref(p);return Z({fullPath:p,hash:re,query:n===Do?Cd(w.query):w.query||{}},F,{redirectedFrom:void 0,href:h})}function T(w){return typeof w=="string"?In(s,w,a.value.path):Z({},w)}function j(w,N){if(f!==w)return Yt(8,{from:N,to:w})}function D(w){return Q(w)}function K(w){return D(Z(T(w),{replace:!0}))}function ie(w){const N=w.matched[w.matched.length-1];if(N&&N.redirect){const{redirect:M}=N;let F=typeof M=="function"?M(w):M;return typeof F=="string"&&(F=F.includes("?")||F.includes("#")?F=T(F):{path:F},F.params={}),Z({query:w.query,hash:w.hash,params:F.path!=null?{}:w.params},F)}}function Q(w,N){const M=f=O(w),F=a.value,re=w.state,p=w.force,h=w.replace===!0,v=ie(M);if(v)return Q(Z(T(v),{state:typeof v=="object"?Z({},re,v.state):re,force:p,replace:h}),N||M);const b=M;b.redirectedFrom=N;let S;return!p&&Qf(n,F,M)&&(S=Yt(16,{to:b,from:F}),qe(F,F,!0,!1)),(S?Promise.resolve(S):He(b,F)).catch(_=>rt(_)?rt(_,2)?_:mt(_):Y(_,b,F)).then(_=>{if(_){if(rt(_,2))return Q(Z({replace:h},T(_.to),{state:typeof _.to=="object"?Z({},re,_.to.state):re,force:p}),N||b)}else _=Rt(b,F,!0,h,re);return ht(b,F,_),_})}function be(w,N){const M=j(w,N);return M?Promise.reject(M):Promise.resolve()}function Ie(w){const N=Lt.values().next().value;return N&&typeof N.runWithContext=="function"?N.runWithContext(w):w()}function He(w,N){let M;const[F,re,p]=Nd(w,N);M=Ln(F.reverse(),"beforeRouteLeave",w,N);for(const v of F)v.leaveGuards.forEach(b=>{M.push(yt(b,w,N))});const h=be.bind(null,w,N);return M.push(h),De(M).then(()=>{M=[];for(const v of o.list())M.push(yt(v,w,N));return M.push(h),De(M)}).then(()=>{M=Ln(re,"beforeRouteUpdate",w,N);for(const v of re)v.updateGuards.forEach(b=>{M.push(yt(b,w,N))});return M.push(h),De(M)}).then(()=>{M=[];for(const v of p)if(v.beforeEnter)if(Ue(v.beforeEnter))for(const b of v.beforeEnter)M.push(yt(b,w,N));else M.push(yt(v.beforeEnter,w,N));return M.push(h),De(M)}).then(()=>(w.matched.forEach(v=>v.enterCallbacks={}),M=Ln(p,"beforeRouteEnter",w,N,Ie),M.push(h),De(M))).then(()=>{M=[];for(const v of i.list())M.push(yt(v,w,N));return M.push(h),De(M)}).catch(v=>rt(v,8)?v:Promise.reject(v))}function ht(w,N,M){l.list().forEach(F=>Ie(()=>F(w,N,M)))}function Rt(w,N,M,F,re){const p=j(w,N);if(p)return p;const h=N===gt,v=Vt?history.state:{};M&&(F||h?r.replace(w.fullPath,Z({scroll:h&&v&&v.scroll},re)):r.push(w.fullPath,re)),a.value=w,qe(w,N,M,h),mt()}let ze;function ss(){ze||(ze=r.listen((w,N,M)=>{if(!$s.listening)return;const F=O(w),re=ie(F);if(re){Q(Z(re,{replace:!0,force:!0}),F).catch(gs);return}f=F;const p=a.value;Vt&&od(Co(p.fullPath,M.delta),Sn()),He(F,p).catch(h=>rt(h,12)?h:rt(h,2)?(Q(Z(T(h.to),{force:!0}),F).then(v=>{rt(v,20)&&!M.delta&&M.type===Cs.pop&&r.go(-1,!1)}).catch(gs),Promise.reject()):(M.delta&&r.go(-M.delta,!1),Y(h,F,p))).then(h=>{h=h||Rt(F,p,!1),h&&(M.delta&&!rt(h,8)?r.go(-M.delta,!1):M.type===Cs.pop&&rt(h,20)&&r.go(-1,!1)),ht(F,p,h)}).catch(gs)}))}let Ft=ls(),pe=ls(),ne;function Y(w,N,M){mt(w);const F=pe.list();return F.length?F.forEach(re=>re(w,N,M)):console.error(w),Promise.reject(w)}function st(){return ne&&a.value!==gt?Promise.resolve():new Promise((w,N)=>{Ft.add([w,N])})}function mt(w){return ne||(ne=!w,ss(),Ft.list().forEach(([N,M])=>w?M(w):N()),Ft.reset()),w}function qe(w,N,M,F){const{scrollBehavior:re}=e;if(!Vt||!re)return Promise.resolve();const p=!M&&id(Co(w.fullPath,0))||(F||!M)&&history.state&&history.state.scroll||null;return wr().then(()=>re(w,N,p)).then(h=>h&&rd(h)).catch(h=>Y(h,w,N))}const Ee=w=>r.go(w);let It;const Lt=new Set,$s={currentRoute:a,listening:!0,addRoute:g,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:R,getRoutes:E,resolve:O,options:e,push:D,replace:K,go:Ee,back:()=>Ee(-1),forward:()=>Ee(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:pe.add,isReady:st,install(w){const N=this;w.component("RouterLink",Td),w.component("RouterView",jd),w.config.globalProperties.$router=N,Object.defineProperty(w.config.globalProperties,"$route",{enumerable:!0,get:()=>Kt(a)}),Vt&&!It&&a.value===gt&&(It=!0,D(r.location).catch(re=>{}));const M={};for(const re in gt)Object.defineProperty(M,re,{get:()=>a.value[re],enumerable:!0});w.provide(Ar,N),w.provide(Ml,ji(M)),w.provide(rr,a);const F=w.unmount;Lt.add(w),w.unmount=function(){Lt.delete(w),Lt.size<1&&(f=gt,ze&&ze(),ze=null,a.value=gt,It=!1,ne=!1),F()}}};function De(w){return w.reduce((N,M)=>N.then(()=>Ie(M)),Promise.resolve())}return $s}function Nd(e,t){const s=[],n=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(f=>Qt(f,l))?n.push(l):s.push(l));const a=e.matched[i];a&&(t.matched.find(f=>Qt(f,a))||r.push(a))}return[s,n,r]}const Nt=(e,t)=>{const s=e.__vccOpts||e;for(const[n,r]of t)s[n]=r;return s},Fd={name:"App",data(){return{user:null,mobileMenuOpen:!1}},computed:{isAuthenticated(){return!!localStorage.getItem("auth_token")}},methods:{async logout(){try{await this.$http.post("/logout"),localStorage.removeItem("auth_token"),localStorage.removeItem("user"),delete this.$http.defaults.headers.common.Authorization,this.$router.push("/")}catch(e){console.error("Logout error:",e),localStorage.removeItem("auth_token"),localStorage.removeItem("user"),delete this.$http.defaults.headers.common.Authorization,this.$router.push("/")}}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e))}},Id={id:"app",class:"min-h-screen"},Ld={class:"glass-effect fixed w-full z-50 backdrop-blur-md"},Bd={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Ud={class:"flex justify-between h-20"},Vd={class:"flex items-center"},Hd={class:"hidden md:flex items-center space-x-6"},zd={key:0,class:"flex items-center space-x-4"},qd={key:1,class:"flex items-center space-x-4"},Kd={class:"md:hidden flex items-center"},Wd={class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Jd={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"},Gd={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},Xd={key:0,class:"md:hidden py-4 border-t border-white/20"},Qd={class:"flex flex-col space-y-2"},Yd={key:0,class:"flex flex-col space-y-2"},Zd={key:1,class:"flex flex-col space-y-2"},ep={class:"flex-1"},tp={class:"bg-gradient-to-br from-accent-900 via-accent-800 to-primary-900 text-white relative overflow-hidden"},sp={class:"relative max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8"},np={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},rp={class:"space-y-3"};function op(e,t,s,n,r,o){const i=Et("router-link"),l=Et("router-view");return V(),H("div",Id,[c("nav",Ld,[c("div",Bd,[c("div",Ud,[c("div",Vd,[J(i,{to:"/",class:"flex items-center space-x-3"},{default:ce(()=>t[7]||(t[7]=[c("div",{class:"w-12 h-12 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200"},[c("span",{class:"text-white font-bold text-xl"},"K")],-1),c("span",{class:"text-2xl font-bold text-gradient"},"KabEvents",-1)])),_:1,__:[7]})]),c("div",Hd,[J(i,{to:"/events",class:"nav-link"},{default:ce(()=>t[8]||(t[8]=[G(" Événements ")])),_:1,__:[8]}),o.isAuthenticated?(V(),H("div",qd,[J(i,{to:"/dashboard",class:"nav-link"},{default:ce(()=>t[11]||(t[11]=[G(" Dashboard ")])),_:1,__:[11]}),c("button",{onClick:t[0]||(t[0]=(...a)=>o.logout&&o.logout(...a)),class:"nav-link"}," Déconnexion ")])):(V(),H("div",zd,[J(i,{to:"/login",class:"nav-link"},{default:ce(()=>t[9]||(t[9]=[G(" Connexion ")])),_:1,__:[9]}),J(i,{to:"/register",class:"btn-primary"},{default:ce(()=>t[10]||(t[10]=[G(" S'inscrire ")])),_:1,__:[10]})]))]),c("div",Kd,[c("button",{onClick:t[1]||(t[1]=a=>r.mobileMenuOpen=!r.mobileMenuOpen),class:"text-accent-700 hover:text-primary-600 p-2 rounded-lg transition-colors duration-200"},[(V(),H("svg",Wd,[r.mobileMenuOpen?(V(),H("path",Gd)):(V(),H("path",Jd))]))])])]),r.mobileMenuOpen?(V(),H("div",Xd,[c("div",Qd,[J(i,{to:"/events",class:"nav-link",onClick:t[2]||(t[2]=a=>r.mobileMenuOpen=!1)},{default:ce(()=>t[12]||(t[12]=[G(" Événements ")])),_:1,__:[12]}),o.isAuthenticated?(V(),H("div",Zd,[J(i,{to:"/dashboard",class:"nav-link",onClick:t[5]||(t[5]=a=>r.mobileMenuOpen=!1)},{default:ce(()=>t[15]||(t[15]=[G(" Dashboard ")])),_:1,__:[15]}),c("button",{onClick:t[6]||(t[6]=a=>{o.logout,r.mobileMenuOpen=!1}),class:"nav-link text-left"}," Déconnexion ")])):(V(),H("div",Yd,[J(i,{to:"/login",class:"nav-link",onClick:t[3]||(t[3]=a=>r.mobileMenuOpen=!1)},{default:ce(()=>t[13]||(t[13]=[G(" Connexion ")])),_:1,__:[13]}),J(i,{to:"/register",class:"btn-primary text-center",onClick:t[4]||(t[4]=a=>r.mobileMenuOpen=!1)},{default:ce(()=>t[14]||(t[14]=[G(" S'inscrire ")])),_:1,__:[14]})]))])])):ct("",!0)])]),c("main",ep,[J(l)]),c("footer",tp,[t[23]||(t[23]=c("div",{class:"absolute inset-0 bg-hero-pattern opacity-10"},null,-1)),c("div",sp,[c("div",np,[t[20]||(t[20]=$t('<div class="md:col-span-2"><div class="flex items-center space-x-3 mb-6"><div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-lg"><span class="text-white font-bold text-xl">K</span></div><span class="text-2xl font-bold">KabEvents</span></div><p class="text-accent-200 text-lg leading-relaxed mb-6"> La plateforme de référence pour les événements culturels kabyles au Canada. Connectons notre communauté à travers la culture et les traditions. </p><div class="flex space-x-4"><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg></a><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"></path></svg></a><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"></path></svg></a></div></div>',1)),c("div",null,[t[19]||(t[19]=c("h3",{class:"text-xl font-semibold mb-6 text-white"},"Liens rapides",-1)),c("ul",rp,[c("li",null,[J(i,{to:"/events",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},{default:ce(()=>t[16]||(t[16]=[c("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"},null,-1),G(" Événements ")])),_:1,__:[16]})]),c("li",null,[J(i,{to:"/register",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},{default:ce(()=>t[17]||(t[17]=[c("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"},null,-1),G(" Devenir organisateur ")])),_:1,__:[17]})]),t[18]||(t[18]=c("li",null,[c("a",{href:"#",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},[c("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"}),G(" À propos ")])],-1))])]),t[21]||(t[21]=$t('<div><h3 class="text-xl font-semibold mb-6 text-white">Contact</h3><div class="space-y-4"><div class="flex items-start space-x-3"><svg class="w-5 h-5 text-secondary-400 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg><div><p class="text-accent-200"><EMAIL></p></div></div><div class="flex items-start space-x-3"><svg class="w-5 h-5 text-secondary-400 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg><div><p class="text-accent-200">+1 (555) 123-4567</p></div></div></div></div>',1))]),t[22]||(t[22]=$t('<div class="mt-12 pt-8 border-t border-white/20"><div class="flex flex-col md:flex-row justify-between items-center"><p class="text-accent-200 text-sm"> © 2024 KabEvents. Tous droits réservés. </p><div class="flex space-x-6 mt-4 md:mt-0"><a href="#" class="text-accent-200 hover:text-white text-sm transition-colors duration-200">Politique de confidentialité</a><a href="#" class="text-accent-200 hover:text-white text-sm transition-colors duration-200">Conditions d&#39;utilisation</a></div></div></div>',1))])])])}const ip=Nt(Fd,[["render",op]]),lp={name:"Home",data(){return{featuredEvents:[],loading:!0}},methods:{async fetchFeaturedEvents(){try{const e=await this.$http.get("/events?limit=3");this.featuredEvents=e.data.data||e.data}catch(e){console.error("Error fetching events:",e)}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric"})}},mounted(){this.fetchFeaturedEvents()}},ap={class:"relative min-h-screen flex items-center justify-center overflow-hidden"},cp={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 text-center"},up={class:"animate-fade-in"},fp={class:"flex flex-col sm:flex-row gap-6 justify-center items-center animate-slide-up"},dp={class:"py-24 relative"},pp={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},hp={key:0,class:"text-center py-16"},mp={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},gp={class:"event-image"},vp={class:"text-center"},xp={class:"text-white text-3xl font-bold"},yp={class:"mt-2 text-white/80 text-sm font-medium"},bp={class:"p-6"},wp={class:"flex items-center justify-between mb-3"},_p={class:"price-tag"},Ep={class:"text-sm text-accent-500 font-medium"},Sp={class:"text-xl font-bold text-accent-900 mb-3 line-clamp-2"},Rp={class:"text-accent-600 mb-6 line-clamp-3 leading-relaxed"},Cp={class:"flex items-center justify-between"},kp={class:"flex items-center space-x-2"},Ap={class:"text-sm text-accent-500"},Op={class:"text-center mt-16"},Tp={class:"py-24 relative overflow-hidden"},Pp={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Mp={class:"text-center mt-20"},$p={class:"card max-w-4xl mx-auto"},jp={class:"flex flex-col sm:flex-row gap-4 justify-center"};function Dp(e,t,s,n,r,o){const i=Et("router-link");return V(),H("div",null,[c("section",ap,[t[5]||(t[5]=$t('<div class="absolute inset-0 hero-gradient" data-v-3683f6a2></div><div class="absolute inset-0 bg-hero-pattern opacity-20" data-v-3683f6a2></div><div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-bounce-gentle" data-v-3683f6a2></div><div class="absolute top-40 right-20 w-16 h-16 bg-secondary-400/20 rounded-full animate-bounce-gentle" style="animation-delay:1s;" data-v-3683f6a2></div><div class="absolute bottom-40 left-20 w-12 h-12 bg-primary-400/20 rounded-full animate-bounce-gentle" style="animation-delay:2s;" data-v-3683f6a2></div>',5)),c("div",cp,[c("div",up,[t[2]||(t[2]=c("h1",{class:"text-5xl md:text-7xl font-bold mb-8 text-white leading-tight"},[G(" Découvrez la "),c("span",{class:"bg-gradient-to-r from-secondary-300 to-secondary-100 bg-clip-text text-transparent"}," Culture Kabyle ")],-1)),t[3]||(t[3]=c("p",{class:"text-xl md:text-2xl mb-12 text-white/90 max-w-3xl mx-auto leading-relaxed"}," Participez aux plus beaux événements culturels kabyles au Canada. Connectez-vous avec votre communauté et célébrez nos traditions ancestrales. ",-1)),c("div",fp,[J(i,{to:"/events",class:"btn-secondary text-lg px-8 py-4"},{default:ce(()=>t[0]||(t[0]=[c("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),G(" Voir les événements ")])),_:1,__:[0]}),J(i,{to:"/register",class:"btn-outline text-lg px-8 py-4 bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-primary-600"},{default:ce(()=>t[1]||(t[1]=[c("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),G(" Devenir organisateur ")])),_:1,__:[1]})])]),t[4]||(t[4]=c("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"},[c("svg",{class:"w-6 h-6 text-white/70",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))])]),c("section",dp,[t[11]||(t[11]=c("div",{class:"absolute inset-0 bg-gradient-to-b from-white via-accent-50/30 to-white"},null,-1)),c("div",pp,[t[10]||(t[10]=c("div",{class:"text-center mb-16"},[c("h2",{class:"text-4xl md:text-5xl font-bold text-gradient mb-6"},"Événements à venir"),c("p",{class:"text-xl text-accent-600 max-w-2xl mx-auto leading-relaxed"}," Ne manquez pas ces événements exceptionnels qui célèbrent notre riche patrimoine culturel ")],-1)),r.loading?(V(),H("div",hp,t[6]||(t[6]=[c("div",{class:"loading-spinner w-12 h-12 mx-auto"},null,-1),c("p",{class:"text-accent-600 mt-4"},"Chargement des événements...",-1)]))):(V(),H("div",mp,[(V(!0),H(Me,null,bn(r.featuredEvents,l=>(V(),H("div",{key:l.id,class:"event-card"},[c("div",gp,[c("div",vp,[c("span",xp,L(l.title.substring(0,2).toUpperCase()),1),c("div",yp,L(l.location||"Canada"),1)])]),c("div",bp,[c("div",wp,[c("span",_p,"$"+L(l.ticket_price),1),c("span",Ep,L(o.formatDate(l.event_date)),1)]),c("h3",Sp,L(l.title),1),c("p",Rp,L(l.description),1),c("div",Cp,[c("div",kp,[t[7]||(t[7]=c("svg",{class:"w-4 h-4 text-accent-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),c("span",Ap,L(l.location||"Canada"),1)]),J(i,{to:`/events/${l.id}`,class:"btn-primary text-sm px-4 py-2"},{default:ce(()=>t[8]||(t[8]=[G(" Voir détails ")])),_:2,__:[8]},1032,["to"])])])]))),128))])),c("div",Op,[J(i,{to:"/events",class:"btn-outline text-lg px-8 py-4"},{default:ce(()=>t[9]||(t[9]=[c("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1),G(" Voir tous les événements ")])),_:1,__:[9]})])])]),c("section",Tp,[t[17]||(t[17]=c("div",{class:"absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-secondary-50"},null,-1)),t[18]||(t[18]=c("div",{class:"absolute top-0 left-0 w-full h-full bg-hero-pattern opacity-5"},null,-1)),c("div",Pp,[t[16]||(t[16]=$t('<div class="text-center mb-20" data-v-3683f6a2><h2 class="text-4xl md:text-5xl font-bold text-gradient mb-6" data-v-3683f6a2>Pourquoi choisir KabEvents ?</h2><p class="text-xl text-accent-600 max-w-3xl mx-auto leading-relaxed" data-v-3683f6a2> Une plateforme moderne et sécurisée dédiée à la préservation et à la célébration de notre patrimoine culturel </p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-12" data-v-3683f6a2><div class="text-center group" data-v-3683f6a2><div class="relative mb-8" data-v-3683f6a2><div class="w-24 h-24 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-3683f6a2><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-3683f6a2><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-3683f6a2></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-secondary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-3683f6a2></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-3683f6a2>Événements authentiques</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-3683f6a2> Des événements culturels kabyles authentiques organisés par et pour notre communauté, préservant nos traditions ancestrales </p></div><div class="text-center group" data-v-3683f6a2><div class="relative mb-8" data-v-3683f6a2><div class="w-24 h-24 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-3683f6a2><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-3683f6a2><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" data-v-3683f6a2></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-primary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-3683f6a2></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-3683f6a2>Réservation sécurisée</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-3683f6a2> Système de paiement sécurisé avec billets électroniques, QR codes et protection complète de vos données personnelles </p></div><div class="text-center group" data-v-3683f6a2><div class="relative mb-8" data-v-3683f6a2><div class="w-24 h-24 bg-gradient-to-br from-success-500 to-success-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-3683f6a2><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-3683f6a2><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" data-v-3683f6a2></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-secondary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-3683f6a2></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-3683f6a2>Communauté unie</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-3683f6a2> Rassemblez-vous avec la communauté kabyle du Canada et créez des liens durables autour de notre culture commune </p></div></div>',2)),c("div",Mp,[c("div",$p,[t[14]||(t[14]=c("h3",{class:"text-3xl font-bold text-accent-900 mb-4"},"Prêt à rejoindre notre communauté ?",-1)),t[15]||(t[15]=c("p",{class:"text-xl text-accent-600 mb-8 leading-relaxed"}," Découvrez des événements exceptionnels et connectez-vous avec des milliers de membres de notre communauté ",-1)),c("div",jp,[J(i,{to:"/register",class:"btn-primary text-lg px-8 py-4"},{default:ce(()=>t[12]||(t[12]=[G(" Créer un compte gratuit ")])),_:1,__:[12]}),J(i,{to:"/events",class:"btn-outline text-lg px-8 py-4"},{default:ce(()=>t[13]||(t[13]=[G(" Explorer les événements ")])),_:1,__:[13]})])])])])])])}const Np=Nt(lp,[["render",Dp],["__scopeId","data-v-3683f6a2"]]),Fp={name:"Login",data(){return{form:{email:"",password:"",remember:!1},loading:!1,error:null}},methods:{async login(){this.loading=!0,this.error=null;try{const e=await this.$http.post("/login",{email:this.form.email,password:this.form.password});localStorage.setItem("auth_token",e.data.token),localStorage.setItem("user",JSON.stringify(e.data.user)),this.$http.defaults.headers.common.Authorization=`Bearer ${e.data.token}`;const t=this.$route.query.redirect||"/dashboard";this.$router.push(t)}catch(e){e.response&&e.response.data?this.error=e.response.data.message||"Erreur de connexion":this.error="Erreur de connexion. Veuillez réessayer."}finally{this.loading=!1}}},mounted(){localStorage.getItem("auth_token")&&this.$router.push("/dashboard")}},Ip={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Lp={class:"max-w-md w-full space-y-8"},Bp={class:"mt-2 text-center text-sm text-gray-600"},Up={key:0,class:"bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded"},Vp={class:"space-y-4"},Hp={class:"flex items-center justify-between"},zp={class:"flex items-center"},qp=["disabled"],Kp={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function Wp(e,t,s,n,r,o){const i=Et("router-link");return V(),H("div",Ip,[c("div",Lp,[c("div",null,[t[6]||(t[6]=c("div",{class:"mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-blue-500 rounded-lg flex items-center justify-center"},[c("span",{class:"text-white font-bold text-xl"},"K")],-1)),t[7]||(t[7]=c("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Connexion à votre compte ",-1)),c("p",Bp,[t[5]||(t[5]=G(" Ou ")),J(i,{to:"/register",class:"font-medium text-orange-600 hover:text-orange-500"},{default:ce(()=>t[4]||(t[4]=[G(" créez un nouveau compte ")])),_:1,__:[4]})])]),c("form",{class:"mt-8 space-y-6",onSubmit:t[3]||(t[3]=yl((...l)=>o.login&&o.login(...l),["prevent"]))},[r.error?(V(),H("div",Up,L(r.error),1)):ct("",!0),c("div",Vp,[c("div",null,[t[8]||(t[8]=c("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Adresse email ",-1)),Ce(c("input",{id:"email","onUpdate:modelValue":t[0]||(t[0]=l=>r.form.email=l),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"<EMAIL>"},null,512),[[Tt,r.form.email]])]),c("div",null,[t[9]||(t[9]=c("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Mot de passe ",-1)),Ce(c("input",{id:"password","onUpdate:modelValue":t[1]||(t[1]=l=>r.form.password=l),name:"password",type:"password",autocomplete:"current-password",required:"",class:"input-field mt-1",placeholder:"Votre mot de passe"},null,512),[[Tt,r.form.password]])])]),c("div",Hp,[c("div",zp,[Ce(c("input",{id:"remember-me","onUpdate:modelValue":t[2]||(t[2]=l=>r.form.remember=l),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"},null,512),[[vl,r.form.remember]]),t[10]||(t[10]=c("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Se souvenir de moi ",-1))]),t[11]||(t[11]=c("div",{class:"text-sm"},[c("a",{href:"#",class:"font-medium text-orange-600 hover:text-orange-500"}," Mot de passe oublié ? ")],-1))]),c("div",null,[c("button",{type:"submit",disabled:r.loading,class:"btn-primary w-full flex justify-center items-center"},[r.loading?(V(),H("svg",Kp,t[12]||(t[12]=[c("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),c("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):ct("",!0),G(" "+L(r.loading?"Connexion...":"Se connecter"),1)],8,qp)])],32)])])}const Jp=Nt(Fp,[["render",Wp]]),Gp={name:"Register",data(){return{form:{name:"",email:"",password:"",password_confirmation:"",role:"user",terms:!1},loading:!1,error:null}},methods:{async register(){this.loading=!0,this.error=null;try{const e=await this.$http.post("/register",{name:this.form.name,email:this.form.email,password:this.form.password,password_confirmation:this.form.password_confirmation,role:this.form.role});localStorage.setItem("auth_token",e.data.token),localStorage.setItem("user",JSON.stringify(e.data.user)),this.$http.defaults.headers.common.Authorization=`Bearer ${e.data.token}`,this.$router.push("/dashboard")}catch(e){if(e.response&&e.response.data)if(e.response.data.errors){const t=Object.values(e.response.data.errors).flat();this.error=t.join(", ")}else this.error=e.response.data.message||"Erreur lors de l'inscription";else this.error="Erreur lors de l'inscription. Veuillez réessayer."}finally{this.loading=!1}}},mounted(){localStorage.getItem("auth_token")&&this.$router.push("/dashboard")}},Xp={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Qp={class:"max-w-md w-full space-y-8"},Yp={class:"mt-2 text-center text-sm text-gray-600"},Zp={key:0,class:"bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded"},eh={class:"space-y-4"},th={class:"flex items-center"},sh=["disabled"],nh={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function rh(e,t,s,n,r,o){const i=Et("router-link");return V(),H("div",Xp,[c("div",Qp,[c("div",null,[t[9]||(t[9]=c("div",{class:"mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-blue-500 rounded-lg flex items-center justify-center"},[c("span",{class:"text-white font-bold text-xl"},"K")],-1)),t[10]||(t[10]=c("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Créer votre compte ",-1)),c("p",Yp,[t[8]||(t[8]=G(" Ou ")),J(i,{to:"/login",class:"font-medium text-orange-600 hover:text-orange-500"},{default:ce(()=>t[7]||(t[7]=[G(" connectez-vous à votre compte existant ")])),_:1,__:[7]})])]),c("form",{class:"mt-8 space-y-6",onSubmit:t[6]||(t[6]=yl((...l)=>o.register&&o.register(...l),["prevent"]))},[r.error?(V(),H("div",Zp,L(r.error),1)):ct("",!0),c("div",eh,[c("div",null,[t[11]||(t[11]=c("label",{for:"name",class:"block text-sm font-medium text-gray-700"}," Nom complet ",-1)),Ce(c("input",{id:"name","onUpdate:modelValue":t[0]||(t[0]=l=>r.form.name=l),name:"name",type:"text",autocomplete:"name",required:"",class:"input-field mt-1",placeholder:"Votre nom complet"},null,512),[[Tt,r.form.name]])]),c("div",null,[t[12]||(t[12]=c("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Adresse email ",-1)),Ce(c("input",{id:"email","onUpdate:modelValue":t[1]||(t[1]=l=>r.form.email=l),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"<EMAIL>"},null,512),[[Tt,r.form.email]])]),c("div",null,[t[14]||(t[14]=c("label",{for:"role",class:"block text-sm font-medium text-gray-700"}," Type de compte ",-1)),Ce(c("select",{id:"role","onUpdate:modelValue":t[2]||(t[2]=l=>r.form.role=l),name:"role",class:"input-field mt-1"},t[13]||(t[13]=[c("option",{value:"user"},"Utilisateur (participer aux événements)",-1),c("option",{value:"organizer"},"Organisateur (créer des événements)",-1)]),512),[[ms,r.form.role]])]),c("div",null,[t[15]||(t[15]=c("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Mot de passe ",-1)),Ce(c("input",{id:"password","onUpdate:modelValue":t[3]||(t[3]=l=>r.form.password=l),name:"password",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Minimum 8 caractères"},null,512),[[Tt,r.form.password]])]),c("div",null,[t[16]||(t[16]=c("label",{for:"password_confirmation",class:"block text-sm font-medium text-gray-700"}," Confirmer le mot de passe ",-1)),Ce(c("input",{id:"password_confirmation","onUpdate:modelValue":t[4]||(t[4]=l=>r.form.password_confirmation=l),name:"password_confirmation",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Répétez votre mot de passe"},null,512),[[Tt,r.form.password_confirmation]])])]),c("div",th,[Ce(c("input",{id:"terms","onUpdate:modelValue":t[5]||(t[5]=l=>r.form.terms=l),name:"terms",type:"checkbox",required:"",class:"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"},null,512),[[vl,r.form.terms]]),t[17]||(t[17]=c("label",{for:"terms",class:"ml-2 block text-sm text-gray-900"},[G(" J'accepte les "),c("a",{href:"#",class:"text-orange-600 hover:text-orange-500"},"conditions d'utilisation"),G(" et la "),c("a",{href:"#",class:"text-orange-600 hover:text-orange-500"},"politique de confidentialité")],-1))]),c("div",null,[c("button",{type:"submit",disabled:r.loading,class:"btn-primary w-full flex justify-center items-center"},[r.loading?(V(),H("svg",nh,t[18]||(t[18]=[c("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),c("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):ct("",!0),G(" "+L(r.loading?"Création...":"Créer mon compte"),1)],8,sh)])],32)])])}const oh=Nt(Gp,[["render",rh]]),ih={name:"Events",data(){return{events:[],filteredEvents:[],loading:!0,filters:{search:"",city:"",maxPrice:"",sortBy:"date"}}},methods:{async fetchEvents(){try{const e=await this.$http.get("/events");this.events=e.data.data||e.data,this.filteredEvents=[...this.events],this.filterEvents()}catch(e){console.error("Error fetching events:",e)}finally{this.loading=!1}},filterEvents(){let e=[...this.events];this.filters.search&&(e=e.filter(t=>t.title.toLowerCase().includes(this.filters.search.toLowerCase())||t.description.toLowerCase().includes(this.filters.search.toLowerCase()))),this.filters.city&&(e=e.filter(t=>t.location.includes(this.filters.city))),this.filters.maxPrice&&(e=e.filter(t=>parseFloat(t.ticket_price)<=parseFloat(this.filters.maxPrice))),e.sort((t,s)=>{switch(this.filters.sortBy){case"price":return parseFloat(t.ticket_price)-parseFloat(s.ticket_price);case"title":return t.title.localeCompare(s.title);case"date":default:return new Date(t.event_date)-new Date(s.event_date)}}),this.filteredEvents=e},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvents()}},lh={class:"min-h-screen bg-gray-50"},ah={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},ch={class:"bg-white rounded-lg shadow p-6"},uh={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},fh={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12"},dh={key:0,class:"text-center py-12"},ph={key:1,class:"text-center py-12"},hh={key:2,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},mh={class:"h-48 bg-gradient-to-r from-orange-400 to-blue-400 rounded-lg mb-4 flex items-center justify-center"},gh={class:"text-white text-2xl font-bold"},vh={class:"space-y-3"},xh={class:"text-xl font-semibold text-gray-900"},yh={class:"text-gray-600 text-sm line-clamp-3"},bh={class:"flex items-center text-sm text-gray-500"},wh={class:"flex items-center text-sm text-gray-500"},_h={class:"flex items-center justify-between"},Eh={class:"flex items-center text-sm text-gray-500"},Sh={class:"text-2xl font-bold text-orange-600"};function Rh(e,t,s,n,r,o){const i=Et("router-link");return V(),H("div",lh,[t[21]||(t[21]=c("div",{class:"bg-white shadow"},[c("div",{class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},[c("h1",{class:"text-3xl font-bold text-gray-900"},"Événements culturels kabyles"),c("p",{class:"mt-2 text-gray-600"},"Découvrez tous les événements à venir")])],-1)),c("div",ah,[c("div",ch,[c("div",uh,[c("div",null,[t[8]||(t[8]=c("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Rechercher",-1)),Ce(c("input",{"onUpdate:modelValue":t[0]||(t[0]=l=>r.filters.search=l),type:"text",placeholder:"Nom de l'événement...",class:"input-field",onInput:t[1]||(t[1]=(...l)=>o.filterEvents&&o.filterEvents(...l))},null,544),[[Tt,r.filters.search]])]),c("div",null,[t[10]||(t[10]=c("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Ville",-1)),Ce(c("select",{"onUpdate:modelValue":t[2]||(t[2]=l=>r.filters.city=l),class:"input-field",onChange:t[3]||(t[3]=(...l)=>o.filterEvents&&o.filterEvents(...l))},t[9]||(t[9]=[$t('<option value="" data-v-a8083e47>Toutes les villes</option><option value="Toronto" data-v-a8083e47>Toronto</option><option value="Montréal" data-v-a8083e47>Montréal</option><option value="Vancouver" data-v-a8083e47>Vancouver</option><option value="Calgary" data-v-a8083e47>Calgary</option>',5)]),544),[[ms,r.filters.city]])]),c("div",null,[t[12]||(t[12]=c("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Prix max",-1)),Ce(c("select",{"onUpdate:modelValue":t[4]||(t[4]=l=>r.filters.maxPrice=l),class:"input-field",onChange:t[5]||(t[5]=(...l)=>o.filterEvents&&o.filterEvents(...l))},t[11]||(t[11]=[c("option",{value:""},"Tous les prix",-1),c("option",{value:"50"},"Moins de 50$",-1),c("option",{value:"75"},"Moins de 75$",-1),c("option",{value:"100"},"Moins de 100$",-1)]),544),[[ms,r.filters.maxPrice]])]),c("div",null,[t[14]||(t[14]=c("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Trier par",-1)),Ce(c("select",{"onUpdate:modelValue":t[6]||(t[6]=l=>r.filters.sortBy=l),class:"input-field",onChange:t[7]||(t[7]=(...l)=>o.filterEvents&&o.filterEvents(...l))},t[13]||(t[13]=[c("option",{value:"date"},"Date",-1),c("option",{value:"price"},"Prix",-1),c("option",{value:"title"},"Nom",-1)]),544),[[ms,r.filters.sortBy]])])])])]),c("div",fh,[r.loading?(V(),H("div",dh,t[15]||(t[15]=[c("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"},null,-1),c("p",{class:"mt-2 text-gray-600"},"Chargement des événements...",-1)]))):r.filteredEvents.length===0?(V(),H("div",ph,t[16]||(t[16]=[c("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),c("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Aucun événement trouvé",-1),c("p",{class:"mt-1 text-sm text-gray-500"},"Essayez de modifier vos critères de recherche.",-1)]))):(V(),H("div",hh,[(V(!0),H(Me,null,bn(r.filteredEvents,l=>(V(),H("div",{key:l.id,class:"card hover:shadow-xl transition duration-300"},[c("div",mh,[c("span",gh,L(l.title.substring(0,2).toUpperCase()),1)]),c("div",vh,[c("h3",xh,L(l.title),1),c("p",yh,L(l.description),1),c("div",bh,[t[17]||(t[17]=c("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),G(" "+L(o.formatDate(l.event_date)),1)]),c("div",wh,[t[18]||(t[18]=c("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),G(" "+L(l.location),1)]),c("div",_h,[c("div",Eh,[t[19]||(t[19]=c("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1)),G(" "+L(l.available_tickets)+" places disponibles ",1)]),c("span",Sh,"$"+L(l.ticket_price),1)]),J(i,{to:`/events/${l.id}`,class:"btn-primary w-full text-center block"},{default:ce(()=>t[20]||(t[20]=[G(" Voir détails ")])),_:2,__:[20]},1032,["to"])])]))),128))]))])])}const Ch=Nt(ih,[["render",Rh],["__scopeId","data-v-a8083e47"]]),kh={name:"EventDetail",props:["id"],data(){return{event:null,loading:!0,quantity:1,reserving:!1,error:null,success:null}},computed:{isAuthenticated(){return!!localStorage.getItem("auth_token")}},methods:{async fetchEvent(){try{const e=await this.$http.get(`/events/${this.id}`);this.event=e.data.data||e.data}catch(e){console.error("Error fetching event:",e),this.event=null}finally{this.loading=!1}},async reserveTickets(){this.reserving=!0,this.error=null,this.success=null;try{const e=await this.$http.post(`/events/${this.id}/reserve`,{quantity:this.quantity});this.success="Réservation effectuée avec succès ! Vous recevrez un email de confirmation.",this.event.available_tickets-=this.quantity,setTimeout(()=>{this.$router.push("/dashboard")},2e3)}catch(e){e.response&&e.response.data?this.error=e.response.data.message||"Erreur lors de la réservation":this.error="Erreur lors de la réservation. Veuillez réessayer."}finally{this.reserving=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric"})},formatTime(e){return new Date(e).toLocaleTimeString("fr-CA",{hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvent()},watch:{id(){this.fetchEvent()}}},Ah={class:"min-h-screen bg-gray-50"},Oh={key:0,class:"flex items-center justify-center min-h-screen"},Th={key:1,class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Ph={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Mh={class:"lg:col-span-2"},$h={class:"h-64 md:h-96 bg-gradient-to-r from-orange-400 to-blue-400 rounded-xl mb-6 flex items-center justify-center"},jh={class:"text-white text-4xl font-bold"},Dh={class:"bg-white rounded-xl shadow-lg p-6"},Nh={class:"text-3xl font-bold text-gray-900 mb-4"},Fh={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"},Ih={class:"flex items-center text-gray-600"},Lh={class:"font-medium"},Bh={class:"text-sm"},Uh={class:"flex items-center text-gray-600"},Vh={class:"font-medium"},Hh={class:"flex items-center text-gray-600"},zh={class:"font-medium"},qh={class:"text-sm"},Kh={class:"flex items-center text-gray-600"},Wh={class:"text-sm"},Jh={class:"border-t pt-6"},Gh={class:"text-gray-700 leading-relaxed"},Xh={class:"lg:col-span-1"},Qh={class:"bg-white rounded-xl shadow-lg p-6 sticky top-8"},Yh={class:"text-center mb-6"},Zh={class:"text-3xl font-bold text-orange-600"},em={key:0,class:"space-y-4"},tm={key:1,class:"space-y-4"},sm=["value"],nm={class:"border-t pt-4"},rm={class:"flex justify-between mb-2"},om={class:"font-semibold"},im={class:"flex justify-between text-lg font-bold"},lm=["disabled"],am={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},cm={key:2,class:"text-center"},um={key:3,class:"mt-4 bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded"},fm={key:4,class:"mt-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded"},dm={key:2,class:"flex items-center justify-center min-h-screen"},pm={class:"text-center"};function hm(e,t,s,n,r,o){var l;const i=Et("router-link");return V(),H("div",Ah,[r.loading?(V(),H("div",Oh,t[3]||(t[3]=[c("div",{class:"text-center"},[c("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"}),c("p",{class:"mt-2 text-gray-600"},"Chargement de l'événement...")],-1)]))):r.event?(V(),H("div",Th,[c("button",{onClick:t[0]||(t[0]=a=>e.$router.go(-1)),class:"mb-6 flex items-center text-orange-600 hover:text-orange-700"},t[4]||(t[4]=[c("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1),G(" Retour ")])),c("div",Ph,[c("div",Mh,[c("div",$h,[c("span",jh,L(r.event.title.substring(0,2).toUpperCase()),1)]),c("div",Dh,[c("h1",Nh,L(r.event.title),1),c("div",Fh,[c("div",Ih,[t[5]||(t[5]=c("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),c("div",null,[c("p",Lh,L(o.formatDate(r.event.event_date)),1),c("p",Bh,L(o.formatTime(r.event.event_date)),1)])]),c("div",Uh,[t[6]||(t[6]=c("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),c("div",null,[c("p",Vh,L(r.event.location),1)])]),c("div",Hh,[t[7]||(t[7]=c("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"})],-1)),c("div",null,[c("p",zh,L(r.event.available_tickets)+" places disponibles",1),c("p",qh,"sur "+L(r.event.total_tickets)+" places",1)])]),c("div",Kh,[t[9]||(t[9]=c("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"})],-1)),c("div",null,[t[8]||(t[8]=c("p",{class:"font-medium"},"Organisé par",-1)),c("p",Wh,L(((l=r.event.organizer)==null?void 0:l.name)||"Organisateur"),1)])])]),c("div",Jh,[t[10]||(t[10]=c("h2",{class:"text-xl font-semibold mb-3"},"Description",-1)),c("p",Gh,L(r.event.description),1)])])]),c("div",Xh,[c("div",Qh,[c("div",Yh,[c("p",Zh,"$"+L(r.event.ticket_price),1),t[11]||(t[11]=c("p",{class:"text-gray-600"},"par billet",-1))]),o.isAuthenticated?r.event.available_tickets>0?(V(),H("div",tm,[c("div",null,[t[15]||(t[15]=c("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Nombre de billets ",-1)),Ce(c("select",{"onUpdate:modelValue":t[1]||(t[1]=a=>r.quantity=a),class:"input-field"},[(V(!0),H(Me,null,bn(Math.min(10,r.event.available_tickets),a=>(V(),H("option",{key:a,value:a},L(a)+" billet"+L(a>1?"s":""),9,sm))),128))],512),[[ms,r.quantity]])]),c("div",nm,[c("div",rm,[c("span",null,L(r.quantity)+" × $"+L(r.event.ticket_price),1),c("span",om,"$"+L((r.quantity*parseFloat(r.event.ticket_price)).toFixed(2)),1)]),c("div",im,[t[16]||(t[16]=c("span",null,"Total",-1)),c("span",null,"$"+L((r.quantity*parseFloat(r.event.ticket_price)).toFixed(2)),1)])]),c("button",{onClick:t[2]||(t[2]=(...a)=>o.reserveTickets&&o.reserveTickets(...a)),disabled:r.reserving,class:"btn-primary w-full flex justify-center items-center"},[r.reserving?(V(),H("svg",am,t[17]||(t[17]=[c("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),c("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):ct("",!0),G(" "+L(r.reserving?"Réservation...":"Réserver maintenant"),1)],8,lm)])):(V(),H("div",cm,t[18]||(t[18]=[c("p",{class:"text-orange-600 font-semibold mb-4"},"Événement complet",-1),c("p",{class:"text-gray-600 text-sm"}," Cet événement n'a plus de places disponibles. ",-1)]))):(V(),H("div",em,[t[14]||(t[14]=c("p",{class:"text-center text-gray-600 mb-4"}," Connectez-vous pour réserver vos billets ",-1)),J(i,{to:"/login",class:"btn-primary w-full text-center block"},{default:ce(()=>t[12]||(t[12]=[G(" Se connecter ")])),_:1,__:[12]}),J(i,{to:"/register",class:"btn-secondary w-full text-center block"},{default:ce(()=>t[13]||(t[13]=[G(" Créer un compte ")])),_:1,__:[13]})])),r.error?(V(),H("div",um,L(r.error),1)):ct("",!0),r.success?(V(),H("div",fm,L(r.success),1)):ct("",!0)])])])])):(V(),H("div",dm,[c("div",pm,[t[20]||(t[20]=c("h2",{class:"text-2xl font-bold text-gray-900 mb-2"},"Événement non trouvé",-1)),t[21]||(t[21]=c("p",{class:"text-gray-600 mb-4"},"L'événement que vous recherchez n'existe pas.",-1)),J(i,{to:"/events",class:"btn-primary"},{default:ce(()=>t[19]||(t[19]=[G(" Voir tous les événements ")])),_:1,__:[19]})])]))])}const mm=Nt(kh,[["render",hm]]),gm={name:"Dashboard",data(){return{user:null,reservations:[],userStats:{totalReservations:0,upcomingEvents:0,totalSpent:0},adminStats:{totalUsers:0,totalEvents:0,totalReservations:0,totalRevenue:0},loading:!0}},methods:{async fetchUserData(){try{const e=await this.$http.get("/me");if(this.user=e.data.user,this.user.role==="user"){const t=await this.$http.get("/user/reservations");this.reservations=t.data.data||t.data,this.userStats.totalReservations=this.reservations.length,this.userStats.upcomingEvents=this.reservations.filter(s=>{var n;return new Date((n=s.event)==null?void 0:n.event_date)>new Date}).length,this.userStats.totalSpent=this.reservations.filter(s=>s.status==="paid").reduce((s,n)=>s+parseFloat(n.total_amount),0).toFixed(2)}}catch(e){console.error("Error fetching user data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})},getStatusClass(e){switch(e){case"paid":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},getStatusText(e){switch(e){case"paid":return"Payé";case"pending":return"En attente";case"cancelled":return"Annulé";default:return e}}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchUserData()}},vm={class:"min-h-screen bg-gray-50"},xm={class:"bg-white shadow"},ym={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},bm={class:"flex justify-between items-center"},wm={class:"text-gray-600"},_m={class:"flex items-center space-x-2"},Em={class:"px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium"},Sm={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Rm={key:0,class:"space-y-8"},Cm={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},km={class:"card"},Am={class:"flex items-center"},Om={class:"ml-4"},Tm={class:"text-2xl font-bold text-gray-900"},Pm={class:"card"},Mm={class:"flex items-center"},$m={class:"ml-4"},jm={class:"text-2xl font-bold text-gray-900"},Dm={class:"card"},Nm={class:"flex items-center"},Fm={class:"ml-4"},Im={class:"text-2xl font-bold text-gray-900"},Lm={class:"card"},Bm={class:"flex justify-between items-center mb-6"},Um={key:0,class:"text-center py-8"},Vm={key:1,class:"text-center py-8"},Hm={key:2,class:"space-y-4"},zm={class:"flex justify-between items-start"},qm={class:"flex-1"},Km={class:"font-semibold text-lg"},Wm={class:"text-gray-600 text-sm mb-2"},Jm={class:"text-gray-600 text-sm"},Gm={class:"mt-2 flex items-center space-x-4"},Xm={class:"text-sm text-gray-500"},Qm={class:"text-sm font-medium"},Ym={class:"ml-4"},Zm={key:1,class:"space-y-8"},e0={key:2,class:"space-y-8"},t0={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},s0={class:"card text-center"},n0={class:"text-3xl font-bold text-orange-600 mb-2"},r0={class:"card text-center"},o0={class:"text-3xl font-bold text-blue-600 mb-2"},i0={class:"card text-center"},l0={class:"text-3xl font-bold text-green-600 mb-2"},a0={class:"card text-center"},c0={class:"text-3xl font-bold text-purple-600 mb-2"};function u0(e,t,s,n,r,o){var l,a,f,u,d,m;const i=Et("router-link");return V(),H("div",vm,[c("div",xm,[c("div",ym,[c("div",bm,[c("div",null,[t[0]||(t[0]=c("h1",{class:"text-2xl font-bold text-gray-900"},"Dashboard",-1)),c("p",wm,"Bienvenue, "+L((l=r.user)==null?void 0:l.name),1)]),c("div",_m,[c("span",Em,L(((a=r.user)==null?void 0:a.role)==="admin"?"Administrateur":((f=r.user)==null?void 0:f.role)==="organizer"?"Organisateur":"Utilisateur"),1)])])])]),c("div",Sm,[((u=r.user)==null?void 0:u.role)==="user"?(V(),H("div",Rm,[c("div",Cm,[c("div",km,[c("div",Am,[t[2]||(t[2]=c("div",{class:"p-3 bg-orange-100 rounded-lg"},[c("svg",{class:"w-6 h-6 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),c("div",Om,[t[1]||(t[1]=c("p",{class:"text-sm font-medium text-gray-600"},"Réservations totales",-1)),c("p",Tm,L(r.userStats.totalReservations),1)])])]),c("div",Pm,[c("div",Mm,[t[4]||(t[4]=c("div",{class:"p-3 bg-blue-100 rounded-lg"},[c("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),c("div",$m,[t[3]||(t[3]=c("p",{class:"text-sm font-medium text-gray-600"},"Événements à venir",-1)),c("p",jm,L(r.userStats.upcomingEvents),1)])])]),c("div",Dm,[c("div",Nm,[t[6]||(t[6]=c("div",{class:"p-3 bg-green-100 rounded-lg"},[c("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),c("div",Fm,[t[5]||(t[5]=c("p",{class:"text-sm font-medium text-gray-600"},"Total dépensé",-1)),c("p",Im,"$"+L(r.userStats.totalSpent),1)])])])]),c("div",Lm,[c("div",Bm,[t[8]||(t[8]=c("h2",{class:"text-xl font-semibold"},"Mes réservations",-1)),J(i,{to:"/events",class:"btn-primary"},{default:ce(()=>t[7]||(t[7]=[G(" Réserver un événement ")])),_:1,__:[7]})]),r.loading?(V(),H("div",Um,t[9]||(t[9]=[c("div",{class:"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-orange-600"},null,-1)]))):r.reservations.length===0?(V(),H("div",Vm,t[10]||(t[10]=[c("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})],-1),c("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Aucune réservation",-1),c("p",{class:"mt-1 text-sm text-gray-500"},"Commencez par réserver votre premier événement.",-1)]))):(V(),H("div",Hm,[(V(!0),H(Me,null,bn(r.reservations,g=>{var y,E,R,O;return V(),H("div",{key:g.id,class:"border rounded-lg p-4 hover:bg-gray-50"},[c("div",zm,[c("div",qm,[c("h3",Km,L((y=g.event)==null?void 0:y.title),1),c("p",Wm,L((E=g.event)==null?void 0:E.location),1),c("p",Jm,L(o.formatDate((R=g.event)==null?void 0:R.event_date)),1),c("div",Gm,[c("span",Xm,L(g.quantity)+" billet(s)",1),c("span",Qm,"$"+L(g.total_amount),1),c("span",{class:mn([o.getStatusClass(g.status),"px-2 py-1 rounded-full text-xs font-medium"])},L(o.getStatusText(g.status)),3)])]),c("div",Ym,[J(i,{to:`/events/${(O=g.event)==null?void 0:O.id}`,class:"text-orange-600 hover:text-orange-700 text-sm"},{default:ce(()=>t[11]||(t[11]=[G(" Voir l'événement ")])),_:2,__:[11]},1032,["to"])])])])}),128))]))])])):((d=r.user)==null?void 0:d.role)==="organizer"?(V(),H("div",Zm,t[12]||(t[12]=[$t('<div class="text-center"><h2 class="text-2xl font-bold text-gray-900 mb-4">Dashboard Organisateur</h2><p class="text-gray-600">Gérez vos événements et suivez vos ventes</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="card text-center"><svg class="mx-auto h-12 w-12 text-orange-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg><h3 class="text-lg font-semibold mb-2">Créer un événement</h3><p class="text-gray-600 mb-4">Organisez votre prochain événement culturel</p><button class="btn-primary">Créer un événement</button></div><div class="card text-center"><svg class="mx-auto h-12 w-12 text-blue-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg><h3 class="text-lg font-semibold mb-2">Voir les statistiques</h3><p class="text-gray-600 mb-4">Analysez les performances de vos événements</p><button class="btn-secondary">Voir les stats</button></div></div>',2)]))):((m=r.user)==null?void 0:m.role)==="admin"?(V(),H("div",e0,[t[17]||(t[17]=c("div",{class:"text-center"},[c("h2",{class:"text-2xl font-bold text-gray-900 mb-4"},"Dashboard Administrateur"),c("p",{class:"text-gray-600"},"Gérez la plateforme KabEvents")],-1)),c("div",t0,[c("div",s0,[c("div",n0,L(r.adminStats.totalUsers),1),t[13]||(t[13]=c("div",{class:"text-gray-600"},"Utilisateurs",-1))]),c("div",r0,[c("div",o0,L(r.adminStats.totalEvents),1),t[14]||(t[14]=c("div",{class:"text-gray-600"},"Événements",-1))]),c("div",i0,[c("div",l0,L(r.adminStats.totalReservations),1),t[15]||(t[15]=c("div",{class:"text-gray-600"},"Réservations",-1))]),c("div",a0,[c("div",c0,"$"+L(r.adminStats.totalRevenue),1),t[16]||(t[16]=c("div",{class:"text-gray-600"},"Revenus",-1))])])])):ct("",!0)])])}const f0=Nt(gm,[["render",u0]]);fe.defaults.baseURL="/api";fe.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";const Uo=localStorage.getItem("auth_token");Uo&&(fe.defaults.headers.common.Authorization=`Bearer ${Uo}`);const d0=[{path:"/",name:"home",component:Np},{path:"/login",name:"login",component:Jp},{path:"/register",name:"register",component:oh},{path:"/events",name:"events",component:Ch},{path:"/events/:id",name:"event-detail",component:mm,props:!0},{path:"/dashboard",name:"dashboard",component:f0,meta:{requiresAuth:!0}}],$l=Dd({history:ud(),routes:d0});$l.beforeEach((e,t,s)=>{const n=localStorage.getItem("auth_token");e.meta.requiresAuth&&!n?s("/login"):s()});const Or=Pf(ip);Or.use($l);Or.config.globalProperties.$http=fe;Or.mount("#app");
