function Go(e,t){return function(){return e.apply(t,arguments)}}const{toString:ql}=Object.prototype,{getPrototypeOf:ur}=Object,{iterator:un,toStringTag:Xo}=Symbol,dn=(e=>t=>{const s=ql.call(t);return e[s]||(e[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Xe=e=>(e=e.toLowerCase(),t=>dn(t)===e),fn=e=>t=>typeof t===e,{isArray:rs}=Array,Ss=fn("undefined");function Hl(e){return e!==null&&!Ss(e)&&e.constructor!==null&&!Ss(e.constructor)&&Pe(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Qo=Xe("ArrayBuffer");function Kl(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Qo(e.buffer),t}const Wl=fn("string"),Pe=fn("function"),Yo=fn("number"),pn=e=>e!==null&&typeof e=="object",Jl=e=>e===!0||e===!1,zs=e=>{if(dn(e)!=="object")return!1;const t=ur(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Xo in e)&&!(un in e)},Gl=Xe("Date"),Xl=Xe("File"),Ql=Xe("Blob"),Yl=Xe("FileList"),Zl=e=>pn(e)&&Pe(e.pipe),ea=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Pe(e.append)&&((t=dn(e))==="formdata"||t==="object"&&Pe(e.toString)&&e.toString()==="[object FormData]"))},ta=Xe("URLSearchParams"),[sa,na,ra,oa]=["ReadableStream","Request","Response","Headers"].map(Xe),ia=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ps(e,t,{allOwnKeys:s=!1}={}){if(e===null||typeof e>"u")return;let r,n;if(typeof e!="object"&&(e=[e]),rs(e))for(r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else{const i=s?Object.getOwnPropertyNames(e):Object.keys(e),l=i.length;let a;for(r=0;r<l;r++)a=i[r],t.call(null,e[a],a,e)}}function Zo(e,t){t=t.toLowerCase();const s=Object.keys(e);let r=s.length,n;for(;r-- >0;)if(n=s[r],t===n.toLowerCase())return n;return null}const Dt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ei=e=>!Ss(e)&&e!==Dt;function Hn(){const{caseless:e}=ei(this)&&this||{},t={},s=(r,n)=>{const i=e&&Zo(t,n)||n;zs(t[i])&&zs(r)?t[i]=Hn(t[i],r):zs(r)?t[i]=Hn({},r):rs(r)?t[i]=r.slice():t[i]=r};for(let r=0,n=arguments.length;r<n;r++)arguments[r]&&Ps(arguments[r],s);return t}const la=(e,t,s,{allOwnKeys:r}={})=>(Ps(t,(n,i)=>{s&&Pe(n)?e[i]=Go(n,s):e[i]=n},{allOwnKeys:r}),e),aa=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),ca=(e,t,s,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),s&&Object.assign(e.prototype,s)},ua=(e,t,s,r)=>{let n,i,l;const a={};if(t=t||{},e==null)return t;do{for(n=Object.getOwnPropertyNames(e),i=n.length;i-- >0;)l=n[i],(!r||r(l,e,t))&&!a[l]&&(t[l]=e[l],a[l]=!0);e=s!==!1&&ur(e)}while(e&&(!s||s(e,t))&&e!==Object.prototype);return t},da=(e,t,s)=>{e=String(e),(s===void 0||s>e.length)&&(s=e.length),s-=t.length;const r=e.indexOf(t,s);return r!==-1&&r===s},fa=e=>{if(!e)return null;if(rs(e))return e;let t=e.length;if(!Yo(t))return null;const s=new Array(t);for(;t-- >0;)s[t]=e[t];return s},pa=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ur(Uint8Array)),ma=(e,t)=>{const r=(e&&e[un]).call(e);let n;for(;(n=r.next())&&!n.done;){const i=n.value;t.call(e,i[0],i[1])}},ha=(e,t)=>{let s;const r=[];for(;(s=e.exec(t))!==null;)r.push(s);return r},ga=Xe("HTMLFormElement"),va=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,r,n){return r.toUpperCase()+n}),Nr=(({hasOwnProperty:e})=>(t,s)=>e.call(t,s))(Object.prototype),xa=Xe("RegExp"),ti=(e,t)=>{const s=Object.getOwnPropertyDescriptors(e),r={};Ps(s,(n,i)=>{let l;(l=t(n,i,e))!==!1&&(r[i]=l||n)}),Object.defineProperties(e,r)},ba=e=>{ti(e,(t,s)=>{if(Pe(e)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const r=e[s];if(Pe(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},ya=(e,t)=>{const s={},r=n=>{n.forEach(i=>{s[i]=!0})};return rs(e)?r(e):r(String(e).split(t)),s},wa=()=>{},_a=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function ka(e){return!!(e&&Pe(e.append)&&e[Xo]==="FormData"&&e[un])}const Sa=e=>{const t=new Array(10),s=(r,n)=>{if(pn(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[n]=r;const i=rs(r)?[]:{};return Ps(r,(l,a)=>{const c=s(l,n+1);!Ss(c)&&(i[a]=c)}),t[n]=void 0,i}}return r};return s(e,0)},Ea=Xe("AsyncFunction"),Ca=e=>e&&(pn(e)||Pe(e))&&Pe(e.then)&&Pe(e.catch),si=((e,t)=>e?setImmediate:t?((s,r)=>(Dt.addEventListener("message",({source:n,data:i})=>{n===Dt&&i===s&&r.length&&r.shift()()},!1),n=>{r.push(n),Dt.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",Pe(Dt.postMessage)),Ra=typeof queueMicrotask<"u"?queueMicrotask.bind(Dt):typeof process<"u"&&process.nextTick||si,Ta=e=>e!=null&&Pe(e[un]),y={isArray:rs,isArrayBuffer:Qo,isBuffer:Hl,isFormData:ea,isArrayBufferView:Kl,isString:Wl,isNumber:Yo,isBoolean:Jl,isObject:pn,isPlainObject:zs,isReadableStream:sa,isRequest:na,isResponse:ra,isHeaders:oa,isUndefined:Ss,isDate:Gl,isFile:Xl,isBlob:Ql,isRegExp:xa,isFunction:Pe,isStream:Zl,isURLSearchParams:ta,isTypedArray:pa,isFileList:Yl,forEach:Ps,merge:Hn,extend:la,trim:ia,stripBOM:aa,inherits:ca,toFlatObject:ua,kindOf:dn,kindOfTest:Xe,endsWith:da,toArray:fa,forEachEntry:ma,matchAll:ha,isHTMLForm:ga,hasOwnProperty:Nr,hasOwnProp:Nr,reduceDescriptors:ti,freezeMethods:ba,toObjectSet:ya,toCamelCase:va,noop:wa,toFiniteNumber:_a,findKey:Zo,global:Dt,isContextDefined:ei,isSpecCompliantForm:ka,toJSONObject:Sa,isAsyncFn:Ea,isThenable:Ca,setImmediate:si,asap:Ra,isIterable:Ta};function G(e,t,s,r,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),s&&(this.config=s),r&&(this.request=r),n&&(this.response=n,this.status=n.status?n.status:null)}y.inherits(G,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:y.toJSONObject(this.config),code:this.code,status:this.status}}});const ni=G.prototype,ri={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ri[e]={value:e}});Object.defineProperties(G,ri);Object.defineProperty(ni,"isAxiosError",{value:!0});G.from=(e,t,s,r,n,i)=>{const l=Object.create(ni);return y.toFlatObject(e,l,function(c){return c!==Error.prototype},a=>a!=="isAxiosError"),G.call(l,e.message,t,s,r,n),l.cause=e,l.name=e.name,i&&Object.assign(l,i),l};const Aa=null;function Kn(e){return y.isPlainObject(e)||y.isArray(e)}function oi(e){return y.endsWith(e,"[]")?e.slice(0,-2):e}function Fr(e,t,s){return e?e.concat(t).map(function(n,i){return n=oi(n),!s&&i?"["+n+"]":n}).join(s?".":""):t}function Oa(e){return y.isArray(e)&&!e.some(Kn)}const $a=y.toFlatObject(y,{},null,function(t){return/^is[A-Z]/.test(t)});function mn(e,t,s){if(!y.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,s=y.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,C){return!y.isUndefined(C[w])});const r=s.metaTokens,n=s.visitor||u,i=s.dots,l=s.indexes,c=(s.Blob||typeof Blob<"u"&&Blob)&&y.isSpecCompliantForm(t);if(!y.isFunction(n))throw new TypeError("visitor must be a function");function d(g){if(g===null)return"";if(y.isDate(g))return g.toISOString();if(!c&&y.isBlob(g))throw new G("Blob is not supported. Use a Buffer instead.");return y.isArrayBuffer(g)||y.isTypedArray(g)?c&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function u(g,w,C){let O=g;if(g&&!C&&typeof g=="object"){if(y.endsWith(w,"{}"))w=r?w:w.slice(0,-2),g=JSON.stringify(g);else if(y.isArray(g)&&Oa(g)||(y.isFileList(g)||y.endsWith(w,"[]"))&&(O=y.toArray(g)))return w=oi(w),O.forEach(function(D,F){!(y.isUndefined(D)||D===null)&&t.append(l===!0?Fr([w],F,i):l===null?w:w+"[]",d(D))}),!1}return Kn(g)?!0:(t.append(Fr(C,w,i),d(g)),!1)}const f=[],h=Object.assign($a,{defaultVisitor:u,convertValue:d,isVisitable:Kn});function v(g,w){if(!y.isUndefined(g)){if(f.indexOf(g)!==-1)throw Error("Circular reference detected in "+w.join("."));f.push(g),y.forEach(g,function(O,$){(!(y.isUndefined(O)||O===null)&&n.call(t,O,y.isString($)?$.trim():$,w,h))===!0&&v(O,w?w.concat($):[$])}),f.pop()}}if(!y.isObject(e))throw new TypeError("data must be an object");return v(e),t}function Ur(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function dr(e,t){this._pairs=[],e&&mn(e,this,t)}const ii=dr.prototype;ii.append=function(t,s){this._pairs.push([t,s])};ii.toString=function(t){const s=t?function(r){return t.call(this,r,Ur)}:Ur;return this._pairs.map(function(n){return s(n[0])+"="+s(n[1])},"").join("&")};function Pa(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function li(e,t,s){if(!t)return e;const r=s&&s.encode||Pa;y.isFunction(s)&&(s={serialize:s});const n=s&&s.serialize;let i;if(n?i=n(t,s):i=y.isURLSearchParams(t)?t.toString():new dr(t,s).toString(r),i){const l=e.indexOf("#");l!==-1&&(e=e.slice(0,l)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class Vr{constructor(){this.handlers=[]}use(t,s,r){return this.handlers.push({fulfilled:t,rejected:s,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){y.forEach(this.handlers,function(r){r!==null&&t(r)})}}const ai={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ma=typeof URLSearchParams<"u"?URLSearchParams:dr,ja=typeof FormData<"u"?FormData:null,Da=typeof Blob<"u"?Blob:null,Ia={isBrowser:!0,classes:{URLSearchParams:Ma,FormData:ja,Blob:Da},protocols:["http","https","file","blob","url","data"]},fr=typeof window<"u"&&typeof document<"u",Wn=typeof navigator=="object"&&navigator||void 0,Na=fr&&(!Wn||["ReactNative","NativeScript","NS"].indexOf(Wn.product)<0),Fa=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Ua=fr&&window.location.href||"http://localhost",Va=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:fr,hasStandardBrowserEnv:Na,hasStandardBrowserWebWorkerEnv:Fa,navigator:Wn,origin:Ua},Symbol.toStringTag,{value:"Module"})),we={...Va,...Ia};function La(e,t){return mn(e,new we.classes.URLSearchParams,Object.assign({visitor:function(s,r,n,i){return we.isNode&&y.isBuffer(s)?(this.append(r,s.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function Ba(e){return y.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function za(e){const t={},s=Object.keys(e);let r;const n=s.length;let i;for(r=0;r<n;r++)i=s[r],t[i]=e[i];return t}function ci(e){function t(s,r,n,i){let l=s[i++];if(l==="__proto__")return!0;const a=Number.isFinite(+l),c=i>=s.length;return l=!l&&y.isArray(n)?n.length:l,c?(y.hasOwnProp(n,l)?n[l]=[n[l],r]:n[l]=r,!a):((!n[l]||!y.isObject(n[l]))&&(n[l]=[]),t(s,r,n[l],i)&&y.isArray(n[l])&&(n[l]=za(n[l])),!a)}if(y.isFormData(e)&&y.isFunction(e.entries)){const s={};return y.forEachEntry(e,(r,n)=>{t(Ba(r),n,s,0)}),s}return null}function qa(e,t,s){if(y.isString(e))try{return(t||JSON.parse)(e),y.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(s||JSON.stringify)(e)}const Ms={transitional:ai,adapter:["xhr","http","fetch"],transformRequest:[function(t,s){const r=s.getContentType()||"",n=r.indexOf("application/json")>-1,i=y.isObject(t);if(i&&y.isHTMLForm(t)&&(t=new FormData(t)),y.isFormData(t))return n?JSON.stringify(ci(t)):t;if(y.isArrayBuffer(t)||y.isBuffer(t)||y.isStream(t)||y.isFile(t)||y.isBlob(t)||y.isReadableStream(t))return t;if(y.isArrayBufferView(t))return t.buffer;if(y.isURLSearchParams(t))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(i){if(r.indexOf("application/x-www-form-urlencoded")>-1)return La(t,this.formSerializer).toString();if((a=y.isFileList(t))||r.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return mn(a?{"files[]":t}:t,c&&new c,this.formSerializer)}}return i||n?(s.setContentType("application/json",!1),qa(t)):t}],transformResponse:[function(t){const s=this.transitional||Ms.transitional,r=s&&s.forcedJSONParsing,n=this.responseType==="json";if(y.isResponse(t)||y.isReadableStream(t))return t;if(t&&y.isString(t)&&(r&&!this.responseType||n)){const l=!(s&&s.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(a){if(l)throw a.name==="SyntaxError"?G.from(a,G.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:we.classes.FormData,Blob:we.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};y.forEach(["delete","get","head","post","put","patch"],e=>{Ms.headers[e]={}});const Ha=y.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ka=e=>{const t={};let s,r,n;return e&&e.split(`
`).forEach(function(l){n=l.indexOf(":"),s=l.substring(0,n).trim().toLowerCase(),r=l.substring(n+1).trim(),!(!s||t[s]&&Ha[s])&&(s==="set-cookie"?t[s]?t[s].push(r):t[s]=[r]:t[s]=t[s]?t[s]+", "+r:r)}),t},Lr=Symbol("internals");function cs(e){return e&&String(e).trim().toLowerCase()}function qs(e){return e===!1||e==null?e:y.isArray(e)?e.map(qs):String(e)}function Wa(e){const t=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=s.exec(e);)t[r[1]]=r[2];return t}const Ja=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function On(e,t,s,r,n){if(y.isFunction(r))return r.call(this,t,s);if(n&&(t=s),!!y.isString(t)){if(y.isString(r))return t.indexOf(r)!==-1;if(y.isRegExp(r))return r.test(t)}}function Ga(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,s,r)=>s.toUpperCase()+r)}function Xa(e,t){const s=y.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+s,{value:function(n,i,l){return this[r].call(this,t,n,i,l)},configurable:!0})})}let Me=class{constructor(t){t&&this.set(t)}set(t,s,r){const n=this;function i(a,c,d){const u=cs(c);if(!u)throw new Error("header name must be a non-empty string");const f=y.findKey(n,u);(!f||n[f]===void 0||d===!0||d===void 0&&n[f]!==!1)&&(n[f||c]=qs(a))}const l=(a,c)=>y.forEach(a,(d,u)=>i(d,u,c));if(y.isPlainObject(t)||t instanceof this.constructor)l(t,s);else if(y.isString(t)&&(t=t.trim())&&!Ja(t))l(Ka(t),s);else if(y.isObject(t)&&y.isIterable(t)){let a={},c,d;for(const u of t){if(!y.isArray(u))throw TypeError("Object iterator must return a key-value pair");a[d=u[0]]=(c=a[d])?y.isArray(c)?[...c,u[1]]:[c,u[1]]:u[1]}l(a,s)}else t!=null&&i(s,t,r);return this}get(t,s){if(t=cs(t),t){const r=y.findKey(this,t);if(r){const n=this[r];if(!s)return n;if(s===!0)return Wa(n);if(y.isFunction(s))return s.call(this,n,r);if(y.isRegExp(s))return s.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,s){if(t=cs(t),t){const r=y.findKey(this,t);return!!(r&&this[r]!==void 0&&(!s||On(this,this[r],r,s)))}return!1}delete(t,s){const r=this;let n=!1;function i(l){if(l=cs(l),l){const a=y.findKey(r,l);a&&(!s||On(r,r[a],a,s))&&(delete r[a],n=!0)}}return y.isArray(t)?t.forEach(i):i(t),n}clear(t){const s=Object.keys(this);let r=s.length,n=!1;for(;r--;){const i=s[r];(!t||On(this,this[i],i,t,!0))&&(delete this[i],n=!0)}return n}normalize(t){const s=this,r={};return y.forEach(this,(n,i)=>{const l=y.findKey(r,i);if(l){s[l]=qs(n),delete s[i];return}const a=t?Ga(i):String(i).trim();a!==i&&delete s[i],s[a]=qs(n),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const s=Object.create(null);return y.forEach(this,(r,n)=>{r!=null&&r!==!1&&(s[n]=t&&y.isArray(r)?r.join(", "):r)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,s])=>t+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...s){const r=new this(t);return s.forEach(n=>r.set(n)),r}static accessor(t){const r=(this[Lr]=this[Lr]={accessors:{}}).accessors,n=this.prototype;function i(l){const a=cs(l);r[a]||(Xa(n,l),r[a]=!0)}return y.isArray(t)?t.forEach(i):i(t),this}};Me.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);y.reduceDescriptors(Me.prototype,({value:e},t)=>{let s=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[s]=r}}});y.freezeMethods(Me);function $n(e,t){const s=this||Ms,r=t||s,n=Me.from(r.headers);let i=r.data;return y.forEach(e,function(a){i=a.call(s,i,n.normalize(),t?t.status:void 0)}),n.normalize(),i}function ui(e){return!!(e&&e.__CANCEL__)}function os(e,t,s){G.call(this,e??"canceled",G.ERR_CANCELED,t,s),this.name="CanceledError"}y.inherits(os,G,{__CANCEL__:!0});function di(e,t,s){const r=s.config.validateStatus;!s.status||!r||r(s.status)?e(s):t(new G("Request failed with status code "+s.status,[G.ERR_BAD_REQUEST,G.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function Qa(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Ya(e,t){e=e||10;const s=new Array(e),r=new Array(e);let n=0,i=0,l;return t=t!==void 0?t:1e3,function(c){const d=Date.now(),u=r[i];l||(l=d),s[n]=c,r[n]=d;let f=i,h=0;for(;f!==n;)h+=s[f++],f=f%e;if(n=(n+1)%e,n===i&&(i=(i+1)%e),d-l<t)return;const v=u&&d-u;return v?Math.round(h*1e3/v):void 0}}function Za(e,t){let s=0,r=1e3/t,n,i;const l=(d,u=Date.now())=>{s=u,n=null,i&&(clearTimeout(i),i=null),e.apply(null,d)};return[(...d)=>{const u=Date.now(),f=u-s;f>=r?l(d,u):(n=d,i||(i=setTimeout(()=>{i=null,l(n)},r-f)))},()=>n&&l(n)]}const Ys=(e,t,s=3)=>{let r=0;const n=Ya(50,250);return Za(i=>{const l=i.loaded,a=i.lengthComputable?i.total:void 0,c=l-r,d=n(c),u=l<=a;r=l;const f={loaded:l,total:a,progress:a?l/a:void 0,bytes:c,rate:d||void 0,estimated:d&&a&&u?(a-l)/d:void 0,event:i,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},s)},Br=(e,t)=>{const s=e!=null;return[r=>t[0]({lengthComputable:s,total:e,loaded:r}),t[1]]},zr=e=>(...t)=>y.asap(()=>e(...t)),ec=we.hasStandardBrowserEnv?((e,t)=>s=>(s=new URL(s,we.origin),e.protocol===s.protocol&&e.host===s.host&&(t||e.port===s.port)))(new URL(we.origin),we.navigator&&/(msie|trident)/i.test(we.navigator.userAgent)):()=>!0,tc=we.hasStandardBrowserEnv?{write(e,t,s,r,n,i){const l=[e+"="+encodeURIComponent(t)];y.isNumber(s)&&l.push("expires="+new Date(s).toGMTString()),y.isString(r)&&l.push("path="+r),y.isString(n)&&l.push("domain="+n),i===!0&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function sc(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function nc(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function fi(e,t,s){let r=!sc(t);return e&&(r||s==!1)?nc(e,t):t}const qr=e=>e instanceof Me?{...e}:e;function Ft(e,t){t=t||{};const s={};function r(d,u,f,h){return y.isPlainObject(d)&&y.isPlainObject(u)?y.merge.call({caseless:h},d,u):y.isPlainObject(u)?y.merge({},u):y.isArray(u)?u.slice():u}function n(d,u,f,h){if(y.isUndefined(u)){if(!y.isUndefined(d))return r(void 0,d,f,h)}else return r(d,u,f,h)}function i(d,u){if(!y.isUndefined(u))return r(void 0,u)}function l(d,u){if(y.isUndefined(u)){if(!y.isUndefined(d))return r(void 0,d)}else return r(void 0,u)}function a(d,u,f){if(f in t)return r(d,u);if(f in e)return r(void 0,d)}const c={url:i,method:i,data:i,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:a,headers:(d,u,f)=>n(qr(d),qr(u),f,!0)};return y.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=c[u]||n,h=f(e[u],t[u],u);y.isUndefined(h)&&f!==a||(s[u]=h)}),s}const pi=e=>{const t=Ft({},e);let{data:s,withXSRFToken:r,xsrfHeaderName:n,xsrfCookieName:i,headers:l,auth:a}=t;t.headers=l=Me.from(l),t.url=li(fi(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&l.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let c;if(y.isFormData(s)){if(we.hasStandardBrowserEnv||we.hasStandardBrowserWebWorkerEnv)l.setContentType(void 0);else if((c=l.getContentType())!==!1){const[d,...u]=c?c.split(";").map(f=>f.trim()).filter(Boolean):[];l.setContentType([d||"multipart/form-data",...u].join("; "))}}if(we.hasStandardBrowserEnv&&(r&&y.isFunction(r)&&(r=r(t)),r||r!==!1&&ec(t.url))){const d=n&&i&&tc.read(i);d&&l.set(n,d)}return t},rc=typeof XMLHttpRequest<"u",oc=rc&&function(e){return new Promise(function(s,r){const n=pi(e);let i=n.data;const l=Me.from(n.headers).normalize();let{responseType:a,onUploadProgress:c,onDownloadProgress:d}=n,u,f,h,v,g;function w(){v&&v(),g&&g(),n.cancelToken&&n.cancelToken.unsubscribe(u),n.signal&&n.signal.removeEventListener("abort",u)}let C=new XMLHttpRequest;C.open(n.method.toUpperCase(),n.url,!0),C.timeout=n.timeout;function O(){if(!C)return;const D=Me.from("getAllResponseHeaders"in C&&C.getAllResponseHeaders()),J={data:!a||a==="text"||a==="json"?C.responseText:C.response,status:C.status,statusText:C.statusText,headers:D,config:e,request:C};di(function(Y){s(Y),w()},function(Y){r(Y),w()},J),C=null}"onloadend"in C?C.onloadend=O:C.onreadystatechange=function(){!C||C.readyState!==4||C.status===0&&!(C.responseURL&&C.responseURL.indexOf("file:")===0)||setTimeout(O)},C.onabort=function(){C&&(r(new G("Request aborted",G.ECONNABORTED,e,C)),C=null)},C.onerror=function(){r(new G("Network Error",G.ERR_NETWORK,e,C)),C=null},C.ontimeout=function(){let F=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const J=n.transitional||ai;n.timeoutErrorMessage&&(F=n.timeoutErrorMessage),r(new G(F,J.clarifyTimeoutError?G.ETIMEDOUT:G.ECONNABORTED,e,C)),C=null},i===void 0&&l.setContentType(null),"setRequestHeader"in C&&y.forEach(l.toJSON(),function(F,J){C.setRequestHeader(J,F)}),y.isUndefined(n.withCredentials)||(C.withCredentials=!!n.withCredentials),a&&a!=="json"&&(C.responseType=n.responseType),d&&([h,g]=Ys(d,!0),C.addEventListener("progress",h)),c&&C.upload&&([f,v]=Ys(c),C.upload.addEventListener("progress",f),C.upload.addEventListener("loadend",v)),(n.cancelToken||n.signal)&&(u=D=>{C&&(r(!D||D.type?new os(null,e,C):D),C.abort(),C=null)},n.cancelToken&&n.cancelToken.subscribe(u),n.signal&&(n.signal.aborted?u():n.signal.addEventListener("abort",u)));const $=Qa(n.url);if($&&we.protocols.indexOf($)===-1){r(new G("Unsupported protocol "+$+":",G.ERR_BAD_REQUEST,e));return}C.send(i||null)})},ic=(e,t)=>{const{length:s}=e=e?e.filter(Boolean):[];if(t||s){let r=new AbortController,n;const i=function(d){if(!n){n=!0,a();const u=d instanceof Error?d:this.reason;r.abort(u instanceof G?u:new os(u instanceof Error?u.message:u))}};let l=t&&setTimeout(()=>{l=null,i(new G(`timeout ${t} of ms exceeded`,G.ETIMEDOUT))},t);const a=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach(d=>{d.unsubscribe?d.unsubscribe(i):d.removeEventListener("abort",i)}),e=null)};e.forEach(d=>d.addEventListener("abort",i));const{signal:c}=r;return c.unsubscribe=()=>y.asap(a),c}},lc=function*(e,t){let s=e.byteLength;if(s<t){yield e;return}let r=0,n;for(;r<s;)n=r+t,yield e.slice(r,n),r=n},ac=async function*(e,t){for await(const s of cc(e))yield*lc(s,t)},cc=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:s,value:r}=await t.read();if(s)break;yield r}}finally{await t.cancel()}},Hr=(e,t,s,r)=>{const n=ac(e,t);let i=0,l,a=c=>{l||(l=!0,r&&r(c))};return new ReadableStream({async pull(c){try{const{done:d,value:u}=await n.next();if(d){a(),c.close();return}let f=u.byteLength;if(s){let h=i+=f;s(h)}c.enqueue(new Uint8Array(u))}catch(d){throw a(d),d}},cancel(c){return a(c),n.return()}},{highWaterMark:2})},hn=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",mi=hn&&typeof ReadableStream=="function",uc=hn&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),hi=(e,...t)=>{try{return!!e(...t)}catch{return!1}},dc=mi&&hi(()=>{let e=!1;const t=new Request(we.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Kr=64*1024,Jn=mi&&hi(()=>y.isReadableStream(new Response("").body)),Zs={stream:Jn&&(e=>e.body)};hn&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Zs[t]&&(Zs[t]=y.isFunction(e[t])?s=>s[t]():(s,r)=>{throw new G(`Response type '${t}' is not supported`,G.ERR_NOT_SUPPORT,r)})})})(new Response);const fc=async e=>{if(e==null)return 0;if(y.isBlob(e))return e.size;if(y.isSpecCompliantForm(e))return(await new Request(we.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(y.isArrayBufferView(e)||y.isArrayBuffer(e))return e.byteLength;if(y.isURLSearchParams(e)&&(e=e+""),y.isString(e))return(await uc(e)).byteLength},pc=async(e,t)=>{const s=y.toFiniteNumber(e.getContentLength());return s??fc(t)},mc=hn&&(async e=>{let{url:t,method:s,data:r,signal:n,cancelToken:i,timeout:l,onDownloadProgress:a,onUploadProgress:c,responseType:d,headers:u,withCredentials:f="same-origin",fetchOptions:h}=pi(e);d=d?(d+"").toLowerCase():"text";let v=ic([n,i&&i.toAbortSignal()],l),g;const w=v&&v.unsubscribe&&(()=>{v.unsubscribe()});let C;try{if(c&&dc&&s!=="get"&&s!=="head"&&(C=await pc(u,r))!==0){let J=new Request(t,{method:"POST",body:r,duplex:"half"}),ue;if(y.isFormData(r)&&(ue=J.headers.get("content-type"))&&u.setContentType(ue),J.body){const[Y,Ee]=Br(C,Ys(zr(c)));r=Hr(J.body,Kr,Y,Ee)}}y.isString(f)||(f=f?"include":"omit");const O="credentials"in Request.prototype;g=new Request(t,{...h,signal:v,method:s.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:O?f:void 0});let $=await fetch(g);const D=Jn&&(d==="stream"||d==="response");if(Jn&&(a||D&&w)){const J={};["status","statusText","headers"].forEach(He=>{J[He]=$[He]});const ue=y.toFiniteNumber($.headers.get("content-length")),[Y,Ee]=a&&Br(ue,Ys(zr(a),!0))||[];$=new Response(Hr($.body,Kr,Y,()=>{Ee&&Ee(),w&&w()}),J)}d=d||"text";let F=await Zs[y.findKey(Zs,d)||"text"]($,e);return!D&&w&&w(),await new Promise((J,ue)=>{di(J,ue,{data:F,headers:Me.from($.headers),status:$.status,statusText:$.statusText,config:e,request:g})})}catch(O){throw w&&w(),O&&O.name==="TypeError"&&/Load failed|fetch/i.test(O.message)?Object.assign(new G("Network Error",G.ERR_NETWORK,e,g),{cause:O.cause||O}):G.from(O,O&&O.code,e,g)}}),Gn={http:Aa,xhr:oc,fetch:mc};y.forEach(Gn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Wr=e=>`- ${e}`,hc=e=>y.isFunction(e)||e===null||e===!1,gi={getAdapter:e=>{e=y.isArray(e)?e:[e];const{length:t}=e;let s,r;const n={};for(let i=0;i<t;i++){s=e[i];let l;if(r=s,!hc(s)&&(r=Gn[(l=String(s)).toLowerCase()],r===void 0))throw new G(`Unknown adapter '${l}'`);if(r)break;n[l||"#"+i]=r}if(!r){const i=Object.entries(n).map(([a,c])=>`adapter ${a} `+(c===!1?"is not supported by the environment":"is not available in the build"));let l=t?i.length>1?`since :
`+i.map(Wr).join(`
`):" "+Wr(i[0]):"as no adapter specified";throw new G("There is no suitable adapter to dispatch the request "+l,"ERR_NOT_SUPPORT")}return r},adapters:Gn};function Pn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new os(null,e)}function Jr(e){return Pn(e),e.headers=Me.from(e.headers),e.data=$n.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),gi.getAdapter(e.adapter||Ms.adapter)(e).then(function(r){return Pn(e),r.data=$n.call(e,e.transformResponse,r),r.headers=Me.from(r.headers),r},function(r){return ui(r)||(Pn(e),r&&r.response&&(r.response.data=$n.call(e,e.transformResponse,r.response),r.response.headers=Me.from(r.response.headers))),Promise.reject(r)})}const vi="1.9.0",gn={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{gn[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Gr={};gn.transitional=function(t,s,r){function n(i,l){return"[Axios v"+vi+"] Transitional option '"+i+"'"+l+(r?". "+r:"")}return(i,l,a)=>{if(t===!1)throw new G(n(l," has been removed"+(s?" in "+s:"")),G.ERR_DEPRECATED);return s&&!Gr[l]&&(Gr[l]=!0,console.warn(n(l," has been deprecated since v"+s+" and will be removed in the near future"))),t?t(i,l,a):!0}};gn.spelling=function(t){return(s,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function gc(e,t,s){if(typeof e!="object")throw new G("options must be an object",G.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let n=r.length;for(;n-- >0;){const i=r[n],l=t[i];if(l){const a=e[i],c=a===void 0||l(a,i,e);if(c!==!0)throw new G("option "+i+" must be "+c,G.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new G("Unknown option "+i,G.ERR_BAD_OPTION)}}const Hs={assertOptions:gc,validators:gn},nt=Hs.validators;let It=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Vr,response:new Vr}}async request(t,s){try{return await this._request(t,s)}catch(r){if(r instanceof Error){let n={};Error.captureStackTrace?Error.captureStackTrace(n):n=new Error;const i=n.stack?n.stack.replace(/^.+\n/,""):"";try{r.stack?i&&!String(r.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+i):r.stack=i}catch{}}throw r}}_request(t,s){typeof t=="string"?(s=s||{},s.url=t):s=t||{},s=Ft(this.defaults,s);const{transitional:r,paramsSerializer:n,headers:i}=s;r!==void 0&&Hs.assertOptions(r,{silentJSONParsing:nt.transitional(nt.boolean),forcedJSONParsing:nt.transitional(nt.boolean),clarifyTimeoutError:nt.transitional(nt.boolean)},!1),n!=null&&(y.isFunction(n)?s.paramsSerializer={serialize:n}:Hs.assertOptions(n,{encode:nt.function,serialize:nt.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Hs.assertOptions(s,{baseUrl:nt.spelling("baseURL"),withXsrfToken:nt.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let l=i&&y.merge(i.common,i[s.method]);i&&y.forEach(["delete","get","head","post","put","patch","common"],g=>{delete i[g]}),s.headers=Me.concat(l,i);const a=[];let c=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(s)===!1||(c=c&&w.synchronous,a.unshift(w.fulfilled,w.rejected))});const d=[];this.interceptors.response.forEach(function(w){d.push(w.fulfilled,w.rejected)});let u,f=0,h;if(!c){const g=[Jr.bind(this),void 0];for(g.unshift.apply(g,a),g.push.apply(g,d),h=g.length,u=Promise.resolve(s);f<h;)u=u.then(g[f++],g[f++]);return u}h=a.length;let v=s;for(f=0;f<h;){const g=a[f++],w=a[f++];try{v=g(v)}catch(C){w.call(this,C);break}}try{u=Jr.call(this,v)}catch(g){return Promise.reject(g)}for(f=0,h=d.length;f<h;)u=u.then(d[f++],d[f++]);return u}getUri(t){t=Ft(this.defaults,t);const s=fi(t.baseURL,t.url,t.allowAbsoluteUrls);return li(s,t.params,t.paramsSerializer)}};y.forEach(["delete","get","head","options"],function(t){It.prototype[t]=function(s,r){return this.request(Ft(r||{},{method:t,url:s,data:(r||{}).data}))}});y.forEach(["post","put","patch"],function(t){function s(r){return function(i,l,a){return this.request(Ft(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:i,data:l}))}}It.prototype[t]=s(),It.prototype[t+"Form"]=s(!0)});let vc=class xi{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(i){s=i});const r=this;this.promise.then(n=>{if(!r._listeners)return;let i=r._listeners.length;for(;i-- >0;)r._listeners[i](n);r._listeners=null}),this.promise.then=n=>{let i;const l=new Promise(a=>{r.subscribe(a),i=a}).then(n);return l.cancel=function(){r.unsubscribe(i)},l},t(function(i,l,a){r.reason||(r.reason=new os(i,l,a),s(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const s=this._listeners.indexOf(t);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const t=new AbortController,s=r=>{t.abort(r)};return this.subscribe(s),t.signal.unsubscribe=()=>this.unsubscribe(s),t.signal}static source(){let t;return{token:new xi(function(n){t=n}),cancel:t}}};function xc(e){return function(s){return e.apply(null,s)}}function bc(e){return y.isObject(e)&&e.isAxiosError===!0}const Xn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Xn).forEach(([e,t])=>{Xn[t]=e});function bi(e){const t=new It(e),s=Go(It.prototype.request,t);return y.extend(s,It.prototype,t,{allOwnKeys:!0}),y.extend(s,t,null,{allOwnKeys:!0}),s.create=function(n){return bi(Ft(e,n))},s}const pe=bi(Ms);pe.Axios=It;pe.CanceledError=os;pe.CancelToken=vc;pe.isCancel=ui;pe.VERSION=vi;pe.toFormData=mn;pe.AxiosError=G;pe.Cancel=pe.CanceledError;pe.all=function(t){return Promise.all(t)};pe.spread=xc;pe.isAxiosError=bc;pe.mergeConfig=Ft;pe.AxiosHeaders=Me;pe.formToJSON=e=>ci(y.isHTMLForm(e)?new FormData(e):e);pe.getAdapter=gi.getAdapter;pe.HttpStatusCode=Xn;pe.default=pe;const{Axios:Tx,AxiosError:Ax,CanceledError:Ox,isCancel:$x,CancelToken:Px,VERSION:Mx,all:jx,Cancel:Dx,isAxiosError:Ix,spread:Nx,toFormData:Fx,AxiosHeaders:Ux,HttpStatusCode:Vx,formToJSON:Lx,getAdapter:Bx,mergeConfig:zx}=pe;window.axios=pe;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function pr(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const ce={},Wt=[],lt=()=>{},yc=()=>!1,vn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),mr=e=>e.startsWith("onUpdate:"),Se=Object.assign,hr=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},wc=Object.prototype.hasOwnProperty,se=(e,t)=>wc.call(e,t),z=Array.isArray,Jt=e=>js(e)==="[object Map]",is=e=>js(e)==="[object Set]",Xr=e=>js(e)==="[object Date]",W=e=>typeof e=="function",ve=e=>typeof e=="string",at=e=>typeof e=="symbol",me=e=>e!==null&&typeof e=="object",yi=e=>(me(e)||W(e))&&W(e.then)&&W(e.catch),wi=Object.prototype.toString,js=e=>wi.call(e),_c=e=>js(e).slice(8,-1),_i=e=>js(e)==="[object Object]",gr=e=>ve(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,gs=pr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),xn=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},kc=/-(\w)/g,ze=xn(e=>e.replace(kc,(t,s)=>s?s.toUpperCase():"")),Sc=/\B([A-Z])/g,Vt=xn(e=>e.replace(Sc,"-$1").toLowerCase()),bn=xn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Mn=xn(e=>e?`on${bn(e)}`:""),Rt=(e,t)=>!Object.is(e,t),Ks=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},ki=(e,t,s,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:s})},en=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Qr;const yn=()=>Qr||(Qr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function vr(e){if(z(e)){const t={};for(let s=0;s<e.length;s++){const r=e[s],n=ve(r)?Tc(r):vr(r);if(n)for(const i in n)t[i]=n[i]}return t}else if(ve(e)||me(e))return e}const Ec=/;(?![^(]*\))/g,Cc=/:([^]+)/,Rc=/\/\*[^]*?\*\//g;function Tc(e){const t={};return e.replace(Rc,"").split(Ec).forEach(s=>{if(s){const r=s.split(Cc);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function oe(e){let t="";if(ve(e))t=e;else if(z(e))for(let s=0;s<e.length;s++){const r=oe(e[s]);r&&(t+=r+" ")}else if(me(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Ac="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Oc=pr(Ac);function Si(e){return!!e||e===""}function $c(e,t){if(e.length!==t.length)return!1;let s=!0;for(let r=0;s&&r<e.length;r++)s=Ut(e[r],t[r]);return s}function Ut(e,t){if(e===t)return!0;let s=Xr(e),r=Xr(t);if(s||r)return s&&r?e.getTime()===t.getTime():!1;if(s=at(e),r=at(t),s||r)return e===t;if(s=z(e),r=z(t),s||r)return s&&r?$c(e,t):!1;if(s=me(e),r=me(t),s||r){if(!s||!r)return!1;const n=Object.keys(e).length,i=Object.keys(t).length;if(n!==i)return!1;for(const l in e){const a=e.hasOwnProperty(l),c=t.hasOwnProperty(l);if(a&&!c||!a&&c||!Ut(e[l],t[l]))return!1}}return String(e)===String(t)}function xr(e,t){return e.findIndex(s=>Ut(s,t))}const Ei=e=>!!(e&&e.__v_isRef===!0),b=e=>ve(e)?e:e==null?"":z(e)||me(e)&&(e.toString===wi||!W(e.toString))?Ei(e)?b(e.value):JSON.stringify(e,Ci,2):String(e),Ci=(e,t)=>Ei(t)?Ci(e,t.value):Jt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[r,n],i)=>(s[jn(r,i)+" =>"]=n,s),{})}:is(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>jn(s))}:at(t)?jn(t):me(t)&&!z(t)&&!_i(t)?String(t):t,jn=(e,t="")=>{var s;return at(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let $e;class Pc{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=$e,!t&&$e&&(this.index=($e.scopes||($e.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=$e;try{return $e=this,t()}finally{$e=s}}}on(){++this._on===1&&(this.prevScope=$e,$e=this)}off(){this._on>0&&--this._on===0&&($e=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,r;for(s=0,r=this.effects.length;s<r;s++)this.effects[s].stop();for(this.effects.length=0,s=0,r=this.cleanups.length;s<r;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,r=this.scopes.length;s<r;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function Mc(){return $e}let fe;const Dn=new WeakSet;class Ri{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,$e&&$e.active&&$e.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Dn.has(this)&&(Dn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ai(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Yr(this),Oi(this);const t=fe,s=We;fe=this,We=!0;try{return this.fn()}finally{$i(this),fe=t,We=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)wr(t);this.deps=this.depsTail=void 0,Yr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Dn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Qn(this)&&this.run()}get dirty(){return Qn(this)}}let Ti=0,vs,xs;function Ai(e,t=!1){if(e.flags|=8,t){e.next=xs,xs=e;return}e.next=vs,vs=e}function br(){Ti++}function yr(){if(--Ti>0)return;if(xs){let t=xs;for(xs=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;vs;){let t=vs;for(vs=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=s}}if(e)throw e}function Oi(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function $i(e){let t,s=e.depsTail,r=s;for(;r;){const n=r.prevDep;r.version===-1?(r===s&&(s=n),wr(r),jc(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=n}e.deps=t,e.depsTail=s}function Qn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Pi(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Pi(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Es)||(e.globalVersion=Es,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Qn(e))))return;e.flags|=2;const t=e.dep,s=fe,r=We;fe=e,We=!0;try{Oi(e);const n=e.fn(e._value);(t.version===0||Rt(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(n){throw t.version++,n}finally{fe=s,We=r,$i(e),e.flags&=-3}}function wr(e,t=!1){const{dep:s,prevSub:r,nextSub:n}=e;if(r&&(r.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=r,e.nextSub=void 0),s.subs===e&&(s.subs=r,!r&&s.computed)){s.computed.flags&=-5;for(let i=s.computed.deps;i;i=i.nextDep)wr(i,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function jc(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let We=!0;const Mi=[];function xt(){Mi.push(We),We=!1}function bt(){const e=Mi.pop();We=e===void 0?!0:e}function Yr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=fe;fe=void 0;try{t()}finally{fe=s}}}let Es=0;class Dc{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class _r{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!fe||!We||fe===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==fe)s=this.activeLink=new Dc(fe,this),fe.deps?(s.prevDep=fe.depsTail,fe.depsTail.nextDep=s,fe.depsTail=s):fe.deps=fe.depsTail=s,ji(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const r=s.nextDep;r.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=r),s.prevDep=fe.depsTail,s.nextDep=void 0,fe.depsTail.nextDep=s,fe.depsTail=s,fe.deps===s&&(fe.deps=r)}return s}trigger(t){this.version++,Es++,this.notify(t)}notify(t){br();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{yr()}}}function ji(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)ji(r)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const Yn=new WeakMap,Nt=Symbol(""),Zn=Symbol(""),Cs=Symbol("");function ye(e,t,s){if(We&&fe){let r=Yn.get(e);r||Yn.set(e,r=new Map);let n=r.get(s);n||(r.set(s,n=new _r),n.map=r,n.key=s),n.track()}}function mt(e,t,s,r,n,i){const l=Yn.get(e);if(!l){Es++;return}const a=c=>{c&&c.trigger()};if(br(),t==="clear")l.forEach(a);else{const c=z(e),d=c&&gr(s);if(c&&s==="length"){const u=Number(r);l.forEach((f,h)=>{(h==="length"||h===Cs||!at(h)&&h>=u)&&a(f)})}else switch((s!==void 0||l.has(void 0))&&a(l.get(s)),d&&a(l.get(Cs)),t){case"add":c?d&&a(l.get("length")):(a(l.get(Nt)),Jt(e)&&a(l.get(Zn)));break;case"delete":c||(a(l.get(Nt)),Jt(e)&&a(l.get(Zn)));break;case"set":Jt(e)&&a(l.get(Nt));break}}yr()}function qt(e){const t=te(e);return t===e?t:(ye(t,"iterate",Cs),Le(e)?t:t.map(be))}function wn(e){return ye(e=te(e),"iterate",Cs),e}const Ic={__proto__:null,[Symbol.iterator](){return In(this,Symbol.iterator,be)},concat(...e){return qt(this).concat(...e.map(t=>z(t)?qt(t):t))},entries(){return In(this,"entries",e=>(e[1]=be(e[1]),e))},every(e,t){return dt(this,"every",e,t,void 0,arguments)},filter(e,t){return dt(this,"filter",e,t,s=>s.map(be),arguments)},find(e,t){return dt(this,"find",e,t,be,arguments)},findIndex(e,t){return dt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return dt(this,"findLast",e,t,be,arguments)},findLastIndex(e,t){return dt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return dt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Nn(this,"includes",e)},indexOf(...e){return Nn(this,"indexOf",e)},join(e){return qt(this).join(e)},lastIndexOf(...e){return Nn(this,"lastIndexOf",e)},map(e,t){return dt(this,"map",e,t,void 0,arguments)},pop(){return us(this,"pop")},push(...e){return us(this,"push",e)},reduce(e,...t){return Zr(this,"reduce",e,t)},reduceRight(e,...t){return Zr(this,"reduceRight",e,t)},shift(){return us(this,"shift")},some(e,t){return dt(this,"some",e,t,void 0,arguments)},splice(...e){return us(this,"splice",e)},toReversed(){return qt(this).toReversed()},toSorted(e){return qt(this).toSorted(e)},toSpliced(...e){return qt(this).toSpliced(...e)},unshift(...e){return us(this,"unshift",e)},values(){return In(this,"values",be)}};function In(e,t,s){const r=wn(e),n=r[t]();return r!==e&&!Le(e)&&(n._next=n.next,n.next=()=>{const i=n._next();return i.value&&(i.value=s(i.value)),i}),n}const Nc=Array.prototype;function dt(e,t,s,r,n,i){const l=wn(e),a=l!==e&&!Le(e),c=l[t];if(c!==Nc[t]){const f=c.apply(e,i);return a?be(f):f}let d=s;l!==e&&(a?d=function(f,h){return s.call(this,be(f),h,e)}:s.length>2&&(d=function(f,h){return s.call(this,f,h,e)}));const u=c.call(l,d,r);return a&&n?n(u):u}function Zr(e,t,s,r){const n=wn(e);let i=s;return n!==e&&(Le(e)?s.length>3&&(i=function(l,a,c){return s.call(this,l,a,c,e)}):i=function(l,a,c){return s.call(this,l,be(a),c,e)}),n[t](i,...r)}function Nn(e,t,s){const r=te(e);ye(r,"iterate",Cs);const n=r[t](...s);return(n===-1||n===!1)&&Er(s[0])?(s[0]=te(s[0]),r[t](...s)):n}function us(e,t,s=[]){xt(),br();const r=te(e)[t].apply(e,s);return yr(),bt(),r}const Fc=pr("__proto__,__v_isRef,__isVue"),Di=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(at));function Uc(e){at(e)||(e=String(e));const t=te(this);return ye(t,"has",e),t.hasOwnProperty(e)}class Ii{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,r){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,i=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return i;if(s==="__v_raw")return r===(n?i?Gc:Vi:i?Ui:Fi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const l=z(t);if(!n){let c;if(l&&(c=Ic[s]))return c;if(s==="hasOwnProperty")return Uc}const a=Reflect.get(t,s,ke(t)?t:r);return(at(s)?Di.has(s):Fc(s))||(n||ye(t,"get",s),i)?a:ke(a)?l&&gr(s)?a:a.value:me(a)?n?Bi(a):_n(a):a}}class Ni extends Ii{constructor(t=!1){super(!1,t)}set(t,s,r,n){let i=t[s];if(!this._isShallow){const c=Tt(i);if(!Le(r)&&!Tt(r)&&(i=te(i),r=te(r)),!z(t)&&ke(i)&&!ke(r))return c?!1:(i.value=r,!0)}const l=z(t)&&gr(s)?Number(s)<t.length:se(t,s),a=Reflect.set(t,s,r,ke(t)?t:n);return t===te(n)&&(l?Rt(r,i)&&mt(t,"set",s,r):mt(t,"add",s,r)),a}deleteProperty(t,s){const r=se(t,s);t[s];const n=Reflect.deleteProperty(t,s);return n&&r&&mt(t,"delete",s,void 0),n}has(t,s){const r=Reflect.has(t,s);return(!at(s)||!Di.has(s))&&ye(t,"has",s),r}ownKeys(t){return ye(t,"iterate",z(t)?"length":Nt),Reflect.ownKeys(t)}}class Vc extends Ii{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const Lc=new Ni,Bc=new Vc,zc=new Ni(!0);const er=e=>e,Fs=e=>Reflect.getPrototypeOf(e);function qc(e,t,s){return function(...r){const n=this.__v_raw,i=te(n),l=Jt(i),a=e==="entries"||e===Symbol.iterator&&l,c=e==="keys"&&l,d=n[e](...r),u=s?er:t?tn:be;return!t&&ye(i,"iterate",c?Zn:Nt),{next(){const{value:f,done:h}=d.next();return h?{value:f,done:h}:{value:a?[u(f[0]),u(f[1])]:u(f),done:h}},[Symbol.iterator](){return this}}}}function Us(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Hc(e,t){const s={get(n){const i=this.__v_raw,l=te(i),a=te(n);e||(Rt(n,a)&&ye(l,"get",n),ye(l,"get",a));const{has:c}=Fs(l),d=t?er:e?tn:be;if(c.call(l,n))return d(i.get(n));if(c.call(l,a))return d(i.get(a));i!==l&&i.get(n)},get size(){const n=this.__v_raw;return!e&&ye(te(n),"iterate",Nt),Reflect.get(n,"size",n)},has(n){const i=this.__v_raw,l=te(i),a=te(n);return e||(Rt(n,a)&&ye(l,"has",n),ye(l,"has",a)),n===a?i.has(n):i.has(n)||i.has(a)},forEach(n,i){const l=this,a=l.__v_raw,c=te(a),d=t?er:e?tn:be;return!e&&ye(c,"iterate",Nt),a.forEach((u,f)=>n.call(i,d(u),d(f),l))}};return Se(s,e?{add:Us("add"),set:Us("set"),delete:Us("delete"),clear:Us("clear")}:{add(n){!t&&!Le(n)&&!Tt(n)&&(n=te(n));const i=te(this);return Fs(i).has.call(i,n)||(i.add(n),mt(i,"add",n,n)),this},set(n,i){!t&&!Le(i)&&!Tt(i)&&(i=te(i));const l=te(this),{has:a,get:c}=Fs(l);let d=a.call(l,n);d||(n=te(n),d=a.call(l,n));const u=c.call(l,n);return l.set(n,i),d?Rt(i,u)&&mt(l,"set",n,i):mt(l,"add",n,i),this},delete(n){const i=te(this),{has:l,get:a}=Fs(i);let c=l.call(i,n);c||(n=te(n),c=l.call(i,n)),a&&a.call(i,n);const d=i.delete(n);return c&&mt(i,"delete",n,void 0),d},clear(){const n=te(this),i=n.size!==0,l=n.clear();return i&&mt(n,"clear",void 0,void 0),l}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=qc(n,e,t)}),s}function kr(e,t){const s=Hc(e,t);return(r,n,i)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?r:Reflect.get(se(s,n)&&n in r?s:r,n,i)}const Kc={get:kr(!1,!1)},Wc={get:kr(!1,!0)},Jc={get:kr(!0,!1)};const Fi=new WeakMap,Ui=new WeakMap,Vi=new WeakMap,Gc=new WeakMap;function Xc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Qc(e){return e.__v_skip||!Object.isExtensible(e)?0:Xc(_c(e))}function _n(e){return Tt(e)?e:Sr(e,!1,Lc,Kc,Fi)}function Li(e){return Sr(e,!1,zc,Wc,Ui)}function Bi(e){return Sr(e,!0,Bc,Jc,Vi)}function Sr(e,t,s,r,n){if(!me(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=Qc(e);if(i===0)return e;const l=n.get(e);if(l)return l;const a=new Proxy(e,i===2?r:s);return n.set(e,a),a}function Gt(e){return Tt(e)?Gt(e.__v_raw):!!(e&&e.__v_isReactive)}function Tt(e){return!!(e&&e.__v_isReadonly)}function Le(e){return!!(e&&e.__v_isShallow)}function Er(e){return e?!!e.__v_raw:!1}function te(e){const t=e&&e.__v_raw;return t?te(t):e}function Yc(e){return!se(e,"__v_skip")&&Object.isExtensible(e)&&ki(e,"__v_skip",!0),e}const be=e=>me(e)?_n(e):e,tn=e=>me(e)?Bi(e):e;function ke(e){return e?e.__v_isRef===!0:!1}function Zc(e){return zi(e,!1)}function eu(e){return zi(e,!0)}function zi(e,t){return ke(e)?e:new tu(e,t)}class tu{constructor(t,s){this.dep=new _r,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:te(t),this._value=s?t:be(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,r=this.__v_isShallow||Le(t)||Tt(t);t=r?t:te(t),Rt(t,s)&&(this._rawValue=t,this._value=r?t:be(t),this.dep.trigger())}}function Xt(e){return ke(e)?e.value:e}const su={get:(e,t,s)=>t==="__v_raw"?e:Xt(Reflect.get(e,t,s)),set:(e,t,s,r)=>{const n=e[t];return ke(n)&&!ke(s)?(n.value=s,!0):Reflect.set(e,t,s,r)}};function qi(e){return Gt(e)?e:new Proxy(e,su)}class nu{constructor(t,s,r){this.fn=t,this.setter=s,this._value=void 0,this.dep=new _r(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Es-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&fe!==this)return Ai(this,!0),!0}get value(){const t=this.dep.track();return Pi(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function ru(e,t,s=!1){let r,n;return W(e)?r=e:(r=e.get,n=e.set),new nu(r,n,s)}const Vs={},sn=new WeakMap;let jt;function ou(e,t=!1,s=jt){if(s){let r=sn.get(s);r||sn.set(s,r=[]),r.push(e)}}function iu(e,t,s=ce){const{immediate:r,deep:n,once:i,scheduler:l,augmentJob:a,call:c}=s,d=F=>n?F:Le(F)||n===!1||n===0?ht(F,1):ht(F);let u,f,h,v,g=!1,w=!1;if(ke(e)?(f=()=>e.value,g=Le(e)):Gt(e)?(f=()=>d(e),g=!0):z(e)?(w=!0,g=e.some(F=>Gt(F)||Le(F)),f=()=>e.map(F=>{if(ke(F))return F.value;if(Gt(F))return d(F);if(W(F))return c?c(F,2):F()})):W(e)?t?f=c?()=>c(e,2):e:f=()=>{if(h){xt();try{h()}finally{bt()}}const F=jt;jt=u;try{return c?c(e,3,[v]):e(v)}finally{jt=F}}:f=lt,t&&n){const F=f,J=n===!0?1/0:n;f=()=>ht(F(),J)}const C=Mc(),O=()=>{u.stop(),C&&C.active&&hr(C.effects,u)};if(i&&t){const F=t;t=(...J)=>{F(...J),O()}}let $=w?new Array(e.length).fill(Vs):Vs;const D=F=>{if(!(!(u.flags&1)||!u.dirty&&!F))if(t){const J=u.run();if(n||g||(w?J.some((ue,Y)=>Rt(ue,$[Y])):Rt(J,$))){h&&h();const ue=jt;jt=u;try{const Y=[J,$===Vs?void 0:w&&$[0]===Vs?[]:$,v];$=J,c?c(t,3,Y):t(...Y)}finally{jt=ue}}}else u.run()};return a&&a(D),u=new Ri(f),u.scheduler=l?()=>l(D,!1):D,v=F=>ou(F,!1,u),h=u.onStop=()=>{const F=sn.get(u);if(F){if(c)c(F,4);else for(const J of F)J();sn.delete(u)}},t?r?D(!0):$=u.run():l?l(D.bind(null,!0),!0):u.run(),O.pause=u.pause.bind(u),O.resume=u.resume.bind(u),O.stop=O,O}function ht(e,t=1/0,s){if(t<=0||!me(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,ke(e))ht(e.value,t,s);else if(z(e))for(let r=0;r<e.length;r++)ht(e[r],t,s);else if(is(e)||Jt(e))e.forEach(r=>{ht(r,t,s)});else if(_i(e)){for(const r in e)ht(e[r],t,s);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&ht(e[r],t,s)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ds(e,t,s,r){try{return r?e(...r):e()}catch(n){kn(n,t,s)}}function ct(e,t,s,r){if(W(e)){const n=Ds(e,t,s,r);return n&&yi(n)&&n.catch(i=>{kn(i,t,s)}),n}if(z(e)){const n=[];for(let i=0;i<e.length;i++)n.push(ct(e[i],t,s,r));return n}}function kn(e,t,s,r=!0){const n=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:l}=t&&t.appContext.config||ce;if(t){let a=t.parent;const c=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${s}`;for(;a;){const u=a.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,c,d)===!1)return}a=a.parent}if(i){xt(),Ds(i,null,10,[e,c,d]),bt();return}}lu(e,s,n,r,l)}function lu(e,t,s,r=!0,n=!1){if(n)throw e;console.error(e)}const Re=[];let ot=-1;const Qt=[];let St=null,Ht=0;const Hi=Promise.resolve();let nn=null;function Cr(e){const t=nn||Hi;return e?t.then(this?e.bind(this):e):t}function au(e){let t=ot+1,s=Re.length;for(;t<s;){const r=t+s>>>1,n=Re[r],i=Rs(n);i<e||i===e&&n.flags&2?t=r+1:s=r}return t}function Rr(e){if(!(e.flags&1)){const t=Rs(e),s=Re[Re.length-1];!s||!(e.flags&2)&&t>=Rs(s)?Re.push(e):Re.splice(au(t),0,e),e.flags|=1,Ki()}}function Ki(){nn||(nn=Hi.then(Ji))}function cu(e){z(e)?Qt.push(...e):St&&e.id===-1?St.splice(Ht+1,0,e):e.flags&1||(Qt.push(e),e.flags|=1),Ki()}function eo(e,t,s=ot+1){for(;s<Re.length;s++){const r=Re[s];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Re.splice(s,1),s--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Wi(e){if(Qt.length){const t=[...new Set(Qt)].sort((s,r)=>Rs(s)-Rs(r));if(Qt.length=0,St){St.push(...t);return}for(St=t,Ht=0;Ht<St.length;Ht++){const s=St[Ht];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}St=null,Ht=0}}const Rs=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ji(e){try{for(ot=0;ot<Re.length;ot++){const t=Re[ot];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ds(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;ot<Re.length;ot++){const t=Re[ot];t&&(t.flags&=-2)}ot=-1,Re.length=0,Wi(),nn=null,(Re.length||Qt.length)&&Ji()}}let Ne=null,Gi=null;function rn(e){const t=Ne;return Ne=e,Gi=e&&e.type.__scopeId||null,t}function ne(e,t=Ne,s){if(!t||e._n)return e;const r=(...n)=>{r._d&&uo(-1);const i=rn(t);let l;try{l=e(...n)}finally{rn(i),r._d&&uo(1)}return l};return r._n=!0,r._c=!0,r._d=!0,r}function X(e,t){if(Ne===null)return e;const s=Rn(Ne),r=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[i,l,a,c=ce]=t[n];i&&(W(i)&&(i={mounted:i,updated:i}),i.deep&&ht(l),r.push({dir:i,instance:s,value:l,oldValue:void 0,arg:a,modifiers:c}))}return e}function Pt(e,t,s,r){const n=e.dirs,i=t&&t.dirs;for(let l=0;l<n.length;l++){const a=n[l];i&&(a.oldValue=i[l].value);let c=a.dir[r];c&&(xt(),ct(c,s,8,[e.el,a,e,t]),bt())}}const uu=Symbol("_vte"),du=e=>e.__isTeleport;function Tr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Tr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Xi(e,t){return W(e)?Se({name:e.name},t,{setup:e}):e}function Qi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function on(e,t,s,r,n=!1){if(z(e)){e.forEach((g,w)=>on(g,t&&(z(t)?t[w]:t),s,r,n));return}if(bs(r)&&!n){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&on(e,t,s,r.component.subTree);return}const i=r.shapeFlag&4?Rn(r.component):r.el,l=n?null:i,{i:a,r:c}=e,d=t&&t.r,u=a.refs===ce?a.refs={}:a.refs,f=a.setupState,h=te(f),v=f===ce?()=>!1:g=>se(h,g);if(d!=null&&d!==c&&(ve(d)?(u[d]=null,v(d)&&(f[d]=null)):ke(d)&&(d.value=null)),W(c))Ds(c,a,12,[l,u]);else{const g=ve(c),w=ke(c);if(g||w){const C=()=>{if(e.f){const O=g?v(c)?f[c]:u[c]:c.value;n?z(O)&&hr(O,i):z(O)?O.includes(i)||O.push(i):g?(u[c]=[i],v(c)&&(f[c]=u[c])):(c.value=[i],e.k&&(u[e.k]=c.value))}else g?(u[c]=l,v(c)&&(f[c]=l)):w&&(c.value=l,e.k&&(u[e.k]=l))};l?(C.id=-1,Ie(C,s)):C()}}}yn().requestIdleCallback;yn().cancelIdleCallback;const bs=e=>!!e.type.__asyncLoader,Yi=e=>e.type.__isKeepAlive;function fu(e,t){Zi(e,"a",t)}function pu(e,t){Zi(e,"da",t)}function Zi(e,t,s=_e){const r=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Sn(t,r,s),s){let n=s.parent;for(;n&&n.parent;)Yi(n.parent.vnode)&&mu(r,t,s,n),n=n.parent}}function mu(e,t,s,r){const n=Sn(t,e,r,!0);el(()=>{hr(r[t],n)},s)}function Sn(e,t,s=_e,r=!1){if(s){const n=s[e]||(s[e]=[]),i=t.__weh||(t.__weh=(...l)=>{xt();const a=Is(s),c=ct(t,s,e,l);return a(),bt(),c});return r?n.unshift(i):n.push(i),i}}const yt=e=>(t,s=_e)=>{(!As||e==="sp")&&Sn(e,(...r)=>t(...r),s)},hu=yt("bm"),gu=yt("m"),vu=yt("bu"),xu=yt("u"),bu=yt("bum"),el=yt("um"),yu=yt("sp"),wu=yt("rtg"),_u=yt("rtc");function ku(e,t=_e){Sn("ec",e,t)}const Su="components";function Je(e,t){return Cu(Su,e,!0,t)||e}const Eu=Symbol.for("v-ndc");function Cu(e,t,s=!0,r=!1){const n=Ne||_e;if(n){const i=n.type;{const a=pd(i,!1);if(a&&(a===t||a===ze(t)||a===bn(ze(t))))return i}const l=to(n[e]||i[e],t)||to(n.appContext[e],t);return!l&&r?i:l}}function to(e,t){return e&&(e[t]||e[ze(t)]||e[bn(ze(t))])}function Te(e,t,s,r){let n;const i=s,l=z(e);if(l||ve(e)){const a=l&&Gt(e);let c=!1,d=!1;a&&(c=!Le(e),d=Tt(e),e=wn(e)),n=new Array(e.length);for(let u=0,f=e.length;u<f;u++)n[u]=t(c?d?tn(be(e[u])):be(e[u]):e[u],u,void 0,i)}else if(typeof e=="number"){n=new Array(e);for(let a=0;a<e;a++)n[a]=t(a+1,a,void 0,i)}else if(me(e))if(e[Symbol.iterator])n=Array.from(e,(a,c)=>t(a,c,void 0,i));else{const a=Object.keys(e);n=new Array(a.length);for(let c=0,d=a.length;c<d;c++){const u=a[c];n[c]=t(e[u],u,c,i)}}else n=[];return n}const tr=e=>e?yl(e)?Rn(e):tr(e.parent):null,ys=Se(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>tr(e.parent),$root:e=>tr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>sl(e),$forceUpdate:e=>e.f||(e.f=()=>{Rr(e.update)}),$nextTick:e=>e.n||(e.n=Cr.bind(e.proxy)),$watch:e=>Wu.bind(e)}),Fn=(e,t)=>e!==ce&&!e.__isScriptSetup&&se(e,t),Ru={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:r,data:n,props:i,accessCache:l,type:a,appContext:c}=e;let d;if(t[0]!=="$"){const v=l[t];if(v!==void 0)switch(v){case 1:return r[t];case 2:return n[t];case 4:return s[t];case 3:return i[t]}else{if(Fn(r,t))return l[t]=1,r[t];if(n!==ce&&se(n,t))return l[t]=2,n[t];if((d=e.propsOptions[0])&&se(d,t))return l[t]=3,i[t];if(s!==ce&&se(s,t))return l[t]=4,s[t];sr&&(l[t]=0)}}const u=ys[t];let f,h;if(u)return t==="$attrs"&&ye(e.attrs,"get",""),u(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(s!==ce&&se(s,t))return l[t]=4,s[t];if(h=c.config.globalProperties,se(h,t))return h[t]},set({_:e},t,s){const{data:r,setupState:n,ctx:i}=e;return Fn(n,t)?(n[t]=s,!0):r!==ce&&se(r,t)?(r[t]=s,!0):se(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:r,appContext:n,propsOptions:i}},l){let a;return!!s[l]||e!==ce&&se(e,l)||Fn(t,l)||(a=i[0])&&se(a,l)||se(r,l)||se(ys,l)||se(n.config.globalProperties,l)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:se(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function so(e){return z(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let sr=!0;function Tu(e){const t=sl(e),s=e.proxy,r=e.ctx;sr=!1,t.beforeCreate&&no(t.beforeCreate,e,"bc");const{data:n,computed:i,methods:l,watch:a,provide:c,inject:d,created:u,beforeMount:f,mounted:h,beforeUpdate:v,updated:g,activated:w,deactivated:C,beforeDestroy:O,beforeUnmount:$,destroyed:D,unmounted:F,render:J,renderTracked:ue,renderTriggered:Y,errorCaptured:Ee,serverPrefetch:He,expose:Qe,inheritAttrs:wt,components:$t,directives:Ye,filters:ls}=t;if(d&&Au(d,r,null),l)for(const ie in l){const Z=l[ie];W(Z)&&(r[ie]=Z.bind(s))}if(n){const ie=n.call(s,s);me(ie)&&(e.data=_n(ie))}if(sr=!0,i)for(const ie in i){const Z=i[ie],ut=W(Z)?Z.bind(s,s):W(Z.get)?Z.get.bind(s,s):lt,_t=!W(Z)&&W(Z.set)?Z.set.bind(s):lt,Ze=Ke({get:ut,set:_t});Object.defineProperty(r,ie,{enumerable:!0,configurable:!0,get:()=>Ze.value,set:Ae=>Ze.value=Ae})}if(a)for(const ie in a)tl(a[ie],r,s,ie);if(c){const ie=W(c)?c.call(s):c;Reflect.ownKeys(ie).forEach(Z=>{Ws(Z,ie[Z])})}u&&no(u,e,"c");function xe(ie,Z){z(Z)?Z.forEach(ut=>ie(ut.bind(s))):Z&&ie(Z.bind(s))}if(xe(hu,f),xe(gu,h),xe(vu,v),xe(xu,g),xe(fu,w),xe(pu,C),xe(ku,Ee),xe(_u,ue),xe(wu,Y),xe(bu,$),xe(el,F),xe(yu,He),z(Qe))if(Qe.length){const ie=e.exposed||(e.exposed={});Qe.forEach(Z=>{Object.defineProperty(ie,Z,{get:()=>s[Z],set:ut=>s[Z]=ut})})}else e.exposed||(e.exposed={});J&&e.render===lt&&(e.render=J),wt!=null&&(e.inheritAttrs=wt),$t&&(e.components=$t),Ye&&(e.directives=Ye),He&&Qi(e)}function Au(e,t,s=lt){z(e)&&(e=nr(e));for(const r in e){const n=e[r];let i;me(n)?"default"in n?i=vt(n.from||r,n.default,!0):i=vt(n.from||r):i=vt(n),ke(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:l=>i.value=l}):t[r]=i}}function no(e,t,s){ct(z(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,s)}function tl(e,t,s,r){let n=r.includes(".")?hl(s,r):()=>s[r];if(ve(e)){const i=t[e];W(i)&&Js(n,i)}else if(W(e))Js(n,e.bind(s));else if(me(e))if(z(e))e.forEach(i=>tl(i,t,s,r));else{const i=W(e.handler)?e.handler.bind(s):t[e.handler];W(i)&&Js(n,i,e)}}function sl(e){const t=e.type,{mixins:s,extends:r}=t,{mixins:n,optionsCache:i,config:{optionMergeStrategies:l}}=e.appContext,a=i.get(t);let c;return a?c=a:!n.length&&!s&&!r?c=t:(c={},n.length&&n.forEach(d=>ln(c,d,l,!0)),ln(c,t,l)),me(t)&&i.set(t,c),c}function ln(e,t,s,r=!1){const{mixins:n,extends:i}=t;i&&ln(e,i,s,!0),n&&n.forEach(l=>ln(e,l,s,!0));for(const l in t)if(!(r&&l==="expose")){const a=Ou[l]||s&&s[l];e[l]=a?a(e[l],t[l]):t[l]}return e}const Ou={data:ro,props:oo,emits:oo,methods:hs,computed:hs,beforeCreate:Ce,created:Ce,beforeMount:Ce,mounted:Ce,beforeUpdate:Ce,updated:Ce,beforeDestroy:Ce,beforeUnmount:Ce,destroyed:Ce,unmounted:Ce,activated:Ce,deactivated:Ce,errorCaptured:Ce,serverPrefetch:Ce,components:hs,directives:hs,watch:Pu,provide:ro,inject:$u};function ro(e,t){return t?e?function(){return Se(W(e)?e.call(this,this):e,W(t)?t.call(this,this):t)}:t:e}function $u(e,t){return hs(nr(e),nr(t))}function nr(e){if(z(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Ce(e,t){return e?[...new Set([].concat(e,t))]:t}function hs(e,t){return e?Se(Object.create(null),e,t):t}function oo(e,t){return e?z(e)&&z(t)?[...new Set([...e,...t])]:Se(Object.create(null),so(e),so(t??{})):t}function Pu(e,t){if(!e)return t;if(!t)return e;const s=Se(Object.create(null),e);for(const r in t)s[r]=Ce(e[r],t[r]);return s}function nl(){return{app:null,config:{isNativeTag:yc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Mu=0;function ju(e,t){return function(r,n=null){W(r)||(r=Se({},r)),n!=null&&!me(n)&&(n=null);const i=nl(),l=new WeakSet,a=[];let c=!1;const d=i.app={_uid:Mu++,_component:r,_props:n,_container:null,_context:i,_instance:null,version:hd,get config(){return i.config},set config(u){},use(u,...f){return l.has(u)||(u&&W(u.install)?(l.add(u),u.install(d,...f)):W(u)&&(l.add(u),u(d,...f))),d},mixin(u){return i.mixins.includes(u)||i.mixins.push(u),d},component(u,f){return f?(i.components[u]=f,d):i.components[u]},directive(u,f){return f?(i.directives[u]=f,d):i.directives[u]},mount(u,f,h){if(!c){const v=d._ceVNode||K(r,n);return v.appContext=i,h===!0?h="svg":h===!1&&(h=void 0),e(v,u,h),c=!0,d._container=u,u.__vue_app__=d,Rn(v.component)}},onUnmount(u){a.push(u)},unmount(){c&&(ct(a,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(u,f){return i.provides[u]=f,d},runWithContext(u){const f=Yt;Yt=d;try{return u()}finally{Yt=f}}};return d}}let Yt=null;function Ws(e,t){if(_e){let s=_e.provides;const r=_e.parent&&_e.parent.provides;r===s&&(s=_e.provides=Object.create(r)),s[e]=t}}function vt(e,t,s=!1){const r=_e||Ne;if(r||Yt){let n=Yt?Yt._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&W(t)?t.call(r&&r.proxy):t}}const rl={},ol=()=>Object.create(rl),il=e=>Object.getPrototypeOf(e)===rl;function Du(e,t,s,r=!1){const n={},i=ol();e.propsDefaults=Object.create(null),ll(e,t,n,i);for(const l in e.propsOptions[0])l in n||(n[l]=void 0);s?e.props=r?n:Li(n):e.type.props?e.props=n:e.props=i,e.attrs=i}function Iu(e,t,s,r){const{props:n,attrs:i,vnode:{patchFlag:l}}=e,a=te(n),[c]=e.propsOptions;let d=!1;if((r||l>0)&&!(l&16)){if(l&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let h=u[f];if(En(e.emitsOptions,h))continue;const v=t[h];if(c)if(se(i,h))v!==i[h]&&(i[h]=v,d=!0);else{const g=ze(h);n[g]=rr(c,a,g,v,e,!1)}else v!==i[h]&&(i[h]=v,d=!0)}}}else{ll(e,t,n,i)&&(d=!0);let u;for(const f in a)(!t||!se(t,f)&&((u=Vt(f))===f||!se(t,u)))&&(c?s&&(s[f]!==void 0||s[u]!==void 0)&&(n[f]=rr(c,a,f,void 0,e,!0)):delete n[f]);if(i!==a)for(const f in i)(!t||!se(t,f))&&(delete i[f],d=!0)}d&&mt(e.attrs,"set","")}function ll(e,t,s,r){const[n,i]=e.propsOptions;let l=!1,a;if(t)for(let c in t){if(gs(c))continue;const d=t[c];let u;n&&se(n,u=ze(c))?!i||!i.includes(u)?s[u]=d:(a||(a={}))[u]=d:En(e.emitsOptions,c)||(!(c in r)||d!==r[c])&&(r[c]=d,l=!0)}if(i){const c=te(s),d=a||ce;for(let u=0;u<i.length;u++){const f=i[u];s[f]=rr(n,c,f,d[f],e,!se(d,f))}}return l}function rr(e,t,s,r,n,i){const l=e[s];if(l!=null){const a=se(l,"default");if(a&&r===void 0){const c=l.default;if(l.type!==Function&&!l.skipFactory&&W(c)){const{propsDefaults:d}=n;if(s in d)r=d[s];else{const u=Is(n);r=d[s]=c.call(null,t),u()}}else r=c;n.ce&&n.ce._setProp(s,r)}l[0]&&(i&&!a?r=!1:l[1]&&(r===""||r===Vt(s))&&(r=!0))}return r}const Nu=new WeakMap;function al(e,t,s=!1){const r=s?Nu:t.propsCache,n=r.get(e);if(n)return n;const i=e.props,l={},a=[];let c=!1;if(!W(e)){const u=f=>{c=!0;const[h,v]=al(f,t,!0);Se(l,h),v&&a.push(...v)};!s&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!i&&!c)return me(e)&&r.set(e,Wt),Wt;if(z(i))for(let u=0;u<i.length;u++){const f=ze(i[u]);io(f)&&(l[f]=ce)}else if(i)for(const u in i){const f=ze(u);if(io(f)){const h=i[u],v=l[f]=z(h)||W(h)?{type:h}:Se({},h),g=v.type;let w=!1,C=!0;if(z(g))for(let O=0;O<g.length;++O){const $=g[O],D=W($)&&$.name;if(D==="Boolean"){w=!0;break}else D==="String"&&(C=!1)}else w=W(g)&&g.name==="Boolean";v[0]=w,v[1]=C,(w||se(v,"default"))&&a.push(f)}}const d=[l,a];return me(e)&&r.set(e,d),d}function io(e){return e[0]!=="$"&&!gs(e)}const Ar=e=>e[0]==="_"||e==="$stable",Or=e=>z(e)?e.map(it):[it(e)],Fu=(e,t,s)=>{if(t._n)return t;const r=ne((...n)=>Or(t(...n)),s);return r._c=!1,r},cl=(e,t,s)=>{const r=e._ctx;for(const n in e){if(Ar(n))continue;const i=e[n];if(W(i))t[n]=Fu(n,i,r);else if(i!=null){const l=Or(i);t[n]=()=>l}}},ul=(e,t)=>{const s=Or(t);e.slots.default=()=>s},dl=(e,t,s)=>{for(const r in t)(s||!Ar(r))&&(e[r]=t[r])},Uu=(e,t,s)=>{const r=e.slots=ol();if(e.vnode.shapeFlag&32){const n=t._;n?(dl(r,t,s),s&&ki(r,"_",n,!0)):cl(t,r)}else t&&ul(e,t)},Vu=(e,t,s)=>{const{vnode:r,slots:n}=e;let i=!0,l=ce;if(r.shapeFlag&32){const a=t._;a?s&&a===1?i=!1:dl(n,t,s):(i=!t.$stable,cl(t,n)),l=t}else t&&(ul(e,t),l={default:1});if(i)for(const a in n)!Ar(a)&&l[a]==null&&delete n[a]},Ie=ed;function Lu(e){return Bu(e)}function Bu(e,t){const s=yn();s.__VUE__=!0;const{insert:r,remove:n,patchProp:i,createElement:l,createText:a,createComment:c,setText:d,setElementText:u,parentNode:f,nextSibling:h,setScopeId:v=lt,insertStaticContent:g}=e,w=(p,m,x,_=null,E=null,S=null,j=void 0,M=null,P=!!m.dynamicChildren)=>{if(p===m)return;p&&!ds(p,m)&&(_=k(p),Ae(p,E,S,!0),p=null),m.patchFlag===-2&&(P=!1,m.dynamicChildren=null);const{type:A,ref:B,shapeFlag:N}=m;switch(A){case Cn:C(p,m,x,_);break;case At:O(p,m,x,_);break;case Gs:p==null&&$(m,x,_,j);break;case ge:$t(p,m,x,_,E,S,j,M,P);break;default:N&1?J(p,m,x,_,E,S,j,M,P):N&6?Ye(p,m,x,_,E,S,j,M,P):(N&64||N&128)&&A.process(p,m,x,_,E,S,j,M,P,V)}B!=null&&E&&on(B,p&&p.ref,S,m||p,!m)},C=(p,m,x,_)=>{if(p==null)r(m.el=a(m.children),x,_);else{const E=m.el=p.el;m.children!==p.children&&d(E,m.children)}},O=(p,m,x,_)=>{p==null?r(m.el=c(m.children||""),x,_):m.el=p.el},$=(p,m,x,_)=>{[p.el,p.anchor]=g(p.children,m,x,_,p.el,p.anchor)},D=({el:p,anchor:m},x,_)=>{let E;for(;p&&p!==m;)E=h(p),r(p,x,_),p=E;r(m,x,_)},F=({el:p,anchor:m})=>{let x;for(;p&&p!==m;)x=h(p),n(p),p=x;n(m)},J=(p,m,x,_,E,S,j,M,P)=>{m.type==="svg"?j="svg":m.type==="math"&&(j="mathml"),p==null?ue(m,x,_,E,S,j,M,P):He(p,m,E,S,j,M,P)},ue=(p,m,x,_,E,S,j,M)=>{let P,A;const{props:B,shapeFlag:N,transition:L,dirs:H}=p;if(P=p.el=l(p.type,S,B&&B.is,B),N&8?u(P,p.children):N&16&&Ee(p.children,P,null,_,E,Un(p,S),j,M),H&&Pt(p,null,_,"created"),Y(P,p,p.scopeId,j,_),B){for(const de in B)de!=="value"&&!gs(de)&&i(P,de,null,B[de],S,_);"value"in B&&i(P,"value",null,B.value,S),(A=B.onVnodeBeforeMount)&&rt(A,_,p)}H&&Pt(p,null,_,"beforeMount");const Q=zu(E,L);Q&&L.beforeEnter(P),r(P,m,x),((A=B&&B.onVnodeMounted)||Q||H)&&Ie(()=>{A&&rt(A,_,p),Q&&L.enter(P),H&&Pt(p,null,_,"mounted")},E)},Y=(p,m,x,_,E)=>{if(x&&v(p,x),_)for(let S=0;S<_.length;S++)v(p,_[S]);if(E){let S=E.subTree;if(m===S||vl(S.type)&&(S.ssContent===m||S.ssFallback===m)){const j=E.vnode;Y(p,j,j.scopeId,j.slotScopeIds,E.parent)}}},Ee=(p,m,x,_,E,S,j,M,P=0)=>{for(let A=P;A<p.length;A++){const B=p[A]=M?Et(p[A]):it(p[A]);w(null,B,m,x,_,E,S,j,M)}},He=(p,m,x,_,E,S,j)=>{const M=m.el=p.el;let{patchFlag:P,dynamicChildren:A,dirs:B}=m;P|=p.patchFlag&16;const N=p.props||ce,L=m.props||ce;let H;if(x&&Mt(x,!1),(H=L.onVnodeBeforeUpdate)&&rt(H,x,m,p),B&&Pt(m,p,x,"beforeUpdate"),x&&Mt(x,!0),(N.innerHTML&&L.innerHTML==null||N.textContent&&L.textContent==null)&&u(M,""),A?Qe(p.dynamicChildren,A,M,x,_,Un(m,E),S):j||Z(p,m,M,null,x,_,Un(m,E),S,!1),P>0){if(P&16)wt(M,N,L,x,E);else if(P&2&&N.class!==L.class&&i(M,"class",null,L.class,E),P&4&&i(M,"style",N.style,L.style,E),P&8){const Q=m.dynamicProps;for(let de=0;de<Q.length;de++){const re=Q[de],je=N[re],Oe=L[re];(Oe!==je||re==="value")&&i(M,re,je,Oe,E,x)}}P&1&&p.children!==m.children&&u(M,m.children)}else!j&&A==null&&wt(M,N,L,x,E);((H=L.onVnodeUpdated)||B)&&Ie(()=>{H&&rt(H,x,m,p),B&&Pt(m,p,x,"updated")},_)},Qe=(p,m,x,_,E,S,j)=>{for(let M=0;M<m.length;M++){const P=p[M],A=m[M],B=P.el&&(P.type===ge||!ds(P,A)||P.shapeFlag&198)?f(P.el):x;w(P,A,B,null,_,E,S,j,!0)}},wt=(p,m,x,_,E)=>{if(m!==x){if(m!==ce)for(const S in m)!gs(S)&&!(S in x)&&i(p,S,m[S],null,E,_);for(const S in x){if(gs(S))continue;const j=x[S],M=m[S];j!==M&&S!=="value"&&i(p,S,M,j,E,_)}"value"in x&&i(p,"value",m.value,x.value,E)}},$t=(p,m,x,_,E,S,j,M,P)=>{const A=m.el=p?p.el:a(""),B=m.anchor=p?p.anchor:a("");let{patchFlag:N,dynamicChildren:L,slotScopeIds:H}=m;H&&(M=M?M.concat(H):H),p==null?(r(A,x,_),r(B,x,_),Ee(m.children||[],x,B,E,S,j,M,P)):N>0&&N&64&&L&&p.dynamicChildren?(Qe(p.dynamicChildren,L,x,E,S,j,M),(m.key!=null||E&&m===E.subTree)&&fl(p,m,!0)):Z(p,m,x,B,E,S,j,M,P)},Ye=(p,m,x,_,E,S,j,M,P)=>{m.slotScopeIds=M,p==null?m.shapeFlag&512?E.ctx.activate(m,x,_,j,P):ls(m,x,_,E,S,j,P):Lt(p,m,P)},ls=(p,m,x,_,E,S,j)=>{const M=p.component=ad(p,_,E);if(Yi(p)&&(M.ctx.renderer=V),cd(M,!1,j),M.asyncDep){if(E&&E.registerDep(M,xe,j),!p.el){const P=M.subTree=K(At);O(null,P,m,x)}}else xe(M,p,m,x,E,S,j)},Lt=(p,m,x)=>{const _=m.component=p.component;if(Yu(p,m,x))if(_.asyncDep&&!_.asyncResolved){ie(_,m,x);return}else _.next=m,_.update();else m.el=p.el,_.vnode=m},xe=(p,m,x,_,E,S,j)=>{const M=()=>{if(p.isMounted){let{next:N,bu:L,u:H,parent:Q,vnode:de}=p;{const tt=pl(p);if(tt){N&&(N.el=de.el,ie(p,N,j)),tt.asyncDep.then(()=>{p.isUnmounted||M()});return}}let re=N,je;Mt(p,!1),N?(N.el=de.el,ie(p,N,j)):N=de,L&&Ks(L),(je=N.props&&N.props.onVnodeBeforeUpdate)&&rt(je,Q,N,de),Mt(p,!0);const Oe=ao(p),et=p.subTree;p.subTree=Oe,w(et,Oe,f(et.el),k(et),p,E,S),N.el=Oe.el,re===null&&Zu(p,Oe.el),H&&Ie(H,E),(je=N.props&&N.props.onVnodeUpdated)&&Ie(()=>rt(je,Q,N,de),E)}else{let N;const{el:L,props:H}=m,{bm:Q,m:de,parent:re,root:je,type:Oe}=p,et=bs(m);Mt(p,!1),Q&&Ks(Q),!et&&(N=H&&H.onVnodeBeforeMount)&&rt(N,re,m),Mt(p,!0);{je.ce&&je.ce._injectChildStyle(Oe);const tt=p.subTree=ao(p);w(null,tt,x,_,p,E,S),m.el=tt.el}if(de&&Ie(de,E),!et&&(N=H&&H.onVnodeMounted)){const tt=m;Ie(()=>rt(N,re,tt),E)}(m.shapeFlag&256||re&&bs(re.vnode)&&re.vnode.shapeFlag&256)&&p.a&&Ie(p.a,E),p.isMounted=!0,m=x=_=null}};p.scope.on();const P=p.effect=new Ri(M);p.scope.off();const A=p.update=P.run.bind(P),B=p.job=P.runIfDirty.bind(P);B.i=p,B.id=p.uid,P.scheduler=()=>Rr(B),Mt(p,!0),A()},ie=(p,m,x)=>{m.component=p;const _=p.vnode.props;p.vnode=m,p.next=null,Iu(p,m.props,_,x),Vu(p,m.children,x),xt(),eo(p),bt()},Z=(p,m,x,_,E,S,j,M,P=!1)=>{const A=p&&p.children,B=p?p.shapeFlag:0,N=m.children,{patchFlag:L,shapeFlag:H}=m;if(L>0){if(L&128){_t(A,N,x,_,E,S,j,M,P);return}else if(L&256){ut(A,N,x,_,E,S,j,M,P);return}}H&8?(B&16&&Ve(A,E,S),N!==A&&u(x,N)):B&16?H&16?_t(A,N,x,_,E,S,j,M,P):Ve(A,E,S,!0):(B&8&&u(x,""),H&16&&Ee(N,x,_,E,S,j,M,P))},ut=(p,m,x,_,E,S,j,M,P)=>{p=p||Wt,m=m||Wt;const A=p.length,B=m.length,N=Math.min(A,B);let L;for(L=0;L<N;L++){const H=m[L]=P?Et(m[L]):it(m[L]);w(p[L],H,x,null,E,S,j,M,P)}A>B?Ve(p,E,S,!0,!1,N):Ee(m,x,_,E,S,j,M,P,N)},_t=(p,m,x,_,E,S,j,M,P)=>{let A=0;const B=m.length;let N=p.length-1,L=B-1;for(;A<=N&&A<=L;){const H=p[A],Q=m[A]=P?Et(m[A]):it(m[A]);if(ds(H,Q))w(H,Q,x,null,E,S,j,M,P);else break;A++}for(;A<=N&&A<=L;){const H=p[N],Q=m[L]=P?Et(m[L]):it(m[L]);if(ds(H,Q))w(H,Q,x,null,E,S,j,M,P);else break;N--,L--}if(A>N){if(A<=L){const H=L+1,Q=H<B?m[H].el:_;for(;A<=L;)w(null,m[A]=P?Et(m[A]):it(m[A]),x,Q,E,S,j,M,P),A++}}else if(A>L)for(;A<=N;)Ae(p[A],E,S,!0),A++;else{const H=A,Q=A,de=new Map;for(A=Q;A<=L;A++){const De=m[A]=P?Et(m[A]):it(m[A]);De.key!=null&&de.set(De.key,A)}let re,je=0;const Oe=L-Q+1;let et=!1,tt=0;const as=new Array(Oe);for(A=0;A<Oe;A++)as[A]=0;for(A=H;A<=N;A++){const De=p[A];if(je>=Oe){Ae(De,E,S,!0);continue}let st;if(De.key!=null)st=de.get(De.key);else for(re=Q;re<=L;re++)if(as[re-Q]===0&&ds(De,m[re])){st=re;break}st===void 0?Ae(De,E,S,!0):(as[st-Q]=A+1,st>=tt?tt=st:et=!0,w(De,m[st],x,null,E,S,j,M,P),je++)}const Dr=et?qu(as):Wt;for(re=Dr.length-1,A=Oe-1;A>=0;A--){const De=Q+A,st=m[De],Ir=De+1<B?m[De+1].el:_;as[A]===0?w(null,st,x,Ir,E,S,j,M,P):et&&(re<0||A!==Dr[re]?Ze(st,x,Ir,2):re--)}}},Ze=(p,m,x,_,E=null)=>{const{el:S,type:j,transition:M,children:P,shapeFlag:A}=p;if(A&6){Ze(p.component.subTree,m,x,_);return}if(A&128){p.suspense.move(m,x,_);return}if(A&64){j.move(p,m,x,V);return}if(j===ge){r(S,m,x);for(let N=0;N<P.length;N++)Ze(P[N],m,x,_);r(p.anchor,m,x);return}if(j===Gs){D(p,m,x);return}if(_!==2&&A&1&&M)if(_===0)M.beforeEnter(S),r(S,m,x),Ie(()=>M.enter(S),E);else{const{leave:N,delayLeave:L,afterLeave:H}=M,Q=()=>{p.ctx.isUnmounted?n(S):r(S,m,x)},de=()=>{N(S,()=>{Q(),H&&H()})};L?L(S,Q,de):de()}else r(S,m,x)},Ae=(p,m,x,_=!1,E=!1)=>{const{type:S,props:j,ref:M,children:P,dynamicChildren:A,shapeFlag:B,patchFlag:N,dirs:L,cacheIndex:H}=p;if(N===-2&&(E=!1),M!=null&&(xt(),on(M,null,x,p,!0),bt()),H!=null&&(m.renderCache[H]=void 0),B&256){m.ctx.deactivate(p);return}const Q=B&1&&L,de=!bs(p);let re;if(de&&(re=j&&j.onVnodeBeforeUnmount)&&rt(re,m,p),B&6)Ns(p.component,x,_);else{if(B&128){p.suspense.unmount(x,_);return}Q&&Pt(p,null,m,"beforeUnmount"),B&64?p.type.remove(p,m,x,V,_):A&&!A.hasOnce&&(S!==ge||N>0&&N&64)?Ve(A,m,x,!1,!0):(S===ge&&N&384||!E&&B&16)&&Ve(P,m,x),_&&Bt(p)}(de&&(re=j&&j.onVnodeUnmounted)||Q)&&Ie(()=>{re&&rt(re,m,p),Q&&Pt(p,null,m,"unmounted")},x)},Bt=p=>{const{type:m,el:x,anchor:_,transition:E}=p;if(m===ge){zt(x,_);return}if(m===Gs){F(p);return}const S=()=>{n(x),E&&!E.persisted&&E.afterLeave&&E.afterLeave()};if(p.shapeFlag&1&&E&&!E.persisted){const{leave:j,delayLeave:M}=E,P=()=>j(x,S);M?M(p.el,S,P):P()}else S()},zt=(p,m)=>{let x;for(;p!==m;)x=h(p),n(p),p=x;n(m)},Ns=(p,m,x)=>{const{bum:_,scope:E,job:S,subTree:j,um:M,m:P,a:A,parent:B,slots:{__:N}}=p;lo(P),lo(A),_&&Ks(_),B&&z(N)&&N.forEach(L=>{B.renderCache[L]=void 0}),E.stop(),S&&(S.flags|=8,Ae(j,p,m,x)),M&&Ie(M,m),Ie(()=>{p.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},Ve=(p,m,x,_=!1,E=!1,S=0)=>{for(let j=S;j<p.length;j++)Ae(p[j],m,x,_,E)},k=p=>{if(p.shapeFlag&6)return k(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const m=h(p.anchor||p.el),x=m&&m[uu];return x?h(x):m};let U=!1;const I=(p,m,x)=>{p==null?m._vnode&&Ae(m._vnode,null,null,!0):w(m._vnode||null,p,m,null,null,null,x),m._vnode=p,U||(U=!0,eo(),Wi(),U=!1)},V={p:w,um:Ae,m:Ze,r:Bt,mt:ls,mc:Ee,pc:Z,pbc:Qe,n:k,o:e};return{render:I,hydrate:void 0,createApp:ju(I)}}function Un({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function Mt({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function zu(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function fl(e,t,s=!1){const r=e.children,n=t.children;if(z(r)&&z(n))for(let i=0;i<r.length;i++){const l=r[i];let a=n[i];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=n[i]=Et(n[i]),a.el=l.el),!s&&a.patchFlag!==-2&&fl(l,a)),a.type===Cn&&(a.el=l.el),a.type===At&&!a.el&&(a.el=l.el)}}function qu(e){const t=e.slice(),s=[0];let r,n,i,l,a;const c=e.length;for(r=0;r<c;r++){const d=e[r];if(d!==0){if(n=s[s.length-1],e[n]<d){t[r]=n,s.push(r);continue}for(i=0,l=s.length-1;i<l;)a=i+l>>1,e[s[a]]<d?i=a+1:l=a;d<e[s[i]]&&(i>0&&(t[r]=s[i-1]),s[i]=r)}}for(i=s.length,l=s[i-1];i-- >0;)s[i]=l,l=t[l];return s}function pl(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:pl(t)}function lo(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Hu=Symbol.for("v-scx"),Ku=()=>vt(Hu);function Js(e,t,s){return ml(e,t,s)}function ml(e,t,s=ce){const{immediate:r,deep:n,flush:i,once:l}=s,a=Se({},s),c=t&&r||!t&&i!=="post";let d;if(As){if(i==="sync"){const v=Ku();d=v.__watcherHandles||(v.__watcherHandles=[])}else if(!c){const v=()=>{};return v.stop=lt,v.resume=lt,v.pause=lt,v}}const u=_e;a.call=(v,g,w)=>ct(v,u,g,w);let f=!1;i==="post"?a.scheduler=v=>{Ie(v,u&&u.suspense)}:i!=="sync"&&(f=!0,a.scheduler=(v,g)=>{g?v():Rr(v)}),a.augmentJob=v=>{t&&(v.flags|=4),f&&(v.flags|=2,u&&(v.id=u.uid,v.i=u))};const h=iu(e,t,a);return As&&(d?d.push(h):c&&h()),h}function Wu(e,t,s){const r=this.proxy,n=ve(e)?e.includes(".")?hl(r,e):()=>r[e]:e.bind(r,r);let i;W(t)?i=t:(i=t.handler,s=t);const l=Is(this),a=ml(n,i.bind(r),s);return l(),a}function hl(e,t){const s=t.split(".");return()=>{let r=e;for(let n=0;n<s.length&&r;n++)r=r[s[n]];return r}}const Ju=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ze(t)}Modifiers`]||e[`${Vt(t)}Modifiers`];function Gu(e,t,...s){if(e.isUnmounted)return;const r=e.vnode.props||ce;let n=s;const i=t.startsWith("update:"),l=i&&Ju(r,t.slice(7));l&&(l.trim&&(n=s.map(u=>ve(u)?u.trim():u)),l.number&&(n=s.map(en)));let a,c=r[a=Mn(t)]||r[a=Mn(ze(t))];!c&&i&&(c=r[a=Mn(Vt(t))]),c&&ct(c,e,6,n);const d=r[a+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,ct(d,e,6,n)}}function gl(e,t,s=!1){const r=t.emitsCache,n=r.get(e);if(n!==void 0)return n;const i=e.emits;let l={},a=!1;if(!W(e)){const c=d=>{const u=gl(d,t,!0);u&&(a=!0,Se(l,u))};!s&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!a?(me(e)&&r.set(e,null),null):(z(i)?i.forEach(c=>l[c]=null):Se(l,i),me(e)&&r.set(e,l),l)}function En(e,t){return!e||!vn(t)?!1:(t=t.slice(2).replace(/Once$/,""),se(e,t[0].toLowerCase()+t.slice(1))||se(e,Vt(t))||se(e,t))}function ao(e){const{type:t,vnode:s,proxy:r,withProxy:n,propsOptions:[i],slots:l,attrs:a,emit:c,render:d,renderCache:u,props:f,data:h,setupState:v,ctx:g,inheritAttrs:w}=e,C=rn(e);let O,$;try{if(s.shapeFlag&4){const F=n||r,J=F;O=it(d.call(J,F,u,f,v,h,g)),$=a}else{const F=t;O=it(F.length>1?F(f,{attrs:a,slots:l,emit:c}):F(f,null)),$=t.props?a:Xu(a)}}catch(F){ws.length=0,kn(F,e,1),O=K(At)}let D=O;if($&&w!==!1){const F=Object.keys($),{shapeFlag:J}=D;F.length&&J&7&&(i&&F.some(mr)&&($=Qu($,i)),D=es(D,$,!1,!0))}return s.dirs&&(D=es(D,null,!1,!0),D.dirs=D.dirs?D.dirs.concat(s.dirs):s.dirs),s.transition&&Tr(D,s.transition),O=D,rn(C),O}const Xu=e=>{let t;for(const s in e)(s==="class"||s==="style"||vn(s))&&((t||(t={}))[s]=e[s]);return t},Qu=(e,t)=>{const s={};for(const r in e)(!mr(r)||!(r.slice(9)in t))&&(s[r]=e[r]);return s};function Yu(e,t,s){const{props:r,children:n,component:i}=e,{props:l,children:a,patchFlag:c}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&c>=0){if(c&1024)return!0;if(c&16)return r?co(r,l,d):!!l;if(c&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const h=u[f];if(l[h]!==r[h]&&!En(d,h))return!0}}}else return(n||a)&&(!a||!a.$stable)?!0:r===l?!1:r?l?co(r,l,d):!0:!!l;return!1}function co(e,t,s){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let n=0;n<r.length;n++){const i=r[n];if(t[i]!==e[i]&&!En(s,i))return!0}return!1}function Zu({vnode:e,parent:t},s){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=s,t=t.parent;else break}}const vl=e=>e.__isSuspense;function ed(e,t){t&&t.pendingBranch?z(e)?t.effects.push(...e):t.effects.push(e):cu(e)}const ge=Symbol.for("v-fgt"),Cn=Symbol.for("v-txt"),At=Symbol.for("v-cmt"),Gs=Symbol.for("v-stc"),ws=[];let Fe=null;function R(e=!1){ws.push(Fe=e?null:[])}function td(){ws.pop(),Fe=ws[ws.length-1]||null}let Ts=1;function uo(e,t=!1){Ts+=e,e<0&&Fe&&t&&(Fe.hasOnce=!0)}function xl(e){return e.dynamicChildren=Ts>0?Fe||Wt:null,td(),Ts>0&&Fe&&Fe.push(e),e}function T(e,t,s,r,n,i){return xl(o(e,t,s,r,n,i,!0))}function sd(e,t,s,r,n){return xl(K(e,t,s,r,n,!0))}function an(e){return e?e.__v_isVNode===!0:!1}function ds(e,t){return e.type===t.type&&e.key===t.key}const bl=({key:e})=>e??null,Xs=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?ve(e)||ke(e)||W(e)?{i:Ne,r:e,k:t,f:!!s}:e:null);function o(e,t=null,s=null,r=0,n=null,i=e===ge?0:1,l=!1,a=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&bl(t),ref:t&&Xs(t),scopeId:Gi,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:Ne};return a?($r(c,s),i&128&&e.normalize(c)):s&&(c.shapeFlag|=ve(s)?8:16),Ts>0&&!l&&Fe&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&Fe.push(c),c}const K=nd;function nd(e,t=null,s=null,r=0,n=null,i=!1){if((!e||e===Eu)&&(e=At),an(e)){const a=es(e,t,!0);return s&&$r(a,s),Ts>0&&!i&&Fe&&(a.shapeFlag&6?Fe[Fe.indexOf(e)]=a:Fe.push(a)),a.patchFlag=-2,a}if(md(e)&&(e=e.__vccOpts),t){t=rd(t);let{class:a,style:c}=t;a&&!ve(a)&&(t.class=oe(a)),me(c)&&(Er(c)&&!z(c)&&(c=Se({},c)),t.style=vr(c))}const l=ve(e)?1:vl(e)?128:du(e)?64:me(e)?4:W(e)?2:0;return o(e,t,s,r,n,l,i,!0)}function rd(e){return e?Er(e)||il(e)?Se({},e):e:null}function es(e,t,s=!1,r=!1){const{props:n,ref:i,patchFlag:l,children:a,transition:c}=e,d=t?od(n||{},t):n,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&bl(d),ref:t&&t.ref?s&&i?z(i)?i.concat(Xs(t)):[i,Xs(t)]:Xs(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ge?l===-1?16:l|16:l,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&es(e.ssContent),ssFallback:e.ssFallback&&es(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&Tr(u,c.clone(u)),u}function q(e=" ",t=0){return K(Cn,null,e,t)}function Ue(e,t){const s=K(Gs,null,e);return s.staticCount=t,s}function le(e="",t=!1){return t?(R(),sd(At,null,e)):K(At,null,e)}function it(e){return e==null||typeof e=="boolean"?K(At):z(e)?K(ge,null,e.slice()):an(e)?Et(e):K(Cn,null,String(e))}function Et(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:es(e)}function $r(e,t){let s=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(z(t))s=16;else if(typeof t=="object")if(r&65){const n=t.default;n&&(n._c&&(n._d=!1),$r(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!il(t)?t._ctx=Ne:n===3&&Ne&&(Ne.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else W(t)?(t={default:t,_ctx:Ne},s=32):(t=String(t),r&64?(s=16,t=[q(t)]):s=8);e.children=t,e.shapeFlag|=s}function od(...e){const t={};for(let s=0;s<e.length;s++){const r=e[s];for(const n in r)if(n==="class")t.class!==r.class&&(t.class=oe([t.class,r.class]));else if(n==="style")t.style=vr([t.style,r.style]);else if(vn(n)){const i=t[n],l=r[n];l&&i!==l&&!(z(i)&&i.includes(l))&&(t[n]=i?[].concat(i,l):l)}else n!==""&&(t[n]=r[n])}return t}function rt(e,t,s,r=null){ct(e,t,7,[s,r])}const id=nl();let ld=0;function ad(e,t,s){const r=e.type,n=(t?t.appContext:e.appContext)||id,i={uid:ld++,vnode:e,type:r,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Pc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:al(r,n),emitsOptions:gl(r,n),emit:null,emitted:null,propsDefaults:ce,inheritAttrs:r.inheritAttrs,ctx:ce,data:ce,props:ce,attrs:ce,slots:ce,refs:ce,setupState:ce,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Gu.bind(null,i),e.ce&&e.ce(i),i}let _e=null,cn,or;{const e=yn(),t=(s,r)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(r),i=>{n.length>1?n.forEach(l=>l(i)):n[0](i)}};cn=t("__VUE_INSTANCE_SETTERS__",s=>_e=s),or=t("__VUE_SSR_SETTERS__",s=>As=s)}const Is=e=>{const t=_e;return cn(e),e.scope.on(),()=>{e.scope.off(),cn(t)}},fo=()=>{_e&&_e.scope.off(),cn(null)};function yl(e){return e.vnode.shapeFlag&4}let As=!1;function cd(e,t=!1,s=!1){t&&or(t);const{props:r,children:n}=e.vnode,i=yl(e);Du(e,r,i,t),Uu(e,n,s||t);const l=i?ud(e,t):void 0;return t&&or(!1),l}function ud(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Ru);const{setup:r}=s;if(r){xt();const n=e.setupContext=r.length>1?fd(e):null,i=Is(e),l=Ds(r,e,0,[e.props,n]),a=yi(l);if(bt(),i(),(a||e.sp)&&!bs(e)&&Qi(e),a){if(l.then(fo,fo),t)return l.then(c=>{po(e,c)}).catch(c=>{kn(c,e,0)});e.asyncDep=l}else po(e,l)}else wl(e)}function po(e,t,s){W(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:me(t)&&(e.setupState=qi(t)),wl(e)}function wl(e,t,s){const r=e.type;e.render||(e.render=r.render||lt);{const n=Is(e);xt();try{Tu(e)}finally{bt(),n()}}}const dd={get(e,t){return ye(e,"get",""),e[t]}};function fd(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,dd),slots:e.slots,emit:e.emit,expose:t}}function Rn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(qi(Yc(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in ys)return ys[s](e)},has(t,s){return s in t||s in ys}})):e.proxy}function pd(e,t=!0){return W(e)?e.displayName||e.name:e.name||t&&e.__name}function md(e){return W(e)&&"__vccOpts"in e}const Ke=(e,t)=>ru(e,t,As);function _l(e,t,s){const r=arguments.length;return r===2?me(t)&&!z(t)?an(t)?K(e,null,[t]):K(e,t):K(e,null,t):(r>3?s=Array.prototype.slice.call(arguments,2):r===3&&an(s)&&(s=[s]),K(e,t,s))}const hd="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ir;const mo=typeof window<"u"&&window.trustedTypes;if(mo)try{ir=mo.createPolicy("vue",{createHTML:e=>e})}catch{}const kl=ir?e=>ir.createHTML(e):e=>e,gd="http://www.w3.org/2000/svg",vd="http://www.w3.org/1998/Math/MathML",pt=typeof document<"u"?document:null,ho=pt&&pt.createElement("template"),xd={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,r)=>{const n=t==="svg"?pt.createElementNS(gd,e):t==="mathml"?pt.createElementNS(vd,e):s?pt.createElement(e,{is:s}):pt.createElement(e);return e==="select"&&r&&r.multiple!=null&&n.setAttribute("multiple",r.multiple),n},createText:e=>pt.createTextNode(e),createComment:e=>pt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>pt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,r,n,i){const l=s?s.previousSibling:t.lastChild;if(n&&(n===i||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===i||!(n=n.nextSibling)););else{ho.innerHTML=kl(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const a=ho.content;if(r==="svg"||r==="mathml"){const c=a.firstChild;for(;c.firstChild;)a.appendChild(c.firstChild);a.removeChild(c)}t.insertBefore(a,s)}return[l?l.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},bd=Symbol("_vtc");function yd(e,t,s){const r=e[bd];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const go=Symbol("_vod"),wd=Symbol("_vsh"),_d=Symbol(""),kd=/(^|;)\s*display\s*:/;function Sd(e,t,s){const r=e.style,n=ve(s);let i=!1;if(s&&!n){if(t)if(ve(t))for(const l of t.split(";")){const a=l.slice(0,l.indexOf(":")).trim();s[a]==null&&Qs(r,a,"")}else for(const l in t)s[l]==null&&Qs(r,l,"");for(const l in s)l==="display"&&(i=!0),Qs(r,l,s[l])}else if(n){if(t!==s){const l=r[_d];l&&(s+=";"+l),r.cssText=s,i=kd.test(s)}}else t&&e.removeAttribute("style");go in e&&(e[go]=i?r.display:"",e[wd]&&(r.display="none"))}const vo=/\s*!important$/;function Qs(e,t,s){if(z(s))s.forEach(r=>Qs(e,t,r));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const r=Ed(e,t);vo.test(s)?e.setProperty(Vt(r),s.replace(vo,""),"important"):e[r]=s}}const xo=["Webkit","Moz","ms"],Vn={};function Ed(e,t){const s=Vn[t];if(s)return s;let r=ze(t);if(r!=="filter"&&r in e)return Vn[t]=r;r=bn(r);for(let n=0;n<xo.length;n++){const i=xo[n]+r;if(i in e)return Vn[t]=i}return t}const bo="http://www.w3.org/1999/xlink";function yo(e,t,s,r,n,i=Oc(t)){r&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(bo,t.slice(6,t.length)):e.setAttributeNS(bo,t,s):s==null||i&&!Si(s)?e.removeAttribute(t):e.setAttribute(t,i?"":at(s)?String(s):s)}function wo(e,t,s,r,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?kl(s):s);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const a=i==="OPTION"?e.getAttribute("value")||"":e.value,c=s==null?e.type==="checkbox"?"on":"":String(s);(a!==c||!("_value"in e))&&(e.value=c),s==null&&e.removeAttribute(t),e._value=s;return}let l=!1;if(s===""||s==null){const a=typeof e[t];a==="boolean"?s=Si(s):s==null&&a==="string"?(s="",l=!0):a==="number"&&(s=0,l=!0)}try{e[t]=s}catch{}l&&e.removeAttribute(n||t)}function gt(e,t,s,r){e.addEventListener(t,s,r)}function Cd(e,t,s,r){e.removeEventListener(t,s,r)}const _o=Symbol("_vei");function Rd(e,t,s,r,n=null){const i=e[_o]||(e[_o]={}),l=i[t];if(r&&l)l.value=r;else{const[a,c]=Td(t);if(r){const d=i[t]=$d(r,n);gt(e,a,d,c)}else l&&(Cd(e,a,l,c),i[t]=void 0)}}const ko=/(?:Once|Passive|Capture)$/;function Td(e){let t;if(ko.test(e)){t={};let r;for(;r=e.match(ko);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Vt(e.slice(2)),t]}let Ln=0;const Ad=Promise.resolve(),Od=()=>Ln||(Ad.then(()=>Ln=0),Ln=Date.now());function $d(e,t){const s=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=s.attached)return;ct(Pd(r,s.value),t,5,[r])};return s.value=e,s.attached=Od(),s}function Pd(e,t){if(z(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(r=>n=>!n._stopped&&r&&r(n))}else return t}const So=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Md=(e,t,s,r,n,i)=>{const l=n==="svg";t==="class"?yd(e,r,l):t==="style"?Sd(e,s,r):vn(t)?mr(t)||Rd(e,t,s,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):jd(e,t,r,l))?(wo(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&yo(e,t,r,l,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ve(r))?wo(e,ze(t),r,i,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),yo(e,t,r,l))};function jd(e,t,s,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&So(t)&&W(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return So(t)&&ve(s)?!1:t in e}const Ot=e=>{const t=e.props["onUpdate:modelValue"]||!1;return z(t)?s=>Ks(t,s):t};function Dd(e){e.target.composing=!0}function Eo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Be=Symbol("_assign"),he={created(e,{modifiers:{lazy:t,trim:s,number:r}},n){e[Be]=Ot(n);const i=r||n.props&&n.props.type==="number";gt(e,t?"change":"input",l=>{if(l.target.composing)return;let a=e.value;s&&(a=a.trim()),i&&(a=en(a)),e[Be](a)}),s&&gt(e,"change",()=>{e.value=e.value.trim()}),t||(gt(e,"compositionstart",Dd),gt(e,"compositionend",Eo),gt(e,"change",Eo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:r,trim:n,number:i}},l){if(e[Be]=Ot(l),e.composing)return;const a=(i||e.type==="number")&&!/^0\d/.test(e.value)?en(e.value):e.value,c=t??"";a!==c&&(document.activeElement===e&&e.type!=="range"&&(r&&t===s||n&&e.value.trim()===c)||(e.value=c))}},Sl={deep:!0,created(e,t,s){e[Be]=Ot(s),gt(e,"change",()=>{const r=e._modelValue,n=ts(e),i=e.checked,l=e[Be];if(z(r)){const a=xr(r,n),c=a!==-1;if(i&&!c)l(r.concat(n));else if(!i&&c){const d=[...r];d.splice(a,1),l(d)}}else if(is(r)){const a=new Set(r);i?a.add(n):a.delete(n),l(a)}else l(El(e,i))})},mounted:Co,beforeUpdate(e,t,s){e[Be]=Ot(s),Co(e,t,s)}};function Co(e,{value:t,oldValue:s},r){e._modelValue=t;let n;if(z(t))n=xr(t,r.props.value)>-1;else if(is(t))n=t.has(r.props.value);else{if(t===s)return;n=Ut(t,El(e,!0))}e.checked!==n&&(e.checked=n)}const Ro={created(e,{value:t},s){e.checked=Ut(t,s.props.value),e[Be]=Ot(s),gt(e,"change",()=>{e[Be](ts(e))})},beforeUpdate(e,{value:t,oldValue:s},r){e[Be]=Ot(r),t!==s&&(e.checked=Ut(t,r.props.value))}},Zt={deep:!0,created(e,{value:t,modifiers:{number:s}},r){const n=is(t);gt(e,"change",()=>{const i=Array.prototype.filter.call(e.options,l=>l.selected).map(l=>s?en(ts(l)):ts(l));e[Be](e.multiple?n?new Set(i):i:i[0]),e._assigning=!0,Cr(()=>{e._assigning=!1})}),e[Be]=Ot(r)},mounted(e,{value:t}){To(e,t)},beforeUpdate(e,t,s){e[Be]=Ot(s)},updated(e,{value:t}){e._assigning||To(e,t)}};function To(e,t){const s=e.multiple,r=z(t);if(!(s&&!r&&!is(t))){for(let n=0,i=e.options.length;n<i;n++){const l=e.options[n],a=ts(l);if(s)if(r){const c=typeof a;c==="string"||c==="number"?l.selected=t.some(d=>String(d)===String(a)):l.selected=xr(t,a)>-1}else l.selected=t.has(a);else if(Ut(ts(l),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function ts(e){return"_value"in e?e._value:e.value}function El(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const Id=["ctrl","shift","alt","meta"],Nd={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Id.some(s=>e[`${s}Key`]&&!t.includes(s))},Tn=(e,t)=>{const s=e._withMods||(e._withMods={}),r=t.join(".");return s[r]||(s[r]=(n,...i)=>{for(let l=0;l<t.length;l++){const a=Nd[t[l]];if(a&&a(n,t))return}return e(n,...i)})},Fd=Se({patchProp:Md},xd);let Ao;function Ud(){return Ao||(Ao=Lu(Fd))}const Vd=(...e)=>{const t=Ud().createApp(...e),{mount:s}=t;return t.mount=r=>{const n=Bd(r);if(!n)return;const i=t._component;!W(i)&&!i.render&&!i.template&&(i.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const l=s(n,!1,Ld(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),l},t};function Ld(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Bd(e){return ve(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Kt=typeof document<"u";function Cl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function zd(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Cl(e.default)}const ee=Object.assign;function Bn(e,t){const s={};for(const r in t){const n=t[r];s[r]=Ge(n)?n.map(e):e(n)}return s}const _s=()=>{},Ge=Array.isArray,Rl=/#/g,qd=/&/g,Hd=/\//g,Kd=/=/g,Wd=/\?/g,Tl=/\+/g,Jd=/%5B/g,Gd=/%5D/g,Al=/%5E/g,Xd=/%60/g,Ol=/%7B/g,Qd=/%7C/g,$l=/%7D/g,Yd=/%20/g;function Pr(e){return encodeURI(""+e).replace(Qd,"|").replace(Jd,"[").replace(Gd,"]")}function Zd(e){return Pr(e).replace(Ol,"{").replace($l,"}").replace(Al,"^")}function lr(e){return Pr(e).replace(Tl,"%2B").replace(Yd,"+").replace(Rl,"%23").replace(qd,"%26").replace(Xd,"`").replace(Ol,"{").replace($l,"}").replace(Al,"^")}function ef(e){return lr(e).replace(Kd,"%3D")}function tf(e){return Pr(e).replace(Rl,"%23").replace(Wd,"%3F")}function sf(e){return e==null?"":tf(e).replace(Hd,"%2F")}function Os(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const nf=/\/$/,rf=e=>e.replace(nf,"");function zn(e,t,s="/"){let r,n={},i="",l="";const a=t.indexOf("#");let c=t.indexOf("?");return a<c&&a>=0&&(c=-1),c>-1&&(r=t.slice(0,c),i=t.slice(c+1,a>-1?a:t.length),n=e(i)),a>-1&&(r=r||t.slice(0,a),l=t.slice(a,t.length)),r=cf(r??t,s),{fullPath:r+(i&&"?")+i+l,path:r,query:n,hash:Os(l)}}function of(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function Oo(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function lf(e,t,s){const r=t.matched.length-1,n=s.matched.length-1;return r>-1&&r===n&&ss(t.matched[r],s.matched[n])&&Pl(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function ss(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Pl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!af(e[s],t[s]))return!1;return!0}function af(e,t){return Ge(e)?$o(e,t):Ge(t)?$o(t,e):e===t}function $o(e,t){return Ge(t)?e.length===t.length&&e.every((s,r)=>s===t[r]):e.length===1&&e[0]===t}function cf(e,t){if(e.startsWith("/"))return e;if(!e)return t;const s=t.split("/"),r=e.split("/"),n=r[r.length-1];(n===".."||n===".")&&r.push("");let i=s.length-1,l,a;for(l=0;l<r.length;l++)if(a=r[l],a!==".")if(a==="..")i>1&&i--;else break;return s.slice(0,i).join("/")+"/"+r.slice(l).join("/")}const kt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var $s;(function(e){e.pop="pop",e.push="push"})($s||($s={}));var ks;(function(e){e.back="back",e.forward="forward",e.unknown=""})(ks||(ks={}));function uf(e){if(!e)if(Kt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),rf(e)}const df=/^[^#]+#/;function ff(e,t){return e.replace(df,"#")+t}function pf(e,t){const s=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-s.left-(t.left||0),top:r.top-s.top-(t.top||0)}}const An=()=>({left:window.scrollX,top:window.scrollY});function mf(e){let t;if("el"in e){const s=e.el,r=typeof s=="string"&&s.startsWith("#"),n=typeof s=="string"?r?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n)return;t=pf(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Po(e,t){return(history.state?history.state.position-t:-1)+e}const ar=new Map;function hf(e,t){ar.set(e,t)}function gf(e){const t=ar.get(e);return ar.delete(e),t}let vf=()=>location.protocol+"//"+location.host;function Ml(e,t){const{pathname:s,search:r,hash:n}=t,i=e.indexOf("#");if(i>-1){let a=n.includes(e.slice(i))?e.slice(i).length:1,c=n.slice(a);return c[0]!=="/"&&(c="/"+c),Oo(c,"")}return Oo(s,e)+r+n}function xf(e,t,s,r){let n=[],i=[],l=null;const a=({state:h})=>{const v=Ml(e,location),g=s.value,w=t.value;let C=0;if(h){if(s.value=v,t.value=h,l&&l===g){l=null;return}C=w?h.position-w.position:0}else r(v);n.forEach(O=>{O(s.value,g,{delta:C,type:$s.pop,direction:C?C>0?ks.forward:ks.back:ks.unknown})})};function c(){l=s.value}function d(h){n.push(h);const v=()=>{const g=n.indexOf(h);g>-1&&n.splice(g,1)};return i.push(v),v}function u(){const{history:h}=window;h.state&&h.replaceState(ee({},h.state,{scroll:An()}),"")}function f(){for(const h of i)h();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:d,destroy:f}}function Mo(e,t,s,r=!1,n=!1){return{back:e,current:t,forward:s,replaced:r,position:window.history.length,scroll:n?An():null}}function bf(e){const{history:t,location:s}=window,r={value:Ml(e,s)},n={value:t.state};n.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(c,d,u){const f=e.indexOf("#"),h=f>-1?(s.host&&document.querySelector("base")?e:e.slice(f))+c:vf()+e+c;try{t[u?"replaceState":"pushState"](d,"",h),n.value=d}catch(v){console.error(v),s[u?"replace":"assign"](h)}}function l(c,d){const u=ee({},t.state,Mo(n.value.back,c,n.value.forward,!0),d,{position:n.value.position});i(c,u,!0),r.value=c}function a(c,d){const u=ee({},n.value,t.state,{forward:c,scroll:An()});i(u.current,u,!0);const f=ee({},Mo(r.value,c,null),{position:u.position+1},d);i(c,f,!1),r.value=c}return{location:r,state:n,push:a,replace:l}}function yf(e){e=uf(e);const t=bf(e),s=xf(e,t.state,t.location,t.replace);function r(i,l=!0){l||s.pauseListeners(),history.go(i)}const n=ee({location:"",base:e,go:r,createHref:ff.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function wf(e){return typeof e=="string"||e&&typeof e=="object"}function jl(e){return typeof e=="string"||typeof e=="symbol"}const Dl=Symbol("");var jo;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(jo||(jo={}));function ns(e,t){return ee(new Error,{type:e,[Dl]:!0},t)}function ft(e,t){return e instanceof Error&&Dl in e&&(t==null||!!(e.type&t))}const Do="[^/]+?",_f={sensitive:!1,strict:!1,start:!0,end:!0},kf=/[.+*?^${}()[\]/\\]/g;function Sf(e,t){const s=ee({},_f,t),r=[];let n=s.start?"^":"";const i=[];for(const d of e){const u=d.length?[]:[90];s.strict&&!d.length&&(n+="/");for(let f=0;f<d.length;f++){const h=d[f];let v=40+(s.sensitive?.25:0);if(h.type===0)f||(n+="/"),n+=h.value.replace(kf,"\\$&"),v+=40;else if(h.type===1){const{value:g,repeatable:w,optional:C,regexp:O}=h;i.push({name:g,repeatable:w,optional:C});const $=O||Do;if($!==Do){v+=10;try{new RegExp(`(${$})`)}catch(F){throw new Error(`Invalid custom RegExp for param "${g}" (${$}): `+F.message)}}let D=w?`((?:${$})(?:/(?:${$}))*)`:`(${$})`;f||(D=C&&d.length<2?`(?:/${D})`:"/"+D),C&&(D+="?"),n+=D,v+=20,C&&(v+=-8),w&&(v+=-20),$===".*"&&(v+=-50)}u.push(v)}r.push(u)}if(s.strict&&s.end){const d=r.length-1;r[d][r[d].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const l=new RegExp(n,s.sensitive?"":"i");function a(d){const u=d.match(l),f={};if(!u)return null;for(let h=1;h<u.length;h++){const v=u[h]||"",g=i[h-1];f[g.name]=v&&g.repeatable?v.split("/"):v}return f}function c(d){let u="",f=!1;for(const h of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const v of h)if(v.type===0)u+=v.value;else if(v.type===1){const{value:g,repeatable:w,optional:C}=v,O=g in d?d[g]:"";if(Ge(O)&&!w)throw new Error(`Provided param "${g}" is an array but it is not repeatable (* or + modifiers)`);const $=Ge(O)?O.join("/"):O;if(!$)if(C)h.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${g}"`);u+=$}}return u||"/"}return{re:l,score:r,keys:i,parse:a,stringify:c}}function Ef(e,t){let s=0;for(;s<e.length&&s<t.length;){const r=t[s]-e[s];if(r)return r;s++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Il(e,t){let s=0;const r=e.score,n=t.score;for(;s<r.length&&s<n.length;){const i=Ef(r[s],n[s]);if(i)return i;s++}if(Math.abs(n.length-r.length)===1){if(Io(r))return 1;if(Io(n))return-1}return n.length-r.length}function Io(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Cf={type:0,value:""},Rf=/[a-zA-Z0-9_]/;function Tf(e){if(!e)return[[]];if(e==="/")return[[Cf]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(v){throw new Error(`ERR (${s})/"${d}": ${v}`)}let s=0,r=s;const n=[];let i;function l(){i&&n.push(i),i=[]}let a=0,c,d="",u="";function f(){d&&(s===0?i.push({type:0,value:d}):s===1||s===2||s===3?(i.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:d,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),d="")}function h(){d+=c}for(;a<e.length;){if(c=e[a++],c==="\\"&&s!==2){r=s,s=4;continue}switch(s){case 0:c==="/"?(d&&f(),l()):c===":"?(f(),s=1):h();break;case 4:h(),s=r;break;case 1:c==="("?s=2:Rf.test(c)?h():(f(),s=0,c!=="*"&&c!=="?"&&c!=="+"&&a--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:s=3:u+=c;break;case 3:f(),s=0,c!=="*"&&c!=="?"&&c!=="+"&&a--,u="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${d}"`),f(),l(),n}function Af(e,t,s){const r=Sf(Tf(e.path),s),n=ee(r,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function Of(e,t){const s=[],r=new Map;t=Vo({strict:!1,end:!0,sensitive:!1},t);function n(f){return r.get(f)}function i(f,h,v){const g=!v,w=Fo(f);w.aliasOf=v&&v.record;const C=Vo(t,f),O=[w];if("alias"in f){const F=typeof f.alias=="string"?[f.alias]:f.alias;for(const J of F)O.push(Fo(ee({},w,{components:v?v.record.components:w.components,path:J,aliasOf:v?v.record:w})))}let $,D;for(const F of O){const{path:J}=F;if(h&&J[0]!=="/"){const ue=h.record.path,Y=ue[ue.length-1]==="/"?"":"/";F.path=h.record.path+(J&&Y+J)}if($=Af(F,h,C),v?v.alias.push($):(D=D||$,D!==$&&D.alias.push($),g&&f.name&&!Uo($)&&l(f.name)),Nl($)&&c($),w.children){const ue=w.children;for(let Y=0;Y<ue.length;Y++)i(ue[Y],$,v&&v.children[Y])}v=v||$}return D?()=>{l(D)}:_s}function l(f){if(jl(f)){const h=r.get(f);h&&(r.delete(f),s.splice(s.indexOf(h),1),h.children.forEach(l),h.alias.forEach(l))}else{const h=s.indexOf(f);h>-1&&(s.splice(h,1),f.record.name&&r.delete(f.record.name),f.children.forEach(l),f.alias.forEach(l))}}function a(){return s}function c(f){const h=Mf(f,s);s.splice(h,0,f),f.record.name&&!Uo(f)&&r.set(f.record.name,f)}function d(f,h){let v,g={},w,C;if("name"in f&&f.name){if(v=r.get(f.name),!v)throw ns(1,{location:f});C=v.record.name,g=ee(No(h.params,v.keys.filter(D=>!D.optional).concat(v.parent?v.parent.keys.filter(D=>D.optional):[]).map(D=>D.name)),f.params&&No(f.params,v.keys.map(D=>D.name))),w=v.stringify(g)}else if(f.path!=null)w=f.path,v=s.find(D=>D.re.test(w)),v&&(g=v.parse(w),C=v.record.name);else{if(v=h.name?r.get(h.name):s.find(D=>D.re.test(h.path)),!v)throw ns(1,{location:f,currentLocation:h});C=v.record.name,g=ee({},h.params,f.params),w=v.stringify(g)}const O=[];let $=v;for(;$;)O.unshift($.record),$=$.parent;return{name:C,path:w,params:g,matched:O,meta:Pf(O)}}e.forEach(f=>i(f));function u(){s.length=0,r.clear()}return{addRoute:i,resolve:d,removeRoute:l,clearRoutes:u,getRoutes:a,getRecordMatcher:n}}function No(e,t){const s={};for(const r of t)r in e&&(s[r]=e[r]);return s}function Fo(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:$f(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function $f(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const r in e.components)t[r]=typeof s=="object"?s[r]:s;return t}function Uo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Pf(e){return e.reduce((t,s)=>ee(t,s.meta),{})}function Vo(e,t){const s={};for(const r in e)s[r]=r in t?t[r]:e[r];return s}function Mf(e,t){let s=0,r=t.length;for(;s!==r;){const i=s+r>>1;Il(e,t[i])<0?r=i:s=i+1}const n=jf(e);return n&&(r=t.lastIndexOf(n,r-1)),r}function jf(e){let t=e;for(;t=t.parent;)if(Nl(t)&&Il(e,t)===0)return t}function Nl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Df(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<r.length;++n){const i=r[n].replace(Tl," "),l=i.indexOf("="),a=Os(l<0?i:i.slice(0,l)),c=l<0?null:Os(i.slice(l+1));if(a in t){let d=t[a];Ge(d)||(d=t[a]=[d]),d.push(c)}else t[a]=c}return t}function Lo(e){let t="";for(let s in e){const r=e[s];if(s=ef(s),r==null){r!==void 0&&(t+=(t.length?"&":"")+s);continue}(Ge(r)?r.map(i=>i&&lr(i)):[r&&lr(r)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+s,i!=null&&(t+="="+i))})}return t}function If(e){const t={};for(const s in e){const r=e[s];r!==void 0&&(t[s]=Ge(r)?r.map(n=>n==null?null:""+n):r==null?r:""+r)}return t}const Nf=Symbol(""),Bo=Symbol(""),Mr=Symbol(""),Fl=Symbol(""),cr=Symbol("");function fs(){let e=[];function t(r){return e.push(r),()=>{const n=e.indexOf(r);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Ct(e,t,s,r,n,i=l=>l()){const l=r&&(r.enterCallbacks[n]=r.enterCallbacks[n]||[]);return()=>new Promise((a,c)=>{const d=h=>{h===!1?c(ns(4,{from:s,to:t})):h instanceof Error?c(h):wf(h)?c(ns(2,{from:t,to:h})):(l&&r.enterCallbacks[n]===l&&typeof h=="function"&&l.push(h),a())},u=i(()=>e.call(r&&r.instances[n],t,s,d));let f=Promise.resolve(u);e.length<3&&(f=f.then(d)),f.catch(h=>c(h))})}function qn(e,t,s,r,n=i=>i()){const i=[];for(const l of e)for(const a in l.components){let c=l.components[a];if(!(t!=="beforeRouteEnter"&&!l.instances[a]))if(Cl(c)){const u=(c.__vccOpts||c)[t];u&&i.push(Ct(u,s,r,l,a,n))}else{let d=c();i.push(()=>d.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${a}" at "${l.path}"`);const f=zd(u)?u.default:u;l.mods[a]=u,l.components[a]=f;const v=(f.__vccOpts||f)[t];return v&&Ct(v,s,r,l,a,n)()}))}}return i}function zo(e){const t=vt(Mr),s=vt(Fl),r=Ke(()=>{const c=Xt(e.to);return t.resolve(c)}),n=Ke(()=>{const{matched:c}=r.value,{length:d}=c,u=c[d-1],f=s.matched;if(!u||!f.length)return-1;const h=f.findIndex(ss.bind(null,u));if(h>-1)return h;const v=qo(c[d-2]);return d>1&&qo(u)===v&&f[f.length-1].path!==v?f.findIndex(ss.bind(null,c[d-2])):h}),i=Ke(()=>n.value>-1&&Bf(s.params,r.value.params)),l=Ke(()=>n.value>-1&&n.value===s.matched.length-1&&Pl(s.params,r.value.params));function a(c={}){if(Lf(c)){const d=t[Xt(e.replace)?"replace":"push"](Xt(e.to)).catch(_s);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:r,href:Ke(()=>r.value.href),isActive:i,isExactActive:l,navigate:a}}function Ff(e){return e.length===1?e[0]:e}const Uf=Xi({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:zo,setup(e,{slots:t}){const s=_n(zo(e)),{options:r}=vt(Mr),n=Ke(()=>({[Ho(e.activeClass,r.linkActiveClass,"router-link-active")]:s.isActive,[Ho(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const i=t.default&&Ff(t.default(s));return e.custom?i:_l("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},i)}}}),Vf=Uf;function Lf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Bf(e,t){for(const s in t){const r=t[s],n=e[s];if(typeof r=="string"){if(r!==n)return!1}else if(!Ge(n)||n.length!==r.length||r.some((i,l)=>i!==n[l]))return!1}return!0}function qo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ho=(e,t,s)=>e??t??s,zf=Xi({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){const r=vt(cr),n=Ke(()=>e.route||r.value),i=vt(Bo,0),l=Ke(()=>{let d=Xt(i);const{matched:u}=n.value;let f;for(;(f=u[d])&&!f.components;)d++;return d}),a=Ke(()=>n.value.matched[l.value]);Ws(Bo,Ke(()=>l.value+1)),Ws(Nf,a),Ws(cr,n);const c=Zc();return Js(()=>[c.value,a.value,e.name],([d,u,f],[h,v,g])=>{u&&(u.instances[f]=d,v&&v!==u&&d&&d===h&&(u.leaveGuards.size||(u.leaveGuards=v.leaveGuards),u.updateGuards.size||(u.updateGuards=v.updateGuards))),d&&u&&(!v||!ss(u,v)||!h)&&(u.enterCallbacks[f]||[]).forEach(w=>w(d))},{flush:"post"}),()=>{const d=n.value,u=e.name,f=a.value,h=f&&f.components[u];if(!h)return Ko(s.default,{Component:h,route:d});const v=f.props[u],g=v?v===!0?d.params:typeof v=="function"?v(d):v:null,C=_l(h,ee({},g,t,{onVnodeUnmounted:O=>{O.component.isUnmounted&&(f.instances[u]=null)},ref:c}));return Ko(s.default,{Component:C,route:d})||C}}});function Ko(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const qf=zf;function Hf(e){const t=Of(e.routes,e),s=e.parseQuery||Df,r=e.stringifyQuery||Lo,n=e.history,i=fs(),l=fs(),a=fs(),c=eu(kt);let d=kt;Kt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Bn.bind(null,k=>""+k),f=Bn.bind(null,sf),h=Bn.bind(null,Os);function v(k,U){let I,V;return jl(k)?(I=t.getRecordMatcher(k),V=U):V=k,t.addRoute(V,I)}function g(k){const U=t.getRecordMatcher(k);U&&t.removeRoute(U)}function w(){return t.getRoutes().map(k=>k.record)}function C(k){return!!t.getRecordMatcher(k)}function O(k,U){if(U=ee({},U||c.value),typeof k=="string"){const x=zn(s,k,U.path),_=t.resolve({path:x.path},U),E=n.createHref(x.fullPath);return ee(x,_,{params:h(_.params),hash:Os(x.hash),redirectedFrom:void 0,href:E})}let I;if(k.path!=null)I=ee({},k,{path:zn(s,k.path,U.path).path});else{const x=ee({},k.params);for(const _ in x)x[_]==null&&delete x[_];I=ee({},k,{params:f(x)}),U.params=f(U.params)}const V=t.resolve(I,U),ae=k.hash||"";V.params=u(h(V.params));const p=of(r,ee({},k,{hash:Zd(ae),path:V.path})),m=n.createHref(p);return ee({fullPath:p,hash:ae,query:r===Lo?If(k.query):k.query||{}},V,{redirectedFrom:void 0,href:m})}function $(k){return typeof k=="string"?zn(s,k,c.value.path):ee({},k)}function D(k,U){if(d!==k)return ns(8,{from:U,to:k})}function F(k){return Y(k)}function J(k){return F(ee($(k),{replace:!0}))}function ue(k){const U=k.matched[k.matched.length-1];if(U&&U.redirect){const{redirect:I}=U;let V=typeof I=="function"?I(k):I;return typeof V=="string"&&(V=V.includes("?")||V.includes("#")?V=$(V):{path:V},V.params={}),ee({query:k.query,hash:k.hash,params:V.path!=null?{}:k.params},V)}}function Y(k,U){const I=d=O(k),V=c.value,ae=k.state,p=k.force,m=k.replace===!0,x=ue(I);if(x)return Y(ee($(x),{state:typeof x=="object"?ee({},ae,x.state):ae,force:p,replace:m}),U||I);const _=I;_.redirectedFrom=U;let E;return!p&&lf(r,V,I)&&(E=ns(16,{to:_,from:V}),Ze(V,V,!0,!1)),(E?Promise.resolve(E):Qe(_,V)).catch(S=>ft(S)?ft(S,2)?S:_t(S):Z(S,_,V)).then(S=>{if(S){if(ft(S,2))return Y(ee({replace:m},$(S.to),{state:typeof S.to=="object"?ee({},ae,S.to.state):ae,force:p}),U||_)}else S=$t(_,V,!0,m,ae);return wt(_,V,S),S})}function Ee(k,U){const I=D(k,U);return I?Promise.reject(I):Promise.resolve()}function He(k){const U=zt.values().next().value;return U&&typeof U.runWithContext=="function"?U.runWithContext(k):k()}function Qe(k,U){let I;const[V,ae,p]=Kf(k,U);I=qn(V.reverse(),"beforeRouteLeave",k,U);for(const x of V)x.leaveGuards.forEach(_=>{I.push(Ct(_,k,U))});const m=Ee.bind(null,k,U);return I.push(m),Ve(I).then(()=>{I=[];for(const x of i.list())I.push(Ct(x,k,U));return I.push(m),Ve(I)}).then(()=>{I=qn(ae,"beforeRouteUpdate",k,U);for(const x of ae)x.updateGuards.forEach(_=>{I.push(Ct(_,k,U))});return I.push(m),Ve(I)}).then(()=>{I=[];for(const x of p)if(x.beforeEnter)if(Ge(x.beforeEnter))for(const _ of x.beforeEnter)I.push(Ct(_,k,U));else I.push(Ct(x.beforeEnter,k,U));return I.push(m),Ve(I)}).then(()=>(k.matched.forEach(x=>x.enterCallbacks={}),I=qn(p,"beforeRouteEnter",k,U,He),I.push(m),Ve(I))).then(()=>{I=[];for(const x of l.list())I.push(Ct(x,k,U));return I.push(m),Ve(I)}).catch(x=>ft(x,8)?x:Promise.reject(x))}function wt(k,U,I){a.list().forEach(V=>He(()=>V(k,U,I)))}function $t(k,U,I,V,ae){const p=D(k,U);if(p)return p;const m=U===kt,x=Kt?history.state:{};I&&(V||m?n.replace(k.fullPath,ee({scroll:m&&x&&x.scroll},ae)):n.push(k.fullPath,ae)),c.value=k,Ze(k,U,I,m),_t()}let Ye;function ls(){Ye||(Ye=n.listen((k,U,I)=>{if(!Ns.listening)return;const V=O(k),ae=ue(V);if(ae){Y(ee(ae,{replace:!0,force:!0}),V).catch(_s);return}d=V;const p=c.value;Kt&&hf(Po(p.fullPath,I.delta),An()),Qe(V,p).catch(m=>ft(m,12)?m:ft(m,2)?(Y(ee($(m.to),{force:!0}),V).then(x=>{ft(x,20)&&!I.delta&&I.type===$s.pop&&n.go(-1,!1)}).catch(_s),Promise.reject()):(I.delta&&n.go(-I.delta,!1),Z(m,V,p))).then(m=>{m=m||$t(V,p,!1),m&&(I.delta&&!ft(m,8)?n.go(-I.delta,!1):I.type===$s.pop&&ft(m,20)&&n.go(-1,!1)),wt(V,p,m)}).catch(_s)}))}let Lt=fs(),xe=fs(),ie;function Z(k,U,I){_t(k);const V=xe.list();return V.length?V.forEach(ae=>ae(k,U,I)):console.error(k),Promise.reject(k)}function ut(){return ie&&c.value!==kt?Promise.resolve():new Promise((k,U)=>{Lt.add([k,U])})}function _t(k){return ie||(ie=!k,ls(),Lt.list().forEach(([U,I])=>k?I(k):U()),Lt.reset()),k}function Ze(k,U,I,V){const{scrollBehavior:ae}=e;if(!Kt||!ae)return Promise.resolve();const p=!I&&gf(Po(k.fullPath,0))||(V||!I)&&history.state&&history.state.scroll||null;return Cr().then(()=>ae(k,U,p)).then(m=>m&&mf(m)).catch(m=>Z(m,k,U))}const Ae=k=>n.go(k);let Bt;const zt=new Set,Ns={currentRoute:c,listening:!0,addRoute:v,removeRoute:g,clearRoutes:t.clearRoutes,hasRoute:C,getRoutes:w,resolve:O,options:e,push:F,replace:J,go:Ae,back:()=>Ae(-1),forward:()=>Ae(1),beforeEach:i.add,beforeResolve:l.add,afterEach:a.add,onError:xe.add,isReady:ut,install(k){const U=this;k.component("RouterLink",Vf),k.component("RouterView",qf),k.config.globalProperties.$router=U,Object.defineProperty(k.config.globalProperties,"$route",{enumerable:!0,get:()=>Xt(c)}),Kt&&!Bt&&c.value===kt&&(Bt=!0,F(n.location).catch(ae=>{}));const I={};for(const ae in kt)Object.defineProperty(I,ae,{get:()=>c.value[ae],enumerable:!0});k.provide(Mr,U),k.provide(Fl,Li(I)),k.provide(cr,c);const V=k.unmount;zt.add(k),k.unmount=function(){zt.delete(k),zt.size<1&&(d=kt,Ye&&Ye(),Ye=null,c.value=kt,Bt=!1,ie=!1),V()}}};function Ve(k){return k.reduce((U,I)=>U.then(()=>He(I)),Promise.resolve())}return Ns}function Kf(e,t){const s=[],r=[],n=[],i=Math.max(t.matched.length,e.matched.length);for(let l=0;l<i;l++){const a=t.matched[l];a&&(e.matched.find(d=>ss(d,a))?r.push(a):s.push(a));const c=e.matched[l];c&&(t.matched.find(d=>ss(d,c))||n.push(c))}return[s,r,n]}const qe=(e,t)=>{const s=e.__vccOpts||e;for(const[r,n]of t)s[r]=n;return s},Wf={name:"App",data(){return{user:null,mobileMenuOpen:!1,isAuthenticated:!1}},mounted(){this.checkAuthStatus(),window.addEventListener("storage",this.handleStorageChange),this.authCheckInterval=setInterval(()=>{this.checkAuthStatus()},5e3)},beforeUnmount(){window.removeEventListener("storage",this.handleStorageChange),this.authCheckInterval&&clearInterval(this.authCheckInterval)},methods:{async logout(){try{await this.$http.post("/logout"),this.clearAuthData()}catch(e){console.error("Logout error:",e),this.clearAuthData()}},clearAuthData(){localStorage.removeItem("auth_token"),localStorage.removeItem("user"),delete this.$http.defaults.headers.common.Authorization,this.user=null,this.isAuthenticated=!1,this.mobileMenuOpen=!1,this.$forceUpdate(),this.$router.push("/").then(()=>{this.$nextTick(()=>{this.checkAuthStatus()})})},checkAuthStatus(){const e=localStorage.getItem("auth_token"),t=localStorage.getItem("user");e&&t?(this.isAuthenticated=!0,this.user=JSON.parse(t),this.$http.defaults.headers.common.Authorization=`Bearer ${e}`):(this.isAuthenticated=!1,this.user=null)},handleStorageChange(e){(e.key==="auth_token"||e.key==="user")&&this.checkAuthStatus()}},watch:{$route(){this.checkAuthStatus()}}},Jf={id:"app",class:"min-h-screen"},Gf={class:"glass-effect fixed w-full z-50 backdrop-blur-md"},Xf={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Qf={class:"flex justify-between h-20"},Yf={class:"flex items-center"},Zf={class:"hidden md:flex items-center space-x-6"},ep={key:0,class:"flex items-center space-x-4"},tp={key:1,class:"flex items-center space-x-4"},sp={class:"md:hidden flex items-center"},np={class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},rp={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"},op={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},ip={key:0,class:"md:hidden py-4 border-t border-white/20"},lp={class:"flex flex-col space-y-2"},ap={key:0,class:"flex flex-col space-y-2"},cp={key:1,class:"flex flex-col space-y-2"},up={class:"flex-1"},dp={class:"bg-gradient-to-br from-accent-900 via-accent-800 to-primary-900 text-white relative overflow-hidden"},fp={class:"relative max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8"},pp={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},mp={class:"space-y-3"};function hp(e,t,s,r,n,i){const l=Je("router-link"),a=Je("router-view");return R(),T("div",Jf,[o("nav",Gf,[o("div",Xf,[o("div",Qf,[o("div",Yf,[K(l,{to:"/",class:"flex items-center space-x-3"},{default:ne(()=>t[7]||(t[7]=[o("div",{class:"w-12 h-12 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200"},[o("span",{class:"text-white font-bold text-xl"},"K")],-1),o("span",{class:"text-2xl font-bold text-gradient"},"KabEvents",-1)])),_:1,__:[7]})]),o("div",Zf,[K(l,{to:"/events",class:"nav-link"},{default:ne(()=>t[8]||(t[8]=[q(" Événements ")])),_:1,__:[8]}),n.isAuthenticated?(R(),T("div",tp,[K(l,{to:"/dashboard",class:"nav-link"},{default:ne(()=>t[11]||(t[11]=[q(" Dashboard ")])),_:1,__:[11]}),o("button",{onClick:t[0]||(t[0]=(...c)=>i.logout&&i.logout(...c)),class:"nav-link"}," Déconnexion ")])):(R(),T("div",ep,[K(l,{to:"/login",class:"nav-link"},{default:ne(()=>t[9]||(t[9]=[q(" Connexion ")])),_:1,__:[9]}),K(l,{to:"/register",class:"btn-primary"},{default:ne(()=>t[10]||(t[10]=[q(" S'inscrire ")])),_:1,__:[10]})]))]),o("div",sp,[o("button",{onClick:t[1]||(t[1]=c=>n.mobileMenuOpen=!n.mobileMenuOpen),class:"text-accent-700 hover:text-primary-600 p-2 rounded-lg transition-colors duration-200"},[(R(),T("svg",np,[n.mobileMenuOpen?(R(),T("path",op)):(R(),T("path",rp))]))])])]),n.mobileMenuOpen?(R(),T("div",ip,[o("div",lp,[K(l,{to:"/events",class:"nav-link",onClick:t[2]||(t[2]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[12]||(t[12]=[q(" Événements ")])),_:1,__:[12]}),n.isAuthenticated?(R(),T("div",cp,[K(l,{to:"/dashboard",class:"nav-link",onClick:t[5]||(t[5]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[15]||(t[15]=[q(" Dashboard ")])),_:1,__:[15]}),o("button",{onClick:t[6]||(t[6]=c=>{i.logout,n.mobileMenuOpen=!1}),class:"nav-link text-left"}," Déconnexion ")])):(R(),T("div",ap,[K(l,{to:"/login",class:"nav-link",onClick:t[3]||(t[3]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[13]||(t[13]=[q(" Connexion ")])),_:1,__:[13]}),K(l,{to:"/register",class:"btn-primary text-center",onClick:t[4]||(t[4]=c=>n.mobileMenuOpen=!1)},{default:ne(()=>t[14]||(t[14]=[q(" S'inscrire ")])),_:1,__:[14]})]))])])):le("",!0)])]),o("main",up,[K(a)]),o("footer",dp,[t[23]||(t[23]=o("div",{class:"absolute inset-0 bg-hero-pattern opacity-10"},null,-1)),o("div",fp,[o("div",pp,[t[20]||(t[20]=Ue('<div class="md:col-span-2"><div class="flex items-center space-x-3 mb-6"><div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-lg"><span class="text-white font-bold text-xl">K</span></div><span class="text-2xl font-bold">KabEvents</span></div><p class="text-accent-200 text-lg leading-relaxed mb-6"> La plateforme de référence pour les événements culturels kabyles au Canada. Connectons notre communauté à travers la culture et les traditions. </p><div class="flex space-x-4"><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg></a><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"></path></svg></a><a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"></path></svg></a></div></div>',1)),o("div",null,[t[19]||(t[19]=o("h3",{class:"text-xl font-semibold mb-6 text-white"},"Liens rapides",-1)),o("ul",mp,[o("li",null,[K(l,{to:"/events",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},{default:ne(()=>t[16]||(t[16]=[o("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"},null,-1),q(" Événements ")])),_:1,__:[16]})]),o("li",null,[K(l,{to:"/register",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},{default:ne(()=>t[17]||(t[17]=[o("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"},null,-1),q(" Devenir organisateur ")])),_:1,__:[17]})]),t[18]||(t[18]=o("li",null,[o("a",{href:"#",class:"text-accent-200 hover:text-white transition-colors duration-200 flex items-center"},[o("span",{class:"w-2 h-2 bg-secondary-400 rounded-full mr-3"}),q(" À propos ")])],-1))])]),t[21]||(t[21]=Ue('<div><h3 class="text-xl font-semibold mb-6 text-white">Contact</h3><div class="space-y-4"><div class="flex items-start space-x-3"><svg class="w-5 h-5 text-secondary-400 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg><div><p class="text-accent-200"><EMAIL></p></div></div><div class="flex items-start space-x-3"><svg class="w-5 h-5 text-secondary-400 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg><div><p class="text-accent-200">+1 (555) 123-4567</p></div></div></div></div>',1))]),t[22]||(t[22]=Ue('<div class="mt-12 pt-8 border-t border-white/20"><div class="flex flex-col md:flex-row justify-between items-center"><p class="text-accent-200 text-sm"> © 2024 KabEvents. Tous droits réservés. </p><div class="flex space-x-6 mt-4 md:mt-0"><a href="#" class="text-accent-200 hover:text-white text-sm transition-colors duration-200">Politique de confidentialité</a><a href="#" class="text-accent-200 hover:text-white text-sm transition-colors duration-200">Conditions d&#39;utilisation</a></div></div></div>',1))])])])}const gp=qe(Wf,[["render",hp]]),vp={name:"Home",data(){return{featuredEvents:[],loading:!0}},methods:{async fetchFeaturedEvents(){try{const e=await this.$http.get("/events?limit=3");e.data.data&&e.data.data.data?this.featuredEvents=e.data.data.data:e.data.data?this.featuredEvents=e.data.data:this.featuredEvents=e.data}catch(e){console.error("Error fetching events:",e)}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric"})}},mounted(){this.fetchFeaturedEvents()}},xp={class:"relative min-h-screen flex items-center justify-center overflow-hidden"},bp={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 text-center"},yp={class:"animate-fade-in"},wp={class:"flex flex-col sm:flex-row gap-6 justify-center items-center animate-slide-up"},_p={class:"py-24 relative"},kp={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Sp={key:0,class:"text-center py-16"},Ep={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Cp={class:"event-image"},Rp={class:"text-center"},Tp={class:"text-white text-3xl font-bold"},Ap={class:"mt-2 text-white/80 text-sm font-medium"},Op={class:"p-6"},$p={class:"flex items-center justify-between mb-3"},Pp={class:"price-tag"},Mp={class:"text-sm text-accent-500 font-medium"},jp={class:"text-xl font-bold text-accent-900 mb-3 line-clamp-2"},Dp={class:"text-accent-600 mb-6 line-clamp-3 leading-relaxed"},Ip={class:"flex items-center justify-between"},Np={class:"flex items-center space-x-2"},Fp={class:"text-sm text-accent-500"},Up={class:"text-center mt-16"},Vp={class:"py-24 relative overflow-hidden"},Lp={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Bp={class:"text-center mt-20"},zp={class:"card max-w-4xl mx-auto"},qp={class:"flex flex-col sm:flex-row gap-4 justify-center"};function Hp(e,t,s,r,n,i){const l=Je("router-link");return R(),T("div",null,[o("section",xp,[t[5]||(t[5]=Ue('<div class="absolute inset-0 hero-gradient" data-v-1138dda5></div><div class="absolute inset-0 bg-hero-pattern opacity-20" data-v-1138dda5></div><div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-bounce-gentle" data-v-1138dda5></div><div class="absolute top-40 right-20 w-16 h-16 bg-secondary-400/20 rounded-full animate-bounce-gentle" style="animation-delay:1s;" data-v-1138dda5></div><div class="absolute bottom-40 left-20 w-12 h-12 bg-primary-400/20 rounded-full animate-bounce-gentle" style="animation-delay:2s;" data-v-1138dda5></div>',5)),o("div",bp,[o("div",yp,[t[2]||(t[2]=o("h1",{class:"text-5xl md:text-7xl font-bold mb-8 text-white leading-tight"},[q(" Découvrez la "),o("span",{class:"bg-gradient-to-r from-secondary-300 to-secondary-100 bg-clip-text text-transparent"}," Culture Kabyle ")],-1)),t[3]||(t[3]=o("p",{class:"text-xl md:text-2xl mb-12 text-white/90 max-w-3xl mx-auto leading-relaxed"}," Participez aux plus beaux événements culturels kabyles au Canada. Connectez-vous avec votre communauté et célébrez nos traditions ancestrales. ",-1)),o("div",wp,[K(l,{to:"/events",class:"btn-secondary text-lg px-8 py-4"},{default:ne(()=>t[0]||(t[0]=[o("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),q(" Voir les événements ")])),_:1,__:[0]}),K(l,{to:"/register",class:"btn-outline text-lg px-8 py-4 bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-primary-600"},{default:ne(()=>t[1]||(t[1]=[o("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),q(" Devenir organisateur ")])),_:1,__:[1]})])]),t[4]||(t[4]=o("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"},[o("svg",{class:"w-6 h-6 text-white/70",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))])]),o("section",_p,[t[11]||(t[11]=o("div",{class:"absolute inset-0 bg-gradient-to-b from-white via-accent-50/30 to-white"},null,-1)),o("div",kp,[t[10]||(t[10]=o("div",{class:"text-center mb-16"},[o("h2",{class:"text-4xl md:text-5xl font-bold text-gradient mb-6"},"Événements à venir"),o("p",{class:"text-xl text-accent-600 max-w-2xl mx-auto leading-relaxed"}," Ne manquez pas ces événements exceptionnels qui célèbrent notre riche patrimoine culturel ")],-1)),n.loading?(R(),T("div",Sp,t[6]||(t[6]=[o("div",{class:"loading-spinner w-12 h-12 mx-auto"},null,-1),o("p",{class:"text-accent-600 mt-4"},"Chargement des événements...",-1)]))):(R(),T("div",Ep,[(R(!0),T(ge,null,Te(n.featuredEvents,a=>(R(),T("div",{key:a.id,class:"event-card"},[o("div",Cp,[o("div",Rp,[o("span",Tp,b(a.title.substring(0,2).toUpperCase()),1),o("div",Ap,b(a.location||"Canada"),1)])]),o("div",Op,[o("div",$p,[o("span",Pp,"$"+b(a.ticket_price),1),o("span",Mp,b(i.formatDate(a.event_date)),1)]),o("h3",jp,b(a.title),1),o("p",Dp,b(a.description),1),o("div",Ip,[o("div",Np,[t[7]||(t[7]=o("svg",{class:"w-4 h-4 text-accent-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),o("span",Fp,b(a.location||"Canada"),1)]),K(l,{to:`/events/${a.id}`,class:"btn-primary text-sm px-4 py-2"},{default:ne(()=>t[8]||(t[8]=[q(" Voir détails ")])),_:2,__:[8]},1032,["to"])])])]))),128))])),o("div",Up,[K(l,{to:"/events",class:"btn-outline text-lg px-8 py-4"},{default:ne(()=>t[9]||(t[9]=[o("svg",{class:"w-5 h-5 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1),q(" Voir tous les événements ")])),_:1,__:[9]})])])]),o("section",Vp,[t[17]||(t[17]=o("div",{class:"absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-secondary-50"},null,-1)),t[18]||(t[18]=o("div",{class:"absolute top-0 left-0 w-full h-full bg-hero-pattern opacity-5"},null,-1)),o("div",Lp,[t[16]||(t[16]=Ue('<div class="text-center mb-20" data-v-1138dda5><h2 class="text-4xl md:text-5xl font-bold text-gradient mb-6" data-v-1138dda5>Pourquoi choisir KabEvents ?</h2><p class="text-xl text-accent-600 max-w-3xl mx-auto leading-relaxed" data-v-1138dda5> Une plateforme moderne et sécurisée dédiée à la préservation et à la célébration de notre patrimoine culturel </p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-12" data-v-1138dda5><div class="text-center group" data-v-1138dda5><div class="relative mb-8" data-v-1138dda5><div class="w-24 h-24 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-1138dda5><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-1138dda5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-1138dda5></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-secondary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-1138dda5></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-1138dda5>Événements authentiques</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-1138dda5> Des événements culturels kabyles authentiques organisés par et pour notre communauté, préservant nos traditions ancestrales </p></div><div class="text-center group" data-v-1138dda5><div class="relative mb-8" data-v-1138dda5><div class="w-24 h-24 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-1138dda5><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-1138dda5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" data-v-1138dda5></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-primary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-1138dda5></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-1138dda5>Réservation sécurisée</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-1138dda5> Système de paiement sécurisé avec billets électroniques, QR codes et protection complète de vos données personnelles </p></div><div class="text-center group" data-v-1138dda5><div class="relative mb-8" data-v-1138dda5><div class="w-24 h-24 bg-gradient-to-br from-success-500 to-success-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300" data-v-1138dda5><svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-1138dda5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" data-v-1138dda5></path></svg></div><div class="absolute -top-2 -right-2 w-6 h-6 bg-secondary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300" data-v-1138dda5></div></div><h3 class="text-2xl font-bold text-accent-900 mb-4" data-v-1138dda5>Communauté unie</h3><p class="text-accent-600 leading-relaxed text-lg" data-v-1138dda5> Rassemblez-vous avec la communauté kabyle du Canada et créez des liens durables autour de notre culture commune </p></div></div>',2)),o("div",Bp,[o("div",zp,[t[14]||(t[14]=o("h3",{class:"text-3xl font-bold text-accent-900 mb-4"},"Prêt à rejoindre notre communauté ?",-1)),t[15]||(t[15]=o("p",{class:"text-xl text-accent-600 mb-8 leading-relaxed"}," Découvrez des événements exceptionnels et connectez-vous avec des milliers de membres de notre communauté ",-1)),o("div",qp,[K(l,{to:"/register",class:"btn-primary text-lg px-8 py-4"},{default:ne(()=>t[12]||(t[12]=[q(" Créer un compte gratuit ")])),_:1,__:[12]}),K(l,{to:"/events",class:"btn-outline text-lg px-8 py-4"},{default:ne(()=>t[13]||(t[13]=[q(" Explorer les événements ")])),_:1,__:[13]})])])])])])])}const Kp=qe(vp,[["render",Hp],["__scopeId","data-v-1138dda5"]]),Wp={name:"Login",data(){return{form:{email:"",password:"",remember:!1},loading:!1,error:null}},methods:{async login(){this.loading=!0,this.error=null;try{const e=await this.$http.post("/login",{email:this.form.email,password:this.form.password});localStorage.setItem("auth_token",e.data.token),localStorage.setItem("user",JSON.stringify(e.data.user)),this.$http.defaults.headers.common.Authorization=`Bearer ${e.data.token}`;const t=this.$route.query.redirect||"/dashboard";this.$router.push(t)}catch(e){e.response&&e.response.data?this.error=e.response.data.message||"Erreur de connexion":this.error="Erreur de connexion. Veuillez réessayer."}finally{this.loading=!1}}},mounted(){localStorage.getItem("auth_token")&&this.$router.push("/dashboard")}},Jp={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Gp={class:"max-w-md w-full space-y-8"},Xp={class:"mt-2 text-center text-sm text-gray-600"},Qp={key:0,class:"bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded"},Yp={class:"space-y-4"},Zp={class:"flex items-center justify-between"},em={class:"flex items-center"},tm=["disabled"],sm={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function nm(e,t,s,r,n,i){const l=Je("router-link");return R(),T("div",Jp,[o("div",Gp,[o("div",null,[t[6]||(t[6]=o("div",{class:"mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-blue-500 rounded-lg flex items-center justify-center"},[o("span",{class:"text-white font-bold text-xl"},"K")],-1)),t[7]||(t[7]=o("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Connexion à votre compte ",-1)),o("p",Xp,[t[5]||(t[5]=q(" Ou ")),K(l,{to:"/register",class:"font-medium text-orange-600 hover:text-orange-500"},{default:ne(()=>t[4]||(t[4]=[q(" créez un nouveau compte ")])),_:1,__:[4]})])]),o("form",{class:"mt-8 space-y-6",onSubmit:t[3]||(t[3]=Tn((...a)=>i.login&&i.login(...a),["prevent"]))},[n.error?(R(),T("div",Qp,b(n.error),1)):le("",!0),o("div",Yp,[o("div",null,[t[8]||(t[8]=o("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Adresse email ",-1)),X(o("input",{id:"email","onUpdate:modelValue":t[0]||(t[0]=a=>n.form.email=a),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"<EMAIL>"},null,512),[[he,n.form.email]])]),o("div",null,[t[9]||(t[9]=o("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Mot de passe ",-1)),X(o("input",{id:"password","onUpdate:modelValue":t[1]||(t[1]=a=>n.form.password=a),name:"password",type:"password",autocomplete:"current-password",required:"",class:"input-field mt-1",placeholder:"Votre mot de passe"},null,512),[[he,n.form.password]])])]),o("div",Zp,[o("div",em,[X(o("input",{id:"remember-me","onUpdate:modelValue":t[2]||(t[2]=a=>n.form.remember=a),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"},null,512),[[Sl,n.form.remember]]),t[10]||(t[10]=o("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Se souvenir de moi ",-1))]),t[11]||(t[11]=o("div",{class:"text-sm"},[o("a",{href:"#",class:"font-medium text-orange-600 hover:text-orange-500"}," Mot de passe oublié ? ")],-1))]),o("div",null,[o("button",{type:"submit",disabled:n.loading,class:"btn-primary w-full flex justify-center items-center"},[n.loading?(R(),T("svg",sm,t[12]||(t[12]=[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):le("",!0),q(" "+b(n.loading?"Connexion...":"Se connecter"),1)],8,tm)])],32)])])}const rm=qe(Wp,[["render",nm]]),om={name:"Register",data(){return{form:{name:"",email:"",password:"",password_confirmation:"",role:"user",terms:!1},loading:!1,error:null}},methods:{async register(){this.loading=!0,this.error=null;try{const e=await this.$http.post("/register",{name:this.form.name,email:this.form.email,password:this.form.password,password_confirmation:this.form.password_confirmation,role:this.form.role});localStorage.setItem("auth_token",e.data.token),localStorage.setItem("user",JSON.stringify(e.data.user)),this.$http.defaults.headers.common.Authorization=`Bearer ${e.data.token}`,this.$router.push("/dashboard")}catch(e){if(e.response&&e.response.data)if(e.response.data.errors){const t=Object.values(e.response.data.errors).flat();this.error=t.join(", ")}else this.error=e.response.data.message||"Erreur lors de l'inscription";else this.error="Erreur lors de l'inscription. Veuillez réessayer."}finally{this.loading=!1}}},mounted(){localStorage.getItem("auth_token")&&this.$router.push("/dashboard")}},im={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},lm={class:"max-w-md w-full space-y-8"},am={class:"mt-2 text-center text-sm text-gray-600"},cm={key:0,class:"bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded"},um={class:"space-y-4"},dm={class:"flex items-center"},fm=["disabled"],pm={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function mm(e,t,s,r,n,i){const l=Je("router-link");return R(),T("div",im,[o("div",lm,[o("div",null,[t[9]||(t[9]=o("div",{class:"mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-blue-500 rounded-lg flex items-center justify-center"},[o("span",{class:"text-white font-bold text-xl"},"K")],-1)),t[10]||(t[10]=o("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Créer votre compte ",-1)),o("p",am,[t[8]||(t[8]=q(" Ou ")),K(l,{to:"/login",class:"font-medium text-orange-600 hover:text-orange-500"},{default:ne(()=>t[7]||(t[7]=[q(" connectez-vous à votre compte existant ")])),_:1,__:[7]})])]),o("form",{class:"mt-8 space-y-6",onSubmit:t[6]||(t[6]=Tn((...a)=>i.register&&i.register(...a),["prevent"]))},[n.error?(R(),T("div",cm,b(n.error),1)):le("",!0),o("div",um,[o("div",null,[t[11]||(t[11]=o("label",{for:"name",class:"block text-sm font-medium text-gray-700"}," Nom complet ",-1)),X(o("input",{id:"name","onUpdate:modelValue":t[0]||(t[0]=a=>n.form.name=a),name:"name",type:"text",autocomplete:"name",required:"",class:"input-field mt-1",placeholder:"Votre nom complet"},null,512),[[he,n.form.name]])]),o("div",null,[t[12]||(t[12]=o("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Adresse email ",-1)),X(o("input",{id:"email","onUpdate:modelValue":t[1]||(t[1]=a=>n.form.email=a),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"<EMAIL>"},null,512),[[he,n.form.email]])]),o("div",null,[t[14]||(t[14]=o("label",{for:"role",class:"block text-sm font-medium text-gray-700"}," Type de compte ",-1)),X(o("select",{id:"role","onUpdate:modelValue":t[2]||(t[2]=a=>n.form.role=a),name:"role",class:"input-field mt-1"},t[13]||(t[13]=[o("option",{value:"user"},"Utilisateur (participer aux événements)",-1),o("option",{value:"organizer"},"Organisateur (créer des événements)",-1)]),512),[[Zt,n.form.role]])]),o("div",null,[t[15]||(t[15]=o("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Mot de passe ",-1)),X(o("input",{id:"password","onUpdate:modelValue":t[3]||(t[3]=a=>n.form.password=a),name:"password",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Minimum 8 caractères"},null,512),[[he,n.form.password]])]),o("div",null,[t[16]||(t[16]=o("label",{for:"password_confirmation",class:"block text-sm font-medium text-gray-700"}," Confirmer le mot de passe ",-1)),X(o("input",{id:"password_confirmation","onUpdate:modelValue":t[4]||(t[4]=a=>n.form.password_confirmation=a),name:"password_confirmation",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Répétez votre mot de passe"},null,512),[[he,n.form.password_confirmation]])])]),o("div",dm,[X(o("input",{id:"terms","onUpdate:modelValue":t[5]||(t[5]=a=>n.form.terms=a),name:"terms",type:"checkbox",required:"",class:"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"},null,512),[[Sl,n.form.terms]]),t[17]||(t[17]=o("label",{for:"terms",class:"ml-2 block text-sm text-gray-900"},[q(" J'accepte les "),o("a",{href:"#",class:"text-orange-600 hover:text-orange-500"},"conditions d'utilisation"),q(" et la "),o("a",{href:"#",class:"text-orange-600 hover:text-orange-500"},"politique de confidentialité")],-1))]),o("div",null,[o("button",{type:"submit",disabled:n.loading,class:"btn-primary w-full flex justify-center items-center"},[n.loading?(R(),T("svg",pm,t[18]||(t[18]=[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):le("",!0),q(" "+b(n.loading?"Création...":"Créer mon compte"),1)],8,fm)])],32)])])}const hm=qe(om,[["render",mm]]),gm={name:"Events",data(){return{events:[],filteredEvents:[],loading:!0,filters:{search:"",city:"",maxPrice:"",sortBy:"date"}}},methods:{async fetchEvents(){try{const e=await this.$http.get("/events");e.data.data&&e.data.data.data?this.events=e.data.data.data:e.data.data?this.events=e.data.data:this.events=e.data,this.filteredEvents=[...this.events],this.filterEvents()}catch(e){console.error("Error fetching events:",e)}finally{this.loading=!1}},filterEvents(){let e=[...this.events];this.filters.search&&(e=e.filter(t=>t.title.toLowerCase().includes(this.filters.search.toLowerCase())||t.description.toLowerCase().includes(this.filters.search.toLowerCase()))),this.filters.city&&(e=e.filter(t=>t.location.includes(this.filters.city))),this.filters.maxPrice&&(e=e.filter(t=>parseFloat(t.ticket_price)<=parseFloat(this.filters.maxPrice))),e.sort((t,s)=>{switch(this.filters.sortBy){case"price":return parseFloat(t.ticket_price)-parseFloat(s.ticket_price);case"title":return t.title.localeCompare(s.title);case"date":default:return new Date(t.event_date)-new Date(s.event_date)}}),this.filteredEvents=e},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvents()}},vm={class:"min-h-screen bg-gray-50"},xm={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},bm={class:"bg-white rounded-lg shadow p-6"},ym={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},wm={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12"},_m={key:0,class:"text-center py-12"},km={key:1,class:"text-center py-12"},Sm={key:2,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Em={class:"h-48 bg-gradient-to-r from-orange-400 to-blue-400 rounded-lg mb-4 flex items-center justify-center"},Cm={class:"text-white text-2xl font-bold"},Rm={class:"space-y-3"},Tm={class:"text-xl font-semibold text-gray-900"},Am={class:"text-gray-600 text-sm line-clamp-3"},Om={class:"flex items-center text-sm text-gray-500"},$m={class:"flex items-center text-sm text-gray-500"},Pm={class:"flex items-center justify-between"},Mm={class:"flex items-center text-sm text-gray-500"},jm={class:"text-2xl font-bold text-orange-600"};function Dm(e,t,s,r,n,i){const l=Je("router-link");return R(),T("div",vm,[t[21]||(t[21]=o("div",{class:"bg-white shadow"},[o("div",{class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},[o("h1",{class:"text-3xl font-bold text-gray-900"},"Événements culturels kabyles"),o("p",{class:"mt-2 text-gray-600"},"Découvrez tous les événements à venir")])],-1)),o("div",xm,[o("div",bm,[o("div",ym,[o("div",null,[t[8]||(t[8]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Rechercher",-1)),X(o("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>n.filters.search=a),type:"text",placeholder:"Nom de l'événement...",class:"input-field",onInput:t[1]||(t[1]=(...a)=>i.filterEvents&&i.filterEvents(...a))},null,544),[[he,n.filters.search]])]),o("div",null,[t[10]||(t[10]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Ville",-1)),X(o("select",{"onUpdate:modelValue":t[2]||(t[2]=a=>n.filters.city=a),class:"input-field",onChange:t[3]||(t[3]=(...a)=>i.filterEvents&&i.filterEvents(...a))},t[9]||(t[9]=[Ue('<option value="" data-v-a6e5b925>Toutes les villes</option><option value="Toronto" data-v-a6e5b925>Toronto</option><option value="Montréal" data-v-a6e5b925>Montréal</option><option value="Vancouver" data-v-a6e5b925>Vancouver</option><option value="Calgary" data-v-a6e5b925>Calgary</option>',5)]),544),[[Zt,n.filters.city]])]),o("div",null,[t[12]||(t[12]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Prix max",-1)),X(o("select",{"onUpdate:modelValue":t[4]||(t[4]=a=>n.filters.maxPrice=a),class:"input-field",onChange:t[5]||(t[5]=(...a)=>i.filterEvents&&i.filterEvents(...a))},t[11]||(t[11]=[o("option",{value:""},"Tous les prix",-1),o("option",{value:"50"},"Moins de 50$",-1),o("option",{value:"75"},"Moins de 75$",-1),o("option",{value:"100"},"Moins de 100$",-1)]),544),[[Zt,n.filters.maxPrice]])]),o("div",null,[t[14]||(t[14]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Trier par",-1)),X(o("select",{"onUpdate:modelValue":t[6]||(t[6]=a=>n.filters.sortBy=a),class:"input-field",onChange:t[7]||(t[7]=(...a)=>i.filterEvents&&i.filterEvents(...a))},t[13]||(t[13]=[o("option",{value:"date"},"Date",-1),o("option",{value:"price"},"Prix",-1),o("option",{value:"title"},"Nom",-1)]),544),[[Zt,n.filters.sortBy]])])])])]),o("div",wm,[n.loading?(R(),T("div",_m,t[15]||(t[15]=[o("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"},null,-1),o("p",{class:"mt-2 text-gray-600"},"Chargement des événements...",-1)]))):n.filteredEvents.length===0?(R(),T("div",km,t[16]||(t[16]=[o("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),o("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Aucun événement trouvé",-1),o("p",{class:"mt-1 text-sm text-gray-500"},"Essayez de modifier vos critères de recherche.",-1)]))):(R(),T("div",Sm,[(R(!0),T(ge,null,Te(n.filteredEvents,a=>(R(),T("div",{key:a.id,class:"card hover:shadow-xl transition duration-300"},[o("div",Em,[o("span",Cm,b(a.title.substring(0,2).toUpperCase()),1)]),o("div",Rm,[o("h3",Tm,b(a.title),1),o("p",Am,b(a.description),1),o("div",Om,[t[17]||(t[17]=o("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),q(" "+b(i.formatDate(a.event_date)),1)]),o("div",$m,[t[18]||(t[18]=o("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),q(" "+b(a.location),1)]),o("div",Pm,[o("div",Mm,[t[19]||(t[19]=o("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1)),q(" "+b(a.available_tickets)+" places disponibles ",1)]),o("span",jm,"$"+b(a.ticket_price),1)]),K(l,{to:`/events/${a.id}`,class:"btn-primary w-full text-center block"},{default:ne(()=>t[20]||(t[20]=[q(" Voir détails ")])),_:2,__:[20]},1032,["to"])])]))),128))]))])])}const Im=qe(gm,[["render",Dm],["__scopeId","data-v-a6e5b925"]]),Nm={name:"EventDetail",props:["id"],data(){return{event:null,loading:!0,quantity:1}},computed:{isAuthenticated(){return!!localStorage.getItem("auth_token")}},methods:{async fetchEvent(){try{const e=await this.$http.get(`/events/${this.id}`);this.event=e.data.data||e.data}catch(e){console.error("Error fetching event:",e),this.event=null}finally{this.loading=!1}},goToCheckout(){localStorage.setItem("checkout_quantity",this.quantity),this.$router.push(`/checkout/${this.id}`)},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric"})},formatTime(e){return new Date(e).toLocaleTimeString("fr-CA",{hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvent()},watch:{id(){this.fetchEvent()}}},Fm={class:"min-h-screen bg-gray-50"},Um={key:0,class:"flex items-center justify-center min-h-screen"},Vm={key:1,class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Lm={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Bm={class:"lg:col-span-2"},zm={class:"h-64 md:h-96 bg-gradient-to-r from-orange-400 to-blue-400 rounded-xl mb-6 flex items-center justify-center"},qm={class:"text-white text-4xl font-bold"},Hm={class:"bg-white rounded-xl shadow-lg p-6"},Km={class:"text-3xl font-bold text-gray-900 mb-4"},Wm={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"},Jm={class:"flex items-center text-gray-600"},Gm={class:"font-medium"},Xm={class:"text-sm"},Qm={class:"flex items-center text-gray-600"},Ym={class:"font-medium"},Zm={class:"flex items-center text-gray-600"},eh={class:"font-medium"},th={class:"text-sm"},sh={class:"flex items-center text-gray-600"},nh={class:"text-sm"},rh={class:"border-t pt-6"},oh={class:"text-gray-700 leading-relaxed"},ih={class:"lg:col-span-1"},lh={class:"bg-white rounded-xl shadow-lg p-6 sticky top-8"},ah={class:"text-center mb-6"},ch={class:"text-3xl font-bold text-orange-600"},uh={key:0,class:"space-y-4"},dh={key:1,class:"space-y-4"},fh=["value"],ph={class:"border-t pt-4"},mh={class:"flex justify-between mb-2"},hh={class:"font-semibold"},gh={class:"flex justify-between text-lg font-bold"},vh={key:2,class:"text-center"},xh={key:2,class:"flex items-center justify-center min-h-screen"},bh={class:"text-center"};function yh(e,t,s,r,n,i){var a;const l=Je("router-link");return R(),T("div",Fm,[n.loading?(R(),T("div",Um,t[3]||(t[3]=[o("div",{class:"text-center"},[o("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"}),o("p",{class:"mt-2 text-gray-600"},"Chargement de l'événement...")],-1)]))):n.event?(R(),T("div",Vm,[o("button",{onClick:t[0]||(t[0]=c=>e.$router.go(-1)),class:"mb-6 flex items-center text-orange-600 hover:text-orange-700"},t[4]||(t[4]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1),q(" Retour ")])),o("div",Lm,[o("div",Bm,[o("div",zm,[o("span",qm,b(n.event.title.substring(0,2).toUpperCase()),1)]),o("div",Hm,[o("h1",Km,b(n.event.title),1),o("div",Wm,[o("div",Jm,[t[5]||(t[5]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),o("div",null,[o("p",Gm,b(i.formatDate(n.event.event_date)),1),o("p",Xm,b(i.formatTime(n.event.event_date)),1)])]),o("div",Qm,[t[6]||(t[6]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),o("div",null,[o("p",Ym,b(n.event.location),1)])]),o("div",Zm,[t[7]||(t[7]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"})],-1)),o("div",null,[o("p",eh,b(n.event.available_tickets)+" places disponibles",1),o("p",th,"sur "+b(n.event.total_tickets)+" places",1)])]),o("div",sh,[t[9]||(t[9]=o("svg",{class:"w-5 h-5 mr-3 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"})],-1)),o("div",null,[t[8]||(t[8]=o("p",{class:"font-medium"},"Organisé par",-1)),o("p",nh,b(((a=n.event.organizer)==null?void 0:a.name)||"Organisateur"),1)])])]),o("div",rh,[t[10]||(t[10]=o("h2",{class:"text-xl font-semibold mb-3"},"Description",-1)),o("p",oh,b(n.event.description),1)])])]),o("div",ih,[o("div",lh,[o("div",ah,[o("p",ch,"$"+b(n.event.ticket_price),1),t[11]||(t[11]=o("p",{class:"text-gray-600"},"par billet",-1))]),i.isAuthenticated?n.event.available_tickets>0?(R(),T("div",dh,[o("div",null,[t[15]||(t[15]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Nombre de billets ",-1)),X(o("select",{"onUpdate:modelValue":t[1]||(t[1]=c=>n.quantity=c),class:"input-field"},[(R(!0),T(ge,null,Te(Math.min(10,n.event.available_tickets),c=>(R(),T("option",{key:c,value:c},b(c)+" billet"+b(c>1?"s":""),9,fh))),128))],512),[[Zt,n.quantity]])]),o("div",ph,[o("div",mh,[o("span",null,b(n.quantity)+" × $"+b(n.event.ticket_price),1),o("span",hh,"$"+b((n.quantity*parseFloat(n.event.ticket_price)).toFixed(2)),1)]),o("div",gh,[t[16]||(t[16]=o("span",null,"Total",-1)),o("span",null,"$"+b((n.quantity*parseFloat(n.event.ticket_price)).toFixed(2)),1)])]),o("button",{onClick:t[2]||(t[2]=(...c)=>i.goToCheckout&&i.goToCheckout(...c)),class:"btn-primary w-full flex justify-center items-center"}," Acheter maintenant ")])):(R(),T("div",vh,t[17]||(t[17]=[o("p",{class:"text-orange-600 font-semibold mb-4"},"Événement complet",-1),o("p",{class:"text-gray-600 text-sm"}," Cet événement n'a plus de places disponibles. ",-1)]))):(R(),T("div",uh,[t[14]||(t[14]=o("p",{class:"text-center text-gray-600 mb-4"}," Connectez-vous pour réserver vos billets ",-1)),K(l,{to:"/login",class:"btn-primary w-full text-center block"},{default:ne(()=>t[12]||(t[12]=[q(" Se connecter ")])),_:1,__:[12]}),K(l,{to:"/register",class:"btn-secondary w-full text-center block"},{default:ne(()=>t[13]||(t[13]=[q(" Créer un compte ")])),_:1,__:[13]})]))])])])])):(R(),T("div",xh,[o("div",bh,[t[19]||(t[19]=o("h2",{class:"text-2xl font-bold text-gray-900 mb-2"},"Événement non trouvé",-1)),t[20]||(t[20]=o("p",{class:"text-gray-600 mb-4"},"L'événement que vous recherchez n'existe pas.",-1)),K(l,{to:"/events",class:"btn-primary"},{default:ne(()=>t[18]||(t[18]=[q(" Voir tous les événements ")])),_:1,__:[18]})])]))])}const wh=qe(Nm,[["render",yh]]);var Ul="basil",_h=function(t){return t===3?"v3":t},Vl="https://js.stripe.com",kh="".concat(Vl,"/").concat(Ul,"/stripe.js"),Sh=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,Eh=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/;var Ch=function(t){return Sh.test(t)||Eh.test(t)},Rh=function(){for(var t=document.querySelectorAll('script[src^="'.concat(Vl,'"]')),s=0;s<t.length;s++){var r=t[s];if(Ch(r.src))return r}return null},Wo=function(t){var s="",r=document.createElement("script");r.src="".concat(kh).concat(s);var n=document.head||document.body;if(!n)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(r),r},Th=function(t,s){!t||!t._registerWrapper||t._registerWrapper({name:"stripe-js",version:"7.3.1",startTime:s})},ps=null,Ls=null,Bs=null,Ah=function(t){return function(s){t(new Error("Failed to load Stripe.js",{cause:s}))}},Oh=function(t,s){return function(){window.Stripe?t(window.Stripe):s(new Error("Stripe.js not available"))}},$h=function(t){return ps!==null?ps:(ps=new Promise(function(s,r){if(typeof window>"u"||typeof document>"u"){s(null);return}if(window.Stripe){s(window.Stripe);return}try{var n=Rh();if(!(n&&t)){if(!n)n=Wo(t);else if(n&&Bs!==null&&Ls!==null){var i;n.removeEventListener("load",Bs),n.removeEventListener("error",Ls),(i=n.parentNode)===null||i===void 0||i.removeChild(n),n=Wo(t)}}Bs=Oh(s,r),Ls=Ah(r),n.addEventListener("load",Bs),n.addEventListener("error",Ls)}catch(l){r(l);return}}),ps.catch(function(s){return ps=null,Promise.reject(s)}))},Ph=function(t,s,r){if(t===null)return null;var n=s[0],i=n.match(/^pk_test/),l=_h(t.version),a=Ul;i&&l!==a&&console.warn("Stripe.js@".concat(l," was loaded on the page, but @stripe/stripe-js@").concat("7.3.1"," expected Stripe.js@").concat(a,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var c=t.apply(void 0,s);return Th(c,r),c},ms,Ll=!1,Bl=function(){return ms||(ms=$h(null).catch(function(t){return ms=null,Promise.reject(t)}),ms)};Promise.resolve().then(function(){return Bl()}).catch(function(e){Ll||console.warn(e)});var Mh=function(){for(var t=arguments.length,s=new Array(t),r=0;r<t;r++)s[r]=arguments[r];Ll=!0;var n=Date.now();return Bl().then(function(i){return Ph(i,s,n)})};const jh={name:"StripePayment",props:{amount:{type:[String,Number],required:!0},eventId:{type:[String,Number],required:!0},quantity:{type:Number,required:!0}},data(){return{loading:!0,processing:!1,error:null,stripeError:null,paymentMethod:"stripe",stripe:null,cardElement:null,clientSecret:null,paymentIntentId:null,cardData:{number:"",expiry:"",cvv:"",name:""}}},async mounted(){await this.initializeStripe()},methods:{async initializeStripe(){try{const t=(await this.$http.get("/stripe/publishable-key")).data.publishable_key;if(!t)throw new Error("Clé Stripe non configurée");this.stripe=await Mh(t),await this.createPaymentIntent(),this.setupStripeElements()}catch(e){console.error("Stripe initialization error:",e),this.error="Erreur lors de l'initialisation du système de paiement"}finally{this.loading=!1}},async createPaymentIntent(){const e=await this.$http.post(`/stripe/create-payment-intent/${this.eventId}`,{quantity:this.quantity});this.clientSecret=e.data.client_secret,this.paymentIntentId=e.data.payment_intent_id},setupStripeElements(){if(!this.stripe)return;const e=this.stripe.elements();this.cardElement=e.create("card",{style:{base:{fontSize:"16px",color:"#424770","::placeholder":{color:"#aab7c4"}}}}),this.cardElement.mount("#stripe-card-element"),this.cardElement.on("change",t=>{this.stripeError=t.error?t.error.message:null})},async processPayment(){this.processing=!0,this.error=null;try{this.paymentMethod==="stripe"?await this.processStripePayment():await this.processSimulatedPayment()}catch(e){console.error("Payment error:",e),this.error=e.message||"Erreur lors du paiement"}finally{this.processing=!1}},async processStripePayment(){const{error:e,paymentIntent:t}=await this.stripe.confirmCardPayment(this.clientSecret,{payment_method:{card:this.cardElement}});if(e)throw new Error(e.message);t.status==="succeeded"&&(await this.$http.post("/stripe/confirm-payment",{payment_intent_id:this.paymentIntentId,event_id:this.eventId,quantity:this.quantity}),this.$emit("payment-success",{paymentIntentId:this.paymentIntentId,method:"stripe"}))},async processSimulatedPayment(){await new Promise(e=>setTimeout(e,2e3)),this.$emit("payment-success",{method:"card",cardData:this.cardData})},formatCardNumber(e){var r;let t=e.target.value.replace(/\s/g,"").replace(/[^0-9]/gi,""),s=((r=t.match(/.{1,4}/g))==null?void 0:r.join(" "))||t;this.cardData.number=s},formatExpiryDate(e){let t=e.target.value.replace(/\D/g,"");t.length>=2&&(t=t.substring(0,2)+"/"+t.substring(2,4)),this.cardData.expiry=t}}},Dh={class:"stripe-payment"},Ih={key:0,class:"text-center py-4"},Nh={key:1,class:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4"},Fh={key:2},Uh={class:"mb-6"},Vh={class:"space-y-3"},Lh={key:0,class:"space-y-4"},Bh={key:0,class:"text-red-600 text-sm mt-1"},zh={key:1,class:"space-y-4"},qh={class:"grid grid-cols-2 gap-4"},Hh={class:"mt-6"},Kh=["disabled"],Wh={key:0,class:"loading-spinner w-4 h-4 mr-2"};function Jh(e,t,s,r,n,i){return R(),T("div",Dh,[n.loading?(R(),T("div",Ih,t[9]||(t[9]=[o("div",{class:"loading-spinner w-6 h-6 mx-auto mb-2"},null,-1),o("p",{class:"text-gray-600"},"Chargement du système de paiement...",-1)]))):n.error?(R(),T("div",Nh,b(n.error),1)):(R(),T("div",Fh,[o("div",Uh,[t[12]||(t[12]=o("h3",{class:"text-lg font-medium mb-4"},"Méthode de paiement",-1)),o("div",Vh,[o("label",{class:oe(["flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50",{"border-primary-500 bg-primary-50":n.paymentMethod==="stripe"}])},[X(o("input",{"onUpdate:modelValue":t[0]||(t[0]=l=>n.paymentMethod=l),type:"radio",value:"stripe",class:"mr-3"},null,512),[[Ro,n.paymentMethod]]),t[10]||(t[10]=o("div",{class:"flex items-center"},[o("svg",{class:"w-6 h-6 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})]),o("span",{class:"font-medium"},"Carte de crédit/débit (Stripe)")],-1))],2),o("label",{class:oe(["flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50",{"border-primary-500 bg-primary-50":n.paymentMethod==="card"}])},[X(o("input",{"onUpdate:modelValue":t[1]||(t[1]=l=>n.paymentMethod=l),type:"radio",value:"card",class:"mr-3"},null,512),[[Ro,n.paymentMethod]]),t[11]||(t[11]=o("div",{class:"flex items-center"},[o("svg",{class:"w-6 h-6 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})]),o("span",{class:"font-medium"},"Carte de crédit/débit (Simulation)")],-1))],2)])]),n.paymentMethod==="stripe"?(R(),T("div",Lh,[o("div",null,[t[13]||(t[13]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Informations de carte ",-1)),t[14]||(t[14]=o("div",{id:"stripe-card-element",class:"p-3 border rounded-lg bg-white"},null,-1)),n.stripeError?(R(),T("div",Bh,b(n.stripeError),1)):le("",!0)]),t[15]||(t[15]=Ue('<div class="bg-blue-50 border border-blue-200 rounded-lg p-4" data-v-5b9cb773><h4 class="font-medium text-blue-900 mb-2" data-v-5b9cb773>💳 Cartes de test Stripe</h4><div class="text-sm text-blue-800 space-y-1" data-v-5b9cb773><p data-v-5b9cb773><strong data-v-5b9cb773>Succès:</strong> 4242 4242 4242 4242</p><p data-v-5b9cb773><strong data-v-5b9cb773>Échec:</strong> 4000 0000 0000 0002</p><p data-v-5b9cb773><strong data-v-5b9cb773>3D Secure:</strong> 4000 0025 0000 3155</p><p data-v-5b9cb773><strong data-v-5b9cb773>Date:</strong> N&#39;importe quelle date future</p><p data-v-5b9cb773><strong data-v-5b9cb773>CVC:</strong> N&#39;importe quel code à 3 chiffres</p></div></div>',1))])):le("",!0),n.paymentMethod==="card"?(R(),T("div",zh,[o("div",null,[t[16]||(t[16]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Numéro de carte",-1)),X(o("input",{"onUpdate:modelValue":t[2]||(t[2]=l=>n.cardData.number=l),type:"text",placeholder:"1234 5678 9012 3456",class:"input-field",maxlength:"19",onInput:t[3]||(t[3]=(...l)=>i.formatCardNumber&&i.formatCardNumber(...l))},null,544),[[he,n.cardData.number]])]),o("div",qh,[o("div",null,[t[17]||(t[17]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Date d'expiration",-1)),X(o("input",{"onUpdate:modelValue":t[4]||(t[4]=l=>n.cardData.expiry=l),type:"text",placeholder:"MM/AA",class:"input-field",maxlength:"5",onInput:t[5]||(t[5]=(...l)=>i.formatExpiryDate&&i.formatExpiryDate(...l))},null,544),[[he,n.cardData.expiry]])]),o("div",null,[t[18]||(t[18]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"CVV",-1)),X(o("input",{"onUpdate:modelValue":t[6]||(t[6]=l=>n.cardData.cvv=l),type:"text",placeholder:"123",class:"input-field",maxlength:"4"},null,512),[[he,n.cardData.cvv]])])]),o("div",null,[t[19]||(t[19]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Nom sur la carte",-1)),X(o("input",{"onUpdate:modelValue":t[7]||(t[7]=l=>n.cardData.name=l),type:"text",class:"input-field"},null,512),[[he,n.cardData.name]])])])):le("",!0),o("div",Hh,[o("button",{onClick:t[8]||(t[8]=(...l)=>i.processPayment&&i.processPayment(...l)),disabled:n.processing,class:"btn-primary w-full flex justify-center items-center"},[n.processing?(R(),T("span",Wh)):le("",!0),q(" "+b(n.processing?"Traitement...":`Payer $${s.amount}`),1)],8,Kh)])]))])}const Gh=qe(jh,[["render",Jh],["__scopeId","data-v-5b9cb773"]]),Xh={name:"Checkout",components:{StripePayment:Gh},props:["eventId"],data(){return{step:1,event:null,loading:!0,processing:!1,confirmationNumber:null,orderData:{quantity:1,firstName:"",lastName:"",email:"",phone:""},paymentResult:null}},computed:{subtotal(){var e;return(this.orderData.quantity*parseFloat(((e=this.event)==null?void 0:e.ticket_price)||0)).toFixed(2)},serviceFee(){return(parseFloat(this.subtotal)*.05).toFixed(2)},totalAmount(){return(parseFloat(this.subtotal)+parseFloat(this.serviceFee)).toFixed(2)}},methods:{async fetchEvent(){var e,t;try{const s=await this.$http.get(`/events/${this.eventId}`);this.event=s.data.data||s.data;const r=localStorage.getItem("user");if(r){const i=JSON.parse(r);this.orderData.firstName=((e=i.name)==null?void 0:e.split(" ")[0])||"",this.orderData.lastName=((t=i.name)==null?void 0:t.split(" ").slice(1).join(" "))||"",this.orderData.email=i.email||""}const n=localStorage.getItem("checkout_quantity");n&&(this.orderData.quantity=parseInt(n),localStorage.removeItem("checkout_quantity"))}catch(s){console.error("Error fetching event:",s),this.$router.push("/events")}finally{this.loading=!1}},nextStep(){this.validateStep()&&this.step++},prevStep(){this.step--},validateStep(){return this.step===1?this.orderData.firstName&&this.orderData.lastName&&this.orderData.email:!0},async handlePaymentSuccess(e){this.paymentResult=e;try{if(e.method!=="stripe"){const t=await this.$http.post(`/events/${this.eventId}/reserve`,{quantity:this.orderData.quantity,customer_info:this.orderData,payment_info:e});this.confirmationNumber=t.data.data.payment_reference||"CONF_"+Date.now()}else this.confirmationNumber=e.paymentIntentId;this.step=3}catch(t){console.error("Reservation creation error:",t),alert("Erreur lors de la création de la réservation. Veuillez contacter le support.")}},downloadTicket(){alert("Téléchargement des billets (à implémenter)")},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvent()}},Qh={class:"min-h-screen bg-gray-50 py-8"},Yh={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"},Zh={class:"mb-8"},e0={class:"flex items-center justify-center space-x-4"},t0={class:"flex items-center"},s0={class:"flex items-center"},n0={class:"flex items-center"},r0={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},o0={class:"lg:col-span-2"},i0={key:0,class:"bg-white rounded-lg shadow p-6"},l0={class:"border rounded-lg p-4 mb-6"},a0={class:"flex items-start space-x-4"},c0={class:"w-16 h-16 bg-gradient-to-r from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center"},u0={class:"text-white font-bold"},d0={class:"flex-1"},f0={class:"font-semibold text-gray-900"},p0={class:"text-gray-600 text-sm"},m0={class:"text-gray-600 text-sm"},h0={class:"mb-6"},g0=["value"],v0={class:"space-y-4"},x0={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},b0={class:"flex justify-end mt-6"},y0={key:1,class:"bg-white rounded-lg shadow p-6"},w0={class:"flex justify-between mt-6"},_0={key:2,class:"bg-white rounded-lg shadow p-6 text-center"},k0={class:"bg-gray-50 rounded-lg p-4 mb-6"},S0={class:"text-lg font-bold text-gray-900"},E0={class:"space-y-3"},C0={class:"lg:col-span-1"},R0={class:"bg-white rounded-lg shadow p-6 sticky top-8"},T0={class:"space-y-3 mb-4"},A0={class:"flex justify-between"},O0={class:"flex justify-between"},$0={class:"border-t pt-3 flex justify-between font-bold text-lg"};function P0(e,t,s,r,n,i){var c,d,u,f,h,v,g;const l=Je("StripePayment"),a=Je("router-link");return R(),T("div",Qh,[o("div",Yh,[t[28]||(t[28]=o("div",{class:"text-center mb-8"},[o("h1",{class:"text-3xl font-bold text-gray-900"},"Finaliser votre commande"),o("p",{class:"text-gray-600 mt-2"},"Complétez votre achat en toute sécurité")],-1)),o("div",Zh,[o("div",e0,[o("div",t0,[o("div",{class:oe([n.step>=1?"bg-primary-600 text-white":"bg-gray-300 text-gray-600","w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"])}," 1 ",2),t[8]||(t[8]=o("span",{class:"ml-2 text-sm font-medium text-gray-900"},"Détails",-1))]),t[11]||(t[11]=o("div",{class:"w-16 h-0.5 bg-gray-300"},null,-1)),o("div",s0,[o("div",{class:oe([n.step>=2?"bg-primary-600 text-white":"bg-gray-300 text-gray-600","w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"])}," 2 ",2),t[9]||(t[9]=o("span",{class:"ml-2 text-sm font-medium text-gray-900"},"Paiement",-1))]),t[12]||(t[12]=o("div",{class:"w-16 h-0.5 bg-gray-300"},null,-1)),o("div",n0,[o("div",{class:oe([n.step>=3?"bg-primary-600 text-white":"bg-gray-300 text-gray-600","w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"])}," 3 ",2),t[10]||(t[10]=o("span",{class:"ml-2 text-sm font-medium text-gray-900"},"Confirmation",-1))])])]),o("div",r0,[o("div",o0,[n.step===1?(R(),T("div",i0,[t[19]||(t[19]=o("h2",{class:"text-xl font-semibold mb-6"},"Détails de votre commande",-1)),o("div",l0,[o("div",a0,[o("div",c0,[o("span",u0,b((d=(c=n.event)==null?void 0:c.title)==null?void 0:d.substring(0,2).toUpperCase()),1)]),o("div",d0,[o("h3",f0,b((u=n.event)==null?void 0:u.title),1),o("p",p0,b((f=n.event)==null?void 0:f.location),1),o("p",m0,b(i.formatDate((h=n.event)==null?void 0:h.event_date)),1)])])]),o("div",h0,[t[13]||(t[13]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Nombre de billets ",-1)),X(o("select",{"onUpdate:modelValue":t[0]||(t[0]=w=>n.orderData.quantity=w),class:"input-field max-w-xs"},[(R(!0),T(ge,null,Te(Math.min(10,((v=n.event)==null?void 0:v.available_tickets)||1),w=>(R(),T("option",{key:w,value:w},b(w)+" billet"+b(w>1?"s":""),9,g0))),128))],512),[[Zt,n.orderData.quantity]])]),o("div",v0,[t[18]||(t[18]=o("h3",{class:"text-lg font-medium"},"Informations personnelles",-1)),o("div",x0,[o("div",null,[t[14]||(t[14]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Prénom",-1)),X(o("input",{"onUpdate:modelValue":t[1]||(t[1]=w=>n.orderData.firstName=w),type:"text",class:"input-field",required:""},null,512),[[he,n.orderData.firstName]])]),o("div",null,[t[15]||(t[15]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Nom",-1)),X(o("input",{"onUpdate:modelValue":t[2]||(t[2]=w=>n.orderData.lastName=w),type:"text",class:"input-field",required:""},null,512),[[he,n.orderData.lastName]])]),o("div",null,[t[16]||(t[16]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Email",-1)),X(o("input",{"onUpdate:modelValue":t[3]||(t[3]=w=>n.orderData.email=w),type:"email",class:"input-field",required:""},null,512),[[he,n.orderData.email]])]),o("div",null,[t[17]||(t[17]=o("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Téléphone",-1)),X(o("input",{"onUpdate:modelValue":t[4]||(t[4]=w=>n.orderData.phone=w),type:"tel",class:"input-field"},null,512),[[he,n.orderData.phone]])])])]),o("div",b0,[o("button",{onClick:t[5]||(t[5]=(...w)=>i.nextStep&&i.nextStep(...w)),class:"btn-primary"}," Continuer vers le paiement ")])])):le("",!0),n.step===2?(R(),T("div",y0,[t[20]||(t[20]=o("h2",{class:"text-xl font-semibold mb-6"},"Informations de paiement",-1)),K(l,{amount:i.totalAmount,"event-id":s.eventId,quantity:n.orderData.quantity,onPaymentSuccess:i.handlePaymentSuccess},null,8,["amount","event-id","quantity","onPaymentSuccess"]),o("div",w0,[o("button",{onClick:t[6]||(t[6]=(...w)=>i.prevStep&&i.prevStep(...w)),class:"btn-outline"}," Retour ")])])):le("",!0),n.step===3?(R(),T("div",_0,[t[23]||(t[23]=Ue('<div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg></div><h2 class="text-2xl font-bold text-gray-900 mb-2">Paiement confirmé !</h2><p class="text-gray-600 mb-6">Votre réservation a été effectuée avec succès.</p>',3)),o("div",k0,[t[21]||(t[21]=o("p",{class:"text-sm text-gray-600"},"Numéro de confirmation",-1)),o("p",S0,b(n.confirmationNumber),1)]),o("div",E0,[o("button",{onClick:t[7]||(t[7]=(...w)=>i.downloadTicket&&i.downloadTicket(...w)),class:"btn-primary w-full"}," Télécharger les billets "),K(a,{to:"/dashboard",class:"btn-outline w-full block text-center"},{default:ne(()=>t[22]||(t[22]=[q(" Voir mes réservations ")])),_:1,__:[22]})])])):le("",!0)]),o("div",C0,[o("div",R0,[t[26]||(t[26]=o("h3",{class:"text-lg font-semibold mb-4"},"Résumé de la commande",-1)),o("div",T0,[o("div",A0,[o("span",null,b(n.orderData.quantity)+" × $"+b((g=n.event)==null?void 0:g.ticket_price),1),o("span",null,"$"+b(i.subtotal),1)]),o("div",O0,[t[24]||(t[24]=o("span",null,"Frais de service",-1)),o("span",null,"$"+b(i.serviceFee),1)]),o("div",$0,[t[25]||(t[25]=o("span",null,"Total",-1)),o("span",null,"$"+b(i.totalAmount),1)])]),t[27]||(t[27]=o("div",{class:"text-xs text-gray-500"},[o("p",null,"• Billets électroniques envoyés par email"),o("p",null,"• Remboursement possible jusqu'à 24h avant l'événement"),o("p",null,"• Support client disponible 24/7")],-1))])])])])])}const M0=qe(Xh,[["render",P0]]),j0={name:"Dashboard",data(){return{user:null,reservations:[],userStats:{totalReservations:0,upcomingEvents:0,totalSpent:0},adminStats:{totalUsers:0,totalEvents:0,totalReservations:0,totalRevenue:0},loading:!0}},methods:{async fetchUserData(){try{const e=await this.$http.get("/me");if(this.user=e.data.user,this.user.role==="user"){const t=await this.$http.get("/user/reservations");this.reservations=t.data.data||t.data,this.userStats.totalReservations=this.reservations.length,this.userStats.upcomingEvents=this.reservations.filter(s=>{var r;return new Date((r=s.event)==null?void 0:r.event_date)>new Date}).length,this.userStats.totalSpent=this.reservations.filter(s=>s.status==="paid").reduce((s,r)=>s+parseFloat(r.total_amount),0).toFixed(2)}}catch(e){console.error("Error fetching user data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})},getStatusClass(e){switch(e){case"paid":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},getStatusText(e){switch(e){case"paid":return"Payé";case"pending":return"En attente";case"cancelled":return"Annulé";default:return e}}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchUserData()}},D0={class:"min-h-screen bg-gray-50"},I0={class:"bg-white shadow"},N0={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},F0={class:"flex justify-between items-center"},U0={class:"text-gray-600"},V0={class:"flex items-center space-x-2"},L0={class:"px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium"},B0={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},z0={key:0,class:"space-y-8"},q0={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},H0={class:"card"},K0={class:"flex items-center"},W0={class:"ml-4"},J0={class:"text-2xl font-bold text-gray-900"},G0={class:"card"},X0={class:"flex items-center"},Q0={class:"ml-4"},Y0={class:"text-2xl font-bold text-gray-900"},Z0={class:"card"},e1={class:"flex items-center"},t1={class:"ml-4"},s1={class:"text-2xl font-bold text-gray-900"},n1={class:"card"},r1={class:"flex justify-between items-center mb-6"},o1={key:0,class:"text-center py-8"},i1={key:1,class:"text-center py-8"},l1={key:2,class:"space-y-4"},a1={class:"flex justify-between items-start"},c1={class:"flex-1"},u1={class:"font-semibold text-lg"},d1={class:"text-gray-600 text-sm mb-2"},f1={class:"text-gray-600 text-sm"},p1={class:"mt-2 flex items-center space-x-4"},m1={class:"text-sm text-gray-500"},h1={class:"text-sm font-medium"},g1={class:"ml-4"},v1={key:1,class:"space-y-8"},x1={key:2,class:"space-y-8"},b1={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},y1={class:"card text-center"},w1={class:"text-3xl font-bold text-orange-600 mb-2"},_1={class:"card text-center"},k1={class:"text-3xl font-bold text-blue-600 mb-2"},S1={class:"card text-center"},E1={class:"text-3xl font-bold text-green-600 mb-2"},C1={class:"card text-center"},R1={class:"text-3xl font-bold text-purple-600 mb-2"};function T1(e,t,s,r,n,i){var a,c,d,u,f,h;const l=Je("router-link");return R(),T("div",D0,[o("div",I0,[o("div",N0,[o("div",F0,[o("div",null,[t[0]||(t[0]=o("h1",{class:"text-2xl font-bold text-gray-900"},"Dashboard",-1)),o("p",U0,"Bienvenue, "+b((a=n.user)==null?void 0:a.name),1)]),o("div",V0,[o("span",L0,b(((c=n.user)==null?void 0:c.role)==="admin"?"Administrateur":((d=n.user)==null?void 0:d.role)==="organizer"?"Organisateur":"Utilisateur"),1)])])])]),o("div",B0,[((u=n.user)==null?void 0:u.role)==="user"?(R(),T("div",z0,[o("div",q0,[o("div",H0,[o("div",K0,[t[2]||(t[2]=o("div",{class:"p-3 bg-orange-100 rounded-lg"},[o("svg",{class:"w-6 h-6 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",W0,[t[1]||(t[1]=o("p",{class:"text-sm font-medium text-gray-600"},"Réservations totales",-1)),o("p",J0,b(n.userStats.totalReservations),1)])])]),o("div",G0,[o("div",X0,[t[4]||(t[4]=o("div",{class:"p-3 bg-blue-100 rounded-lg"},[o("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",Q0,[t[3]||(t[3]=o("p",{class:"text-sm font-medium text-gray-600"},"Événements à venir",-1)),o("p",Y0,b(n.userStats.upcomingEvents),1)])])]),o("div",Z0,[o("div",e1,[t[6]||(t[6]=o("div",{class:"p-3 bg-green-100 rounded-lg"},[o("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",t1,[t[5]||(t[5]=o("p",{class:"text-sm font-medium text-gray-600"},"Total dépensé",-1)),o("p",s1,"$"+b(n.userStats.totalSpent),1)])])])]),o("div",n1,[o("div",r1,[t[8]||(t[8]=o("h2",{class:"text-xl font-semibold"},"Mes réservations",-1)),K(l,{to:"/events",class:"btn-primary"},{default:ne(()=>t[7]||(t[7]=[q(" Réserver un événement ")])),_:1,__:[7]})]),n.loading?(R(),T("div",o1,t[9]||(t[9]=[o("div",{class:"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-orange-600"},null,-1)]))):n.reservations.length===0?(R(),T("div",i1,t[10]||(t[10]=[o("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})],-1),o("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Aucune réservation",-1),o("p",{class:"mt-1 text-sm text-gray-500"},"Commencez par réserver votre premier événement.",-1)]))):(R(),T("div",l1,[(R(!0),T(ge,null,Te(n.reservations,v=>{var g,w,C,O;return R(),T("div",{key:v.id,class:"border rounded-lg p-4 hover:bg-gray-50"},[o("div",a1,[o("div",c1,[o("h3",u1,b((g=v.event)==null?void 0:g.title),1),o("p",d1,b((w=v.event)==null?void 0:w.location),1),o("p",f1,b(i.formatDate((C=v.event)==null?void 0:C.event_date)),1),o("div",p1,[o("span",m1,b(v.quantity)+" billet(s)",1),o("span",h1,"$"+b(v.total_amount),1),o("span",{class:oe([i.getStatusClass(v.status),"px-2 py-1 rounded-full text-xs font-medium"])},b(i.getStatusText(v.status)),3)])]),o("div",g1,[K(l,{to:`/events/${(O=v.event)==null?void 0:O.id}`,class:"text-orange-600 hover:text-orange-700 text-sm"},{default:ne(()=>t[11]||(t[11]=[q(" Voir l'événement ")])),_:2,__:[11]},1032,["to"])])])])}),128))]))])])):((f=n.user)==null?void 0:f.role)==="organizer"?(R(),T("div",v1,t[12]||(t[12]=[Ue('<div class="text-center"><h2 class="text-2xl font-bold text-gray-900 mb-4">Dashboard Organisateur</h2><p class="text-gray-600">Gérez vos événements et suivez vos ventes</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="card text-center"><svg class="mx-auto h-12 w-12 text-orange-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg><h3 class="text-lg font-semibold mb-2">Créer un événement</h3><p class="text-gray-600 mb-4">Organisez votre prochain événement culturel</p><button class="btn-primary">Créer un événement</button></div><div class="card text-center"><svg class="mx-auto h-12 w-12 text-blue-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg><h3 class="text-lg font-semibold mb-2">Voir les statistiques</h3><p class="text-gray-600 mb-4">Analysez les performances de vos événements</p><button class="btn-secondary">Voir les stats</button></div></div>',2)]))):((h=n.user)==null?void 0:h.role)==="admin"?(R(),T("div",x1,[t[17]||(t[17]=o("div",{class:"text-center"},[o("h2",{class:"text-2xl font-bold text-gray-900 mb-4"},"Dashboard Administrateur"),o("p",{class:"text-gray-600"},"Gérez la plateforme KabEvents")],-1)),o("div",b1,[o("div",y1,[o("div",w1,b(n.adminStats.totalUsers),1),t[13]||(t[13]=o("div",{class:"text-gray-600"},"Utilisateurs",-1))]),o("div",_1,[o("div",k1,b(n.adminStats.totalEvents),1),t[14]||(t[14]=o("div",{class:"text-gray-600"},"Événements",-1))]),o("div",S1,[o("div",E1,b(n.adminStats.totalReservations),1),t[15]||(t[15]=o("div",{class:"text-gray-600"},"Réservations",-1))]),o("div",C1,[o("div",R1,"$"+b(n.adminStats.totalRevenue),1),t[16]||(t[16]=o("div",{class:"text-gray-600"},"Revenus",-1))])])])):le("",!0)])])}const A1=qe(j0,[["render",T1]]),O1={name:"UserDashboard",data(){return{user:null,activeTab:"overview",userStats:{totalReservations:0,upcomingEvents:0,totalSpent:0},reservations:[],upcomingReservations:[],availableEvents:[],profileForm:{name:"",email:"",password:"",password_confirmation:""},loading:!0,updating:!1}},methods:{async fetchData(){try{const e=await this.$http.get("/me");this.user=e.data.user,this.profileForm.name=this.user.name,this.profileForm.email=this.user.email;const t=await this.$http.get("/user/reservations");this.reservations=t.data.data||t.data,this.userStats.totalReservations=this.reservations.length,this.upcomingReservations=this.reservations.filter(r=>{var n;return new Date((n=r.event)==null?void 0:n.event_date)>new Date}),this.userStats.upcomingEvents=this.upcomingReservations.length,this.userStats.totalSpent=this.reservations.filter(r=>r.status==="paid").reduce((r,n)=>r+parseFloat(n.total_amount),0).toFixed(2);const s=await this.$http.get("/events?limit=6");s.data.data&&s.data.data.data?this.availableEvents=s.data.data.data:s.data.data?this.availableEvents=s.data.data:this.availableEvents=s.data}catch(e){console.error("Error fetching user data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},async updateProfile(){this.updating=!0;try{const e=await this.$http.put("/user/profile",this.profileForm);this.user=e.data.user,localStorage.setItem("user",JSON.stringify(this.user)),alert("Profil mis à jour avec succès !"),this.profileForm.password="",this.profileForm.password_confirmation=""}catch(e){console.error("Error updating profile:",e),alert("Erreur lors de la mise à jour du profil")}finally{this.updating=!1}},downloadTicket(e){console.log("Download ticket for reservation:",e),alert("Fonctionnalité de téléchargement à implémenter")},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})},getStatusClass(e){switch(e){case"paid":return"status-active";case"pending":return"bg-secondary-100 text-secondary-800";case"cancelled":return"status-inactive";default:return"bg-accent-100 text-accent-800"}},getStatusText(e){switch(e){case"paid":return"Payé";case"pending":return"En attente";case"cancelled":return"Annulé";default:return e}},getRoleClass(e){switch(e){case"admin":return"bg-danger-100 text-danger-800";case"organizer":return"bg-secondary-100 text-secondary-800";default:return"bg-primary-100 text-primary-800"}},getRoleText(e){switch(e){case"admin":return"Administrateur";case"organizer":return"Organisateur";default:return"Utilisateur"}}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchData()}},$1={class:"min-h-screen"},P1={class:"bg-white shadow-sm"},M1={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},j1={class:"flex justify-between items-center"},D1={class:"text-accent-600 mt-1"},I1={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},N1={class:"mb-8"},F1={class:"flex space-x-8"},U1={key:0,class:"space-y-8"},V1={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},L1={class:"card text-center"},B1={class:"text-3xl font-bold text-primary-600 mb-2"},z1={class:"card text-center"},q1={class:"text-3xl font-bold text-secondary-600 mb-2"},H1={class:"card text-center"},K1={class:"text-3xl font-bold text-success-600 mb-2"},W1={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},J1={class:"card"},G1={class:"flex justify-between items-center mb-6"},X1={key:0,class:"text-center py-8"},Q1={class:"mt-6"},Y1={key:1,class:"space-y-4"},Z1={class:"w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center"},eg={class:"text-white font-bold"},tg={class:"flex-1"},sg={class:"font-semibold text-accent-900"},ng={class:"text-accent-600 text-sm"},rg={class:"text-accent-500 text-sm"},og={class:"text-right"},ig={class:"text-accent-500 text-sm mt-1"},lg={key:1,class:"space-y-6"},ag={class:"flex justify-between items-center"},cg={key:0,class:"text-center py-16"},ug={key:1,class:"text-center py-16"},dg={class:"mt-6"},fg={key:2,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},pg={class:"flex items-start space-x-4"},mg={class:"w-16 h-16 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center flex-shrink-0"},hg={class:"text-white font-bold text-lg"},gg={class:"flex-1 min-w-0"},vg={class:"font-bold text-lg text-accent-900 mb-1"},xg={class:"text-accent-600 text-sm mb-2"},bg={class:"text-accent-500 text-sm mb-3"},yg={class:"flex items-center justify-between mb-4"},wg={class:"flex items-center space-x-4"},_g={class:"text-sm text-accent-600"},kg={class:"font-semibold text-accent-900"},Sg={class:"flex space-x-2"},Eg=["onClick"],Cg={key:2,class:"space-y-6"},Rg={class:"flex justify-between items-center"},Tg={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Ag={class:"event-image"},Og={class:"text-center"},$g={class:"text-white text-2xl font-bold"},Pg={class:"mt-2 text-white/80 text-sm"},Mg={class:"p-6"},jg={class:"text-lg font-bold text-accent-900 mb-2"},Dg={class:"text-accent-600 text-sm mb-4 line-clamp-2"},Ig={class:"flex justify-between items-center mb-4"},Ng={class:"price-tag"},Fg={class:"text-sm text-accent-500"},Ug={key:3,class:"space-y-6"},Vg={class:"card max-w-2xl"},Lg={class:"flex items-center space-x-6"},Bg={class:"w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center"},zg={class:"text-white font-bold text-2xl"},qg={class:"text-lg font-semibold text-accent-900"},Hg={class:"text-accent-600"},Kg={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Wg={class:"flex justify-end"},Jg=["disabled"],Gg={key:0,class:"loading-spinner w-4 h-4 mr-2"};function Xg(e,t,s,r,n,i){var a,c,d,u,f,h,v;const l=Je("router-link");return R(),T("div",$1,[o("div",P1,[o("div",M1,[o("div",j1,[o("div",null,[t[15]||(t[15]=o("h1",{class:"text-3xl font-bold text-gradient"},"Mon Dashboard",-1)),o("p",D1,"Bienvenue, "+b((a=n.user)==null?void 0:a.name),1)]),t[16]||(t[16]=o("div",{class:"flex items-center space-x-4"},[o("span",{class:"status-badge bg-primary-100 text-primary-800"}," Utilisateur ")],-1))])])]),o("div",I1,[o("div",N1,[o("nav",F1,[o("button",{onClick:t[0]||(t[0]=g=>n.activeTab="overview"),class:oe([n.activeTab==="overview"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Vue d'ensemble ",2),o("button",{onClick:t[1]||(t[1]=g=>n.activeTab="reservations"),class:oe([n.activeTab==="reservations"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Mes réservations ",2),o("button",{onClick:t[2]||(t[2]=g=>n.activeTab="events"),class:oe([n.activeTab==="events"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Événements disponibles ",2),o("button",{onClick:t[3]||(t[3]=g=>n.activeTab="profile"),class:oe([n.activeTab==="profile"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Mon profil ",2)])]),n.activeTab==="overview"?(R(),T("div",U1,[o("div",V1,[o("div",L1,[t[17]||(t[17]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",B1,b(n.userStats.totalReservations),1),t[18]||(t[18]=o("div",{class:"text-accent-600 font-medium"},"Réservations totales",-1))]),o("div",z1,[t[19]||(t[19]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",q1,b(n.userStats.upcomingEvents),1),t[20]||(t[20]=o("div",{class:"text-accent-600 font-medium"},"Événements à venir",-1))]),o("div",H1,[t[21]||(t[21]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",K1,"$"+b(n.userStats.totalSpent),1),t[22]||(t[22]=o("div",{class:"text-accent-600 font-medium"},"Total dépensé",-1))])]),o("div",W1,[o("div",{class:"card text-center group cursor-pointer",onClick:t[4]||(t[4]=g=>n.activeTab="events")},t[23]||(t[23]=[Ue('<div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-c323b33e><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-c323b33e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" data-v-c323b33e></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-c323b33e>Découvrir des événements</h3><p class="text-accent-600 mb-6" data-v-c323b33e>Explorez les événements culturels kabyles disponibles</p><div class="btn-primary inline-block" data-v-c323b33e>Explorer</div>',4)])),o("div",{class:"card text-center group cursor-pointer",onClick:t[5]||(t[5]=g=>n.activeTab="reservations")},t[24]||(t[24]=[Ue('<div class="w-20 h-20 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-c323b33e><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-c323b33e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" data-v-c323b33e></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-c323b33e>Mes réservations</h3><p class="text-accent-600 mb-6" data-v-c323b33e>Gérez vos billets et réservations d&#39;événements</p><div class="btn-outline inline-block" data-v-c323b33e>Voir mes billets</div>',4)]))]),o("div",J1,[o("div",G1,[t[25]||(t[25]=o("h3",{class:"text-xl font-bold text-accent-900"},"Mes prochains événements",-1)),o("button",{onClick:t[6]||(t[6]=g=>n.activeTab="reservations"),class:"text-primary-600 hover:text-primary-700 font-medium"}," Voir tous ")]),n.upcomingReservations.length===0?(R(),T("div",X1,[t[26]||(t[26]=o("svg",{class:"mx-auto h-12 w-12 text-accent-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),t[27]||(t[27]=o("h3",{class:"mt-2 text-sm font-medium text-accent-900"},"Aucun événement à venir",-1)),t[28]||(t[28]=o("p",{class:"mt-1 text-sm text-accent-500"},"Découvrez et réservez votre prochain événement culturel.",-1)),o("div",Q1,[o("button",{onClick:t[7]||(t[7]=g=>n.activeTab="events"),class:"btn-primary"}," Découvrir des événements ")])])):(R(),T("div",Y1,[(R(!0),T(ge,null,Te(n.upcomingReservations,g=>{var w,C,O,$;return R(),T("div",{key:g.id,class:"flex items-center space-x-4 p-4 bg-accent-50 rounded-lg hover:bg-accent-100 transition-colors duration-200"},[o("div",Z1,[o("span",eg,b((w=g.event)==null?void 0:w.title.substring(0,2).toUpperCase()),1)]),o("div",tg,[o("h4",sg,b((C=g.event)==null?void 0:C.title),1),o("p",ng,b((O=g.event)==null?void 0:O.location),1),o("p",rg,b(i.formatDate(($=g.event)==null?void 0:$.event_date)),1)]),o("div",og,[o("span",{class:oe([i.getStatusClass(g.status),"status-badge"])},b(i.getStatusText(g.status)),3),o("p",ig,b(g.quantity)+" billet(s)",1)])])}),128))]))])])):le("",!0),n.activeTab==="reservations"?(R(),T("div",lg,[o("div",ag,[t[30]||(t[30]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Mes réservations",-1)),o("button",{onClick:t[8]||(t[8]=g=>n.activeTab="events"),class:"btn-primary"},t[29]||(t[29]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),q(" Nouvelle réservation ")]))]),n.loading?(R(),T("div",cg,t[31]||(t[31]=[o("div",{class:"loading-spinner w-12 h-12 mx-auto"},null,-1),o("p",{class:"text-accent-600 mt-4"},"Chargement de vos réservations...",-1)]))):n.reservations.length===0?(R(),T("div",ug,[t[32]||(t[32]=o("svg",{class:"mx-auto h-16 w-16 text-accent-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})],-1)),t[33]||(t[33]=o("h3",{class:"mt-4 text-lg font-medium text-accent-900"},"Aucune réservation",-1)),t[34]||(t[34]=o("p",{class:"mt-2 text-accent-500"},"Vous n'avez pas encore réservé d'événements.",-1)),o("div",dg,[o("button",{onClick:t[9]||(t[9]=g=>n.activeTab="events"),class:"btn-primary"}," Découvrir des événements ")])])):(R(),T("div",fg,[(R(!0),T(ge,null,Te(n.reservations,g=>{var w,C,O,$,D;return R(),T("div",{key:g.id,class:"card"},[o("div",pg,[o("div",mg,[o("span",hg,b((w=g.event)==null?void 0:w.title.substring(0,2).toUpperCase()),1)]),o("div",gg,[o("h3",vg,b((C=g.event)==null?void 0:C.title),1),o("p",xg,b((O=g.event)==null?void 0:O.location),1),o("p",bg,b(i.formatDate(($=g.event)==null?void 0:$.event_date)),1),o("div",yg,[o("div",wg,[o("span",_g,b(g.quantity)+" billet(s)",1),o("span",kg,"$"+b(g.total_amount),1)]),o("span",{class:oe([i.getStatusClass(g.status),"status-badge"])},b(i.getStatusText(g.status)),3)]),o("div",Sg,[K(l,{to:`/events/${(D=g.event)==null?void 0:D.id}`,class:"btn-outline text-sm px-3 py-1 flex-1 text-center"},{default:ne(()=>t[35]||(t[35]=[q(" Voir l'événement ")])),_:2,__:[35]},1032,["to"]),g.status==="paid"?(R(),T("button",{key:0,onClick:F=>i.downloadTicket(g),class:"btn-primary text-sm px-3 py-1"}," Télécharger ",8,Eg)):le("",!0)])])])])}),128))]))])):le("",!0),n.activeTab==="events"?(R(),T("div",Cg,[o("div",Rg,[t[37]||(t[37]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Événements disponibles",-1)),K(l,{to:"/events",class:"btn-outline"},{default:ne(()=>t[36]||(t[36]=[q(" Voir tous les événements ")])),_:1,__:[36]})]),o("div",Tg,[(R(!0),T(ge,null,Te(n.availableEvents,g=>(R(),T("div",{key:g.id,class:"event-card"},[o("div",Ag,[o("div",Og,[o("span",$g,b(g.title.substring(0,2).toUpperCase()),1),o("div",Pg,b(g.location),1)])]),o("div",Mg,[o("h3",jg,b(g.title),1),o("p",Dg,b(g.description),1),o("div",Ig,[o("span",Ng,"$"+b(g.ticket_price),1),o("span",Fg,b(i.formatDate(g.event_date)),1)]),K(l,{to:`/events/${g.id}`,class:"btn-primary w-full text-center"},{default:ne(()=>t[38]||(t[38]=[q(" Réserver ")])),_:2,__:[38]},1032,["to"])])]))),128))])])):le("",!0),n.activeTab==="profile"?(R(),T("div",Ug,[t[43]||(t[43]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Mon profil",-1)),o("div",Vg,[o("form",{onSubmit:t[14]||(t[14]=Tn((...g)=>i.updateProfile&&i.updateProfile(...g),["prevent"])),class:"space-y-6"},[o("div",Lg,[o("div",Bg,[o("span",zg,b((d=(c=n.user)==null?void 0:c.name)==null?void 0:d.charAt(0).toUpperCase()),1)]),o("div",null,[o("h3",qg,b((u=n.user)==null?void 0:u.name),1),o("p",Hg,b((f=n.user)==null?void 0:f.email),1),o("span",{class:oe([i.getRoleClass((h=n.user)==null?void 0:h.role),"status-badge mt-2"])},b(i.getRoleText((v=n.user)==null?void 0:v.role)),3)])]),o("div",Kg,[o("div",null,[t[39]||(t[39]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Nom complet",-1)),X(o("input",{"onUpdate:modelValue":t[10]||(t[10]=g=>n.profileForm.name=g),type:"text",class:"input-field",required:""},null,512),[[he,n.profileForm.name]])]),o("div",null,[t[40]||(t[40]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Email",-1)),X(o("input",{"onUpdate:modelValue":t[11]||(t[11]=g=>n.profileForm.email=g),type:"email",class:"input-field",required:""},null,512),[[he,n.profileForm.email]])])]),o("div",null,[t[41]||(t[41]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Nouveau mot de passe (optionnel)",-1)),X(o("input",{"onUpdate:modelValue":t[12]||(t[12]=g=>n.profileForm.password=g),type:"password",class:"input-field",placeholder:"Laissez vide pour ne pas changer"},null,512),[[he,n.profileForm.password]])]),o("div",null,[t[42]||(t[42]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Confirmer le mot de passe",-1)),X(o("input",{"onUpdate:modelValue":t[13]||(t[13]=g=>n.profileForm.password_confirmation=g),type:"password",class:"input-field"},null,512),[[he,n.profileForm.password_confirmation]])]),o("div",Wg,[o("button",{type:"submit",disabled:n.updating,class:"btn-primary"},[n.updating?(R(),T("span",Gg)):le("",!0),q(" "+b(n.updating?"Mise à jour...":"Mettre à jour"),1)],8,Jg)])],32)])])):le("",!0)])])}const Qg=qe(O1,[["render",Xg],["__scopeId","data-v-c323b33e"]]),Yg={name:"OrganizerDashboard",data(){return{user:null,activeTab:"overview",organizerStats:{total_events:0,total_reservations:0,total_revenue:0,active_events:0,upcoming_events:0},myEvents:[],recentEvents:[],popularEvents:[],newEvent:{title:"",description:"",location:"",event_date:"",ticket_price:"",max_attendees:""},creating:!1,loading:!0}},methods:{async fetchData(){try{const e=await this.$http.get("/me");this.user=e.data.user;const t=await this.$http.get("/organizer/stats");this.organizerStats=t.data;const s=await this.$http.get("/organizer/events");s.data.data&&s.data.data.data?this.myEvents=s.data.data.data:s.data.data?this.myEvents=s.data.data:this.myEvents=s.data,this.recentEvents=this.myEvents.slice(0,5)}catch(e){console.error("Error fetching organizer data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},async createEvent(){this.creating=!0;try{const e=await this.$http.post("/organizer/events",this.newEvent);this.myEvents.unshift(e.data.event),this.resetForm(),this.activeTab="events",alert("Événement créé avec succès !")}catch(e){console.error("Error creating event:",e),alert("Erreur lors de la création de l'événement")}finally{this.creating=!1}},resetForm(){this.newEvent={title:"",description:"",location:"",event_date:"",ticket_price:"",max_attendees:""}},editEvent(e){console.log("Edit event:",e)},viewEventStats(e){console.log("View event stats:",e)},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchData()}},Zg={class:"min-h-screen"},ev={class:"bg-white shadow-sm"},tv={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},sv={class:"flex justify-between items-center"},nv={class:"text-accent-600 mt-1"},rv={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},ov={class:"mb-8"},iv={class:"flex space-x-8"},lv={key:0,class:"space-y-8"},av={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},cv={class:"card text-center"},uv={class:"text-3xl font-bold text-primary-600 mb-2"},dv={class:"card text-center"},fv={class:"text-3xl font-bold text-secondary-600 mb-2"},pv={class:"card text-center"},mv={class:"text-3xl font-bold text-success-600 mb-2"},hv={class:"card text-center"},gv={class:"text-3xl font-bold text-accent-600 mb-2"},vv={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},xv={class:"card"},bv={class:"flex justify-between items-center mb-6"},yv={class:"space-y-4"},wv={class:"w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center"},_v={class:"text-white font-bold"},kv={class:"flex-1"},Sv={class:"font-semibold text-accent-900"},Ev={class:"text-accent-600 text-sm"},Cv={class:"text-right"},Rv={class:"font-semibold text-accent-900"},Tv={class:"text-accent-500 text-sm"},Av={key:1,class:"space-y-6"},Ov={class:"flex justify-between items-center"},$v={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Pv={class:"event-image"},Mv={class:"text-center"},jv={class:"text-white text-2xl font-bold"},Dv={class:"mt-2 text-white/80 text-sm"},Iv={class:"p-6"},Nv={class:"flex justify-between items-start mb-3"},Fv={class:"text-lg font-bold text-accent-900"},Uv={class:"text-accent-600 text-sm mb-4 line-clamp-2"},Vv={class:"flex justify-between items-center mb-4"},Lv={class:"price-tag"},Bv={class:"text-sm text-accent-500"},zv={class:"flex space-x-2"},qv=["onClick"],Hv=["onClick"],Kv={key:2,class:"space-y-6"},Wv={class:"card max-w-4xl"},Jv={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Gv={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Xv={class:"flex justify-end space-x-4"},Qv=["disabled"],Yv={key:0,class:"loading-spinner w-4 h-4 mr-2"},Zv={key:3,class:"space-y-6"},e2={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},t2={class:"card"},s2={class:"space-y-3"},n2={class:"font-medium text-accent-900"},r2={class:"text-sm text-accent-500"},o2={class:"text-right"},i2={class:"font-semibold text-success-600"};function l2(e,t,s,r,n,i){var l;return R(),T("div",Zg,[o("div",ev,[o("div",tv,[o("div",sv,[o("div",null,[t[16]||(t[16]=o("h1",{class:"text-3xl font-bold text-gradient"},"Dashboard Organisateur",-1)),o("p",nv,"Bienvenue, "+b((l=n.user)==null?void 0:l.name),1)]),t[17]||(t[17]=o("div",{class:"flex items-center space-x-4"},[o("span",{class:"status-badge bg-secondary-100 text-secondary-800"}," Organisateur ")],-1))])])]),o("div",rv,[o("div",ov,[o("nav",iv,[o("button",{onClick:t[0]||(t[0]=a=>n.activeTab="overview"),class:oe([n.activeTab==="overview"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Vue d'ensemble ",2),o("button",{onClick:t[1]||(t[1]=a=>n.activeTab="events"),class:oe([n.activeTab==="events"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Mes événements ",2),o("button",{onClick:t[2]||(t[2]=a=>n.activeTab="create"),class:oe([n.activeTab==="create"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Créer un événement ",2),o("button",{onClick:t[3]||(t[3]=a=>n.activeTab="analytics"),class:oe([n.activeTab==="analytics"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Statistiques ",2)])]),n.activeTab==="overview"?(R(),T("div",lv,[o("div",av,[o("div",cv,[t[18]||(t[18]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",uv,b(n.organizerStats.total_events),1),t[19]||(t[19]=o("div",{class:"text-accent-600 font-medium"},"Événements créés",-1))]),o("div",dv,[t[20]||(t[20]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",fv,b(n.organizerStats.total_reservations),1),t[21]||(t[21]=o("div",{class:"text-accent-600 font-medium"},"Réservations",-1))]),o("div",pv,[t[22]||(t[22]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",mv,"$"+b(n.organizerStats.total_revenue),1),t[23]||(t[23]=o("div",{class:"text-accent-600 font-medium"},"Revenus",-1))]),o("div",hv,[t[24]||(t[24]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),o("div",gv,b(n.organizerStats.upcoming_events),1),t[25]||(t[25]=o("div",{class:"text-accent-600 font-medium"},"Événements à venir",-1))])]),o("div",vv,[o("div",{class:"card text-center group cursor-pointer",onClick:t[4]||(t[4]=a=>n.activeTab="create")},t[26]||(t[26]=[Ue('<div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-6c3dd2e8><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-6c3dd2e8><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" data-v-6c3dd2e8></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-6c3dd2e8>Créer un événement</h3><p class="text-accent-600 mb-6" data-v-6c3dd2e8>Organisez votre prochain événement culturel kabyle</p><div class="btn-primary inline-block" data-v-6c3dd2e8>Commencer</div>',4)])),o("div",{class:"card text-center group cursor-pointer",onClick:t[5]||(t[5]=a=>n.activeTab="analytics")},t[27]||(t[27]=[Ue('<div class="w-20 h-20 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200" data-v-6c3dd2e8><svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-6c3dd2e8><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" data-v-6c3dd2e8></path></svg></div><h3 class="text-xl font-bold text-accent-900 mb-3" data-v-6c3dd2e8>Voir les statistiques</h3><p class="text-accent-600 mb-6" data-v-6c3dd2e8>Analysez les performances de vos événements</p><div class="btn-outline inline-block" data-v-6c3dd2e8>Analyser</div>',4)]))]),o("div",xv,[o("div",bv,[t[28]||(t[28]=o("h3",{class:"text-xl font-bold text-accent-900"},"Événements récents",-1)),o("button",{onClick:t[6]||(t[6]=a=>n.activeTab="events"),class:"text-primary-600 hover:text-primary-700 font-medium"}," Voir tous ")]),o("div",yv,[(R(!0),T(ge,null,Te(n.recentEvents,a=>(R(),T("div",{key:a.id,class:"flex items-center space-x-4 p-4 bg-accent-50 rounded-lg hover:bg-accent-100 transition-colors duration-200"},[o("div",wv,[o("span",_v,b(a.title.substring(0,2).toUpperCase()),1)]),o("div",kv,[o("h4",Sv,b(a.title),1),o("p",Ev,b(i.formatDate(a.event_date)),1)]),o("div",Cv,[o("p",Rv,"$"+b(a.ticket_price),1),o("p",Tv,b(a.total_reservations||0)+" réservations",1)])]))),128))])])])):le("",!0),n.activeTab==="events"?(R(),T("div",Av,[o("div",Ov,[t[30]||(t[30]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Mes événements",-1)),o("button",{onClick:t[7]||(t[7]=a=>n.activeTab="create"),class:"btn-primary"},t[29]||(t[29]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),q(" Nouvel événement ")]))]),o("div",$v,[(R(!0),T(ge,null,Te(n.myEvents,a=>(R(),T("div",{key:a.id,class:"event-card"},[o("div",Pv,[o("div",Mv,[o("span",jv,b(a.title.substring(0,2).toUpperCase()),1),o("div",Dv,b(a.location),1)])]),o("div",Iv,[o("div",Nv,[o("h3",Fv,b(a.title),1),o("span",{class:oe([a.is_active?"status-active":"status-inactive","status-badge"])},b(a.is_active?"Actif":"Inactif"),3)]),o("p",Uv,b(a.description),1),o("div",Vv,[o("span",Lv,"$"+b(a.ticket_price),1),o("span",Bv,b(i.formatDate(a.event_date)),1)]),o("div",zv,[o("button",{onClick:c=>i.editEvent(a),class:"btn-primary text-sm px-3 py-1 flex-1"},"Modifier",8,qv),o("button",{onClick:c=>i.viewEventStats(a),class:"btn-outline text-sm px-3 py-1"},"Stats",8,Hv)])])]))),128))])])):le("",!0),n.activeTab==="create"?(R(),T("div",Kv,[t[37]||(t[37]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Créer un nouvel événement",-1)),o("div",Wv,[o("form",{onSubmit:t[15]||(t[15]=Tn((...a)=>i.createEvent&&i.createEvent(...a),["prevent"])),class:"space-y-6"},[o("div",Jv,[o("div",null,[t[31]||(t[31]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Titre de l'événement",-1)),X(o("input",{"onUpdate:modelValue":t[8]||(t[8]=a=>n.newEvent.title=a),type:"text",class:"input-field",placeholder:"Ex: Festival de musique kabyle",required:""},null,512),[[he,n.newEvent.title]])]),o("div",null,[t[32]||(t[32]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Lieu",-1)),X(o("input",{"onUpdate:modelValue":t[9]||(t[9]=a=>n.newEvent.location=a),type:"text",class:"input-field",placeholder:"Ex: Centre culturel, Montréal",required:""},null,512),[[he,n.newEvent.location]])])]),o("div",null,[t[33]||(t[33]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Description",-1)),X(o("textarea",{"onUpdate:modelValue":t[10]||(t[10]=a=>n.newEvent.description=a),rows:"4",class:"input-field",placeholder:"Décrivez votre événement...",required:""},null,512),[[he,n.newEvent.description]])]),o("div",Gv,[o("div",null,[t[34]||(t[34]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Date et heure",-1)),X(o("input",{"onUpdate:modelValue":t[11]||(t[11]=a=>n.newEvent.event_date=a),type:"datetime-local",class:"input-field",required:""},null,512),[[he,n.newEvent.event_date]])]),o("div",null,[t[35]||(t[35]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Prix du billet ($)",-1)),X(o("input",{"onUpdate:modelValue":t[12]||(t[12]=a=>n.newEvent.ticket_price=a),type:"number",step:"0.01",class:"input-field",placeholder:"25.00",required:""},null,512),[[he,n.newEvent.ticket_price]])]),o("div",null,[t[36]||(t[36]=o("label",{class:"block text-sm font-medium text-accent-700 mb-2"},"Nombre de places",-1)),X(o("input",{"onUpdate:modelValue":t[13]||(t[13]=a=>n.newEvent.max_attendees=a),type:"number",class:"input-field",placeholder:"100",required:""},null,512),[[he,n.newEvent.max_attendees]])])]),o("div",Xv,[o("button",{type:"button",onClick:t[14]||(t[14]=(...a)=>i.resetForm&&i.resetForm(...a)),class:"btn-outline"},"Annuler"),o("button",{type:"submit",disabled:n.creating,class:"btn-primary"},[n.creating?(R(),T("span",Yv)):le("",!0),q(" "+b(n.creating?"Création...":"Créer l'événement"),1)],8,Qv)])],32)])])):le("",!0),n.activeTab==="analytics"?(R(),T("div",Zv,[t[40]||(t[40]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Statistiques et analyses",-1)),o("div",e2,[t[39]||(t[39]=o("div",{class:"card"},[o("h3",{class:"text-lg font-bold text-accent-900 mb-4"},"Revenus par mois"),o("div",{class:"h-64 flex items-center justify-center bg-accent-50 rounded-lg"},[o("p",{class:"text-accent-500"},"Graphique des revenus (à implémenter)")])],-1)),o("div",t2,[t[38]||(t[38]=o("h3",{class:"text-lg font-bold text-accent-900 mb-4"},"Événements populaires",-1)),o("div",s2,[(R(!0),T(ge,null,Te(n.popularEvents,a=>(R(),T("div",{key:a.id,class:"flex justify-between items-center p-3 bg-accent-50 rounded-lg"},[o("div",null,[o("p",n2,b(a.title),1),o("p",r2,b(a.reservations_count)+" réservations",1)]),o("div",o2,[o("p",i2,"$"+b(a.revenue),1)])]))),128))])])])])):le("",!0)])])}const a2=qe(Yg,[["render",l2],["__scopeId","data-v-6c3dd2e8"]]),c2={name:"AdminDashboard",data(){return{user:null,activeTab:"overview",adminStats:{totalUsers:0,totalEvents:0,totalReservations:0,totalRevenue:0},users:[],events:[],reservations:[],recentActivity:[],loading:!0,showCreateUserModal:!1}},methods:{async fetchData(){try{const e=await this.$http.get("/me");this.user=e.data.user;const t=await this.$http.get("/admin/stats");this.adminStats=t.data;const s=await this.$http.get("/admin/users");s.data.data&&s.data.data.data?this.users=s.data.data.data:s.data.data?this.users=s.data.data:this.users=s.data;const r=await this.$http.get("/admin/events");r.data.data&&r.data.data.data?this.events=r.data.data.data:r.data.data?this.events=r.data.data:this.events=r.data;const n=await this.$http.get("/admin/reservations");n.data.data&&n.data.data.data?this.reservations=n.data.data.data:n.data.data?this.reservations=n.data.data:this.reservations=n.data}catch(e){console.error("Error fetching admin data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"short",day:"numeric"})},getRoleClass(e){switch(e){case"admin":return"bg-danger-100 text-danger-800";case"organizer":return"bg-secondary-100 text-secondary-800";default:return"bg-primary-100 text-primary-800"}},getRoleText(e){switch(e){case"admin":return"Administrateur";case"organizer":return"Organisateur";default:return"Utilisateur"}},getStatusClass(e){switch(e){case"paid":return"status-active";case"pending":return"bg-secondary-100 text-secondary-800";case"cancelled":return"status-inactive";default:return"bg-accent-100 text-accent-800"}},getStatusText(e){switch(e){case"paid":return"Payé";case"pending":return"En attente";case"cancelled":return"Annulé";default:return e}},editUser(e){console.log("Edit user:",e)},deleteUser(e){console.log("Delete user:",e)},editEvent(e){console.log("Edit event:",e)},deleteEvent(e){console.log("Delete event:",e)},viewReservation(e){console.log("View reservation:",e)}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchData()}},u2={class:"min-h-screen"},d2={class:"bg-white shadow-sm"},f2={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},p2={class:"flex justify-between items-center"},m2={class:"text-accent-600 mt-1"},h2={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},g2={class:"mb-8"},v2={class:"flex space-x-8"},x2={key:0,class:"space-y-8"},b2={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},y2={class:"card text-center"},w2={class:"text-3xl font-bold text-primary-600 mb-2"},_2={class:"card text-center"},k2={class:"text-3xl font-bold text-secondary-600 mb-2"},S2={class:"card text-center"},E2={class:"text-3xl font-bold text-success-600 mb-2"},C2={class:"card text-center"},R2={class:"text-3xl font-bold text-accent-600 mb-2"},T2={class:"card"},A2={class:"space-y-4"},O2={class:"flex-1"},$2={class:"text-accent-900 font-medium"},P2={class:"text-accent-500 text-sm"},M2={key:1,class:"space-y-6"},j2={class:"flex justify-between items-center"},D2={class:"card"},I2={class:"overflow-x-auto"},N2={class:"min-w-full divide-y divide-accent-200"},F2={class:"bg-white divide-y divide-accent-200"},U2={class:"px-6 py-4 whitespace-nowrap"},V2={class:"flex items-center"},L2={class:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center"},B2={class:"text-primary-600 font-medium"},z2={class:"ml-4"},q2={class:"text-sm font-medium text-accent-900"},H2={class:"text-sm text-accent-500"},K2={class:"px-6 py-4 whitespace-nowrap"},W2={class:"px-6 py-4 whitespace-nowrap"},J2={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-500"},G2={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},X2=["onClick"],Q2=["onClick"],Y2={key:2,class:"space-y-6"},Z2={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},ex={class:"event-image"},tx={class:"text-center"},sx={class:"text-white text-2xl font-bold"},nx={class:"p-6"},rx={class:"text-lg font-bold text-accent-900 mb-2"},ox={class:"text-accent-600 text-sm mb-4"},ix={class:"flex justify-between items-center mb-4"},lx={class:"price-tag"},ax={class:"flex space-x-2"},cx=["onClick"],ux=["onClick"],dx={key:3,class:"space-y-6"},fx={class:"card"},px={class:"overflow-x-auto"},mx={class:"min-w-full divide-y divide-accent-200"},hx={class:"bg-white divide-y divide-accent-200"},gx={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-accent-900"},vx={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-900"},xx={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-900"},bx={class:"px-6 py-4 whitespace-nowrap text-sm text-accent-900"},yx={class:"px-6 py-4 whitespace-nowrap"},wx={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},_x=["onClick"];function kx(e,t,s,r,n,i){var l;return R(),T("div",u2,[o("div",d2,[o("div",f2,[o("div",p2,[o("div",null,[t[5]||(t[5]=o("h1",{class:"text-3xl font-bold text-gradient"},"Dashboard Administrateur",-1)),o("p",m2,"Bienvenue, "+b((l=n.user)==null?void 0:l.name),1)]),t[6]||(t[6]=o("div",{class:"flex items-center space-x-4"},[o("span",{class:"status-badge bg-danger-100 text-danger-800"}," Administrateur ")],-1))])])]),o("div",h2,[o("div",g2,[o("nav",v2,[o("button",{onClick:t[0]||(t[0]=a=>n.activeTab="overview"),class:oe([n.activeTab==="overview"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Vue d'ensemble ",2),o("button",{onClick:t[1]||(t[1]=a=>n.activeTab="users"),class:oe([n.activeTab==="users"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Utilisateurs ",2),o("button",{onClick:t[2]||(t[2]=a=>n.activeTab="events"),class:oe([n.activeTab==="events"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Événements ",2),o("button",{onClick:t[3]||(t[3]=a=>n.activeTab="reservations"),class:oe([n.activeTab==="reservations"?"border-primary-500 text-primary-600":"border-transparent text-accent-500 hover:text-accent-700","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"])}," Réservations ",2)])]),n.activeTab==="overview"?(R(),T("div",x2,[o("div",b2,[o("div",y2,[t[7]||(t[7]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})])],-1)),o("div",w2,b(n.adminStats.totalUsers),1),t[8]||(t[8]=o("div",{class:"text-accent-600 font-medium"},"Utilisateurs",-1))]),o("div",_2,[t[9]||(t[9]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),o("div",k2,b(n.adminStats.totalEvents),1),t[10]||(t[10]=o("div",{class:"text-accent-600 font-medium"},"Événements",-1))]),o("div",S2,[t[11]||(t[11]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),o("div",E2,b(n.adminStats.totalReservations),1),t[12]||(t[12]=o("div",{class:"text-accent-600 font-medium"},"Réservations",-1))]),o("div",C2,[t[13]||(t[13]=o("div",{class:"w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4"},[o("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),o("div",R2,"$"+b(n.adminStats.totalRevenue),1),t[14]||(t[14]=o("div",{class:"text-accent-600 font-medium"},"Revenus",-1))])]),o("div",T2,[t[16]||(t[16]=o("h3",{class:"text-xl font-bold text-accent-900 mb-6"},"Activité récente",-1)),o("div",A2,[(R(!0),T(ge,null,Te(n.recentActivity,a=>(R(),T("div",{key:a.id,class:"flex items-center space-x-4 p-4 bg-accent-50 rounded-lg"},[t[15]||(t[15]=o("div",{class:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center"},[o("svg",{class:"w-5 h-5 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),o("div",O2,[o("p",$2,b(a.description),1),o("p",P2,b(i.formatDate(a.created_at)),1)])]))),128))])])])):le("",!0),n.activeTab==="users"?(R(),T("div",M2,[o("div",j2,[t[18]||(t[18]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Gestion des utilisateurs",-1)),o("button",{onClick:t[4]||(t[4]=a=>n.showCreateUserModal=!0),class:"btn-primary"},t[17]||(t[17]=[o("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),q(" Nouvel utilisateur ")]))]),o("div",D2,[o("div",I2,[o("table",N2,[t[19]||(t[19]=o("thead",{class:"bg-accent-50"},[o("tr",null,[o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Utilisateur"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Rôle"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Statut"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Inscrit le"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Actions")])],-1)),o("tbody",F2,[(R(!0),T(ge,null,Te(n.users,a=>(R(),T("tr",{key:a.id,class:"hover:bg-accent-50"},[o("td",U2,[o("div",V2,[o("div",L2,[o("span",B2,b(a.name.charAt(0).toUpperCase()),1)]),o("div",z2,[o("div",q2,b(a.name),1),o("div",H2,b(a.email),1)])])]),o("td",K2,[o("span",{class:oe([i.getRoleClass(a.role),"status-badge"])},b(i.getRoleText(a.role)),3)]),o("td",W2,[o("span",{class:oe([a.email_verified_at?"status-active":"status-inactive","status-badge"])},b(a.email_verified_at?"Vérifié":"Non vérifié"),3)]),o("td",J2,b(i.formatDate(a.created_at)),1),o("td",G2,[o("button",{onClick:c=>i.editUser(a),class:"text-primary-600 hover:text-primary-900 mr-3"},"Modifier",8,X2),o("button",{onClick:c=>i.deleteUser(a),class:"text-danger-600 hover:text-danger-900"},"Supprimer",8,Q2)])]))),128))])])])])])):le("",!0),n.activeTab==="events"?(R(),T("div",Y2,[t[20]||(t[20]=o("div",{class:"flex justify-between items-center"},[o("h2",{class:"text-2xl font-bold text-accent-900"},"Gestion des événements")],-1)),o("div",Z2,[(R(!0),T(ge,null,Te(n.events,a=>(R(),T("div",{key:a.id,class:"event-card"},[o("div",ex,[o("div",tx,[o("span",sx,b(a.title.substring(0,2).toUpperCase()),1)])]),o("div",nx,[o("h3",rx,b(a.title),1),o("p",ox,b(a.description.substring(0,100))+"...",1),o("div",ix,[o("span",lx,"$"+b(a.ticket_price),1),o("span",{class:oe([a.status==="active"?"status-active":"status-inactive","status-badge"])},b(a.status==="active"?"Actif":"Inactif"),3)]),o("div",ax,[o("button",{onClick:c=>i.editEvent(a),class:"btn-primary text-sm px-3 py-1 flex-1"},"Modifier",8,cx),o("button",{onClick:c=>i.deleteEvent(a),class:"btn-outline text-sm px-3 py-1"},"Supprimer",8,ux)])])]))),128))])])):le("",!0),n.activeTab==="reservations"?(R(),T("div",dx,[t[22]||(t[22]=o("h2",{class:"text-2xl font-bold text-accent-900"},"Gestion des réservations",-1)),o("div",fx,[o("div",px,[o("table",mx,[t[21]||(t[21]=o("thead",{class:"bg-accent-50"},[o("tr",null,[o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Réservation"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Utilisateur"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Événement"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Montant"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Statut"),o("th",{class:"px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider"},"Actions")])],-1)),o("tbody",hx,[(R(!0),T(ge,null,Te(n.reservations,a=>{var c,d;return R(),T("tr",{key:a.id,class:"hover:bg-accent-50"},[o("td",gx," #"+b(a.id),1),o("td",vx,b((c=a.user)==null?void 0:c.name),1),o("td",xx,b((d=a.event)==null?void 0:d.title),1),o("td",bx," $"+b(a.total_amount),1),o("td",yx,[o("span",{class:oe([i.getStatusClass(a.status),"status-badge"])},b(i.getStatusText(a.status)),3)]),o("td",wx,[o("button",{onClick:u=>i.viewReservation(a),class:"text-primary-600 hover:text-primary-900"},"Voir",8,_x)])])}),128))])])])])])):le("",!0)])])}const Sx=qe(c2,[["render",kx]]);pe.defaults.baseURL="/api";pe.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";const Jo=localStorage.getItem("auth_token");Jo&&(pe.defaults.headers.common.Authorization=`Bearer ${Jo}`);const Ex=[{path:"/",name:"home",component:Kp},{path:"/login",name:"login",component:rm},{path:"/register",name:"register",component:hm},{path:"/events",name:"events",component:Im},{path:"/events/:id",name:"event-detail",component:wh,props:!0},{path:"/checkout/:eventId",name:"checkout",component:M0,props:!0,meta:{requiresAuth:!0}},{path:"/dashboard",name:"dashboard",component:A1,meta:{requiresAuth:!0}},{path:"/dashboard/user",name:"user-dashboard",component:Qg,meta:{requiresAuth:!0,role:"user"}},{path:"/dashboard/organizer",name:"organizer-dashboard",component:a2,meta:{requiresAuth:!0,role:"organizer"}},{path:"/dashboard/admin",name:"admin-dashboard",component:Sx,meta:{requiresAuth:!0,role:"admin"}}],zl=Hf({history:yf(),routes:Ex});zl.beforeEach((e,t,s)=>{const r=localStorage.getItem("auth_token"),n=localStorage.getItem("user"),i=n?JSON.parse(n):null;if(e.meta.requiresAuth&&!r){s("/login");return}if(e.path==="/dashboard")if(i)switch(i.role){case"admin":s("/dashboard/admin");return;case"organizer":s("/dashboard/organizer");return;case"user":s("/dashboard/user");return;default:s("/dashboard/user");return}else if(r){pe.get("/me",{headers:{Authorization:`Bearer ${r}`}}).then(l=>{const a=l.data.user;switch(localStorage.setItem("user",JSON.stringify(a)),a.role){case"admin":s("/dashboard/admin");return;case"organizer":s("/dashboard/organizer");return;case"user":s("/dashboard/user");return;default:s("/dashboard/user");return}}).catch(()=>{localStorage.removeItem("auth_token"),localStorage.removeItem("user"),s("/login")});return}else{s("/login");return}if(e.meta.role&&i&&i.role!==e.meta.role)switch(i.role){case"admin":s("/dashboard/admin");return;case"organizer":s("/dashboard/organizer");return;case"user":s("/dashboard/user");return;default:s("/");return}s()});const jr=Vd(gp);jr.use(zl);jr.config.globalProperties.$http=pe;jr.mount("#app");
