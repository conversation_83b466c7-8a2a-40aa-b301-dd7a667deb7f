function Vo(e,t){return function(){return e.apply(t,arguments)}}const{toString:Nl}=Object.prototype,{getPrototypeOf:rr}=Object,{iterator:sn,toStringTag:Ho}=Symbol,nn=(e=>t=>{const s=Nl.call(t);return e[s]||(e[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Ve=e=>(e=e.toLowerCase(),t=>nn(t)===e),rn=e=>t=>typeof t===e,{isArray:Yt}=Array,ys=rn("undefined");function Dl(e){return e!==null&&!ys(e)&&e.constructor!==null&&!ys(e.constructor)&&ke(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const zo=Ve("ArrayBuffer");function jl(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&zo(e.buffer),t}const Fl=rn("string"),ke=rn("function"),qo=rn("number"),on=e=>e!==null&&typeof e=="object",Il=e=>e===!0||e===!1,js=e=>{if(nn(e)!=="object")return!1;const t=rr(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ho in e)&&!(sn in e)},Ll=Ve("Date"),Ul=Ve("File"),Bl=Ve("Blob"),Vl=Ve("FileList"),Hl=e=>on(e)&&ke(e.pipe),zl=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||ke(e.append)&&((t=nn(e))==="formdata"||t==="object"&&ke(e.toString)&&e.toString()==="[object FormData]"))},ql=Ve("URLSearchParams"),[Kl,Wl,Jl,Gl]=["ReadableStream","Request","Response","Headers"].map(Ve),Xl=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Cs(e,t,{allOwnKeys:s=!1}={}){if(e===null||typeof e>"u")return;let n,r;if(typeof e!="object"&&(e=[e]),Yt(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const o=s?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(n=0;n<i;n++)l=o[n],t.call(null,e[l],l,e)}}function Ko(e,t){t=t.toLowerCase();const s=Object.keys(e);let n=s.length,r;for(;n-- >0;)if(r=s[n],t===r.toLowerCase())return r;return null}const Ot=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Wo=e=>!ys(e)&&e!==Ot;function Ln(){const{caseless:e}=Wo(this)&&this||{},t={},s=(n,r)=>{const o=e&&Ko(t,r)||r;js(t[o])&&js(n)?t[o]=Ln(t[o],n):js(n)?t[o]=Ln({},n):Yt(n)?t[o]=n.slice():t[o]=n};for(let n=0,r=arguments.length;n<r;n++)arguments[n]&&Cs(arguments[n],s);return t}const Ql=(e,t,s,{allOwnKeys:n}={})=>(Cs(t,(r,o)=>{s&&ke(r)?e[o]=Vo(r,s):e[o]=r},{allOwnKeys:n}),e),Yl=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Zl=(e,t,s,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),s&&Object.assign(e.prototype,s)},ea=(e,t,s,n)=>{let r,o,i;const l={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),o=r.length;o-- >0;)i=r[o],(!n||n(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=s!==!1&&rr(e)}while(e&&(!s||s(e,t))&&e!==Object.prototype);return t},ta=(e,t,s)=>{e=String(e),(s===void 0||s>e.length)&&(s=e.length),s-=t.length;const n=e.indexOf(t,s);return n!==-1&&n===s},sa=e=>{if(!e)return null;if(Yt(e))return e;let t=e.length;if(!qo(t))return null;const s=new Array(t);for(;t-- >0;)s[t]=e[t];return s},na=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&rr(Uint8Array)),ra=(e,t)=>{const n=(e&&e[sn]).call(e);let r;for(;(r=n.next())&&!r.done;){const o=r.value;t.call(e,o[0],o[1])}},oa=(e,t)=>{let s;const n=[];for(;(s=e.exec(t))!==null;)n.push(s);return n},ia=Ve("HTMLFormElement"),la=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,n,r){return n.toUpperCase()+r}),Mr=(({hasOwnProperty:e})=>(t,s)=>e.call(t,s))(Object.prototype),aa=Ve("RegExp"),Jo=(e,t)=>{const s=Object.getOwnPropertyDescriptors(e),n={};Cs(s,(r,o)=>{let i;(i=t(r,o,e))!==!1&&(n[o]=i||r)}),Object.defineProperties(e,n)},ca=e=>{Jo(e,(t,s)=>{if(ke(e)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const n=e[s];if(ke(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},ua=(e,t)=>{const s={},n=r=>{r.forEach(o=>{s[o]=!0})};return Yt(e)?n(e):n(String(e).split(t)),s},fa=()=>{},da=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function pa(e){return!!(e&&ke(e.append)&&e[Ho]==="FormData"&&e[sn])}const ha=e=>{const t=new Array(10),s=(n,r)=>{if(on(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[r]=n;const o=Yt(n)?[]:{};return Cs(n,(i,l)=>{const a=s(i,r+1);!ys(a)&&(o[l]=a)}),t[r]=void 0,o}}return n};return s(e,0)},ma=Ve("AsyncFunction"),ga=e=>e&&(on(e)||ke(e))&&ke(e.then)&&ke(e.catch),Go=((e,t)=>e?setImmediate:t?((s,n)=>(Ot.addEventListener("message",({source:r,data:o})=>{r===Ot&&o===s&&n.length&&n.shift()()},!1),r=>{n.push(r),Ot.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",ke(Ot.postMessage)),ya=typeof queueMicrotask<"u"?queueMicrotask.bind(Ot):typeof process<"u"&&process.nextTick||Go,ba=e=>e!=null&&ke(e[sn]),b={isArray:Yt,isArrayBuffer:zo,isBuffer:Dl,isFormData:zl,isArrayBufferView:jl,isString:Fl,isNumber:qo,isBoolean:Il,isObject:on,isPlainObject:js,isReadableStream:Kl,isRequest:Wl,isResponse:Jl,isHeaders:Gl,isUndefined:ys,isDate:Ll,isFile:Ul,isBlob:Bl,isRegExp:aa,isFunction:ke,isStream:Hl,isURLSearchParams:ql,isTypedArray:na,isFileList:Vl,forEach:Cs,merge:Ln,extend:Ql,trim:Xl,stripBOM:Yl,inherits:Zl,toFlatObject:ea,kindOf:nn,kindOfTest:Ve,endsWith:ta,toArray:sa,forEachEntry:ra,matchAll:oa,isHTMLForm:ia,hasOwnProperty:Mr,hasOwnProp:Mr,reduceDescriptors:Jo,freezeMethods:ca,toObjectSet:ua,toCamelCase:la,noop:fa,toFiniteNumber:da,findKey:Ko,global:Ot,isContextDefined:Wo,isSpecCompliantForm:pa,toJSONObject:ha,isAsyncFn:ma,isThenable:ga,setImmediate:Go,asap:ya,isIterable:ba};function K(e,t,s,n,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),s&&(this.config=s),n&&(this.request=n),r&&(this.response=r,this.status=r.status?r.status:null)}b.inherits(K,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:b.toJSONObject(this.config),code:this.code,status:this.status}}});const Xo=K.prototype,Qo={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Qo[e]={value:e}});Object.defineProperties(K,Qo);Object.defineProperty(Xo,"isAxiosError",{value:!0});K.from=(e,t,s,n,r,o)=>{const i=Object.create(Xo);return b.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),K.call(i,e.message,t,s,n,r),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const xa=null;function Un(e){return b.isPlainObject(e)||b.isArray(e)}function Yo(e){return b.endsWith(e,"[]")?e.slice(0,-2):e}function $r(e,t,s){return e?e.concat(t).map(function(r,o){return r=Yo(r),!s&&o?"["+r+"]":r}).join(s?".":""):t}function va(e){return b.isArray(e)&&!e.some(Un)}const _a=b.toFlatObject(b,{},null,function(t){return/^is[A-Z]/.test(t)});function ln(e,t,s){if(!b.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,s=b.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(E,R){return!b.isUndefined(R[E])});const n=s.metaTokens,r=s.visitor||c,o=s.dots,i=s.indexes,a=(s.Blob||typeof Blob<"u"&&Blob)&&b.isSpecCompliantForm(t);if(!b.isFunction(r))throw new TypeError("visitor must be a function");function f(x){if(x===null)return"";if(b.isDate(x))return x.toISOString();if(!a&&b.isBlob(x))throw new K("Blob is not supported. Use a Buffer instead.");return b.isArrayBuffer(x)||b.isTypedArray(x)?a&&typeof Blob=="function"?new Blob([x]):Buffer.from(x):x}function c(x,E,R){let O=x;if(x&&!R&&typeof x=="object"){if(b.endsWith(E,"{}"))E=n?E:E.slice(0,-2),x=JSON.stringify(x);else if(b.isArray(x)&&va(x)||(b.isFileList(x)||b.endsWith(E,"[]"))&&(O=b.toArray(x)))return E=Yo(E),O.forEach(function(N,D){!(b.isUndefined(N)||N===null)&&t.append(i===!0?$r([E],D,o):i===null?E:E+"[]",f(N))}),!1}return Un(x)?!0:(t.append($r(R,E,o),f(x)),!1)}const d=[],m=Object.assign(_a,{defaultVisitor:c,convertValue:f,isVisitable:Un});function g(x,E){if(!b.isUndefined(x)){if(d.indexOf(x)!==-1)throw Error("Circular reference detected in "+E.join("."));d.push(x),b.forEach(x,function(O,T){(!(b.isUndefined(O)||O===null)&&r.call(t,O,b.isString(T)?T.trim():T,E,m))===!0&&g(O,E?E.concat(T):[T])}),d.pop()}}if(!b.isObject(e))throw new TypeError("data must be an object");return g(e),t}function Nr(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function or(e,t){this._pairs=[],e&&ln(e,this,t)}const Zo=or.prototype;Zo.append=function(t,s){this._pairs.push([t,s])};Zo.toString=function(t){const s=t?function(n){return t.call(this,n,Nr)}:Nr;return this._pairs.map(function(r){return s(r[0])+"="+s(r[1])},"").join("&")};function wa(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ei(e,t,s){if(!t)return e;const n=s&&s.encode||wa;b.isFunction(s)&&(s={serialize:s});const r=s&&s.serialize;let o;if(r?o=r(t,s):o=b.isURLSearchParams(t)?t.toString():new or(t,s).toString(n),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Dr{constructor(){this.handlers=[]}use(t,s,n){return this.handlers.push({fulfilled:t,rejected:s,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){b.forEach(this.handlers,function(n){n!==null&&t(n)})}}const ti={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ea=typeof URLSearchParams<"u"?URLSearchParams:or,Sa=typeof FormData<"u"?FormData:null,Ra=typeof Blob<"u"?Blob:null,Ca={isBrowser:!0,classes:{URLSearchParams:Ea,FormData:Sa,Blob:Ra},protocols:["http","https","file","blob","url","data"]},ir=typeof window<"u"&&typeof document<"u",Bn=typeof navigator=="object"&&navigator||void 0,ka=ir&&(!Bn||["ReactNative","NativeScript","NS"].indexOf(Bn.product)<0),Aa=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Oa=ir&&window.location.href||"http://localhost",Ta=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ir,hasStandardBrowserEnv:ka,hasStandardBrowserWebWorkerEnv:Aa,navigator:Bn,origin:Oa},Symbol.toStringTag,{value:"Module"})),ge={...Ta,...Ca};function Pa(e,t){return ln(e,new ge.classes.URLSearchParams,Object.assign({visitor:function(s,n,r,o){return ge.isNode&&b.isBuffer(s)?(this.append(n,s.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Ma(e){return b.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function $a(e){const t={},s=Object.keys(e);let n;const r=s.length;let o;for(n=0;n<r;n++)o=s[n],t[o]=e[o];return t}function si(e){function t(s,n,r,o){let i=s[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=o>=s.length;return i=!i&&b.isArray(r)?r.length:i,a?(b.hasOwnProp(r,i)?r[i]=[r[i],n]:r[i]=n,!l):((!r[i]||!b.isObject(r[i]))&&(r[i]=[]),t(s,n,r[i],o)&&b.isArray(r[i])&&(r[i]=$a(r[i])),!l)}if(b.isFormData(e)&&b.isFunction(e.entries)){const s={};return b.forEachEntry(e,(n,r)=>{t(Ma(n),r,s,0)}),s}return null}function Na(e,t,s){if(b.isString(e))try{return(t||JSON.parse)(e),b.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(s||JSON.stringify)(e)}const ks={transitional:ti,adapter:["xhr","http","fetch"],transformRequest:[function(t,s){const n=s.getContentType()||"",r=n.indexOf("application/json")>-1,o=b.isObject(t);if(o&&b.isHTMLForm(t)&&(t=new FormData(t)),b.isFormData(t))return r?JSON.stringify(si(t)):t;if(b.isArrayBuffer(t)||b.isBuffer(t)||b.isStream(t)||b.isFile(t)||b.isBlob(t)||b.isReadableStream(t))return t;if(b.isArrayBufferView(t))return t.buffer;if(b.isURLSearchParams(t))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Pa(t,this.formSerializer).toString();if((l=b.isFileList(t))||n.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return ln(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return o||r?(s.setContentType("application/json",!1),Na(t)):t}],transformResponse:[function(t){const s=this.transitional||ks.transitional,n=s&&s.forcedJSONParsing,r=this.responseType==="json";if(b.isResponse(t)||b.isReadableStream(t))return t;if(t&&b.isString(t)&&(n&&!this.responseType||r)){const i=!(s&&s.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?K.from(l,K.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ge.classes.FormData,Blob:ge.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};b.forEach(["delete","get","head","post","put","patch"],e=>{ks.headers[e]={}});const Da=b.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ja=e=>{const t={};let s,n,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),s=i.substring(0,r).trim().toLowerCase(),n=i.substring(r+1).trim(),!(!s||t[s]&&Da[s])&&(s==="set-cookie"?t[s]?t[s].push(n):t[s]=[n]:t[s]=t[s]?t[s]+", "+n:n)}),t},jr=Symbol("internals");function ns(e){return e&&String(e).trim().toLowerCase()}function Fs(e){return e===!1||e==null?e:b.isArray(e)?e.map(Fs):String(e)}function Fa(e){const t=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=s.exec(e);)t[n[1]]=n[2];return t}const Ia=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Sn(e,t,s,n,r){if(b.isFunction(n))return n.call(this,t,s);if(r&&(t=s),!!b.isString(t)){if(b.isString(n))return t.indexOf(n)!==-1;if(b.isRegExp(n))return n.test(t)}}function La(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,s,n)=>s.toUpperCase()+n)}function Ua(e,t){const s=b.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+s,{value:function(r,o,i){return this[n].call(this,t,r,o,i)},configurable:!0})})}let Ae=class{constructor(t){t&&this.set(t)}set(t,s,n){const r=this;function o(l,a,f){const c=ns(a);if(!c)throw new Error("header name must be a non-empty string");const d=b.findKey(r,c);(!d||r[d]===void 0||f===!0||f===void 0&&r[d]!==!1)&&(r[d||a]=Fs(l))}const i=(l,a)=>b.forEach(l,(f,c)=>o(f,c,a));if(b.isPlainObject(t)||t instanceof this.constructor)i(t,s);else if(b.isString(t)&&(t=t.trim())&&!Ia(t))i(ja(t),s);else if(b.isObject(t)&&b.isIterable(t)){let l={},a,f;for(const c of t){if(!b.isArray(c))throw TypeError("Object iterator must return a key-value pair");l[f=c[0]]=(a=l[f])?b.isArray(a)?[...a,c[1]]:[a,c[1]]:c[1]}i(l,s)}else t!=null&&o(s,t,n);return this}get(t,s){if(t=ns(t),t){const n=b.findKey(this,t);if(n){const r=this[n];if(!s)return r;if(s===!0)return Fa(r);if(b.isFunction(s))return s.call(this,r,n);if(b.isRegExp(s))return s.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,s){if(t=ns(t),t){const n=b.findKey(this,t);return!!(n&&this[n]!==void 0&&(!s||Sn(this,this[n],n,s)))}return!1}delete(t,s){const n=this;let r=!1;function o(i){if(i=ns(i),i){const l=b.findKey(n,i);l&&(!s||Sn(n,n[l],l,s))&&(delete n[l],r=!0)}}return b.isArray(t)?t.forEach(o):o(t),r}clear(t){const s=Object.keys(this);let n=s.length,r=!1;for(;n--;){const o=s[n];(!t||Sn(this,this[o],o,t,!0))&&(delete this[o],r=!0)}return r}normalize(t){const s=this,n={};return b.forEach(this,(r,o)=>{const i=b.findKey(n,o);if(i){s[i]=Fs(r),delete s[o];return}const l=t?La(o):String(o).trim();l!==o&&delete s[o],s[l]=Fs(r),n[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const s=Object.create(null);return b.forEach(this,(n,r)=>{n!=null&&n!==!1&&(s[r]=t&&b.isArray(n)?n.join(", "):n)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,s])=>t+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...s){const n=new this(t);return s.forEach(r=>n.set(r)),n}static accessor(t){const n=(this[jr]=this[jr]={accessors:{}}).accessors,r=this.prototype;function o(i){const l=ns(i);n[l]||(Ua(r,i),n[l]=!0)}return b.isArray(t)?t.forEach(o):o(t),this}};Ae.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);b.reduceDescriptors(Ae.prototype,({value:e},t)=>{let s=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[s]=n}}});b.freezeMethods(Ae);function Rn(e,t){const s=this||ks,n=t||s,r=Ae.from(n.headers);let o=n.data;return b.forEach(e,function(l){o=l.call(s,o,r.normalize(),t?t.status:void 0)}),r.normalize(),o}function ni(e){return!!(e&&e.__CANCEL__)}function Zt(e,t,s){K.call(this,e??"canceled",K.ERR_CANCELED,t,s),this.name="CanceledError"}b.inherits(Zt,K,{__CANCEL__:!0});function ri(e,t,s){const n=s.config.validateStatus;!s.status||!n||n(s.status)?e(s):t(new K("Request failed with status code "+s.status,[K.ERR_BAD_REQUEST,K.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function Ba(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Va(e,t){e=e||10;const s=new Array(e),n=new Array(e);let r=0,o=0,i;return t=t!==void 0?t:1e3,function(a){const f=Date.now(),c=n[o];i||(i=f),s[r]=a,n[r]=f;let d=o,m=0;for(;d!==r;)m+=s[d++],d=d%e;if(r=(r+1)%e,r===o&&(o=(o+1)%e),f-i<t)return;const g=c&&f-c;return g?Math.round(m*1e3/g):void 0}}function Ha(e,t){let s=0,n=1e3/t,r,o;const i=(f,c=Date.now())=>{s=c,r=null,o&&(clearTimeout(o),o=null),e.apply(null,f)};return[(...f)=>{const c=Date.now(),d=c-s;d>=n?i(f,c):(r=f,o||(o=setTimeout(()=>{o=null,i(r)},n-d)))},()=>r&&i(r)]}const qs=(e,t,s=3)=>{let n=0;const r=Va(50,250);return Ha(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,a=i-n,f=r(a),c=i<=l;n=i;const d={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:f||void 0,estimated:f&&l&&c?(l-i)/f:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(d)},s)},Fr=(e,t)=>{const s=e!=null;return[n=>t[0]({lengthComputable:s,total:e,loaded:n}),t[1]]},Ir=e=>(...t)=>b.asap(()=>e(...t)),za=ge.hasStandardBrowserEnv?((e,t)=>s=>(s=new URL(s,ge.origin),e.protocol===s.protocol&&e.host===s.host&&(t||e.port===s.port)))(new URL(ge.origin),ge.navigator&&/(msie|trident)/i.test(ge.navigator.userAgent)):()=>!0,qa=ge.hasStandardBrowserEnv?{write(e,t,s,n,r,o){const i=[e+"="+encodeURIComponent(t)];b.isNumber(s)&&i.push("expires="+new Date(s).toGMTString()),b.isString(n)&&i.push("path="+n),b.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ka(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Wa(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function oi(e,t,s){let n=!Ka(t);return e&&(n||s==!1)?Wa(e,t):t}const Lr=e=>e instanceof Ae?{...e}:e;function $t(e,t){t=t||{};const s={};function n(f,c,d,m){return b.isPlainObject(f)&&b.isPlainObject(c)?b.merge.call({caseless:m},f,c):b.isPlainObject(c)?b.merge({},c):b.isArray(c)?c.slice():c}function r(f,c,d,m){if(b.isUndefined(c)){if(!b.isUndefined(f))return n(void 0,f,d,m)}else return n(f,c,d,m)}function o(f,c){if(!b.isUndefined(c))return n(void 0,c)}function i(f,c){if(b.isUndefined(c)){if(!b.isUndefined(f))return n(void 0,f)}else return n(void 0,c)}function l(f,c,d){if(d in t)return n(f,c);if(d in e)return n(void 0,f)}const a={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(f,c,d)=>r(Lr(f),Lr(c),d,!0)};return b.forEach(Object.keys(Object.assign({},e,t)),function(c){const d=a[c]||r,m=d(e[c],t[c],c);b.isUndefined(m)&&d!==l||(s[c]=m)}),s}const ii=e=>{const t=$t({},e);let{data:s,withXSRFToken:n,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=Ae.from(i),t.url=ei(oi(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(b.isFormData(s)){if(ge.hasStandardBrowserEnv||ge.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[f,...c]=a?a.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([f||"multipart/form-data",...c].join("; "))}}if(ge.hasStandardBrowserEnv&&(n&&b.isFunction(n)&&(n=n(t)),n||n!==!1&&za(t.url))){const f=r&&o&&qa.read(o);f&&i.set(r,f)}return t},Ja=typeof XMLHttpRequest<"u",Ga=Ja&&function(e){return new Promise(function(s,n){const r=ii(e);let o=r.data;const i=Ae.from(r.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:f}=r,c,d,m,g,x;function E(){g&&g(),x&&x(),r.cancelToken&&r.cancelToken.unsubscribe(c),r.signal&&r.signal.removeEventListener("abort",c)}let R=new XMLHttpRequest;R.open(r.method.toUpperCase(),r.url,!0),R.timeout=r.timeout;function O(){if(!R)return;const N=Ae.from("getAllResponseHeaders"in R&&R.getAllResponseHeaders()),z={data:!l||l==="text"||l==="json"?R.responseText:R.response,status:R.status,statusText:R.statusText,headers:N,config:e,request:R};ri(function(Q){s(Q),E()},function(Q){n(Q),E()},z),R=null}"onloadend"in R?R.onloadend=O:R.onreadystatechange=function(){!R||R.readyState!==4||R.status===0&&!(R.responseURL&&R.responseURL.indexOf("file:")===0)||setTimeout(O)},R.onabort=function(){R&&(n(new K("Request aborted",K.ECONNABORTED,e,R)),R=null)},R.onerror=function(){n(new K("Network Error",K.ERR_NETWORK,e,R)),R=null},R.ontimeout=function(){let D=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const z=r.transitional||ti;r.timeoutErrorMessage&&(D=r.timeoutErrorMessage),n(new K(D,z.clarifyTimeoutError?K.ETIMEDOUT:K.ECONNABORTED,e,R)),R=null},o===void 0&&i.setContentType(null),"setRequestHeader"in R&&b.forEach(i.toJSON(),function(D,z){R.setRequestHeader(z,D)}),b.isUndefined(r.withCredentials)||(R.withCredentials=!!r.withCredentials),l&&l!=="json"&&(R.responseType=r.responseType),f&&([m,x]=qs(f,!0),R.addEventListener("progress",m)),a&&R.upload&&([d,g]=qs(a),R.upload.addEventListener("progress",d),R.upload.addEventListener("loadend",g)),(r.cancelToken||r.signal)&&(c=N=>{R&&(n(!N||N.type?new Zt(null,e,R):N),R.abort(),R=null)},r.cancelToken&&r.cancelToken.subscribe(c),r.signal&&(r.signal.aborted?c():r.signal.addEventListener("abort",c)));const T=Ba(r.url);if(T&&ge.protocols.indexOf(T)===-1){n(new K("Unsupported protocol "+T+":",K.ERR_BAD_REQUEST,e));return}R.send(o||null)})},Xa=(e,t)=>{const{length:s}=e=e?e.filter(Boolean):[];if(t||s){let n=new AbortController,r;const o=function(f){if(!r){r=!0,l();const c=f instanceof Error?f:this.reason;n.abort(c instanceof K?c:new Zt(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,o(new K(`timeout ${t} of ms exceeded`,K.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(f=>{f.unsubscribe?f.unsubscribe(o):f.removeEventListener("abort",o)}),e=null)};e.forEach(f=>f.addEventListener("abort",o));const{signal:a}=n;return a.unsubscribe=()=>b.asap(l),a}},Qa=function*(e,t){let s=e.byteLength;if(s<t){yield e;return}let n=0,r;for(;n<s;)r=n+t,yield e.slice(n,r),n=r},Ya=async function*(e,t){for await(const s of Za(e))yield*Qa(s,t)},Za=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:s,value:n}=await t.read();if(s)break;yield n}}finally{await t.cancel()}},Ur=(e,t,s,n)=>{const r=Ya(e,t);let o=0,i,l=a=>{i||(i=!0,n&&n(a))};return new ReadableStream({async pull(a){try{const{done:f,value:c}=await r.next();if(f){l(),a.close();return}let d=c.byteLength;if(s){let m=o+=d;s(m)}a.enqueue(new Uint8Array(c))}catch(f){throw l(f),f}},cancel(a){return l(a),r.return()}},{highWaterMark:2})},an=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",li=an&&typeof ReadableStream=="function",ec=an&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ai=(e,...t)=>{try{return!!e(...t)}catch{return!1}},tc=li&&ai(()=>{let e=!1;const t=new Request(ge.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Br=64*1024,Vn=li&&ai(()=>b.isReadableStream(new Response("").body)),Ks={stream:Vn&&(e=>e.body)};an&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Ks[t]&&(Ks[t]=b.isFunction(e[t])?s=>s[t]():(s,n)=>{throw new K(`Response type '${t}' is not supported`,K.ERR_NOT_SUPPORT,n)})})})(new Response);const sc=async e=>{if(e==null)return 0;if(b.isBlob(e))return e.size;if(b.isSpecCompliantForm(e))return(await new Request(ge.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(b.isArrayBufferView(e)||b.isArrayBuffer(e))return e.byteLength;if(b.isURLSearchParams(e)&&(e=e+""),b.isString(e))return(await ec(e)).byteLength},nc=async(e,t)=>{const s=b.toFiniteNumber(e.getContentLength());return s??sc(t)},rc=an&&(async e=>{let{url:t,method:s,data:n,signal:r,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:f,headers:c,withCredentials:d="same-origin",fetchOptions:m}=ii(e);f=f?(f+"").toLowerCase():"text";let g=Xa([r,o&&o.toAbortSignal()],i),x;const E=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let R;try{if(a&&tc&&s!=="get"&&s!=="head"&&(R=await nc(c,n))!==0){let z=new Request(t,{method:"POST",body:n,duplex:"half"}),ie;if(b.isFormData(n)&&(ie=z.headers.get("content-type"))&&c.setContentType(ie),z.body){const[Q,ve]=Fr(R,qs(Ir(a)));n=Ur(z.body,Br,Q,ve)}}b.isString(d)||(d=d?"include":"omit");const O="credentials"in Request.prototype;x=new Request(t,{...m,signal:g,method:s.toUpperCase(),headers:c.normalize().toJSON(),body:n,duplex:"half",credentials:O?d:void 0});let T=await fetch(x);const N=Vn&&(f==="stream"||f==="response");if(Vn&&(l||N&&E)){const z={};["status","statusText","headers"].forEach(Ie=>{z[Ie]=T[Ie]});const ie=b.toFiniteNumber(T.headers.get("content-length")),[Q,ve]=l&&Fr(ie,qs(Ir(l),!0))||[];T=new Response(Ur(T.body,Br,Q,()=>{ve&&ve(),E&&E()}),z)}f=f||"text";let D=await Ks[b.findKey(Ks,f)||"text"](T,e);return!N&&E&&E(),await new Promise((z,ie)=>{ri(z,ie,{data:D,headers:Ae.from(T.headers),status:T.status,statusText:T.statusText,config:e,request:x})})}catch(O){throw E&&E(),O&&O.name==="TypeError"&&/Load failed|fetch/i.test(O.message)?Object.assign(new K("Network Error",K.ERR_NETWORK,e,x),{cause:O.cause||O}):K.from(O,O&&O.code,e,x)}}),Hn={http:xa,xhr:Ga,fetch:rc};b.forEach(Hn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Vr=e=>`- ${e}`,oc=e=>b.isFunction(e)||e===null||e===!1,ci={getAdapter:e=>{e=b.isArray(e)?e:[e];const{length:t}=e;let s,n;const r={};for(let o=0;o<t;o++){s=e[o];let i;if(n=s,!oc(s)&&(n=Hn[(i=String(s)).toLowerCase()],n===void 0))throw new K(`Unknown adapter '${i}'`);if(n)break;r[i||"#"+o]=n}if(!n){const o=Object.entries(r).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Vr).join(`
`):" "+Vr(o[0]):"as no adapter specified";throw new K("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:Hn};function Cn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Zt(null,e)}function Hr(e){return Cn(e),e.headers=Ae.from(e.headers),e.data=Rn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ci.getAdapter(e.adapter||ks.adapter)(e).then(function(n){return Cn(e),n.data=Rn.call(e,e.transformResponse,n),n.headers=Ae.from(n.headers),n},function(n){return ni(n)||(Cn(e),n&&n.response&&(n.response.data=Rn.call(e,e.transformResponse,n.response),n.response.headers=Ae.from(n.response.headers))),Promise.reject(n)})}const ui="1.9.0",cn={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{cn[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const zr={};cn.transitional=function(t,s,n){function r(o,i){return"[Axios v"+ui+"] Transitional option '"+o+"'"+i+(n?". "+n:"")}return(o,i,l)=>{if(t===!1)throw new K(r(i," has been removed"+(s?" in "+s:"")),K.ERR_DEPRECATED);return s&&!zr[i]&&(zr[i]=!0,console.warn(r(i," has been deprecated since v"+s+" and will be removed in the near future"))),t?t(o,i,l):!0}};cn.spelling=function(t){return(s,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function ic(e,t,s){if(typeof e!="object")throw new K("options must be an object",K.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let r=n.length;for(;r-- >0;){const o=n[r],i=t[o];if(i){const l=e[o],a=l===void 0||i(l,o,e);if(a!==!0)throw new K("option "+o+" must be "+a,K.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new K("Unknown option "+o,K.ERR_BAD_OPTION)}}const Is={assertOptions:ic,validators:cn},Ge=Is.validators;let Pt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Dr,response:new Dr}}async request(t,s){try{return await this._request(t,s)}catch(n){if(n instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(t,s){typeof t=="string"?(s=s||{},s.url=t):s=t||{},s=$t(this.defaults,s);const{transitional:n,paramsSerializer:r,headers:o}=s;n!==void 0&&Is.assertOptions(n,{silentJSONParsing:Ge.transitional(Ge.boolean),forcedJSONParsing:Ge.transitional(Ge.boolean),clarifyTimeoutError:Ge.transitional(Ge.boolean)},!1),r!=null&&(b.isFunction(r)?s.paramsSerializer={serialize:r}:Is.assertOptions(r,{encode:Ge.function,serialize:Ge.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Is.assertOptions(s,{baseUrl:Ge.spelling("baseURL"),withXsrfToken:Ge.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let i=o&&b.merge(o.common,o[s.method]);o&&b.forEach(["delete","get","head","post","put","patch","common"],x=>{delete o[x]}),s.headers=Ae.concat(i,o);const l=[];let a=!0;this.interceptors.request.forEach(function(E){typeof E.runWhen=="function"&&E.runWhen(s)===!1||(a=a&&E.synchronous,l.unshift(E.fulfilled,E.rejected))});const f=[];this.interceptors.response.forEach(function(E){f.push(E.fulfilled,E.rejected)});let c,d=0,m;if(!a){const x=[Hr.bind(this),void 0];for(x.unshift.apply(x,l),x.push.apply(x,f),m=x.length,c=Promise.resolve(s);d<m;)c=c.then(x[d++],x[d++]);return c}m=l.length;let g=s;for(d=0;d<m;){const x=l[d++],E=l[d++];try{g=x(g)}catch(R){E.call(this,R);break}}try{c=Hr.call(this,g)}catch(x){return Promise.reject(x)}for(d=0,m=f.length;d<m;)c=c.then(f[d++],f[d++]);return c}getUri(t){t=$t(this.defaults,t);const s=oi(t.baseURL,t.url,t.allowAbsoluteUrls);return ei(s,t.params,t.paramsSerializer)}};b.forEach(["delete","get","head","options"],function(t){Pt.prototype[t]=function(s,n){return this.request($t(n||{},{method:t,url:s,data:(n||{}).data}))}});b.forEach(["post","put","patch"],function(t){function s(n){return function(o,i,l){return this.request($t(l||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Pt.prototype[t]=s(),Pt.prototype[t+"Form"]=s(!0)});let lc=class fi{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(o){s=o});const n=this;this.promise.then(r=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](r);n._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(l=>{n.subscribe(l),o=l}).then(r);return i.cancel=function(){n.unsubscribe(o)},i},t(function(o,i,l){n.reason||(n.reason=new Zt(o,i,l),s(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const s=this._listeners.indexOf(t);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const t=new AbortController,s=n=>{t.abort(n)};return this.subscribe(s),t.signal.unsubscribe=()=>this.unsubscribe(s),t.signal}static source(){let t;return{token:new fi(function(r){t=r}),cancel:t}}};function ac(e){return function(s){return e.apply(null,s)}}function cc(e){return b.isObject(e)&&e.isAxiosError===!0}const zn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(zn).forEach(([e,t])=>{zn[t]=e});function di(e){const t=new Pt(e),s=Vo(Pt.prototype.request,t);return b.extend(s,Pt.prototype,t,{allOwnKeys:!0}),b.extend(s,t,null,{allOwnKeys:!0}),s.create=function(r){return di($t(e,r))},s}const ue=di(ks);ue.Axios=Pt;ue.CanceledError=Zt;ue.CancelToken=lc;ue.isCancel=ni;ue.VERSION=ui;ue.toFormData=ln;ue.AxiosError=K;ue.Cancel=ue.CanceledError;ue.all=function(t){return Promise.all(t)};ue.spread=ac;ue.isAxiosError=cc;ue.mergeConfig=$t;ue.AxiosHeaders=Ae;ue.formToJSON=e=>si(b.isHTMLForm(e)?new FormData(e):e);ue.getAdapter=ci.getAdapter;ue.HttpStatusCode=zn;ue.default=ue;const{Axios:Xm,AxiosError:Qm,CanceledError:Ym,isCancel:Zm,CancelToken:eg,VERSION:tg,all:sg,Cancel:ng,isAxiosError:rg,spread:og,toFormData:ig,AxiosHeaders:lg,HttpStatusCode:ag,formToJSON:cg,getAdapter:ug,mergeConfig:fg}=ue;window.axios=ue;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function lr(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const oe={},Vt=[],Ze=()=>{},uc=()=>!1,un=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ar=e=>e.startsWith("onUpdate:"),xe=Object.assign,cr=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},fc=Object.prototype.hasOwnProperty,te=(e,t)=>fc.call(e,t),B=Array.isArray,Ht=e=>As(e)==="[object Map]",es=e=>As(e)==="[object Set]",qr=e=>As(e)==="[object Date]",H=e=>typeof e=="function",de=e=>typeof e=="string",et=e=>typeof e=="symbol",ce=e=>e!==null&&typeof e=="object",pi=e=>(ce(e)||H(e))&&H(e.then)&&H(e.catch),hi=Object.prototype.toString,As=e=>hi.call(e),dc=e=>As(e).slice(8,-1),mi=e=>As(e)==="[object Object]",ur=e=>de(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,as=lr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),fn=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},pc=/-(\w)/g,Fe=fn(e=>e.replace(pc,(t,s)=>s?s.toUpperCase():"")),hc=/\B([A-Z])/g,Nt=fn(e=>e.replace(hc,"-$1").toLowerCase()),dn=fn(e=>e.charAt(0).toUpperCase()+e.slice(1)),kn=fn(e=>e?`on${dn(e)}`:""),vt=(e,t)=>!Object.is(e,t),Ls=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},gi=(e,t,s,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:s})},Ws=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Kr;const pn=()=>Kr||(Kr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function fr(e){if(B(e)){const t={};for(let s=0;s<e.length;s++){const n=e[s],r=de(n)?bc(n):fr(n);if(r)for(const o in r)t[o]=r[o]}return t}else if(de(e)||ce(e))return e}const mc=/;(?![^(]*\))/g,gc=/:([^]+)/,yc=/\/\*[^]*?\*\//g;function bc(e){const t={};return e.replace(yc,"").split(mc).forEach(s=>{if(s){const n=s.split(gc);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function hn(e){let t="";if(de(e))t=e;else if(B(e))for(let s=0;s<e.length;s++){const n=hn(e[s]);n&&(t+=n+" ")}else if(ce(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const xc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",vc=lr(xc);function yi(e){return!!e||e===""}function _c(e,t){if(e.length!==t.length)return!1;let s=!0;for(let n=0;s&&n<e.length;n++)s=Os(e[n],t[n]);return s}function Os(e,t){if(e===t)return!0;let s=qr(e),n=qr(t);if(s||n)return s&&n?e.getTime()===t.getTime():!1;if(s=et(e),n=et(t),s||n)return e===t;if(s=B(e),n=B(t),s||n)return s&&n?_c(e,t):!1;if(s=ce(e),n=ce(t),s||n){if(!s||!n)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),a=t.hasOwnProperty(i);if(l&&!a||!l&&a||!Os(e[i],t[i]))return!1}}return String(e)===String(t)}function dr(e,t){return e.findIndex(s=>Os(s,t))}const bi=e=>!!(e&&e.__v_isRef===!0),U=e=>de(e)?e:e==null?"":B(e)||ce(e)&&(e.toString===hi||!H(e.toString))?bi(e)?U(e.value):JSON.stringify(e,xi,2):String(e),xi=(e,t)=>bi(t)?xi(e,t.value):Ht(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[n,r],o)=>(s[An(n,o)+" =>"]=r,s),{})}:es(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>An(s))}:et(t)?An(t):ce(t)&&!B(t)&&!mi(t)?String(t):t,An=(e,t="")=>{var s;return et(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Re;class wc{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Re,!t&&Re&&(this.index=(Re.scopes||(Re.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=Re;try{return Re=this,t()}finally{Re=s}}}on(){++this._on===1&&(this.prevScope=Re,Re=this)}off(){this._on>0&&--this._on===0&&(Re=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Ec(){return Re}let ae;const On=new WeakSet;class vi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Re&&Re.active&&Re.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,On.has(this)&&(On.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||wi(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Wr(this),Ei(this);const t=ae,s=Ue;ae=this,Ue=!0;try{return this.fn()}finally{Si(this),ae=t,Ue=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)mr(t);this.deps=this.depsTail=void 0,Wr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?On.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){qn(this)&&this.run()}get dirty(){return qn(this)}}let _i=0,cs,us;function wi(e,t=!1){if(e.flags|=8,t){e.next=us,us=e;return}e.next=cs,cs=e}function pr(){_i++}function hr(){if(--_i>0)return;if(us){let t=us;for(us=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;cs;){let t=cs;for(cs=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=s}}if(e)throw e}function Ei(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Si(e){let t,s=e.depsTail,n=s;for(;n;){const r=n.prevDep;n.version===-1?(n===s&&(s=r),mr(n),Sc(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=r}e.deps=t,e.depsTail=s}function qn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ri(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ri(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===bs)||(e.globalVersion=bs,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!qn(e))))return;e.flags|=2;const t=e.dep,s=ae,n=Ue;ae=e,Ue=!0;try{Ei(e);const r=e.fn(e._value);(t.version===0||vt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ae=s,Ue=n,Si(e),e.flags&=-3}}function mr(e,t=!1){const{dep:s,prevSub:n,nextSub:r}=e;if(n&&(n.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=n,e.nextSub=void 0),s.subs===e&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let o=s.computed.deps;o;o=o.nextDep)mr(o,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function Sc(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Ue=!0;const Ci=[];function ut(){Ci.push(Ue),Ue=!1}function ft(){const e=Ci.pop();Ue=e===void 0?!0:e}function Wr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=ae;ae=void 0;try{t()}finally{ae=s}}}let bs=0;class Rc{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class gr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ae||!Ue||ae===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==ae)s=this.activeLink=new Rc(ae,this),ae.deps?(s.prevDep=ae.depsTail,ae.depsTail.nextDep=s,ae.depsTail=s):ae.deps=ae.depsTail=s,ki(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=ae.depsTail,s.nextDep=void 0,ae.depsTail.nextDep=s,ae.depsTail=s,ae.deps===s&&(ae.deps=n)}return s}trigger(t){this.version++,bs++,this.notify(t)}notify(t){pr();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{hr()}}}function ki(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)ki(n)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const Kn=new WeakMap,Mt=Symbol(""),Wn=Symbol(""),xs=Symbol("");function me(e,t,s){if(Ue&&ae){let n=Kn.get(e);n||Kn.set(e,n=new Map);let r=n.get(s);r||(n.set(s,r=new gr),r.map=n,r.key=s),r.track()}}function it(e,t,s,n,r,o){const i=Kn.get(e);if(!i){bs++;return}const l=a=>{a&&a.trigger()};if(pr(),t==="clear")i.forEach(l);else{const a=B(e),f=a&&ur(s);if(a&&s==="length"){const c=Number(n);i.forEach((d,m)=>{(m==="length"||m===xs||!et(m)&&m>=c)&&l(d)})}else switch((s!==void 0||i.has(void 0))&&l(i.get(s)),f&&l(i.get(xs)),t){case"add":a?f&&l(i.get("length")):(l(i.get(Mt)),Ht(e)&&l(i.get(Wn)));break;case"delete":a||(l(i.get(Mt)),Ht(e)&&l(i.get(Wn)));break;case"set":Ht(e)&&l(i.get(Mt));break}}hr()}function Lt(e){const t=ee(e);return t===e?t:(me(t,"iterate",xs),je(e)?t:t.map(he))}function mn(e){return me(e=ee(e),"iterate",xs),e}const Cc={__proto__:null,[Symbol.iterator](){return Tn(this,Symbol.iterator,he)},concat(...e){return Lt(this).concat(...e.map(t=>B(t)?Lt(t):t))},entries(){return Tn(this,"entries",e=>(e[1]=he(e[1]),e))},every(e,t){return nt(this,"every",e,t,void 0,arguments)},filter(e,t){return nt(this,"filter",e,t,s=>s.map(he),arguments)},find(e,t){return nt(this,"find",e,t,he,arguments)},findIndex(e,t){return nt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return nt(this,"findLast",e,t,he,arguments)},findLastIndex(e,t){return nt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return nt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Pn(this,"includes",e)},indexOf(...e){return Pn(this,"indexOf",e)},join(e){return Lt(this).join(e)},lastIndexOf(...e){return Pn(this,"lastIndexOf",e)},map(e,t){return nt(this,"map",e,t,void 0,arguments)},pop(){return rs(this,"pop")},push(...e){return rs(this,"push",e)},reduce(e,...t){return Jr(this,"reduce",e,t)},reduceRight(e,...t){return Jr(this,"reduceRight",e,t)},shift(){return rs(this,"shift")},some(e,t){return nt(this,"some",e,t,void 0,arguments)},splice(...e){return rs(this,"splice",e)},toReversed(){return Lt(this).toReversed()},toSorted(e){return Lt(this).toSorted(e)},toSpliced(...e){return Lt(this).toSpliced(...e)},unshift(...e){return rs(this,"unshift",e)},values(){return Tn(this,"values",he)}};function Tn(e,t,s){const n=mn(e),r=n[t]();return n!==e&&!je(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=s(o.value)),o}),r}const kc=Array.prototype;function nt(e,t,s,n,r,o){const i=mn(e),l=i!==e&&!je(e),a=i[t];if(a!==kc[t]){const d=a.apply(e,o);return l?he(d):d}let f=s;i!==e&&(l?f=function(d,m){return s.call(this,he(d),m,e)}:s.length>2&&(f=function(d,m){return s.call(this,d,m,e)}));const c=a.call(i,f,n);return l&&r?r(c):c}function Jr(e,t,s,n){const r=mn(e);let o=s;return r!==e&&(je(e)?s.length>3&&(o=function(i,l,a){return s.call(this,i,l,a,e)}):o=function(i,l,a){return s.call(this,i,he(l),a,e)}),r[t](o,...n)}function Pn(e,t,s){const n=ee(e);me(n,"iterate",xs);const r=n[t](...s);return(r===-1||r===!1)&&xr(s[0])?(s[0]=ee(s[0]),n[t](...s)):r}function rs(e,t,s=[]){ut(),pr();const n=ee(e)[t].apply(e,s);return hr(),ft(),n}const Ac=lr("__proto__,__v_isRef,__isVue"),Ai=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(et));function Oc(e){et(e)||(e=String(e));const t=ee(this);return me(t,"has",e),t.hasOwnProperty(e)}class Oi{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,n){if(s==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(s==="__v_isReactive")return!r;if(s==="__v_isReadonly")return r;if(s==="__v_isShallow")return o;if(s==="__v_raw")return n===(r?o?Lc:$i:o?Mi:Pi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const i=B(t);if(!r){let a;if(i&&(a=Cc[s]))return a;if(s==="hasOwnProperty")return Oc}const l=Reflect.get(t,s,be(t)?t:n);return(et(s)?Ai.has(s):Ac(s))||(r||me(t,"get",s),o)?l:be(l)?i&&ur(s)?l:l.value:ce(l)?r?Di(l):gn(l):l}}class Ti extends Oi{constructor(t=!1){super(!1,t)}set(t,s,n,r){let o=t[s];if(!this._isShallow){const a=wt(o);if(!je(n)&&!wt(n)&&(o=ee(o),n=ee(n)),!B(t)&&be(o)&&!be(n))return a?!1:(o.value=n,!0)}const i=B(t)&&ur(s)?Number(s)<t.length:te(t,s),l=Reflect.set(t,s,n,be(t)?t:r);return t===ee(r)&&(i?vt(n,o)&&it(t,"set",s,n):it(t,"add",s,n)),l}deleteProperty(t,s){const n=te(t,s);t[s];const r=Reflect.deleteProperty(t,s);return r&&n&&it(t,"delete",s,void 0),r}has(t,s){const n=Reflect.has(t,s);return(!et(s)||!Ai.has(s))&&me(t,"has",s),n}ownKeys(t){return me(t,"iterate",B(t)?"length":Mt),Reflect.ownKeys(t)}}class Tc extends Oi{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const Pc=new Ti,Mc=new Tc,$c=new Ti(!0);const Jn=e=>e,$s=e=>Reflect.getPrototypeOf(e);function Nc(e,t,s){return function(...n){const r=this.__v_raw,o=ee(r),i=Ht(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,f=r[e](...n),c=s?Jn:t?Js:he;return!t&&me(o,"iterate",a?Wn:Mt),{next(){const{value:d,done:m}=f.next();return m?{value:d,done:m}:{value:l?[c(d[0]),c(d[1])]:c(d),done:m}},[Symbol.iterator](){return this}}}}function Ns(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Dc(e,t){const s={get(r){const o=this.__v_raw,i=ee(o),l=ee(r);e||(vt(r,l)&&me(i,"get",r),me(i,"get",l));const{has:a}=$s(i),f=t?Jn:e?Js:he;if(a.call(i,r))return f(o.get(r));if(a.call(i,l))return f(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&me(ee(r),"iterate",Mt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=ee(o),l=ee(r);return e||(vt(r,l)&&me(i,"has",r),me(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,a=ee(l),f=t?Jn:e?Js:he;return!e&&me(a,"iterate",Mt),l.forEach((c,d)=>r.call(o,f(c),f(d),i))}};return xe(s,e?{add:Ns("add"),set:Ns("set"),delete:Ns("delete"),clear:Ns("clear")}:{add(r){!t&&!je(r)&&!wt(r)&&(r=ee(r));const o=ee(this);return $s(o).has.call(o,r)||(o.add(r),it(o,"add",r,r)),this},set(r,o){!t&&!je(o)&&!wt(o)&&(o=ee(o));const i=ee(this),{has:l,get:a}=$s(i);let f=l.call(i,r);f||(r=ee(r),f=l.call(i,r));const c=a.call(i,r);return i.set(r,o),f?vt(o,c)&&it(i,"set",r,o):it(i,"add",r,o),this},delete(r){const o=ee(this),{has:i,get:l}=$s(o);let a=i.call(o,r);a||(r=ee(r),a=i.call(o,r)),l&&l.call(o,r);const f=o.delete(r);return a&&it(o,"delete",r,void 0),f},clear(){const r=ee(this),o=r.size!==0,i=r.clear();return o&&it(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{s[r]=Nc(r,e,t)}),s}function yr(e,t){const s=Dc(e,t);return(n,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?n:Reflect.get(te(s,r)&&r in n?s:n,r,o)}const jc={get:yr(!1,!1)},Fc={get:yr(!1,!0)},Ic={get:yr(!0,!1)};const Pi=new WeakMap,Mi=new WeakMap,$i=new WeakMap,Lc=new WeakMap;function Uc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Bc(e){return e.__v_skip||!Object.isExtensible(e)?0:Uc(dc(e))}function gn(e){return wt(e)?e:br(e,!1,Pc,jc,Pi)}function Ni(e){return br(e,!1,$c,Fc,Mi)}function Di(e){return br(e,!0,Mc,Ic,$i)}function br(e,t,s,n,r){if(!ce(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Bc(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?n:s);return r.set(e,l),l}function zt(e){return wt(e)?zt(e.__v_raw):!!(e&&e.__v_isReactive)}function wt(e){return!!(e&&e.__v_isReadonly)}function je(e){return!!(e&&e.__v_isShallow)}function xr(e){return e?!!e.__v_raw:!1}function ee(e){const t=e&&e.__v_raw;return t?ee(t):e}function Vc(e){return!te(e,"__v_skip")&&Object.isExtensible(e)&&gi(e,"__v_skip",!0),e}const he=e=>ce(e)?gn(e):e,Js=e=>ce(e)?Di(e):e;function be(e){return e?e.__v_isRef===!0:!1}function Hc(e){return ji(e,!1)}function zc(e){return ji(e,!0)}function ji(e,t){return be(e)?e:new qc(e,t)}class qc{constructor(t,s){this.dep=new gr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:ee(t),this._value=s?t:he(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,n=this.__v_isShallow||je(t)||wt(t);t=n?t:ee(t),vt(t,s)&&(this._rawValue=t,this._value=n?t:he(t),this.dep.trigger())}}function qt(e){return be(e)?e.value:e}const Kc={get:(e,t,s)=>t==="__v_raw"?e:qt(Reflect.get(e,t,s)),set:(e,t,s,n)=>{const r=e[t];return be(r)&&!be(s)?(r.value=s,!0):Reflect.set(e,t,s,n)}};function Fi(e){return zt(e)?e:new Proxy(e,Kc)}class Wc{constructor(t,s,n){this.fn=t,this.setter=s,this._value=void 0,this.dep=new gr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=bs-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&ae!==this)return wi(this,!0),!0}get value(){const t=this.dep.track();return Ri(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Jc(e,t,s=!1){let n,r;return H(e)?n=e:(n=e.get,r=e.set),new Wc(n,r,s)}const Ds={},Gs=new WeakMap;let At;function Gc(e,t=!1,s=At){if(s){let n=Gs.get(s);n||Gs.set(s,n=[]),n.push(e)}}function Xc(e,t,s=oe){const{immediate:n,deep:r,once:o,scheduler:i,augmentJob:l,call:a}=s,f=D=>r?D:je(D)||r===!1||r===0?lt(D,1):lt(D);let c,d,m,g,x=!1,E=!1;if(be(e)?(d=()=>e.value,x=je(e)):zt(e)?(d=()=>f(e),x=!0):B(e)?(E=!0,x=e.some(D=>zt(D)||je(D)),d=()=>e.map(D=>{if(be(D))return D.value;if(zt(D))return f(D);if(H(D))return a?a(D,2):D()})):H(e)?t?d=a?()=>a(e,2):e:d=()=>{if(m){ut();try{m()}finally{ft()}}const D=At;At=c;try{return a?a(e,3,[g]):e(g)}finally{At=D}}:d=Ze,t&&r){const D=d,z=r===!0?1/0:r;d=()=>lt(D(),z)}const R=Ec(),O=()=>{c.stop(),R&&R.active&&cr(R.effects,c)};if(o&&t){const D=t;t=(...z)=>{D(...z),O()}}let T=E?new Array(e.length).fill(Ds):Ds;const N=D=>{if(!(!(c.flags&1)||!c.dirty&&!D))if(t){const z=c.run();if(r||x||(E?z.some((ie,Q)=>vt(ie,T[Q])):vt(z,T))){m&&m();const ie=At;At=c;try{const Q=[z,T===Ds?void 0:E&&T[0]===Ds?[]:T,g];T=z,a?a(t,3,Q):t(...Q)}finally{At=ie}}}else c.run()};return l&&l(N),c=new vi(d),c.scheduler=i?()=>i(N,!1):N,g=D=>Gc(D,!1,c),m=c.onStop=()=>{const D=Gs.get(c);if(D){if(a)a(D,4);else for(const z of D)z();Gs.delete(c)}},t?n?N(!0):T=c.run():i?i(N.bind(null,!0),!0):c.run(),O.pause=c.pause.bind(c),O.resume=c.resume.bind(c),O.stop=O,O}function lt(e,t=1/0,s){if(t<=0||!ce(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,be(e))lt(e.value,t,s);else if(B(e))for(let n=0;n<e.length;n++)lt(e[n],t,s);else if(es(e)||Ht(e))e.forEach(n=>{lt(n,t,s)});else if(mi(e)){for(const n in e)lt(e[n],t,s);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&lt(e[n],t,s)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ts(e,t,s,n){try{return n?e(...n):e()}catch(r){yn(r,t,s)}}function tt(e,t,s,n){if(H(e)){const r=Ts(e,t,s,n);return r&&pi(r)&&r.catch(o=>{yn(o,t,s)}),r}if(B(e)){const r=[];for(let o=0;o<e.length;o++)r.push(tt(e[o],t,s,n));return r}}function yn(e,t,s,n=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||oe;if(t){let l=t.parent;const a=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const c=l.ec;if(c){for(let d=0;d<c.length;d++)if(c[d](e,a,f)===!1)return}l=l.parent}if(o){ut(),Ts(o,null,10,[e,a,f]),ft();return}}Qc(e,s,r,n,i)}function Qc(e,t,s,n=!0,r=!1){if(r)throw e;console.error(e)}const we=[];let Qe=-1;const Kt=[];let gt=null,Ut=0;const Ii=Promise.resolve();let Xs=null;function vr(e){const t=Xs||Ii;return e?t.then(this?e.bind(this):e):t}function Yc(e){let t=Qe+1,s=we.length;for(;t<s;){const n=t+s>>>1,r=we[n],o=vs(r);o<e||o===e&&r.flags&2?t=n+1:s=n}return t}function _r(e){if(!(e.flags&1)){const t=vs(e),s=we[we.length-1];!s||!(e.flags&2)&&t>=vs(s)?we.push(e):we.splice(Yc(t),0,e),e.flags|=1,Li()}}function Li(){Xs||(Xs=Ii.then(Bi))}function Zc(e){B(e)?Kt.push(...e):gt&&e.id===-1?gt.splice(Ut+1,0,e):e.flags&1||(Kt.push(e),e.flags|=1),Li()}function Gr(e,t,s=Qe+1){for(;s<we.length;s++){const n=we[s];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;we.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function Ui(e){if(Kt.length){const t=[...new Set(Kt)].sort((s,n)=>vs(s)-vs(n));if(Kt.length=0,gt){gt.push(...t);return}for(gt=t,Ut=0;Ut<gt.length;Ut++){const s=gt[Ut];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}gt=null,Ut=0}}const vs=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Bi(e){try{for(Qe=0;Qe<we.length;Qe++){const t=we[Qe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ts(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Qe<we.length;Qe++){const t=we[Qe];t&&(t.flags&=-2)}Qe=-1,we.length=0,Ui(),Xs=null,(we.length||Kt.length)&&Bi()}}let $e=null,Vi=null;function Qs(e){const t=$e;return $e=e,Vi=e&&e.type.__scopeId||null,t}function fe(e,t=$e,s){if(!t||e._n)return e;const n=(...r)=>{n._d&&oo(-1);const o=Qs(t);let i;try{i=e(...r)}finally{Qs(o),n._d&&oo(1)}return i};return n._n=!0,n._c=!0,n._d=!0,n}function Ce(e,t){if($e===null)return e;const s=wn($e),n=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,a=oe]=t[r];o&&(H(o)&&(o={mounted:o,updated:o}),o.deep&&lt(i),n.push({dir:o,instance:s,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function Ct(e,t,s,n){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let a=l.dir[n];a&&(ut(),tt(a,s,8,[e.el,l,e,t]),ft())}}const eu=Symbol("_vte"),tu=e=>e.__isTeleport;function wr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,wr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Hi(e,t){return H(e)?xe({name:e.name},t,{setup:e}):e}function zi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Ys(e,t,s,n,r=!1){if(B(e)){e.forEach((x,E)=>Ys(x,t&&(B(t)?t[E]:t),s,n,r));return}if(fs(n)&&!r){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&Ys(e,t,s,n.component.subTree);return}const o=n.shapeFlag&4?wn(n.component):n.el,i=r?null:o,{i:l,r:a}=e,f=t&&t.r,c=l.refs===oe?l.refs={}:l.refs,d=l.setupState,m=ee(d),g=d===oe?()=>!1:x=>te(m,x);if(f!=null&&f!==a&&(de(f)?(c[f]=null,g(f)&&(d[f]=null)):be(f)&&(f.value=null)),H(a))Ts(a,l,12,[i,c]);else{const x=de(a),E=be(a);if(x||E){const R=()=>{if(e.f){const O=x?g(a)?d[a]:c[a]:a.value;r?B(O)&&cr(O,o):B(O)?O.includes(o)||O.push(o):x?(c[a]=[o],g(a)&&(d[a]=c[a])):(a.value=[o],e.k&&(c[e.k]=a.value))}else x?(c[a]=i,g(a)&&(d[a]=i)):E&&(a.value=i,e.k&&(c[e.k]=i))};i?(R.id=-1,Pe(R,s)):R()}}}pn().requestIdleCallback;pn().cancelIdleCallback;const fs=e=>!!e.type.__asyncLoader,qi=e=>e.type.__isKeepAlive;function su(e,t){Ki(e,"a",t)}function nu(e,t){Ki(e,"da",t)}function Ki(e,t,s=ye){const n=e.__wdc||(e.__wdc=()=>{let r=s;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(bn(t,n,s),s){let r=s.parent;for(;r&&r.parent;)qi(r.parent.vnode)&&ru(n,t,s,r),r=r.parent}}function ru(e,t,s,n){const r=bn(t,e,n,!0);Wi(()=>{cr(n[t],r)},s)}function bn(e,t,s=ye,n=!1){if(s){const r=s[e]||(s[e]=[]),o=t.__weh||(t.__weh=(...i)=>{ut();const l=Ps(s),a=tt(t,s,e,i);return l(),ft(),a});return n?r.unshift(o):r.push(o),o}}const dt=e=>(t,s=ye)=>{(!ws||e==="sp")&&bn(e,(...n)=>t(...n),s)},ou=dt("bm"),iu=dt("m"),lu=dt("bu"),au=dt("u"),cu=dt("bum"),Wi=dt("um"),uu=dt("sp"),fu=dt("rtg"),du=dt("rtc");function pu(e,t=ye){bn("ec",e,t)}const hu="components";function Et(e,t){return gu(hu,e,!0,t)||e}const mu=Symbol.for("v-ndc");function gu(e,t,s=!0,n=!1){const r=$e||ye;if(r){const o=r.type;{const l=nf(o,!1);if(l&&(l===t||l===Fe(t)||l===dn(Fe(t))))return o}const i=Xr(r[e]||o[e],t)||Xr(r.appContext[e],t);return!i&&n?o:i}}function Xr(e,t){return e&&(e[t]||e[Fe(t)]||e[dn(Fe(t))])}function xn(e,t,s,n){let r;const o=s,i=B(e);if(i||de(e)){const l=i&&zt(e);let a=!1,f=!1;l&&(a=!je(e),f=wt(e),e=mn(e)),r=new Array(e.length);for(let c=0,d=e.length;c<d;c++)r[c]=t(a?f?Js(he(e[c])):he(e[c]):e[c],c,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(ce(e))if(e[Symbol.iterator])r=Array.from(e,(l,a)=>t(l,a,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let a=0,f=l.length;a<f;a++){const c=l[a];r[a]=t(e[c],c,a,o)}}else r=[];return r}const Gn=e=>e?pl(e)?wn(e):Gn(e.parent):null,ds=xe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Gn(e.parent),$root:e=>Gn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Gi(e),$forceUpdate:e=>e.f||(e.f=()=>{_r(e.update)}),$nextTick:e=>e.n||(e.n=vr.bind(e.proxy)),$watch:e=>Fu.bind(e)}),Mn=(e,t)=>e!==oe&&!e.__isScriptSetup&&te(e,t),yu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:n,data:r,props:o,accessCache:i,type:l,appContext:a}=e;let f;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return n[t];case 2:return r[t];case 4:return s[t];case 3:return o[t]}else{if(Mn(n,t))return i[t]=1,n[t];if(r!==oe&&te(r,t))return i[t]=2,r[t];if((f=e.propsOptions[0])&&te(f,t))return i[t]=3,o[t];if(s!==oe&&te(s,t))return i[t]=4,s[t];Xn&&(i[t]=0)}}const c=ds[t];let d,m;if(c)return t==="$attrs"&&me(e.attrs,"get",""),c(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(s!==oe&&te(s,t))return i[t]=4,s[t];if(m=a.config.globalProperties,te(m,t))return m[t]},set({_:e},t,s){const{data:n,setupState:r,ctx:o}=e;return Mn(r,t)?(r[t]=s,!0):n!==oe&&te(n,t)?(n[t]=s,!0):te(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:n,appContext:r,propsOptions:o}},i){let l;return!!s[i]||e!==oe&&te(e,i)||Mn(t,i)||(l=o[0])&&te(l,i)||te(n,i)||te(ds,i)||te(r.config.globalProperties,i)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:te(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function Qr(e){return B(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let Xn=!0;function bu(e){const t=Gi(e),s=e.proxy,n=e.ctx;Xn=!1,t.beforeCreate&&Yr(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:a,inject:f,created:c,beforeMount:d,mounted:m,beforeUpdate:g,updated:x,activated:E,deactivated:R,beforeDestroy:O,beforeUnmount:T,destroyed:N,unmounted:D,render:z,renderTracked:ie,renderTriggered:Q,errorCaptured:ve,serverPrefetch:Ie,expose:He,inheritAttrs:pt,components:Rt,directives:ze,filters:ts}=t;if(f&&xu(f,n,null),i)for(const ne in i){const Y=i[ne];H(Y)&&(n[ne]=Y.bind(s))}if(r){const ne=r.call(s,s);ce(ne)&&(e.data=gn(ne))}if(Xn=!0,o)for(const ne in o){const Y=o[ne],st=H(Y)?Y.bind(s,s):H(Y.get)?Y.get.bind(s,s):Ze,ht=!H(Y)&&H(Y.set)?Y.set.bind(s):Ze,qe=Le({get:st,set:ht});Object.defineProperty(n,ne,{enumerable:!0,configurable:!0,get:()=>qe.value,set:Ee=>qe.value=Ee})}if(l)for(const ne in l)Ji(l[ne],n,s,ne);if(a){const ne=H(a)?a.call(s):a;Reflect.ownKeys(ne).forEach(Y=>{Us(Y,ne[Y])})}c&&Yr(c,e,"c");function pe(ne,Y){B(Y)?Y.forEach(st=>ne(st.bind(s))):Y&&ne(Y.bind(s))}if(pe(ou,d),pe(iu,m),pe(lu,g),pe(au,x),pe(su,E),pe(nu,R),pe(pu,ve),pe(du,ie),pe(fu,Q),pe(cu,T),pe(Wi,D),pe(uu,Ie),B(He))if(He.length){const ne=e.exposed||(e.exposed={});He.forEach(Y=>{Object.defineProperty(ne,Y,{get:()=>s[Y],set:st=>s[Y]=st})})}else e.exposed||(e.exposed={});z&&e.render===Ze&&(e.render=z),pt!=null&&(e.inheritAttrs=pt),Rt&&(e.components=Rt),ze&&(e.directives=ze),Ie&&zi(e)}function xu(e,t,s=Ze){B(e)&&(e=Qn(e));for(const n in e){const r=e[n];let o;ce(r)?"default"in r?o=at(r.from||n,r.default,!0):o=at(r.from||n):o=at(r),be(o)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[n]=o}}function Yr(e,t,s){tt(B(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,s)}function Ji(e,t,s,n){let r=n.includes(".")?al(s,n):()=>s[n];if(de(e)){const o=t[e];H(o)&&Bs(r,o)}else if(H(e))Bs(r,e.bind(s));else if(ce(e))if(B(e))e.forEach(o=>Ji(o,t,s,n));else{const o=H(e.handler)?e.handler.bind(s):t[e.handler];H(o)&&Bs(r,o,e)}}function Gi(e){const t=e.type,{mixins:s,extends:n}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!r.length&&!s&&!n?a=t:(a={},r.length&&r.forEach(f=>Zs(a,f,i,!0)),Zs(a,t,i)),ce(t)&&o.set(t,a),a}function Zs(e,t,s,n=!1){const{mixins:r,extends:o}=t;o&&Zs(e,o,s,!0),r&&r.forEach(i=>Zs(e,i,s,!0));for(const i in t)if(!(n&&i==="expose")){const l=vu[i]||s&&s[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const vu={data:Zr,props:eo,emits:eo,methods:ls,computed:ls,beforeCreate:_e,created:_e,beforeMount:_e,mounted:_e,beforeUpdate:_e,updated:_e,beforeDestroy:_e,beforeUnmount:_e,destroyed:_e,unmounted:_e,activated:_e,deactivated:_e,errorCaptured:_e,serverPrefetch:_e,components:ls,directives:ls,watch:wu,provide:Zr,inject:_u};function Zr(e,t){return t?e?function(){return xe(H(e)?e.call(this,this):e,H(t)?t.call(this,this):t)}:t:e}function _u(e,t){return ls(Qn(e),Qn(t))}function Qn(e){if(B(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function _e(e,t){return e?[...new Set([].concat(e,t))]:t}function ls(e,t){return e?xe(Object.create(null),e,t):t}function eo(e,t){return e?B(e)&&B(t)?[...new Set([...e,...t])]:xe(Object.create(null),Qr(e),Qr(t??{})):t}function wu(e,t){if(!e)return t;if(!t)return e;const s=xe(Object.create(null),e);for(const n in t)s[n]=_e(e[n],t[n]);return s}function Xi(){return{app:null,config:{isNativeTag:uc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Eu=0;function Su(e,t){return function(n,r=null){H(n)||(n=xe({},n)),r!=null&&!ce(r)&&(r=null);const o=Xi(),i=new WeakSet,l=[];let a=!1;const f=o.app={_uid:Eu++,_component:n,_props:r,_container:null,_context:o,_instance:null,version:of,get config(){return o.config},set config(c){},use(c,...d){return i.has(c)||(c&&H(c.install)?(i.add(c),c.install(f,...d)):H(c)&&(i.add(c),c(f,...d))),f},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),f},component(c,d){return d?(o.components[c]=d,f):o.components[c]},directive(c,d){return d?(o.directives[c]=d,f):o.directives[c]},mount(c,d,m){if(!a){const g=f._ceVNode||G(n,r);return g.appContext=o,m===!0?m="svg":m===!1&&(m=void 0),e(g,c,m),a=!0,f._container=c,c.__vue_app__=f,wn(g.component)}},onUnmount(c){l.push(c)},unmount(){a&&(tt(l,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(c,d){return o.provides[c]=d,f},runWithContext(c){const d=Wt;Wt=f;try{return c()}finally{Wt=d}}};return f}}let Wt=null;function Us(e,t){if(ye){let s=ye.provides;const n=ye.parent&&ye.parent.provides;n===s&&(s=ye.provides=Object.create(n)),s[e]=t}}function at(e,t,s=!1){const n=ye||$e;if(n||Wt){let r=Wt?Wt._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return s&&H(t)?t.call(n&&n.proxy):t}}const Qi={},Yi=()=>Object.create(Qi),Zi=e=>Object.getPrototypeOf(e)===Qi;function Ru(e,t,s,n=!1){const r={},o=Yi();e.propsDefaults=Object.create(null),el(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);s?e.props=n?r:Ni(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Cu(e,t,s,n){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=ee(r),[a]=e.propsOptions;let f=!1;if((n||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let d=0;d<c.length;d++){let m=c[d];if(vn(e.emitsOptions,m))continue;const g=t[m];if(a)if(te(o,m))g!==o[m]&&(o[m]=g,f=!0);else{const x=Fe(m);r[x]=Yn(a,l,x,g,e,!1)}else g!==o[m]&&(o[m]=g,f=!0)}}}else{el(e,t,r,o)&&(f=!0);let c;for(const d in l)(!t||!te(t,d)&&((c=Nt(d))===d||!te(t,c)))&&(a?s&&(s[d]!==void 0||s[c]!==void 0)&&(r[d]=Yn(a,l,d,void 0,e,!0)):delete r[d]);if(o!==l)for(const d in o)(!t||!te(t,d))&&(delete o[d],f=!0)}f&&it(e.attrs,"set","")}function el(e,t,s,n){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(as(a))continue;const f=t[a];let c;r&&te(r,c=Fe(a))?!o||!o.includes(c)?s[c]=f:(l||(l={}))[c]=f:vn(e.emitsOptions,a)||(!(a in n)||f!==n[a])&&(n[a]=f,i=!0)}if(o){const a=ee(s),f=l||oe;for(let c=0;c<o.length;c++){const d=o[c];s[d]=Yn(r,a,d,f[d],e,!te(f,d))}}return i}function Yn(e,t,s,n,r,o){const i=e[s];if(i!=null){const l=te(i,"default");if(l&&n===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&H(a)){const{propsDefaults:f}=r;if(s in f)n=f[s];else{const c=Ps(r);n=f[s]=a.call(null,t),c()}}else n=a;r.ce&&r.ce._setProp(s,n)}i[0]&&(o&&!l?n=!1:i[1]&&(n===""||n===Nt(s))&&(n=!0))}return n}const ku=new WeakMap;function tl(e,t,s=!1){const n=s?ku:t.propsCache,r=n.get(e);if(r)return r;const o=e.props,i={},l=[];let a=!1;if(!H(e)){const c=d=>{a=!0;const[m,g]=tl(d,t,!0);xe(i,m),g&&l.push(...g)};!s&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!a)return ce(e)&&n.set(e,Vt),Vt;if(B(o))for(let c=0;c<o.length;c++){const d=Fe(o[c]);to(d)&&(i[d]=oe)}else if(o)for(const c in o){const d=Fe(c);if(to(d)){const m=o[c],g=i[d]=B(m)||H(m)?{type:m}:xe({},m),x=g.type;let E=!1,R=!0;if(B(x))for(let O=0;O<x.length;++O){const T=x[O],N=H(T)&&T.name;if(N==="Boolean"){E=!0;break}else N==="String"&&(R=!1)}else E=H(x)&&x.name==="Boolean";g[0]=E,g[1]=R,(E||te(g,"default"))&&l.push(d)}}const f=[i,l];return ce(e)&&n.set(e,f),f}function to(e){return e[0]!=="$"&&!as(e)}const Er=e=>e[0]==="_"||e==="$stable",Sr=e=>B(e)?e.map(Ye):[Ye(e)],Au=(e,t,s)=>{if(t._n)return t;const n=fe((...r)=>Sr(t(...r)),s);return n._c=!1,n},sl=(e,t,s)=>{const n=e._ctx;for(const r in e){if(Er(r))continue;const o=e[r];if(H(o))t[r]=Au(r,o,n);else if(o!=null){const i=Sr(o);t[r]=()=>i}}},nl=(e,t)=>{const s=Sr(t);e.slots.default=()=>s},rl=(e,t,s)=>{for(const n in t)(s||!Er(n))&&(e[n]=t[n])},Ou=(e,t,s)=>{const n=e.slots=Yi();if(e.vnode.shapeFlag&32){const r=t._;r?(rl(n,t,s),s&&gi(n,"_",r,!0)):sl(t,n)}else t&&nl(e,t)},Tu=(e,t,s)=>{const{vnode:n,slots:r}=e;let o=!0,i=oe;if(n.shapeFlag&32){const l=t._;l?s&&l===1?o=!1:rl(r,t,s):(o=!t.$stable,sl(t,r)),i=t}else t&&(nl(e,t),i={default:1});if(o)for(const l in r)!Er(l)&&i[l]==null&&delete r[l]},Pe=zu;function Pu(e){return Mu(e)}function Mu(e,t){const s=pn();s.__VUE__=!0;const{insert:n,remove:r,patchProp:o,createElement:i,createText:l,createComment:a,setText:f,setElementText:c,parentNode:d,nextSibling:m,setScopeId:g=Ze,insertStaticContent:x}=e,E=(p,h,y,v=null,S=null,w=null,P=void 0,A=null,k=!!h.dynamicChildren)=>{if(p===h)return;p&&!os(p,h)&&(v=_(p),Ee(p,S,w,!0),p=null),h.patchFlag===-2&&(k=!1,h.dynamicChildren=null);const{type:C,ref:L,shapeFlag:$}=h;switch(C){case _n:R(p,h,y,v);break;case St:O(p,h,y,v);break;case Vs:p==null&&T(h,y,v,P);break;case Me:Rt(p,h,y,v,S,w,P,A,k);break;default:$&1?z(p,h,y,v,S,w,P,A,k):$&6?ze(p,h,y,v,S,w,P,A,k):($&64||$&128)&&C.process(p,h,y,v,S,w,P,A,k,F)}L!=null&&S&&Ys(L,p&&p.ref,w,h||p,!h)},R=(p,h,y,v)=>{if(p==null)n(h.el=l(h.children),y,v);else{const S=h.el=p.el;h.children!==p.children&&f(S,h.children)}},O=(p,h,y,v)=>{p==null?n(h.el=a(h.children||""),y,v):h.el=p.el},T=(p,h,y,v)=>{[p.el,p.anchor]=x(p.children,h,y,v,p.el,p.anchor)},N=({el:p,anchor:h},y,v)=>{let S;for(;p&&p!==h;)S=m(p),n(p,y,v),p=S;n(h,y,v)},D=({el:p,anchor:h})=>{let y;for(;p&&p!==h;)y=m(p),r(p),p=y;r(h)},z=(p,h,y,v,S,w,P,A,k)=>{h.type==="svg"?P="svg":h.type==="math"&&(P="mathml"),p==null?ie(h,y,v,S,w,P,A,k):Ie(p,h,S,w,P,A,k)},ie=(p,h,y,v,S,w,P,A)=>{let k,C;const{props:L,shapeFlag:$,transition:I,dirs:V}=p;if(k=p.el=i(p.type,w,L&&L.is,L),$&8?c(k,p.children):$&16&&ve(p.children,k,null,v,S,$n(p,w),P,A),V&&Ct(p,null,v,"created"),Q(k,p,p.scopeId,P,v),L){for(const le in L)le!=="value"&&!as(le)&&o(k,le,null,L[le],w,v);"value"in L&&o(k,"value",null,L.value,w),(C=L.onVnodeBeforeMount)&&Xe(C,v,p)}V&&Ct(p,null,v,"beforeMount");const J=$u(S,I);J&&I.beforeEnter(k),n(k,h,y),((C=L&&L.onVnodeMounted)||J||V)&&Pe(()=>{C&&Xe(C,v,p),J&&I.enter(k),V&&Ct(p,null,v,"mounted")},S)},Q=(p,h,y,v,S)=>{if(y&&g(p,y),v)for(let w=0;w<v.length;w++)g(p,v[w]);if(S){let w=S.subTree;if(h===w||ul(w.type)&&(w.ssContent===h||w.ssFallback===h)){const P=S.vnode;Q(p,P,P.scopeId,P.slotScopeIds,S.parent)}}},ve=(p,h,y,v,S,w,P,A,k=0)=>{for(let C=k;C<p.length;C++){const L=p[C]=A?yt(p[C]):Ye(p[C]);E(null,L,h,y,v,S,w,P,A)}},Ie=(p,h,y,v,S,w,P)=>{const A=h.el=p.el;let{patchFlag:k,dynamicChildren:C,dirs:L}=h;k|=p.patchFlag&16;const $=p.props||oe,I=h.props||oe;let V;if(y&&kt(y,!1),(V=I.onVnodeBeforeUpdate)&&Xe(V,y,h,p),L&&Ct(h,p,y,"beforeUpdate"),y&&kt(y,!0),($.innerHTML&&I.innerHTML==null||$.textContent&&I.textContent==null)&&c(A,""),C?He(p.dynamicChildren,C,A,y,v,$n(h,S),w):P||Y(p,h,A,null,y,v,$n(h,S),w,!1),k>0){if(k&16)pt(A,$,I,y,S);else if(k&2&&$.class!==I.class&&o(A,"class",null,I.class,S),k&4&&o(A,"style",$.style,I.style,S),k&8){const J=h.dynamicProps;for(let le=0;le<J.length;le++){const se=J[le],Oe=$[se],Se=I[se];(Se!==Oe||se==="value")&&o(A,se,Oe,Se,S,y)}}k&1&&p.children!==h.children&&c(A,h.children)}else!P&&C==null&&pt(A,$,I,y,S);((V=I.onVnodeUpdated)||L)&&Pe(()=>{V&&Xe(V,y,h,p),L&&Ct(h,p,y,"updated")},v)},He=(p,h,y,v,S,w,P)=>{for(let A=0;A<h.length;A++){const k=p[A],C=h[A],L=k.el&&(k.type===Me||!os(k,C)||k.shapeFlag&198)?d(k.el):y;E(k,C,L,null,v,S,w,P,!0)}},pt=(p,h,y,v,S)=>{if(h!==y){if(h!==oe)for(const w in h)!as(w)&&!(w in y)&&o(p,w,h[w],null,S,v);for(const w in y){if(as(w))continue;const P=y[w],A=h[w];P!==A&&w!=="value"&&o(p,w,A,P,S,v)}"value"in y&&o(p,"value",h.value,y.value,S)}},Rt=(p,h,y,v,S,w,P,A,k)=>{const C=h.el=p?p.el:l(""),L=h.anchor=p?p.anchor:l("");let{patchFlag:$,dynamicChildren:I,slotScopeIds:V}=h;V&&(A=A?A.concat(V):V),p==null?(n(C,y,v),n(L,y,v),ve(h.children||[],y,L,S,w,P,A,k)):$>0&&$&64&&I&&p.dynamicChildren?(He(p.dynamicChildren,I,y,S,w,P,A),(h.key!=null||S&&h===S.subTree)&&ol(p,h,!0)):Y(p,h,y,L,S,w,P,A,k)},ze=(p,h,y,v,S,w,P,A,k)=>{h.slotScopeIds=A,p==null?h.shapeFlag&512?S.ctx.activate(h,y,v,P,k):ts(h,y,v,S,w,P,k):jt(p,h,k)},ts=(p,h,y,v,S,w,P)=>{const A=p.component=Yu(p,v,S);if(qi(p)&&(A.ctx.renderer=F),Zu(A,!1,P),A.asyncDep){if(S&&S.registerDep(A,pe,P),!p.el){const k=A.subTree=G(St);O(null,k,h,y)}}else pe(A,p,h,y,S,w,P)},jt=(p,h,y)=>{const v=h.component=p.component;if(Vu(p,h,y))if(v.asyncDep&&!v.asyncResolved){ne(v,h,y);return}else v.next=h,v.update();else h.el=p.el,v.vnode=h},pe=(p,h,y,v,S,w,P)=>{const A=()=>{if(p.isMounted){let{next:$,bu:I,u:V,parent:J,vnode:le}=p;{const We=il(p);if(We){$&&($.el=le.el,ne(p,$,P)),We.asyncDep.then(()=>{p.isUnmounted||A()});return}}let se=$,Oe;kt(p,!1),$?($.el=le.el,ne(p,$,P)):$=le,I&&Ls(I),(Oe=$.props&&$.props.onVnodeBeforeUpdate)&&Xe(Oe,J,$,le),kt(p,!0);const Se=no(p),Ke=p.subTree;p.subTree=Se,E(Ke,Se,d(Ke.el),_(Ke),p,S,w),$.el=Se.el,se===null&&Hu(p,Se.el),V&&Pe(V,S),(Oe=$.props&&$.props.onVnodeUpdated)&&Pe(()=>Xe(Oe,J,$,le),S)}else{let $;const{el:I,props:V}=h,{bm:J,m:le,parent:se,root:Oe,type:Se}=p,Ke=fs(h);kt(p,!1),J&&Ls(J),!Ke&&($=V&&V.onVnodeBeforeMount)&&Xe($,se,h),kt(p,!0);{Oe.ce&&Oe.ce._injectChildStyle(Se);const We=p.subTree=no(p);E(null,We,y,v,p,S,w),h.el=We.el}if(le&&Pe(le,S),!Ke&&($=V&&V.onVnodeMounted)){const We=h;Pe(()=>Xe($,se,We),S)}(h.shapeFlag&256||se&&fs(se.vnode)&&se.vnode.shapeFlag&256)&&p.a&&Pe(p.a,S),p.isMounted=!0,h=y=v=null}};p.scope.on();const k=p.effect=new vi(A);p.scope.off();const C=p.update=k.run.bind(k),L=p.job=k.runIfDirty.bind(k);L.i=p,L.id=p.uid,k.scheduler=()=>_r(L),kt(p,!0),C()},ne=(p,h,y)=>{h.component=p;const v=p.vnode.props;p.vnode=h,p.next=null,Cu(p,h.props,v,y),Tu(p,h.children,y),ut(),Gr(p),ft()},Y=(p,h,y,v,S,w,P,A,k=!1)=>{const C=p&&p.children,L=p?p.shapeFlag:0,$=h.children,{patchFlag:I,shapeFlag:V}=h;if(I>0){if(I&128){ht(C,$,y,v,S,w,P,A,k);return}else if(I&256){st(C,$,y,v,S,w,P,A,k);return}}V&8?(L&16&&De(C,S,w),$!==C&&c(y,$)):L&16?V&16?ht(C,$,y,v,S,w,P,A,k):De(C,S,w,!0):(L&8&&c(y,""),V&16&&ve($,y,v,S,w,P,A,k))},st=(p,h,y,v,S,w,P,A,k)=>{p=p||Vt,h=h||Vt;const C=p.length,L=h.length,$=Math.min(C,L);let I;for(I=0;I<$;I++){const V=h[I]=k?yt(h[I]):Ye(h[I]);E(p[I],V,y,null,S,w,P,A,k)}C>L?De(p,S,w,!0,!1,$):ve(h,y,v,S,w,P,A,k,$)},ht=(p,h,y,v,S,w,P,A,k)=>{let C=0;const L=h.length;let $=p.length-1,I=L-1;for(;C<=$&&C<=I;){const V=p[C],J=h[C]=k?yt(h[C]):Ye(h[C]);if(os(V,J))E(V,J,y,null,S,w,P,A,k);else break;C++}for(;C<=$&&C<=I;){const V=p[$],J=h[I]=k?yt(h[I]):Ye(h[I]);if(os(V,J))E(V,J,y,null,S,w,P,A,k);else break;$--,I--}if(C>$){if(C<=I){const V=I+1,J=V<L?h[V].el:v;for(;C<=I;)E(null,h[C]=k?yt(h[C]):Ye(h[C]),y,J,S,w,P,A,k),C++}}else if(C>I)for(;C<=$;)Ee(p[C],S,w,!0),C++;else{const V=C,J=C,le=new Map;for(C=J;C<=I;C++){const Te=h[C]=k?yt(h[C]):Ye(h[C]);Te.key!=null&&le.set(Te.key,C)}let se,Oe=0;const Se=I-J+1;let Ke=!1,We=0;const ss=new Array(Se);for(C=0;C<Se;C++)ss[C]=0;for(C=V;C<=$;C++){const Te=p[C];if(Oe>=Se){Ee(Te,S,w,!0);continue}let Je;if(Te.key!=null)Je=le.get(Te.key);else for(se=J;se<=I;se++)if(ss[se-J]===0&&os(Te,h[se])){Je=se;break}Je===void 0?Ee(Te,S,w,!0):(ss[Je-J]=C+1,Je>=We?We=Je:Ke=!0,E(Te,h[Je],y,null,S,w,P,A,k),Oe++)}const Tr=Ke?Nu(ss):Vt;for(se=Tr.length-1,C=Se-1;C>=0;C--){const Te=J+C,Je=h[Te],Pr=Te+1<L?h[Te+1].el:v;ss[C]===0?E(null,Je,y,Pr,S,w,P,A,k):Ke&&(se<0||C!==Tr[se]?qe(Je,y,Pr,2):se--)}}},qe=(p,h,y,v,S=null)=>{const{el:w,type:P,transition:A,children:k,shapeFlag:C}=p;if(C&6){qe(p.component.subTree,h,y,v);return}if(C&128){p.suspense.move(h,y,v);return}if(C&64){P.move(p,h,y,F);return}if(P===Me){n(w,h,y);for(let $=0;$<k.length;$++)qe(k[$],h,y,v);n(p.anchor,h,y);return}if(P===Vs){N(p,h,y);return}if(v!==2&&C&1&&A)if(v===0)A.beforeEnter(w),n(w,h,y),Pe(()=>A.enter(w),S);else{const{leave:$,delayLeave:I,afterLeave:V}=A,J=()=>{p.ctx.isUnmounted?r(w):n(w,h,y)},le=()=>{$(w,()=>{J(),V&&V()})};I?I(w,J,le):le()}else n(w,h,y)},Ee=(p,h,y,v=!1,S=!1)=>{const{type:w,props:P,ref:A,children:k,dynamicChildren:C,shapeFlag:L,patchFlag:$,dirs:I,cacheIndex:V}=p;if($===-2&&(S=!1),A!=null&&(ut(),Ys(A,null,y,p,!0),ft()),V!=null&&(h.renderCache[V]=void 0),L&256){h.ctx.deactivate(p);return}const J=L&1&&I,le=!fs(p);let se;if(le&&(se=P&&P.onVnodeBeforeUnmount)&&Xe(se,h,p),L&6)Ms(p.component,y,v);else{if(L&128){p.suspense.unmount(y,v);return}J&&Ct(p,null,h,"beforeUnmount"),L&64?p.type.remove(p,h,y,F,v):C&&!C.hasOnce&&(w!==Me||$>0&&$&64)?De(C,h,y,!1,!0):(w===Me&&$&384||!S&&L&16)&&De(k,h,y),v&&Ft(p)}(le&&(se=P&&P.onVnodeUnmounted)||J)&&Pe(()=>{se&&Xe(se,h,p),J&&Ct(p,null,h,"unmounted")},y)},Ft=p=>{const{type:h,el:y,anchor:v,transition:S}=p;if(h===Me){It(y,v);return}if(h===Vs){D(p);return}const w=()=>{r(y),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(p.shapeFlag&1&&S&&!S.persisted){const{leave:P,delayLeave:A}=S,k=()=>P(y,w);A?A(p.el,w,k):k()}else w()},It=(p,h)=>{let y;for(;p!==h;)y=m(p),r(p),p=y;r(h)},Ms=(p,h,y)=>{const{bum:v,scope:S,job:w,subTree:P,um:A,m:k,a:C,parent:L,slots:{__:$}}=p;so(k),so(C),v&&Ls(v),L&&B($)&&$.forEach(I=>{L.renderCache[I]=void 0}),S.stop(),w&&(w.flags|=8,Ee(P,p,h,y)),A&&Pe(A,h),Pe(()=>{p.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},De=(p,h,y,v=!1,S=!1,w=0)=>{for(let P=w;P<p.length;P++)Ee(p[P],h,y,v,S)},_=p=>{if(p.shapeFlag&6)return _(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const h=m(p.anchor||p.el),y=h&&h[eu];return y?m(y):h};let j=!1;const M=(p,h,y)=>{p==null?h._vnode&&Ee(h._vnode,null,null,!0):E(h._vnode||null,p,h,null,null,null,y),h._vnode=p,j||(j=!0,Gr(),Ui(),j=!1)},F={p:E,um:Ee,m:qe,r:Ft,mt:ts,mc:ve,pc:Y,pbc:He,n:_,o:e};return{render:M,hydrate:void 0,createApp:Su(M)}}function $n({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function kt({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function $u(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ol(e,t,s=!1){const n=e.children,r=t.children;if(B(n)&&B(r))for(let o=0;o<n.length;o++){const i=n[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=yt(r[o]),l.el=i.el),!s&&l.patchFlag!==-2&&ol(i,l)),l.type===_n&&(l.el=i.el),l.type===St&&!l.el&&(l.el=i.el)}}function Nu(e){const t=e.slice(),s=[0];let n,r,o,i,l;const a=e.length;for(n=0;n<a;n++){const f=e[n];if(f!==0){if(r=s[s.length-1],e[r]<f){t[n]=r,s.push(n);continue}for(o=0,i=s.length-1;o<i;)l=o+i>>1,e[s[l]]<f?o=l+1:i=l;f<e[s[o]]&&(o>0&&(t[n]=s[o-1]),s[o]=n)}}for(o=s.length,i=s[o-1];o-- >0;)s[o]=i,i=t[i];return s}function il(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:il(t)}function so(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Du=Symbol.for("v-scx"),ju=()=>at(Du);function Bs(e,t,s){return ll(e,t,s)}function ll(e,t,s=oe){const{immediate:n,deep:r,flush:o,once:i}=s,l=xe({},s),a=t&&n||!t&&o!=="post";let f;if(ws){if(o==="sync"){const g=ju();f=g.__watcherHandles||(g.__watcherHandles=[])}else if(!a){const g=()=>{};return g.stop=Ze,g.resume=Ze,g.pause=Ze,g}}const c=ye;l.call=(g,x,E)=>tt(g,c,x,E);let d=!1;o==="post"?l.scheduler=g=>{Pe(g,c&&c.suspense)}:o!=="sync"&&(d=!0,l.scheduler=(g,x)=>{x?g():_r(g)}),l.augmentJob=g=>{t&&(g.flags|=4),d&&(g.flags|=2,c&&(g.id=c.uid,g.i=c))};const m=Xc(e,t,l);return ws&&(f?f.push(m):a&&m()),m}function Fu(e,t,s){const n=this.proxy,r=de(e)?e.includes(".")?al(n,e):()=>n[e]:e.bind(n,n);let o;H(t)?o=t:(o=t.handler,s=t);const i=Ps(this),l=ll(r,o.bind(n),s);return i(),l}function al(e,t){const s=t.split(".");return()=>{let n=e;for(let r=0;r<s.length&&n;r++)n=n[s[r]];return n}}const Iu=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Fe(t)}Modifiers`]||e[`${Nt(t)}Modifiers`];function Lu(e,t,...s){if(e.isUnmounted)return;const n=e.vnode.props||oe;let r=s;const o=t.startsWith("update:"),i=o&&Iu(n,t.slice(7));i&&(i.trim&&(r=s.map(c=>de(c)?c.trim():c)),i.number&&(r=s.map(Ws)));let l,a=n[l=kn(t)]||n[l=kn(Fe(t))];!a&&o&&(a=n[l=kn(Nt(t))]),a&&tt(a,e,6,r);const f=n[l+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,tt(f,e,6,r)}}function cl(e,t,s=!1){const n=t.emitsCache,r=n.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!H(e)){const a=f=>{const c=cl(f,t,!0);c&&(l=!0,xe(i,c))};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(ce(e)&&n.set(e,null),null):(B(o)?o.forEach(a=>i[a]=null):xe(i,o),ce(e)&&n.set(e,i),i)}function vn(e,t){return!e||!un(t)?!1:(t=t.slice(2).replace(/Once$/,""),te(e,t[0].toLowerCase()+t.slice(1))||te(e,Nt(t))||te(e,t))}function no(e){const{type:t,vnode:s,proxy:n,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:a,render:f,renderCache:c,props:d,data:m,setupState:g,ctx:x,inheritAttrs:E}=e,R=Qs(e);let O,T;try{if(s.shapeFlag&4){const D=r||n,z=D;O=Ye(f.call(z,D,c,d,g,m,x)),T=l}else{const D=t;O=Ye(D.length>1?D(d,{attrs:l,slots:i,emit:a}):D(d,null)),T=t.props?l:Uu(l)}}catch(D){ps.length=0,yn(D,e,1),O=G(St)}let N=O;if(T&&E!==!1){const D=Object.keys(T),{shapeFlag:z}=N;D.length&&z&7&&(o&&D.some(ar)&&(T=Bu(T,o)),N=Jt(N,T,!1,!0))}return s.dirs&&(N=Jt(N,null,!1,!0),N.dirs=N.dirs?N.dirs.concat(s.dirs):s.dirs),s.transition&&wr(N,s.transition),O=N,Qs(R),O}const Uu=e=>{let t;for(const s in e)(s==="class"||s==="style"||un(s))&&((t||(t={}))[s]=e[s]);return t},Bu=(e,t)=>{const s={};for(const n in e)(!ar(n)||!(n.slice(9)in t))&&(s[n]=e[n]);return s};function Vu(e,t,s){const{props:n,children:r,component:o}=e,{props:i,children:l,patchFlag:a}=t,f=o.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&a>=0){if(a&1024)return!0;if(a&16)return n?ro(n,i,f):!!i;if(a&8){const c=t.dynamicProps;for(let d=0;d<c.length;d++){const m=c[d];if(i[m]!==n[m]&&!vn(f,m))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:n===i?!1:n?i?ro(n,i,f):!0:!!i;return!1}function ro(e,t,s){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let r=0;r<n.length;r++){const o=n[r];if(t[o]!==e[o]&&!vn(s,o))return!0}return!1}function Hu({vnode:e,parent:t},s){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=s,t=t.parent;else break}}const ul=e=>e.__isSuspense;function zu(e,t){t&&t.pendingBranch?B(e)?t.effects.push(...e):t.effects.push(e):Zc(e)}const Me=Symbol.for("v-fgt"),_n=Symbol.for("v-txt"),St=Symbol.for("v-cmt"),Vs=Symbol.for("v-stc"),ps=[];let Ne=null;function q(e=!1){ps.push(Ne=e?null:[])}function qu(){ps.pop(),Ne=ps[ps.length-1]||null}let _s=1;function oo(e,t=!1){_s+=e,e<0&&Ne&&t&&(Ne.hasOnce=!0)}function fl(e){return e.dynamicChildren=_s>0?Ne||Vt:null,qu(),_s>0&&Ne&&Ne.push(e),e}function W(e,t,s,n,r,o){return fl(u(e,t,s,n,r,o,!0))}function Ku(e,t,s,n,r){return fl(G(e,t,s,n,r,!0))}function en(e){return e?e.__v_isVNode===!0:!1}function os(e,t){return e.type===t.type&&e.key===t.key}const dl=({key:e})=>e??null,Hs=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?de(e)||be(e)||H(e)?{i:$e,r:e,k:t,f:!!s}:e:null);function u(e,t=null,s=null,n=0,r=null,o=e===Me?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&dl(t),ref:t&&Hs(t),scopeId:Vi,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:n,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:$e};return l?(Cr(a,s),o&128&&e.normalize(a)):s&&(a.shapeFlag|=de(s)?8:16),_s>0&&!i&&Ne&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&Ne.push(a),a}const G=Wu;function Wu(e,t=null,s=null,n=0,r=null,o=!1){if((!e||e===mu)&&(e=St),en(e)){const l=Jt(e,t,!0);return s&&Cr(l,s),_s>0&&!o&&Ne&&(l.shapeFlag&6?Ne[Ne.indexOf(e)]=l:Ne.push(l)),l.patchFlag=-2,l}if(rf(e)&&(e=e.__vccOpts),t){t=Ju(t);let{class:l,style:a}=t;l&&!de(l)&&(t.class=hn(l)),ce(a)&&(xr(a)&&!B(a)&&(a=xe({},a)),t.style=fr(a))}const i=de(e)?1:ul(e)?128:tu(e)?64:ce(e)?4:H(e)?2:0;return u(e,t,s,n,r,i,o,!0)}function Ju(e){return e?xr(e)||Zi(e)?xe({},e):e:null}function Jt(e,t,s=!1,n=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:a}=e,f=t?Gu(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&dl(f),ref:t&&t.ref?s&&o?B(o)?o.concat(Hs(t)):[o,Hs(t)]:Hs(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Me?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Jt(e.ssContent),ssFallback:e.ssFallback&&Jt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&n&&wr(c,a.clone(c)),c}function X(e=" ",t=0){return G(_n,null,e,t)}function Rr(e,t){const s=G(Vs,null,e);return s.staticCount=t,s}function _t(e="",t=!1){return t?(q(),Ku(St,null,e)):G(St,null,e)}function Ye(e){return e==null||typeof e=="boolean"?G(St):B(e)?G(Me,null,e.slice()):en(e)?yt(e):G(_n,null,String(e))}function yt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Jt(e)}function Cr(e,t){let s=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(B(t))s=16;else if(typeof t=="object")if(n&65){const r=t.default;r&&(r._c&&(r._d=!1),Cr(e,r()),r._c&&(r._d=!0));return}else{s=32;const r=t._;!r&&!Zi(t)?t._ctx=$e:r===3&&$e&&($e.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else H(t)?(t={default:t,_ctx:$e},s=32):(t=String(t),n&64?(s=16,t=[X(t)]):s=8);e.children=t,e.shapeFlag|=s}function Gu(...e){const t={};for(let s=0;s<e.length;s++){const n=e[s];for(const r in n)if(r==="class")t.class!==n.class&&(t.class=hn([t.class,n.class]));else if(r==="style")t.style=fr([t.style,n.style]);else if(un(r)){const o=t[r],i=n[r];i&&o!==i&&!(B(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=n[r])}return t}function Xe(e,t,s,n=null){tt(e,t,7,[s,n])}const Xu=Xi();let Qu=0;function Yu(e,t,s){const n=e.type,r=(t?t.appContext:e.appContext)||Xu,o={uid:Qu++,vnode:e,type:n,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new wc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:tl(n,r),emitsOptions:cl(n,r),emit:null,emitted:null,propsDefaults:oe,inheritAttrs:n.inheritAttrs,ctx:oe,data:oe,props:oe,attrs:oe,slots:oe,refs:oe,setupState:oe,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Lu.bind(null,o),e.ce&&e.ce(o),o}let ye=null,tn,Zn;{const e=pn(),t=(s,n)=>{let r;return(r=e[s])||(r=e[s]=[]),r.push(n),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};tn=t("__VUE_INSTANCE_SETTERS__",s=>ye=s),Zn=t("__VUE_SSR_SETTERS__",s=>ws=s)}const Ps=e=>{const t=ye;return tn(e),e.scope.on(),()=>{e.scope.off(),tn(t)}},io=()=>{ye&&ye.scope.off(),tn(null)};function pl(e){return e.vnode.shapeFlag&4}let ws=!1;function Zu(e,t=!1,s=!1){t&&Zn(t);const{props:n,children:r}=e.vnode,o=pl(e);Ru(e,n,o,t),Ou(e,r,s||t);const i=o?ef(e,t):void 0;return t&&Zn(!1),i}function ef(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,yu);const{setup:n}=s;if(n){ut();const r=e.setupContext=n.length>1?sf(e):null,o=Ps(e),i=Ts(n,e,0,[e.props,r]),l=pi(i);if(ft(),o(),(l||e.sp)&&!fs(e)&&zi(e),l){if(i.then(io,io),t)return i.then(a=>{lo(e,a)}).catch(a=>{yn(a,e,0)});e.asyncDep=i}else lo(e,i)}else hl(e)}function lo(e,t,s){H(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ce(t)&&(e.setupState=Fi(t)),hl(e)}function hl(e,t,s){const n=e.type;e.render||(e.render=n.render||Ze);{const r=Ps(e);ut();try{bu(e)}finally{ft(),r()}}}const tf={get(e,t){return me(e,"get",""),e[t]}};function sf(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,tf),slots:e.slots,emit:e.emit,expose:t}}function wn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Fi(Vc(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in ds)return ds[s](e)},has(t,s){return s in t||s in ds}})):e.proxy}function nf(e,t=!0){return H(e)?e.displayName||e.name:e.name||t&&e.__name}function rf(e){return H(e)&&"__vccOpts"in e}const Le=(e,t)=>Jc(e,t,ws);function ml(e,t,s){const n=arguments.length;return n===2?ce(t)&&!B(t)?en(t)?G(e,null,[t]):G(e,t):G(e,null,t):(n>3?s=Array.prototype.slice.call(arguments,2):n===3&&en(s)&&(s=[s]),G(e,t,s))}const of="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let er;const ao=typeof window<"u"&&window.trustedTypes;if(ao)try{er=ao.createPolicy("vue",{createHTML:e=>e})}catch{}const gl=er?e=>er.createHTML(e):e=>e,lf="http://www.w3.org/2000/svg",af="http://www.w3.org/1998/Math/MathML",ot=typeof document<"u"?document:null,co=ot&&ot.createElement("template"),cf={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,n)=>{const r=t==="svg"?ot.createElementNS(lf,e):t==="mathml"?ot.createElementNS(af,e):s?ot.createElement(e,{is:s}):ot.createElement(e);return e==="select"&&n&&n.multiple!=null&&r.setAttribute("multiple",n.multiple),r},createText:e=>ot.createTextNode(e),createComment:e=>ot.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ot.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,n,r,o){const i=s?s.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),s),!(r===o||!(r=r.nextSibling)););else{co.innerHTML=gl(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const l=co.content;if(n==="svg"||n==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,s)}return[i?i.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},uf=Symbol("_vtc");function ff(e,t,s){const n=e[uf];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const uo=Symbol("_vod"),df=Symbol("_vsh"),pf=Symbol(""),hf=/(^|;)\s*display\s*:/;function mf(e,t,s){const n=e.style,r=de(s);let o=!1;if(s&&!r){if(t)if(de(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();s[l]==null&&zs(n,l,"")}else for(const i in t)s[i]==null&&zs(n,i,"");for(const i in s)i==="display"&&(o=!0),zs(n,i,s[i])}else if(r){if(t!==s){const i=n[pf];i&&(s+=";"+i),n.cssText=s,o=hf.test(s)}}else t&&e.removeAttribute("style");uo in e&&(e[uo]=o?n.display:"",e[df]&&(n.display="none"))}const fo=/\s*!important$/;function zs(e,t,s){if(B(s))s.forEach(n=>zs(e,t,n));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const n=gf(e,t);fo.test(s)?e.setProperty(Nt(n),s.replace(fo,""),"important"):e[n]=s}}const po=["Webkit","Moz","ms"],Nn={};function gf(e,t){const s=Nn[t];if(s)return s;let n=Fe(t);if(n!=="filter"&&n in e)return Nn[t]=n;n=dn(n);for(let r=0;r<po.length;r++){const o=po[r]+n;if(o in e)return Nn[t]=o}return t}const ho="http://www.w3.org/1999/xlink";function mo(e,t,s,n,r,o=vc(t)){n&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(ho,t.slice(6,t.length)):e.setAttributeNS(ho,t,s):s==null||o&&!yi(s)?e.removeAttribute(t):e.setAttribute(t,o?"":et(s)?String(s):s)}function go(e,t,s,n,r){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?gl(s):s);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=s==null?e.type==="checkbox"?"on":"":String(s);(l!==a||!("_value"in e))&&(e.value=a),s==null&&e.removeAttribute(t),e._value=s;return}let i=!1;if(s===""||s==null){const l=typeof e[t];l==="boolean"?s=yi(s):s==null&&l==="string"?(s="",i=!0):l==="number"&&(s=0,i=!0)}try{e[t]=s}catch{}i&&e.removeAttribute(r||t)}function xt(e,t,s,n){e.addEventListener(t,s,n)}function yf(e,t,s,n){e.removeEventListener(t,s,n)}const yo=Symbol("_vei");function bf(e,t,s,n,r=null){const o=e[yo]||(e[yo]={}),i=o[t];if(n&&i)i.value=n;else{const[l,a]=xf(t);if(n){const f=o[t]=wf(n,r);xt(e,l,f,a)}else i&&(yf(e,l,i,a),o[t]=void 0)}}const bo=/(?:Once|Passive|Capture)$/;function xf(e){let t;if(bo.test(e)){t={};let n;for(;n=e.match(bo);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Nt(e.slice(2)),t]}let Dn=0;const vf=Promise.resolve(),_f=()=>Dn||(vf.then(()=>Dn=0),Dn=Date.now());function wf(e,t){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;tt(Ef(n,s.value),t,5,[n])};return s.value=e,s.attached=_f(),s}function Ef(e,t){if(B(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(n=>r=>!r._stopped&&n&&n(r))}else return t}const xo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Sf=(e,t,s,n,r,o)=>{const i=r==="svg";t==="class"?ff(e,n,i):t==="style"?mf(e,s,n):un(t)?ar(t)||bf(e,t,s,n,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Rf(e,t,n,i))?(go(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&mo(e,t,n,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!de(n))?go(e,Fe(t),n,o,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),mo(e,t,n,i))};function Rf(e,t,s,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&xo(t)&&H(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return xo(t)&&de(s)?!1:t in e}const Gt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return B(t)?s=>Ls(t,s):t};function Cf(e){e.target.composing=!0}function vo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ct=Symbol("_assign"),Tt={created(e,{modifiers:{lazy:t,trim:s,number:n}},r){e[ct]=Gt(r);const o=n||r.props&&r.props.type==="number";xt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;s&&(l=l.trim()),o&&(l=Ws(l)),e[ct](l)}),s&&xt(e,"change",()=>{e.value=e.value.trim()}),t||(xt(e,"compositionstart",Cf),xt(e,"compositionend",vo),xt(e,"change",vo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:n,trim:r,number:o}},i){if(e[ct]=Gt(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?Ws(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(n&&t===s||r&&e.value.trim()===a)||(e.value=a))}},yl={deep:!0,created(e,t,s){e[ct]=Gt(s),xt(e,"change",()=>{const n=e._modelValue,r=Es(e),o=e.checked,i=e[ct];if(B(n)){const l=dr(n,r),a=l!==-1;if(o&&!a)i(n.concat(r));else if(!o&&a){const f=[...n];f.splice(l,1),i(f)}}else if(es(n)){const l=new Set(n);o?l.add(r):l.delete(r),i(l)}else i(bl(e,o))})},mounted:_o,beforeUpdate(e,t,s){e[ct]=Gt(s),_o(e,t,s)}};function _o(e,{value:t,oldValue:s},n){e._modelValue=t;let r;if(B(t))r=dr(t,n.props.value)>-1;else if(es(t))r=t.has(n.props.value);else{if(t===s)return;r=Os(t,bl(e,!0))}e.checked!==r&&(e.checked=r)}const hs={deep:!0,created(e,{value:t,modifiers:{number:s}},n){const r=es(t);xt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>s?Ws(Es(i)):Es(i));e[ct](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,vr(()=>{e._assigning=!1})}),e[ct]=Gt(n)},mounted(e,{value:t}){wo(e,t)},beforeUpdate(e,t,s){e[ct]=Gt(s)},updated(e,{value:t}){e._assigning||wo(e,t)}};function wo(e,t){const s=e.multiple,n=B(t);if(!(s&&!n&&!es(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],l=Es(i);if(s)if(n){const a=typeof l;a==="string"||a==="number"?i.selected=t.some(f=>String(f)===String(l)):i.selected=dr(t,l)>-1}else i.selected=t.has(l);else if(Os(Es(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Es(e){return"_value"in e?e._value:e.value}function bl(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const kf=["ctrl","shift","alt","meta"],Af={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>kf.some(s=>e[`${s}Key`]&&!t.includes(s))},xl=(e,t)=>{const s=e._withMods||(e._withMods={}),n=t.join(".");return s[n]||(s[n]=(r,...o)=>{for(let i=0;i<t.length;i++){const l=Af[t[i]];if(l&&l(r,t))return}return e(r,...o)})},Of=xe({patchProp:Sf},cf);let Eo;function Tf(){return Eo||(Eo=Pu(Of))}const Pf=(...e)=>{const t=Tf().createApp(...e),{mount:s}=t;return t.mount=n=>{const r=$f(n);if(!r)return;const o=t._component;!H(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=s(r,!1,Mf(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Mf(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function $f(e){return de(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Bt=typeof document<"u";function vl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Nf(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&vl(e.default)}const Z=Object.assign;function jn(e,t){const s={};for(const n in t){const r=t[n];s[n]=Be(r)?r.map(e):e(r)}return s}const ms=()=>{},Be=Array.isArray,_l=/#/g,Df=/&/g,jf=/\//g,Ff=/=/g,If=/\?/g,wl=/\+/g,Lf=/%5B/g,Uf=/%5D/g,El=/%5E/g,Bf=/%60/g,Sl=/%7B/g,Vf=/%7C/g,Rl=/%7D/g,Hf=/%20/g;function kr(e){return encodeURI(""+e).replace(Vf,"|").replace(Lf,"[").replace(Uf,"]")}function zf(e){return kr(e).replace(Sl,"{").replace(Rl,"}").replace(El,"^")}function tr(e){return kr(e).replace(wl,"%2B").replace(Hf,"+").replace(_l,"%23").replace(Df,"%26").replace(Bf,"`").replace(Sl,"{").replace(Rl,"}").replace(El,"^")}function qf(e){return tr(e).replace(Ff,"%3D")}function Kf(e){return kr(e).replace(_l,"%23").replace(If,"%3F")}function Wf(e){return e==null?"":Kf(e).replace(jf,"%2F")}function Ss(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Jf=/\/$/,Gf=e=>e.replace(Jf,"");function Fn(e,t,s="/"){let n,r={},o="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(n=t.slice(0,a),o=t.slice(a+1,l>-1?l:t.length),r=e(o)),l>-1&&(n=n||t.slice(0,l),i=t.slice(l,t.length)),n=Zf(n??t,s),{fullPath:n+(o&&"?")+o+i,path:n,query:r,hash:Ss(i)}}function Xf(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function So(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Qf(e,t,s){const n=t.matched.length-1,r=s.matched.length-1;return n>-1&&n===r&&Xt(t.matched[n],s.matched[r])&&Cl(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Xt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Cl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!Yf(e[s],t[s]))return!1;return!0}function Yf(e,t){return Be(e)?Ro(e,t):Be(t)?Ro(t,e):e===t}function Ro(e,t){return Be(t)?e.length===t.length&&e.every((s,n)=>s===t[n]):e.length===1&&e[0]===t}function Zf(e,t){if(e.startsWith("/"))return e;if(!e)return t;const s=t.split("/"),n=e.split("/"),r=n[n.length-1];(r===".."||r===".")&&n.push("");let o=s.length-1,i,l;for(i=0;i<n.length;i++)if(l=n[i],l!==".")if(l==="..")o>1&&o--;else break;return s.slice(0,o).join("/")+"/"+n.slice(i).join("/")}const mt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Rs;(function(e){e.pop="pop",e.push="push"})(Rs||(Rs={}));var gs;(function(e){e.back="back",e.forward="forward",e.unknown=""})(gs||(gs={}));function ed(e){if(!e)if(Bt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Gf(e)}const td=/^[^#]+#/;function sd(e,t){return e.replace(td,"#")+t}function nd(e,t){const s=document.documentElement.getBoundingClientRect(),n=e.getBoundingClientRect();return{behavior:t.behavior,left:n.left-s.left-(t.left||0),top:n.top-s.top-(t.top||0)}}const En=()=>({left:window.scrollX,top:window.scrollY});function rd(e){let t;if("el"in e){const s=e.el,n=typeof s=="string"&&s.startsWith("#"),r=typeof s=="string"?n?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!r)return;t=nd(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Co(e,t){return(history.state?history.state.position-t:-1)+e}const sr=new Map;function od(e,t){sr.set(e,t)}function id(e){const t=sr.get(e);return sr.delete(e),t}let ld=()=>location.protocol+"//"+location.host;function kl(e,t){const{pathname:s,search:n,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,a=r.slice(l);return a[0]!=="/"&&(a="/"+a),So(a,"")}return So(s,e)+n+r}function ad(e,t,s,n){let r=[],o=[],i=null;const l=({state:m})=>{const g=kl(e,location),x=s.value,E=t.value;let R=0;if(m){if(s.value=g,t.value=m,i&&i===x){i=null;return}R=E?m.position-E.position:0}else n(g);r.forEach(O=>{O(s.value,x,{delta:R,type:Rs.pop,direction:R?R>0?gs.forward:gs.back:gs.unknown})})};function a(){i=s.value}function f(m){r.push(m);const g=()=>{const x=r.indexOf(m);x>-1&&r.splice(x,1)};return o.push(g),g}function c(){const{history:m}=window;m.state&&m.replaceState(Z({},m.state,{scroll:En()}),"")}function d(){for(const m of o)m();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:f,destroy:d}}function ko(e,t,s,n=!1,r=!1){return{back:e,current:t,forward:s,replaced:n,position:window.history.length,scroll:r?En():null}}function cd(e){const{history:t,location:s}=window,n={value:kl(e,s)},r={value:t.state};r.value||o(n.value,{back:null,current:n.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,f,c){const d=e.indexOf("#"),m=d>-1?(s.host&&document.querySelector("base")?e:e.slice(d))+a:ld()+e+a;try{t[c?"replaceState":"pushState"](f,"",m),r.value=f}catch(g){console.error(g),s[c?"replace":"assign"](m)}}function i(a,f){const c=Z({},t.state,ko(r.value.back,a,r.value.forward,!0),f,{position:r.value.position});o(a,c,!0),n.value=a}function l(a,f){const c=Z({},r.value,t.state,{forward:a,scroll:En()});o(c.current,c,!0);const d=Z({},ko(n.value,a,null),{position:c.position+1},f);o(a,d,!1),n.value=a}return{location:n,state:r,push:l,replace:i}}function ud(e){e=ed(e);const t=cd(e),s=ad(e,t.state,t.location,t.replace);function n(o,i=!0){i||s.pauseListeners(),history.go(o)}const r=Z({location:"",base:e,go:n,createHref:sd.bind(null,e)},t,s);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function fd(e){return typeof e=="string"||e&&typeof e=="object"}function Al(e){return typeof e=="string"||typeof e=="symbol"}const Ol=Symbol("");var Ao;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ao||(Ao={}));function Qt(e,t){return Z(new Error,{type:e,[Ol]:!0},t)}function rt(e,t){return e instanceof Error&&Ol in e&&(t==null||!!(e.type&t))}const Oo="[^/]+?",dd={sensitive:!1,strict:!1,start:!0,end:!0},pd=/[.+*?^${}()[\]/\\]/g;function hd(e,t){const s=Z({},dd,t),n=[];let r=s.start?"^":"";const o=[];for(const f of e){const c=f.length?[]:[90];s.strict&&!f.length&&(r+="/");for(let d=0;d<f.length;d++){const m=f[d];let g=40+(s.sensitive?.25:0);if(m.type===0)d||(r+="/"),r+=m.value.replace(pd,"\\$&"),g+=40;else if(m.type===1){const{value:x,repeatable:E,optional:R,regexp:O}=m;o.push({name:x,repeatable:E,optional:R});const T=O||Oo;if(T!==Oo){g+=10;try{new RegExp(`(${T})`)}catch(D){throw new Error(`Invalid custom RegExp for param "${x}" (${T}): `+D.message)}}let N=E?`((?:${T})(?:/(?:${T}))*)`:`(${T})`;d||(N=R&&f.length<2?`(?:/${N})`:"/"+N),R&&(N+="?"),r+=N,g+=20,R&&(g+=-8),E&&(g+=-20),T===".*"&&(g+=-50)}c.push(g)}n.push(c)}if(s.strict&&s.end){const f=n.length-1;n[f][n[f].length-1]+=.7000000000000001}s.strict||(r+="/?"),s.end?r+="$":s.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,s.sensitive?"":"i");function l(f){const c=f.match(i),d={};if(!c)return null;for(let m=1;m<c.length;m++){const g=c[m]||"",x=o[m-1];d[x.name]=g&&x.repeatable?g.split("/"):g}return d}function a(f){let c="",d=!1;for(const m of e){(!d||!c.endsWith("/"))&&(c+="/"),d=!1;for(const g of m)if(g.type===0)c+=g.value;else if(g.type===1){const{value:x,repeatable:E,optional:R}=g,O=x in f?f[x]:"";if(Be(O)&&!E)throw new Error(`Provided param "${x}" is an array but it is not repeatable (* or + modifiers)`);const T=Be(O)?O.join("/"):O;if(!T)if(R)m.length<2&&(c.endsWith("/")?c=c.slice(0,-1):d=!0);else throw new Error(`Missing required param "${x}"`);c+=T}}return c||"/"}return{re:i,score:n,keys:o,parse:l,stringify:a}}function md(e,t){let s=0;for(;s<e.length&&s<t.length;){const n=t[s]-e[s];if(n)return n;s++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Tl(e,t){let s=0;const n=e.score,r=t.score;for(;s<n.length&&s<r.length;){const o=md(n[s],r[s]);if(o)return o;s++}if(Math.abs(r.length-n.length)===1){if(To(n))return 1;if(To(r))return-1}return r.length-n.length}function To(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const gd={type:0,value:""},yd=/[a-zA-Z0-9_]/;function bd(e){if(!e)return[[]];if(e==="/")return[[gd]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${s})/"${f}": ${g}`)}let s=0,n=s;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,a,f="",c="";function d(){f&&(s===0?o.push({type:0,value:f}):s===1||s===2||s===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:f,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),f="")}function m(){f+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&s!==2){n=s,s=4;continue}switch(s){case 0:a==="/"?(f&&d(),i()):a===":"?(d(),s=1):m();break;case 4:m(),s=n;break;case 1:a==="("?s=2:yd.test(a)?m():(d(),s=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:s=3:c+=a;break;case 3:d(),s=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${f}"`),d(),i(),r}function xd(e,t,s){const n=hd(bd(e.path),s),r=Z(n,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function vd(e,t){const s=[],n=new Map;t=No({strict:!1,end:!0,sensitive:!1},t);function r(d){return n.get(d)}function o(d,m,g){const x=!g,E=Mo(d);E.aliasOf=g&&g.record;const R=No(t,d),O=[E];if("alias"in d){const D=typeof d.alias=="string"?[d.alias]:d.alias;for(const z of D)O.push(Mo(Z({},E,{components:g?g.record.components:E.components,path:z,aliasOf:g?g.record:E})))}let T,N;for(const D of O){const{path:z}=D;if(m&&z[0]!=="/"){const ie=m.record.path,Q=ie[ie.length-1]==="/"?"":"/";D.path=m.record.path+(z&&Q+z)}if(T=xd(D,m,R),g?g.alias.push(T):(N=N||T,N!==T&&N.alias.push(T),x&&d.name&&!$o(T)&&i(d.name)),Pl(T)&&a(T),E.children){const ie=E.children;for(let Q=0;Q<ie.length;Q++)o(ie[Q],T,g&&g.children[Q])}g=g||T}return N?()=>{i(N)}:ms}function i(d){if(Al(d)){const m=n.get(d);m&&(n.delete(d),s.splice(s.indexOf(m),1),m.children.forEach(i),m.alias.forEach(i))}else{const m=s.indexOf(d);m>-1&&(s.splice(m,1),d.record.name&&n.delete(d.record.name),d.children.forEach(i),d.alias.forEach(i))}}function l(){return s}function a(d){const m=Ed(d,s);s.splice(m,0,d),d.record.name&&!$o(d)&&n.set(d.record.name,d)}function f(d,m){let g,x={},E,R;if("name"in d&&d.name){if(g=n.get(d.name),!g)throw Qt(1,{location:d});R=g.record.name,x=Z(Po(m.params,g.keys.filter(N=>!N.optional).concat(g.parent?g.parent.keys.filter(N=>N.optional):[]).map(N=>N.name)),d.params&&Po(d.params,g.keys.map(N=>N.name))),E=g.stringify(x)}else if(d.path!=null)E=d.path,g=s.find(N=>N.re.test(E)),g&&(x=g.parse(E),R=g.record.name);else{if(g=m.name?n.get(m.name):s.find(N=>N.re.test(m.path)),!g)throw Qt(1,{location:d,currentLocation:m});R=g.record.name,x=Z({},m.params,d.params),E=g.stringify(x)}const O=[];let T=g;for(;T;)O.unshift(T.record),T=T.parent;return{name:R,path:E,params:x,matched:O,meta:wd(O)}}e.forEach(d=>o(d));function c(){s.length=0,n.clear()}return{addRoute:o,resolve:f,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:r}}function Po(e,t){const s={};for(const n of t)n in e&&(s[n]=e[n]);return s}function Mo(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:_d(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function _d(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const n in e.components)t[n]=typeof s=="object"?s[n]:s;return t}function $o(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function wd(e){return e.reduce((t,s)=>Z(t,s.meta),{})}function No(e,t){const s={};for(const n in e)s[n]=n in t?t[n]:e[n];return s}function Ed(e,t){let s=0,n=t.length;for(;s!==n;){const o=s+n>>1;Tl(e,t[o])<0?n=o:s=o+1}const r=Sd(e);return r&&(n=t.lastIndexOf(r,n-1)),n}function Sd(e){let t=e;for(;t=t.parent;)if(Pl(t)&&Tl(e,t)===0)return t}function Pl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Rd(e){const t={};if(e===""||e==="?")return t;const n=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<n.length;++r){const o=n[r].replace(wl," "),i=o.indexOf("="),l=Ss(i<0?o:o.slice(0,i)),a=i<0?null:Ss(o.slice(i+1));if(l in t){let f=t[l];Be(f)||(f=t[l]=[f]),f.push(a)}else t[l]=a}return t}function Do(e){let t="";for(let s in e){const n=e[s];if(s=qf(s),n==null){n!==void 0&&(t+=(t.length?"&":"")+s);continue}(Be(n)?n.map(o=>o&&tr(o)):[n&&tr(n)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+s,o!=null&&(t+="="+o))})}return t}function Cd(e){const t={};for(const s in e){const n=e[s];n!==void 0&&(t[s]=Be(n)?n.map(r=>r==null?null:""+r):n==null?n:""+n)}return t}const kd=Symbol(""),jo=Symbol(""),Ar=Symbol(""),Ml=Symbol(""),nr=Symbol("");function is(){let e=[];function t(n){return e.push(n),()=>{const r=e.indexOf(n);r>-1&&e.splice(r,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function bt(e,t,s,n,r,o=i=>i()){const i=n&&(n.enterCallbacks[r]=n.enterCallbacks[r]||[]);return()=>new Promise((l,a)=>{const f=m=>{m===!1?a(Qt(4,{from:s,to:t})):m instanceof Error?a(m):fd(m)?a(Qt(2,{from:t,to:m})):(i&&n.enterCallbacks[r]===i&&typeof m=="function"&&i.push(m),l())},c=o(()=>e.call(n&&n.instances[r],t,s,f));let d=Promise.resolve(c);e.length<3&&(d=d.then(f)),d.catch(m=>a(m))})}function In(e,t,s,n,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(vl(a)){const c=(a.__vccOpts||a)[t];c&&o.push(bt(c,s,n,i,l,r))}else{let f=a();o.push(()=>f.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const d=Nf(c)?c.default:c;i.mods[l]=c,i.components[l]=d;const g=(d.__vccOpts||d)[t];return g&&bt(g,s,n,i,l,r)()}))}}return o}function Fo(e){const t=at(Ar),s=at(Ml),n=Le(()=>{const a=qt(e.to);return t.resolve(a)}),r=Le(()=>{const{matched:a}=n.value,{length:f}=a,c=a[f-1],d=s.matched;if(!c||!d.length)return-1;const m=d.findIndex(Xt.bind(null,c));if(m>-1)return m;const g=Io(a[f-2]);return f>1&&Io(c)===g&&d[d.length-1].path!==g?d.findIndex(Xt.bind(null,a[f-2])):m}),o=Le(()=>r.value>-1&&Md(s.params,n.value.params)),i=Le(()=>r.value>-1&&r.value===s.matched.length-1&&Cl(s.params,n.value.params));function l(a={}){if(Pd(a)){const f=t[qt(e.replace)?"replace":"push"](qt(e.to)).catch(ms);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>f),f}return Promise.resolve()}return{route:n,href:Le(()=>n.value.href),isActive:o,isExactActive:i,navigate:l}}function Ad(e){return e.length===1?e[0]:e}const Od=Hi({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Fo,setup(e,{slots:t}){const s=gn(Fo(e)),{options:n}=at(Ar),r=Le(()=>({[Lo(e.activeClass,n.linkActiveClass,"router-link-active")]:s.isActive,[Lo(e.exactActiveClass,n.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const o=t.default&&Ad(t.default(s));return e.custom?o:ml("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:r.value},o)}}}),Td=Od;function Pd(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Md(e,t){for(const s in t){const n=t[s],r=e[s];if(typeof n=="string"){if(n!==r)return!1}else if(!Be(r)||r.length!==n.length||n.some((o,i)=>o!==r[i]))return!1}return!0}function Io(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Lo=(e,t,s)=>e??t??s,$d=Hi({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){const n=at(nr),r=Le(()=>e.route||n.value),o=at(jo,0),i=Le(()=>{let f=qt(o);const{matched:c}=r.value;let d;for(;(d=c[f])&&!d.components;)f++;return f}),l=Le(()=>r.value.matched[i.value]);Us(jo,Le(()=>i.value+1)),Us(kd,l),Us(nr,r);const a=Hc();return Bs(()=>[a.value,l.value,e.name],([f,c,d],[m,g,x])=>{c&&(c.instances[d]=f,g&&g!==c&&f&&f===m&&(c.leaveGuards.size||(c.leaveGuards=g.leaveGuards),c.updateGuards.size||(c.updateGuards=g.updateGuards))),f&&c&&(!g||!Xt(c,g)||!m)&&(c.enterCallbacks[d]||[]).forEach(E=>E(f))},{flush:"post"}),()=>{const f=r.value,c=e.name,d=l.value,m=d&&d.components[c];if(!m)return Uo(s.default,{Component:m,route:f});const g=d.props[c],x=g?g===!0?f.params:typeof g=="function"?g(f):g:null,R=ml(m,Z({},x,t,{onVnodeUnmounted:O=>{O.component.isUnmounted&&(d.instances[c]=null)},ref:a}));return Uo(s.default,{Component:R,route:f})||R}}});function Uo(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const Nd=$d;function Dd(e){const t=vd(e.routes,e),s=e.parseQuery||Rd,n=e.stringifyQuery||Do,r=e.history,o=is(),i=is(),l=is(),a=zc(mt);let f=mt;Bt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=jn.bind(null,_=>""+_),d=jn.bind(null,Wf),m=jn.bind(null,Ss);function g(_,j){let M,F;return Al(_)?(M=t.getRecordMatcher(_),F=j):F=_,t.addRoute(F,M)}function x(_){const j=t.getRecordMatcher(_);j&&t.removeRoute(j)}function E(){return t.getRoutes().map(_=>_.record)}function R(_){return!!t.getRecordMatcher(_)}function O(_,j){if(j=Z({},j||a.value),typeof _=="string"){const y=Fn(s,_,j.path),v=t.resolve({path:y.path},j),S=r.createHref(y.fullPath);return Z(y,v,{params:m(v.params),hash:Ss(y.hash),redirectedFrom:void 0,href:S})}let M;if(_.path!=null)M=Z({},_,{path:Fn(s,_.path,j.path).path});else{const y=Z({},_.params);for(const v in y)y[v]==null&&delete y[v];M=Z({},_,{params:d(y)}),j.params=d(j.params)}const F=t.resolve(M,j),re=_.hash||"";F.params=c(m(F.params));const p=Xf(n,Z({},_,{hash:zf(re),path:F.path})),h=r.createHref(p);return Z({fullPath:p,hash:re,query:n===Do?Cd(_.query):_.query||{}},F,{redirectedFrom:void 0,href:h})}function T(_){return typeof _=="string"?Fn(s,_,a.value.path):Z({},_)}function N(_,j){if(f!==_)return Qt(8,{from:j,to:_})}function D(_){return Q(_)}function z(_){return D(Z(T(_),{replace:!0}))}function ie(_){const j=_.matched[_.matched.length-1];if(j&&j.redirect){const{redirect:M}=j;let F=typeof M=="function"?M(_):M;return typeof F=="string"&&(F=F.includes("?")||F.includes("#")?F=T(F):{path:F},F.params={}),Z({query:_.query,hash:_.hash,params:F.path!=null?{}:_.params},F)}}function Q(_,j){const M=f=O(_),F=a.value,re=_.state,p=_.force,h=_.replace===!0,y=ie(M);if(y)return Q(Z(T(y),{state:typeof y=="object"?Z({},re,y.state):re,force:p,replace:h}),j||M);const v=M;v.redirectedFrom=j;let S;return!p&&Qf(n,F,M)&&(S=Qt(16,{to:v,from:F}),qe(F,F,!0,!1)),(S?Promise.resolve(S):He(v,F)).catch(w=>rt(w)?rt(w,2)?w:ht(w):Y(w,v,F)).then(w=>{if(w){if(rt(w,2))return Q(Z({replace:h},T(w.to),{state:typeof w.to=="object"?Z({},re,w.to.state):re,force:p}),j||v)}else w=Rt(v,F,!0,h,re);return pt(v,F,w),w})}function ve(_,j){const M=N(_,j);return M?Promise.reject(M):Promise.resolve()}function Ie(_){const j=It.values().next().value;return j&&typeof j.runWithContext=="function"?j.runWithContext(_):_()}function He(_,j){let M;const[F,re,p]=jd(_,j);M=In(F.reverse(),"beforeRouteLeave",_,j);for(const y of F)y.leaveGuards.forEach(v=>{M.push(bt(v,_,j))});const h=ve.bind(null,_,j);return M.push(h),De(M).then(()=>{M=[];for(const y of o.list())M.push(bt(y,_,j));return M.push(h),De(M)}).then(()=>{M=In(re,"beforeRouteUpdate",_,j);for(const y of re)y.updateGuards.forEach(v=>{M.push(bt(v,_,j))});return M.push(h),De(M)}).then(()=>{M=[];for(const y of p)if(y.beforeEnter)if(Be(y.beforeEnter))for(const v of y.beforeEnter)M.push(bt(v,_,j));else M.push(bt(y.beforeEnter,_,j));return M.push(h),De(M)}).then(()=>(_.matched.forEach(y=>y.enterCallbacks={}),M=In(p,"beforeRouteEnter",_,j,Ie),M.push(h),De(M))).then(()=>{M=[];for(const y of i.list())M.push(bt(y,_,j));return M.push(h),De(M)}).catch(y=>rt(y,8)?y:Promise.reject(y))}function pt(_,j,M){l.list().forEach(F=>Ie(()=>F(_,j,M)))}function Rt(_,j,M,F,re){const p=N(_,j);if(p)return p;const h=j===mt,y=Bt?history.state:{};M&&(F||h?r.replace(_.fullPath,Z({scroll:h&&y&&y.scroll},re)):r.push(_.fullPath,re)),a.value=_,qe(_,j,M,h),ht()}let ze;function ts(){ze||(ze=r.listen((_,j,M)=>{if(!Ms.listening)return;const F=O(_),re=ie(F);if(re){Q(Z(re,{replace:!0,force:!0}),F).catch(ms);return}f=F;const p=a.value;Bt&&od(Co(p.fullPath,M.delta),En()),He(F,p).catch(h=>rt(h,12)?h:rt(h,2)?(Q(Z(T(h.to),{force:!0}),F).then(y=>{rt(y,20)&&!M.delta&&M.type===Rs.pop&&r.go(-1,!1)}).catch(ms),Promise.reject()):(M.delta&&r.go(-M.delta,!1),Y(h,F,p))).then(h=>{h=h||Rt(F,p,!1),h&&(M.delta&&!rt(h,8)?r.go(-M.delta,!1):M.type===Rs.pop&&rt(h,20)&&r.go(-1,!1)),pt(F,p,h)}).catch(ms)}))}let jt=is(),pe=is(),ne;function Y(_,j,M){ht(_);const F=pe.list();return F.length?F.forEach(re=>re(_,j,M)):console.error(_),Promise.reject(_)}function st(){return ne&&a.value!==mt?Promise.resolve():new Promise((_,j)=>{jt.add([_,j])})}function ht(_){return ne||(ne=!_,ts(),jt.list().forEach(([j,M])=>_?M(_):j()),jt.reset()),_}function qe(_,j,M,F){const{scrollBehavior:re}=e;if(!Bt||!re)return Promise.resolve();const p=!M&&id(Co(_.fullPath,0))||(F||!M)&&history.state&&history.state.scroll||null;return vr().then(()=>re(_,j,p)).then(h=>h&&rd(h)).catch(h=>Y(h,_,j))}const Ee=_=>r.go(_);let Ft;const It=new Set,Ms={currentRoute:a,listening:!0,addRoute:g,removeRoute:x,clearRoutes:t.clearRoutes,hasRoute:R,getRoutes:E,resolve:O,options:e,push:D,replace:z,go:Ee,back:()=>Ee(-1),forward:()=>Ee(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:pe.add,isReady:st,install(_){const j=this;_.component("RouterLink",Td),_.component("RouterView",Nd),_.config.globalProperties.$router=j,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>qt(a)}),Bt&&!Ft&&a.value===mt&&(Ft=!0,D(r.location).catch(re=>{}));const M={};for(const re in mt)Object.defineProperty(M,re,{get:()=>a.value[re],enumerable:!0});_.provide(Ar,j),_.provide(Ml,Ni(M)),_.provide(nr,a);const F=_.unmount;It.add(_),_.unmount=function(){It.delete(_),It.size<1&&(f=mt,ze&&ze(),ze=null,a.value=mt,Ft=!1,ne=!1),F()}}};function De(_){return _.reduce((j,M)=>j.then(()=>Ie(M)),Promise.resolve())}return Ms}function jd(e,t){const s=[],n=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(f=>Xt(f,l))?n.push(l):s.push(l));const a=e.matched[i];a&&(t.matched.find(f=>Xt(f,a))||r.push(a))}return[s,n,r]}const Dt=(e,t)=>{const s=e.__vccOpts||e;for(const[n,r]of t)s[n]=r;return s},Fd={name:"App",data(){return{user:null}},computed:{isAuthenticated(){return!!localStorage.getItem("auth_token")}},methods:{async logout(){try{await this.$http.post("/logout"),localStorage.removeItem("auth_token"),localStorage.removeItem("user"),delete this.$http.defaults.headers.common.Authorization,this.$router.push("/")}catch(e){console.error("Logout error:",e),localStorage.removeItem("auth_token"),localStorage.removeItem("user"),delete this.$http.defaults.headers.common.Authorization,this.$router.push("/")}}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e))}},Id={id:"app",class:"min-h-screen bg-gray-50"},Ld={class:"bg-white shadow-lg border-b border-kabyle-200"},Ud={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Bd={class:"flex justify-between h-16"},Vd={class:"flex items-center"},Hd={class:"flex items-center space-x-4"},zd={key:0,class:"flex items-center space-x-2"},qd={key:1,class:"flex items-center space-x-2"},Kd={class:"flex-1"},Wd={class:"bg-gray-900 text-white"},Jd={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},Gd={class:"grid grid-cols-1 md:grid-cols-3 gap-8"},Xd={class:"space-y-2"};function Qd(e,t,s,n,r,o){const i=Et("router-link"),l=Et("router-view");return q(),W("div",Id,[u("nav",Ld,[u("div",Ud,[u("div",Bd,[u("div",Vd,[G(i,{to:"/",class:"flex items-center space-x-2"},{default:fe(()=>t[1]||(t[1]=[u("div",{class:"w-8 h-8 bg-gradient-to-r from-kabyle-500 to-amazigh-500 rounded-lg flex items-center justify-center"},[u("span",{class:"text-white font-bold text-lg"},"K")],-1),u("span",{class:"text-xl font-bold text-gray-900"},"KabEvents",-1)])),_:1,__:[1]})]),u("div",Hd,[G(i,{to:"/events",class:"text-gray-700 hover:text-kabyle-600 px-3 py-2 rounded-md text-sm font-medium"},{default:fe(()=>t[2]||(t[2]=[X(" Événements ")])),_:1,__:[2]}),o.isAuthenticated?(q(),W("div",qd,[G(i,{to:"/dashboard",class:"text-gray-700 hover:text-kabyle-600 px-3 py-2 rounded-md text-sm font-medium"},{default:fe(()=>t[5]||(t[5]=[X(" Dashboard ")])),_:1,__:[5]}),u("button",{onClick:t[0]||(t[0]=(...a)=>o.logout&&o.logout(...a)),class:"text-gray-700 hover:text-red-600 px-3 py-2 rounded-md text-sm font-medium"}," Déconnexion ")])):(q(),W("div",zd,[G(i,{to:"/login",class:"text-gray-700 hover:text-kabyle-600 px-3 py-2 rounded-md text-sm font-medium"},{default:fe(()=>t[3]||(t[3]=[X(" Connexion ")])),_:1,__:[3]}),G(i,{to:"/register",class:"btn-primary"},{default:fe(()=>t[4]||(t[4]=[X(" S'inscrire ")])),_:1,__:[4]})]))])])])]),u("main",Kd,[G(l)]),u("footer",Wd,[u("div",Jd,[u("div",Gd,[t[9]||(t[9]=u("div",null,[u("h3",{class:"text-lg font-semibold mb-4"},"KabEvents"),u("p",{class:"text-gray-300"}," La plateforme de référence pour les événements culturels kabyles au Canada. ")],-1)),u("div",null,[t[8]||(t[8]=u("h3",{class:"text-lg font-semibold mb-4"},"Liens rapides",-1)),u("ul",Xd,[u("li",null,[G(i,{to:"/events",class:"text-gray-300 hover:text-white"},{default:fe(()=>t[6]||(t[6]=[X("Événements")])),_:1,__:[6]})]),u("li",null,[G(i,{to:"/register",class:"text-gray-300 hover:text-white"},{default:fe(()=>t[7]||(t[7]=[X("Devenir organisateur")])),_:1,__:[7]})])])]),t[10]||(t[10]=u("div",null,[u("h3",{class:"text-lg font-semibold mb-4"},"Contact"),u("p",{class:"text-gray-300"},[X(" Email: <EMAIL>"),u("br"),X(" Téléphone: +**************** ")])],-1))]),t[11]||(t[11]=u("div",{class:"mt-8 pt-8 border-t border-gray-700 text-center"},[u("p",{class:"text-gray-300"},"© 2024 KabEvents. Tous droits réservés.")],-1))])])])}const Yd=Dt(Fd,[["render",Qd]]),Zd={name:"Home",data(){return{featuredEvents:[],loading:!0}},methods:{async fetchFeaturedEvents(){try{const e=await this.$http.get("/events?limit=3");this.featuredEvents=e.data.data||e.data}catch(e){console.error("Error fetching events:",e)}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric"})}},mounted(){this.fetchFeaturedEvents()}},ep={class:"bg-gradient-to-r from-kabyle-600 to-amazigh-600 text-white"},tp={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24"},sp={class:"text-center"},np={class:"space-x-4"},rp={class:"py-16 bg-white"},op={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ip={key:0,class:"text-center"},lp={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},ap={class:"h-48 bg-gradient-to-r from-kabyle-400 to-amazigh-400 rounded-lg mb-4 flex items-center justify-center"},cp={class:"text-white text-lg font-semibold"},up={class:"text-xl font-semibold mb-2"},fp={class:"text-gray-600 mb-4 line-clamp-3"},dp={class:"flex justify-between items-center mb-4"},pp={class:"text-sm text-gray-500"},hp={class:"text-lg font-bold text-kabyle-600"},mp={class:"text-center mt-12"};function gp(e,t,s,n,r,o){const i=Et("router-link");return q(),W("div",null,[u("section",ep,[u("div",tp,[u("div",sp,[t[2]||(t[2]=u("h1",{class:"text-4xl md:text-6xl font-bold mb-6"}," Découvrez la Culture Kabyle ",-1)),t[3]||(t[3]=u("p",{class:"text-xl md:text-2xl mb-8 text-kabyle-100"}," Participez aux plus beaux événements culturels kabyles au Canada ",-1)),u("div",np,[G(i,{to:"/events",class:"bg-white text-kabyle-600 hover:bg-gray-100 font-bold py-3 px-8 rounded-lg transition duration-200"},{default:fe(()=>t[0]||(t[0]=[X(" Voir les événements ")])),_:1,__:[0]}),G(i,{to:"/register",class:"border-2 border-white text-white hover:bg-white hover:text-kabyle-600 font-bold py-3 px-8 rounded-lg transition duration-200"},{default:fe(()=>t[1]||(t[1]=[X(" Devenir organisateur ")])),_:1,__:[1]})])])])]),u("section",rp,[u("div",op,[t[7]||(t[7]=u("div",{class:"text-center mb-12"},[u("h2",{class:"text-3xl font-bold text-gray-900 mb-4"},"Événements à venir"),u("p",{class:"text-lg text-gray-600"},"Ne manquez pas ces événements exceptionnels")],-1)),r.loading?(q(),W("div",ip,t[4]||(t[4]=[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-kabyle-600"},null,-1)]))):(q(),W("div",lp,[(q(!0),W(Me,null,xn(r.featuredEvents,l=>(q(),W("div",{key:l.id,class:"card hover:shadow-xl transition duration-300"},[u("div",ap,[u("span",cp,U(l.title.substring(0,2).toUpperCase()),1)]),u("h3",up,U(l.title),1),u("p",fp,U(l.description),1),u("div",dp,[u("span",pp,U(o.formatDate(l.event_date)),1),u("span",hp,"$"+U(l.ticket_price),1)]),G(i,{to:`/events/${l.id}`,class:"btn-primary w-full text-center"},{default:fe(()=>t[5]||(t[5]=[X(" Voir détails ")])),_:2,__:[5]},1032,["to"])]))),128))])),u("div",mp,[G(i,{to:"/events",class:"btn-secondary"},{default:fe(()=>t[6]||(t[6]=[X(" Voir tous les événements ")])),_:1,__:[6]})])])]),t[8]||(t[8]=Rr('<section class="py-16 bg-gray-50" data-v-92f077fd><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-v-92f077fd><div class="text-center mb-12" data-v-92f077fd><h2 class="text-3xl font-bold text-gray-900 mb-4" data-v-92f077fd>Pourquoi choisir KabEvents ?</h2></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-v-92f077fd><div class="text-center" data-v-92f077fd><div class="w-16 h-16 bg-kabyle-600 rounded-full flex items-center justify-center mx-auto mb-4" data-v-92f077fd><svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-92f077fd><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-92f077fd></path></svg></div><h3 class="text-xl font-semibold mb-2" data-v-92f077fd>Événements authentiques</h3><p class="text-gray-600" data-v-92f077fd>Des événements culturels kabyles authentiques organisés par la communauté</p></div><div class="text-center" data-v-92f077fd><div class="w-16 h-16 bg-amazigh-600 rounded-full flex items-center justify-center mx-auto mb-4" data-v-92f077fd><svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-92f077fd><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" data-v-92f077fd></path></svg></div><h3 class="text-xl font-semibold mb-2" data-v-92f077fd>Réservation sécurisée</h3><p class="text-gray-600" data-v-92f077fd>Système de paiement sécurisé avec billets électroniques et QR codes</p></div><div class="text-center" data-v-92f077fd><div class="w-16 h-16 bg-kabyle-600 rounded-full flex items-center justify-center mx-auto mb-4" data-v-92f077fd><svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-92f077fd><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" data-v-92f077fd></path></svg></div><h3 class="text-xl font-semibold mb-2" data-v-92f077fd>Communauté unie</h3><p class="text-gray-600" data-v-92f077fd>Rassemblez-vous avec la communauté kabyle du Canada</p></div></div></div></section>',1))])}const yp=Dt(Zd,[["render",gp],["__scopeId","data-v-92f077fd"]]),bp={name:"Login",data(){return{form:{email:"",password:"",remember:!1},loading:!1,error:null}},methods:{async login(){this.loading=!0,this.error=null;try{const e=await this.$http.post("/login",{email:this.form.email,password:this.form.password});localStorage.setItem("auth_token",e.data.token),localStorage.setItem("user",JSON.stringify(e.data.user)),this.$http.defaults.headers.common.Authorization=`Bearer ${e.data.token}`;const t=this.$route.query.redirect||"/dashboard";this.$router.push(t)}catch(e){e.response&&e.response.data?this.error=e.response.data.message||"Erreur de connexion":this.error="Erreur de connexion. Veuillez réessayer."}finally{this.loading=!1}}},mounted(){localStorage.getItem("auth_token")&&this.$router.push("/dashboard")}},xp={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},vp={class:"max-w-md w-full space-y-8"},_p={class:"mt-2 text-center text-sm text-gray-600"},wp={key:0,class:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded"},Ep={class:"space-y-4"},Sp={class:"flex items-center justify-between"},Rp={class:"flex items-center"},Cp=["disabled"],kp={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function Ap(e,t,s,n,r,o){const i=Et("router-link");return q(),W("div",xp,[u("div",vp,[u("div",null,[t[6]||(t[6]=u("div",{class:"mx-auto h-12 w-12 bg-gradient-to-r from-kabyle-500 to-amazigh-500 rounded-lg flex items-center justify-center"},[u("span",{class:"text-white font-bold text-xl"},"K")],-1)),t[7]||(t[7]=u("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Connexion à votre compte ",-1)),u("p",_p,[t[5]||(t[5]=X(" Ou ")),G(i,{to:"/register",class:"font-medium text-kabyle-600 hover:text-kabyle-500"},{default:fe(()=>t[4]||(t[4]=[X(" créez un nouveau compte ")])),_:1,__:[4]})])]),u("form",{class:"mt-8 space-y-6",onSubmit:t[3]||(t[3]=xl((...l)=>o.login&&o.login(...l),["prevent"]))},[r.error?(q(),W("div",wp,U(r.error),1)):_t("",!0),u("div",Ep,[u("div",null,[t[8]||(t[8]=u("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Adresse email ",-1)),Ce(u("input",{id:"email","onUpdate:modelValue":t[0]||(t[0]=l=>r.form.email=l),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"<EMAIL>"},null,512),[[Tt,r.form.email]])]),u("div",null,[t[9]||(t[9]=u("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Mot de passe ",-1)),Ce(u("input",{id:"password","onUpdate:modelValue":t[1]||(t[1]=l=>r.form.password=l),name:"password",type:"password",autocomplete:"current-password",required:"",class:"input-field mt-1",placeholder:"Votre mot de passe"},null,512),[[Tt,r.form.password]])])]),u("div",Sp,[u("div",Rp,[Ce(u("input",{id:"remember-me","onUpdate:modelValue":t[2]||(t[2]=l=>r.form.remember=l),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-kabyle-600 focus:ring-kabyle-500 border-gray-300 rounded"},null,512),[[yl,r.form.remember]]),t[10]||(t[10]=u("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Se souvenir de moi ",-1))]),t[11]||(t[11]=u("div",{class:"text-sm"},[u("a",{href:"#",class:"font-medium text-kabyle-600 hover:text-kabyle-500"}," Mot de passe oublié ? ")],-1))]),u("div",null,[u("button",{type:"submit",disabled:r.loading,class:"btn-primary w-full flex justify-center items-center"},[r.loading?(q(),W("svg",kp,t[12]||(t[12]=[u("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),u("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):_t("",!0),X(" "+U(r.loading?"Connexion...":"Se connecter"),1)],8,Cp)])],32)])])}const Op=Dt(bp,[["render",Ap]]),Tp={name:"Register",data(){return{form:{name:"",email:"",password:"",password_confirmation:"",role:"user",terms:!1},loading:!1,error:null}},methods:{async register(){this.loading=!0,this.error=null;try{const e=await this.$http.post("/register",{name:this.form.name,email:this.form.email,password:this.form.password,password_confirmation:this.form.password_confirmation,role:this.form.role});localStorage.setItem("auth_token",e.data.token),localStorage.setItem("user",JSON.stringify(e.data.user)),this.$http.defaults.headers.common.Authorization=`Bearer ${e.data.token}`,this.$router.push("/dashboard")}catch(e){if(e.response&&e.response.data)if(e.response.data.errors){const t=Object.values(e.response.data.errors).flat();this.error=t.join(", ")}else this.error=e.response.data.message||"Erreur lors de l'inscription";else this.error="Erreur lors de l'inscription. Veuillez réessayer."}finally{this.loading=!1}}},mounted(){localStorage.getItem("auth_token")&&this.$router.push("/dashboard")}},Pp={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Mp={class:"max-w-md w-full space-y-8"},$p={class:"mt-2 text-center text-sm text-gray-600"},Np={key:0,class:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded"},Dp={class:"space-y-4"},jp={class:"flex items-center"},Fp=["disabled"],Ip={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function Lp(e,t,s,n,r,o){const i=Et("router-link");return q(),W("div",Pp,[u("div",Mp,[u("div",null,[t[9]||(t[9]=u("div",{class:"mx-auto h-12 w-12 bg-gradient-to-r from-kabyle-500 to-amazigh-500 rounded-lg flex items-center justify-center"},[u("span",{class:"text-white font-bold text-xl"},"K")],-1)),t[10]||(t[10]=u("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Créer votre compte ",-1)),u("p",$p,[t[8]||(t[8]=X(" Ou ")),G(i,{to:"/login",class:"font-medium text-kabyle-600 hover:text-kabyle-500"},{default:fe(()=>t[7]||(t[7]=[X(" connectez-vous à votre compte existant ")])),_:1,__:[7]})])]),u("form",{class:"mt-8 space-y-6",onSubmit:t[6]||(t[6]=xl((...l)=>o.register&&o.register(...l),["prevent"]))},[r.error?(q(),W("div",Np,U(r.error),1)):_t("",!0),u("div",Dp,[u("div",null,[t[11]||(t[11]=u("label",{for:"name",class:"block text-sm font-medium text-gray-700"}," Nom complet ",-1)),Ce(u("input",{id:"name","onUpdate:modelValue":t[0]||(t[0]=l=>r.form.name=l),name:"name",type:"text",autocomplete:"name",required:"",class:"input-field mt-1",placeholder:"Votre nom complet"},null,512),[[Tt,r.form.name]])]),u("div",null,[t[12]||(t[12]=u("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Adresse email ",-1)),Ce(u("input",{id:"email","onUpdate:modelValue":t[1]||(t[1]=l=>r.form.email=l),name:"email",type:"email",autocomplete:"email",required:"",class:"input-field mt-1",placeholder:"<EMAIL>"},null,512),[[Tt,r.form.email]])]),u("div",null,[t[14]||(t[14]=u("label",{for:"role",class:"block text-sm font-medium text-gray-700"}," Type de compte ",-1)),Ce(u("select",{id:"role","onUpdate:modelValue":t[2]||(t[2]=l=>r.form.role=l),name:"role",class:"input-field mt-1"},t[13]||(t[13]=[u("option",{value:"user"},"Utilisateur (participer aux événements)",-1),u("option",{value:"organizer"},"Organisateur (créer des événements)",-1)]),512),[[hs,r.form.role]])]),u("div",null,[t[15]||(t[15]=u("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Mot de passe ",-1)),Ce(u("input",{id:"password","onUpdate:modelValue":t[3]||(t[3]=l=>r.form.password=l),name:"password",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Minimum 8 caractères"},null,512),[[Tt,r.form.password]])]),u("div",null,[t[16]||(t[16]=u("label",{for:"password_confirmation",class:"block text-sm font-medium text-gray-700"}," Confirmer le mot de passe ",-1)),Ce(u("input",{id:"password_confirmation","onUpdate:modelValue":t[4]||(t[4]=l=>r.form.password_confirmation=l),name:"password_confirmation",type:"password",autocomplete:"new-password",required:"",class:"input-field mt-1",placeholder:"Répétez votre mot de passe"},null,512),[[Tt,r.form.password_confirmation]])])]),u("div",jp,[Ce(u("input",{id:"terms","onUpdate:modelValue":t[5]||(t[5]=l=>r.form.terms=l),name:"terms",type:"checkbox",required:"",class:"h-4 w-4 text-kabyle-600 focus:ring-kabyle-500 border-gray-300 rounded"},null,512),[[yl,r.form.terms]]),t[17]||(t[17]=u("label",{for:"terms",class:"ml-2 block text-sm text-gray-900"},[X(" J'accepte les "),u("a",{href:"#",class:"text-kabyle-600 hover:text-kabyle-500"},"conditions d'utilisation"),X(" et la "),u("a",{href:"#",class:"text-kabyle-600 hover:text-kabyle-500"},"politique de confidentialité")],-1))]),u("div",null,[u("button",{type:"submit",disabled:r.loading,class:"btn-primary w-full flex justify-center items-center"},[r.loading?(q(),W("svg",Ip,t[18]||(t[18]=[u("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),u("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):_t("",!0),X(" "+U(r.loading?"Création...":"Créer mon compte"),1)],8,Fp)])],32)])])}const Up=Dt(Tp,[["render",Lp]]),Bp={name:"Events",data(){return{events:[],filteredEvents:[],loading:!0,filters:{search:"",city:"",maxPrice:"",sortBy:"date"}}},methods:{async fetchEvents(){try{const e=await this.$http.get("/events");this.events=e.data.data||e.data,this.filteredEvents=[...this.events],this.filterEvents()}catch(e){console.error("Error fetching events:",e)}finally{this.loading=!1}},filterEvents(){let e=[...this.events];this.filters.search&&(e=e.filter(t=>t.title.toLowerCase().includes(this.filters.search.toLowerCase())||t.description.toLowerCase().includes(this.filters.search.toLowerCase()))),this.filters.city&&(e=e.filter(t=>t.location.includes(this.filters.city))),this.filters.maxPrice&&(e=e.filter(t=>parseFloat(t.ticket_price)<=parseFloat(this.filters.maxPrice))),e.sort((t,s)=>{switch(this.filters.sortBy){case"price":return parseFloat(t.ticket_price)-parseFloat(s.ticket_price);case"title":return t.title.localeCompare(s.title);case"date":default:return new Date(t.event_date)-new Date(s.event_date)}}),this.filteredEvents=e},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvents()}},Vp={class:"min-h-screen bg-gray-50"},Hp={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},zp={class:"bg-white rounded-lg shadow p-6"},qp={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Kp={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12"},Wp={key:0,class:"text-center py-12"},Jp={key:1,class:"text-center py-12"},Gp={key:2,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Xp={class:"h-48 bg-gradient-to-r from-kabyle-400 to-amazigh-400 rounded-lg mb-4 flex items-center justify-center"},Qp={class:"text-white text-2xl font-bold"},Yp={class:"space-y-3"},Zp={class:"text-xl font-semibold text-gray-900"},eh={class:"text-gray-600 text-sm line-clamp-3"},th={class:"flex items-center text-sm text-gray-500"},sh={class:"flex items-center text-sm text-gray-500"},nh={class:"flex items-center justify-between"},rh={class:"flex items-center text-sm text-gray-500"},oh={class:"text-2xl font-bold text-kabyle-600"};function ih(e,t,s,n,r,o){const i=Et("router-link");return q(),W("div",Vp,[t[21]||(t[21]=u("div",{class:"bg-white shadow"},[u("div",{class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},[u("h1",{class:"text-3xl font-bold text-gray-900"},"Événements culturels kabyles"),u("p",{class:"mt-2 text-gray-600"},"Découvrez tous les événements à venir")])],-1)),u("div",Hp,[u("div",zp,[u("div",qp,[u("div",null,[t[8]||(t[8]=u("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Rechercher",-1)),Ce(u("input",{"onUpdate:modelValue":t[0]||(t[0]=l=>r.filters.search=l),type:"text",placeholder:"Nom de l'événement...",class:"input-field",onInput:t[1]||(t[1]=(...l)=>o.filterEvents&&o.filterEvents(...l))},null,544),[[Tt,r.filters.search]])]),u("div",null,[t[10]||(t[10]=u("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Ville",-1)),Ce(u("select",{"onUpdate:modelValue":t[2]||(t[2]=l=>r.filters.city=l),class:"input-field",onChange:t[3]||(t[3]=(...l)=>o.filterEvents&&o.filterEvents(...l))},t[9]||(t[9]=[Rr('<option value="" data-v-2833de03>Toutes les villes</option><option value="Toronto" data-v-2833de03>Toronto</option><option value="Montréal" data-v-2833de03>Montréal</option><option value="Vancouver" data-v-2833de03>Vancouver</option><option value="Calgary" data-v-2833de03>Calgary</option>',5)]),544),[[hs,r.filters.city]])]),u("div",null,[t[12]||(t[12]=u("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Prix max",-1)),Ce(u("select",{"onUpdate:modelValue":t[4]||(t[4]=l=>r.filters.maxPrice=l),class:"input-field",onChange:t[5]||(t[5]=(...l)=>o.filterEvents&&o.filterEvents(...l))},t[11]||(t[11]=[u("option",{value:""},"Tous les prix",-1),u("option",{value:"50"},"Moins de 50$",-1),u("option",{value:"75"},"Moins de 75$",-1),u("option",{value:"100"},"Moins de 100$",-1)]),544),[[hs,r.filters.maxPrice]])]),u("div",null,[t[14]||(t[14]=u("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Trier par",-1)),Ce(u("select",{"onUpdate:modelValue":t[6]||(t[6]=l=>r.filters.sortBy=l),class:"input-field",onChange:t[7]||(t[7]=(...l)=>o.filterEvents&&o.filterEvents(...l))},t[13]||(t[13]=[u("option",{value:"date"},"Date",-1),u("option",{value:"price"},"Prix",-1),u("option",{value:"title"},"Nom",-1)]),544),[[hs,r.filters.sortBy]])])])])]),u("div",Kp,[r.loading?(q(),W("div",Wp,t[15]||(t[15]=[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-kabyle-600"},null,-1),u("p",{class:"mt-2 text-gray-600"},"Chargement des événements...",-1)]))):r.filteredEvents.length===0?(q(),W("div",Jp,t[16]||(t[16]=[u("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),u("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Aucun événement trouvé",-1),u("p",{class:"mt-1 text-sm text-gray-500"},"Essayez de modifier vos critères de recherche.",-1)]))):(q(),W("div",Gp,[(q(!0),W(Me,null,xn(r.filteredEvents,l=>(q(),W("div",{key:l.id,class:"card hover:shadow-xl transition duration-300"},[u("div",Xp,[u("span",Qp,U(l.title.substring(0,2).toUpperCase()),1)]),u("div",Yp,[u("h3",Zp,U(l.title),1),u("p",eh,U(l.description),1),u("div",th,[t[17]||(t[17]=u("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),X(" "+U(o.formatDate(l.event_date)),1)]),u("div",sh,[t[18]||(t[18]=u("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),X(" "+U(l.location),1)]),u("div",nh,[u("div",rh,[t[19]||(t[19]=u("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1)),X(" "+U(l.available_tickets)+" places disponibles ",1)]),u("span",oh,"$"+U(l.ticket_price),1)]),G(i,{to:`/events/${l.id}`,class:"btn-primary w-full text-center block"},{default:fe(()=>t[20]||(t[20]=[X(" Voir détails ")])),_:2,__:[20]},1032,["to"])])]))),128))]))])])}const lh=Dt(Bp,[["render",ih],["__scopeId","data-v-2833de03"]]),ah={name:"EventDetail",props:["id"],data(){return{event:null,loading:!0,quantity:1,reserving:!1,error:null,success:null}},computed:{isAuthenticated(){return!!localStorage.getItem("auth_token")}},methods:{async fetchEvent(){try{const e=await this.$http.get(`/events/${this.id}`);this.event=e.data.data||e.data}catch(e){console.error("Error fetching event:",e),this.event=null}finally{this.loading=!1}},async reserveTickets(){this.reserving=!0,this.error=null,this.success=null;try{const e=await this.$http.post(`/events/${this.id}/reserve`,{quantity:this.quantity});this.success="Réservation effectuée avec succès ! Vous recevrez un email de confirmation.",this.event.available_tickets-=this.quantity,setTimeout(()=>{this.$router.push("/dashboard")},2e3)}catch(e){e.response&&e.response.data?this.error=e.response.data.message||"Erreur lors de la réservation":this.error="Erreur lors de la réservation. Veuillez réessayer."}finally{this.reserving=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric"})},formatTime(e){return new Date(e).toLocaleTimeString("fr-CA",{hour:"2-digit",minute:"2-digit"})}},mounted(){this.fetchEvent()},watch:{id(){this.fetchEvent()}}},ch={class:"min-h-screen bg-gray-50"},uh={key:0,class:"flex items-center justify-center min-h-screen"},fh={key:1,class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},dh={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},ph={class:"lg:col-span-2"},hh={class:"h-64 md:h-96 bg-gradient-to-r from-kabyle-400 to-amazigh-400 rounded-xl mb-6 flex items-center justify-center"},mh={class:"text-white text-4xl font-bold"},gh={class:"bg-white rounded-xl shadow-lg p-6"},yh={class:"text-3xl font-bold text-gray-900 mb-4"},bh={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"},xh={class:"flex items-center text-gray-600"},vh={class:"font-medium"},_h={class:"text-sm"},wh={class:"flex items-center text-gray-600"},Eh={class:"font-medium"},Sh={class:"flex items-center text-gray-600"},Rh={class:"font-medium"},Ch={class:"text-sm"},kh={class:"flex items-center text-gray-600"},Ah={class:"text-sm"},Oh={class:"border-t pt-6"},Th={class:"text-gray-700 leading-relaxed"},Ph={class:"lg:col-span-1"},Mh={class:"bg-white rounded-xl shadow-lg p-6 sticky top-8"},$h={class:"text-center mb-6"},Nh={class:"text-3xl font-bold text-kabyle-600"},Dh={key:0,class:"space-y-4"},jh={key:1,class:"space-y-4"},Fh=["value"],Ih={class:"border-t pt-4"},Lh={class:"flex justify-between mb-2"},Uh={class:"font-semibold"},Bh={class:"flex justify-between text-lg font-bold"},Vh=["disabled"],Hh={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},zh={key:2,class:"text-center"},qh={key:3,class:"mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded"},Kh={key:4,class:"mt-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded"},Wh={key:2,class:"flex items-center justify-center min-h-screen"},Jh={class:"text-center"};function Gh(e,t,s,n,r,o){var l;const i=Et("router-link");return q(),W("div",ch,[r.loading?(q(),W("div",uh,t[3]||(t[3]=[u("div",{class:"text-center"},[u("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-kabyle-600"}),u("p",{class:"mt-2 text-gray-600"},"Chargement de l'événement...")],-1)]))):r.event?(q(),W("div",fh,[u("button",{onClick:t[0]||(t[0]=a=>e.$router.go(-1)),class:"mb-6 flex items-center text-kabyle-600 hover:text-kabyle-700"},t[4]||(t[4]=[u("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1),X(" Retour ")])),u("div",dh,[u("div",ph,[u("div",hh,[u("span",mh,U(r.event.title.substring(0,2).toUpperCase()),1)]),u("div",gh,[u("h1",yh,U(r.event.title),1),u("div",bh,[u("div",xh,[t[5]||(t[5]=u("svg",{class:"w-5 h-5 mr-3 text-kabyle-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),u("div",null,[u("p",vh,U(o.formatDate(r.event.event_date)),1),u("p",_h,U(o.formatTime(r.event.event_date)),1)])]),u("div",wh,[t[6]||(t[6]=u("svg",{class:"w-5 h-5 mr-3 text-kabyle-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),u("div",null,[u("p",Eh,U(r.event.location),1)])]),u("div",Sh,[t[7]||(t[7]=u("svg",{class:"w-5 h-5 mr-3 text-kabyle-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"})],-1)),u("div",null,[u("p",Rh,U(r.event.available_tickets)+" places disponibles",1),u("p",Ch,"sur "+U(r.event.total_tickets)+" places",1)])]),u("div",kh,[t[9]||(t[9]=u("svg",{class:"w-5 h-5 mr-3 text-kabyle-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"})],-1)),u("div",null,[t[8]||(t[8]=u("p",{class:"font-medium"},"Organisé par",-1)),u("p",Ah,U(((l=r.event.organizer)==null?void 0:l.name)||"Organisateur"),1)])])]),u("div",Oh,[t[10]||(t[10]=u("h2",{class:"text-xl font-semibold mb-3"},"Description",-1)),u("p",Th,U(r.event.description),1)])])]),u("div",Ph,[u("div",Mh,[u("div",$h,[u("p",Nh,"$"+U(r.event.ticket_price),1),t[11]||(t[11]=u("p",{class:"text-gray-600"},"par billet",-1))]),o.isAuthenticated?r.event.available_tickets>0?(q(),W("div",jh,[u("div",null,[t[15]||(t[15]=u("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Nombre de billets ",-1)),Ce(u("select",{"onUpdate:modelValue":t[1]||(t[1]=a=>r.quantity=a),class:"input-field"},[(q(!0),W(Me,null,xn(Math.min(10,r.event.available_tickets),a=>(q(),W("option",{key:a,value:a},U(a)+" billet"+U(a>1?"s":""),9,Fh))),128))],512),[[hs,r.quantity]])]),u("div",Ih,[u("div",Lh,[u("span",null,U(r.quantity)+" × $"+U(r.event.ticket_price),1),u("span",Uh,"$"+U((r.quantity*parseFloat(r.event.ticket_price)).toFixed(2)),1)]),u("div",Bh,[t[16]||(t[16]=u("span",null,"Total",-1)),u("span",null,"$"+U((r.quantity*parseFloat(r.event.ticket_price)).toFixed(2)),1)])]),u("button",{onClick:t[2]||(t[2]=(...a)=>o.reserveTickets&&o.reserveTickets(...a)),disabled:r.reserving,class:"btn-primary w-full flex justify-center items-center"},[r.reserving?(q(),W("svg",Hh,t[17]||(t[17]=[u("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),u("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):_t("",!0),X(" "+U(r.reserving?"Réservation...":"Réserver maintenant"),1)],8,Vh)])):(q(),W("div",zh,t[18]||(t[18]=[u("p",{class:"text-red-600 font-semibold mb-4"},"Événement complet",-1),u("p",{class:"text-gray-600 text-sm"}," Cet événement n'a plus de places disponibles. ",-1)]))):(q(),W("div",Dh,[t[14]||(t[14]=u("p",{class:"text-center text-gray-600 mb-4"}," Connectez-vous pour réserver vos billets ",-1)),G(i,{to:"/login",class:"btn-primary w-full text-center block"},{default:fe(()=>t[12]||(t[12]=[X(" Se connecter ")])),_:1,__:[12]}),G(i,{to:"/register",class:"btn-secondary w-full text-center block"},{default:fe(()=>t[13]||(t[13]=[X(" Créer un compte ")])),_:1,__:[13]})])),r.error?(q(),W("div",qh,U(r.error),1)):_t("",!0),r.success?(q(),W("div",Kh,U(r.success),1)):_t("",!0)])])])])):(q(),W("div",Wh,[u("div",Jh,[t[20]||(t[20]=u("h2",{class:"text-2xl font-bold text-gray-900 mb-2"},"Événement non trouvé",-1)),t[21]||(t[21]=u("p",{class:"text-gray-600 mb-4"},"L'événement que vous recherchez n'existe pas.",-1)),G(i,{to:"/events",class:"btn-primary"},{default:fe(()=>t[19]||(t[19]=[X(" Voir tous les événements ")])),_:1,__:[19]})])]))])}const Xh=Dt(ah,[["render",Gh]]),Qh={name:"Dashboard",data(){return{user:null,reservations:[],userStats:{totalReservations:0,upcomingEvents:0,totalSpent:0},adminStats:{totalUsers:0,totalEvents:0,totalReservations:0,totalRevenue:0},loading:!0}},methods:{async fetchUserData(){try{const e=await this.$http.get("/me");if(this.user=e.data.user,this.user.role==="user"){const t=await this.$http.get("/user/reservations");this.reservations=t.data.data||t.data,this.userStats.totalReservations=this.reservations.length,this.userStats.upcomingEvents=this.reservations.filter(s=>{var n;return new Date((n=s.event)==null?void 0:n.event_date)>new Date}).length,this.userStats.totalSpent=this.reservations.filter(s=>s.status==="paid").reduce((s,n)=>s+parseFloat(n.total_amount),0).toFixed(2)}}catch(e){console.error("Error fetching user data:",e),e.response&&e.response.status===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),this.$router.push("/login"))}finally{this.loading=!1}},formatDate(e){return new Date(e).toLocaleDateString("fr-CA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})},getStatusClass(e){switch(e){case"paid":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},getStatusText(e){switch(e){case"paid":return"Payé";case"pending":return"En attente";case"cancelled":return"Annulé";default:return e}}},mounted(){const e=localStorage.getItem("user");e&&(this.user=JSON.parse(e)),this.fetchUserData()}},Yh={class:"min-h-screen bg-gray-50"},Zh={class:"bg-white shadow"},em={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},tm={class:"flex justify-between items-center"},sm={class:"text-gray-600"},nm={class:"flex items-center space-x-2"},rm={class:"px-3 py-1 bg-kabyle-100 text-kabyle-800 rounded-full text-sm font-medium"},om={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},im={key:0,class:"space-y-8"},lm={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},am={class:"card"},cm={class:"flex items-center"},um={class:"ml-4"},fm={class:"text-2xl font-bold text-gray-900"},dm={class:"card"},pm={class:"flex items-center"},hm={class:"ml-4"},mm={class:"text-2xl font-bold text-gray-900"},gm={class:"card"},ym={class:"flex items-center"},bm={class:"ml-4"},xm={class:"text-2xl font-bold text-gray-900"},vm={class:"card"},_m={class:"flex justify-between items-center mb-6"},wm={key:0,class:"text-center py-8"},Em={key:1,class:"text-center py-8"},Sm={key:2,class:"space-y-4"},Rm={class:"flex justify-between items-start"},Cm={class:"flex-1"},km={class:"font-semibold text-lg"},Am={class:"text-gray-600 text-sm mb-2"},Om={class:"text-gray-600 text-sm"},Tm={class:"mt-2 flex items-center space-x-4"},Pm={class:"text-sm text-gray-500"},Mm={class:"text-sm font-medium"},$m={class:"ml-4"},Nm={key:1,class:"space-y-8"},Dm={key:2,class:"space-y-8"},jm={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Fm={class:"card text-center"},Im={class:"text-3xl font-bold text-kabyle-600 mb-2"},Lm={class:"card text-center"},Um={class:"text-3xl font-bold text-amazigh-600 mb-2"},Bm={class:"card text-center"},Vm={class:"text-3xl font-bold text-green-600 mb-2"},Hm={class:"card text-center"},zm={class:"text-3xl font-bold text-purple-600 mb-2"};function qm(e,t,s,n,r,o){var l,a,f,c,d,m;const i=Et("router-link");return q(),W("div",Yh,[u("div",Zh,[u("div",em,[u("div",tm,[u("div",null,[t[0]||(t[0]=u("h1",{class:"text-2xl font-bold text-gray-900"},"Dashboard",-1)),u("p",sm,"Bienvenue, "+U((l=r.user)==null?void 0:l.name),1)]),u("div",nm,[u("span",rm,U(((a=r.user)==null?void 0:a.role)==="admin"?"Administrateur":((f=r.user)==null?void 0:f.role)==="organizer"?"Organisateur":"Utilisateur"),1)])])])]),u("div",om,[((c=r.user)==null?void 0:c.role)==="user"?(q(),W("div",im,[u("div",lm,[u("div",am,[u("div",cm,[t[2]||(t[2]=u("div",{class:"p-3 bg-kabyle-100 rounded-lg"},[u("svg",{class:"w-6 h-6 text-kabyle-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),u("div",um,[t[1]||(t[1]=u("p",{class:"text-sm font-medium text-gray-600"},"Réservations totales",-1)),u("p",fm,U(r.userStats.totalReservations),1)])])]),u("div",dm,[u("div",pm,[t[4]||(t[4]=u("div",{class:"p-3 bg-amazigh-100 rounded-lg"},[u("svg",{class:"w-6 h-6 text-amazigh-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),u("div",hm,[t[3]||(t[3]=u("p",{class:"text-sm font-medium text-gray-600"},"Événements à venir",-1)),u("p",mm,U(r.userStats.upcomingEvents),1)])])]),u("div",gm,[u("div",ym,[t[6]||(t[6]=u("div",{class:"p-3 bg-green-100 rounded-lg"},[u("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),u("div",bm,[t[5]||(t[5]=u("p",{class:"text-sm font-medium text-gray-600"},"Total dépensé",-1)),u("p",xm,"$"+U(r.userStats.totalSpent),1)])])])]),u("div",vm,[u("div",_m,[t[8]||(t[8]=u("h2",{class:"text-xl font-semibold"},"Mes réservations",-1)),G(i,{to:"/events",class:"btn-primary"},{default:fe(()=>t[7]||(t[7]=[X(" Réserver un événement ")])),_:1,__:[7]})]),r.loading?(q(),W("div",wm,t[9]||(t[9]=[u("div",{class:"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-kabyle-600"},null,-1)]))):r.reservations.length===0?(q(),W("div",Em,t[10]||(t[10]=[u("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})],-1),u("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Aucune réservation",-1),u("p",{class:"mt-1 text-sm text-gray-500"},"Commencez par réserver votre premier événement.",-1)]))):(q(),W("div",Sm,[(q(!0),W(Me,null,xn(r.reservations,g=>{var x,E,R,O;return q(),W("div",{key:g.id,class:"border rounded-lg p-4 hover:bg-gray-50"},[u("div",Rm,[u("div",Cm,[u("h3",km,U((x=g.event)==null?void 0:x.title),1),u("p",Am,U((E=g.event)==null?void 0:E.location),1),u("p",Om,U(o.formatDate((R=g.event)==null?void 0:R.event_date)),1),u("div",Tm,[u("span",Pm,U(g.quantity)+" billet(s)",1),u("span",Mm,"$"+U(g.total_amount),1),u("span",{class:hn([o.getStatusClass(g.status),"px-2 py-1 rounded-full text-xs font-medium"])},U(o.getStatusText(g.status)),3)])]),u("div",$m,[G(i,{to:`/events/${(O=g.event)==null?void 0:O.id}`,class:"text-kabyle-600 hover:text-kabyle-700 text-sm"},{default:fe(()=>t[11]||(t[11]=[X(" Voir l'événement ")])),_:2,__:[11]},1032,["to"])])])])}),128))]))])])):((d=r.user)==null?void 0:d.role)==="organizer"?(q(),W("div",Nm,t[12]||(t[12]=[Rr('<div class="text-center"><h2 class="text-2xl font-bold text-gray-900 mb-4">Dashboard Organisateur</h2><p class="text-gray-600">Gérez vos événements et suivez vos ventes</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="card text-center"><svg class="mx-auto h-12 w-12 text-kabyle-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg><h3 class="text-lg font-semibold mb-2">Créer un événement</h3><p class="text-gray-600 mb-4">Organisez votre prochain événement culturel</p><button class="btn-primary">Créer un événement</button></div><div class="card text-center"><svg class="mx-auto h-12 w-12 text-amazigh-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg><h3 class="text-lg font-semibold mb-2">Voir les statistiques</h3><p class="text-gray-600 mb-4">Analysez les performances de vos événements</p><button class="btn-secondary">Voir les stats</button></div></div>',2)]))):((m=r.user)==null?void 0:m.role)==="admin"?(q(),W("div",Dm,[t[17]||(t[17]=u("div",{class:"text-center"},[u("h2",{class:"text-2xl font-bold text-gray-900 mb-4"},"Dashboard Administrateur"),u("p",{class:"text-gray-600"},"Gérez la plateforme KabEvents")],-1)),u("div",jm,[u("div",Fm,[u("div",Im,U(r.adminStats.totalUsers),1),t[13]||(t[13]=u("div",{class:"text-gray-600"},"Utilisateurs",-1))]),u("div",Lm,[u("div",Um,U(r.adminStats.totalEvents),1),t[14]||(t[14]=u("div",{class:"text-gray-600"},"Événements",-1))]),u("div",Bm,[u("div",Vm,U(r.adminStats.totalReservations),1),t[15]||(t[15]=u("div",{class:"text-gray-600"},"Réservations",-1))]),u("div",Hm,[u("div",zm,"$"+U(r.adminStats.totalRevenue),1),t[16]||(t[16]=u("div",{class:"text-gray-600"},"Revenus",-1))])])])):_t("",!0)])])}const Km=Dt(Qh,[["render",qm]]);ue.defaults.baseURL="/api";ue.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";const Bo=localStorage.getItem("auth_token");Bo&&(ue.defaults.headers.common.Authorization=`Bearer ${Bo}`);const Wm=[{path:"/",name:"home",component:yp},{path:"/login",name:"login",component:Op},{path:"/register",name:"register",component:Up},{path:"/events",name:"events",component:lh},{path:"/events/:id",name:"event-detail",component:Xh,props:!0},{path:"/dashboard",name:"dashboard",component:Km,meta:{requiresAuth:!0}}],$l=Dd({history:ud(),routes:Wm});$l.beforeEach((e,t,s)=>{const n=localStorage.getItem("auth_token");e.meta.requiresAuth&&!n?s("/login"):s()});const Or=Pf(Yd);Or.use($l);Or.config.globalProperties.$http=ue;Or.mount("#app");
