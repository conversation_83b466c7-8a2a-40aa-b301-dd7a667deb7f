import './bootstrap';
import { createApp } from 'vue';
import { createRouter, createWebHistory } from 'vue-router';
import axios from 'axios';

// Import components
import App from './components/App.vue';
import Home from './components/pages/Home.vue';
import Login from './components/auth/Login.vue';
import Register from './components/auth/Register.vue';
import Events from './components/events/Events.vue';
import EventDetail from './components/events/EventDetail.vue';
import Checkout from './components/checkout/Checkout.vue';
import Dashboard from './components/dashboard/Dashboard.vue';
import UserDashboard from './components/dashboard/UserDashboard.vue';
import OrganizerDashboard from './components/dashboard/OrganizerDashboard.vue';
import AdminDashboard from './components/dashboard/AdminDashboard.vue';

// Configure axios
axios.defaults.baseURL = '/api';
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// Add auth token to requests
const token = localStorage.getItem('auth_token');
if (token) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
}

// Router configuration
const routes = [
    { path: '/', name: 'home', component: Home },
    { path: '/login', name: 'login', component: Login },
    { path: '/register', name: 'register', component: Register },
    { path: '/events', name: 'events', component: Events },
    { path: '/events/:id', name: 'event-detail', component: EventDetail, props: true },
    { path: '/checkout/:eventId', name: 'checkout', component: Checkout, props: true, meta: { requiresAuth: true } },
    { path: '/dashboard', name: 'dashboard', component: Dashboard, meta: { requiresAuth: true } },
    { path: '/dashboard/user', name: 'user-dashboard', component: UserDashboard, meta: { requiresAuth: true, role: 'user' } },
    { path: '/dashboard/organizer', name: 'organizer-dashboard', component: OrganizerDashboard, meta: { requiresAuth: true, role: 'organizer' } },
    { path: '/dashboard/admin', name: 'admin-dashboard', component: AdminDashboard, meta: { requiresAuth: true, role: 'admin' } },
];

const router = createRouter({
    history: createWebHistory(),
    routes,
});

// Navigation guard for authentication
router.beforeEach((to, from, next) => {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user');
    const user = userData ? JSON.parse(userData) : null;

    if (to.meta.requiresAuth && !token) {
        next('/login');
        return;
    }

    // Redirect /dashboard to role-specific dashboard
    if (to.path === '/dashboard') {
        if (user) {
            switch (user.role) {
                case 'admin':
                    next('/dashboard/admin');
                    return;
                case 'organizer':
                    next('/dashboard/organizer');
                    return;
                case 'user':
                    next('/dashboard/user');
                    return;
                default:
                    next('/dashboard/user');
                    return;
            }
        } else if (token) {
            // If we have a token but no user data, try to get user data from API
            axios.get('/me', {
                headers: { Authorization: `Bearer ${token}` }
            }).then(response => {
                const userData = response.data.user;
                localStorage.setItem('user', JSON.stringify(userData));
                switch (userData.role) {
                    case 'admin':
                        next('/dashboard/admin');
                        return;
                    case 'organizer':
                        next('/dashboard/organizer');
                        return;
                    case 'user':
                        next('/dashboard/user');
                        return;
                    default:
                        next('/dashboard/user');
                        return;
                }
            }).catch(() => {
                // If API call fails, redirect to login
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user');
                next('/login');
            });
            return;
        } else {
            next('/login');
            return;
        }
    }

    // Check role-based access
    if (to.meta.role && user && user.role !== to.meta.role) {
        // Redirect to appropriate dashboard if user tries to access wrong role dashboard
        switch (user.role) {
            case 'admin':
                next('/dashboard/admin');
                return;
            case 'organizer':
                next('/dashboard/organizer');
                return;
            case 'user':
                next('/dashboard/user');
                return;
            default:
                next('/');
                return;
        }
    }

    next();
});

// Create Vue app
const app = createApp(App);
app.use(router);
app.config.globalProperties.$http = axios;
app.mount('#app');
