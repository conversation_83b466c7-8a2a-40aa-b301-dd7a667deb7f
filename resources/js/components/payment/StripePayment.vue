<template>
  <div class="stripe-payment">
    <div v-if="loading" class="text-center py-4">
      <div class="loading-spinner w-6 h-6 mx-auto mb-2"></div>
      <p class="text-gray-600">Chargement du système de paiement...</p>
    </div>

    <div v-else-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
      {{ error }}
    </div>

    <div v-else>
      <!-- Payment Method Selection -->
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-4">Méthode de paiement</h3>
        <div class="space-y-3">
          <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50"
                 :class="{ 'border-primary-500 bg-primary-50': paymentMethod === 'stripe' }">
            <input v-model="paymentMethod" type="radio" value="stripe" class="mr-3">
            <div class="flex items-center">
              <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
              </svg>
              <span class="font-medium">Carte de crédit/débit (Stripe)</span>
            </div>
          </label>
          <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50"
                 :class="{ 'border-primary-500 bg-primary-50': paymentMethod === 'card' }">
            <input v-model="paymentMethod" type="radio" value="card" class="mr-3">
            <div class="flex items-center">
              <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
              </svg>
              <span class="font-medium">Carte de crédit/débit (Simulation)</span>
            </div>
          </label>
        </div>
      </div>

      <!-- Stripe Payment Form -->
      <div v-if="paymentMethod === 'stripe'" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Informations de carte
          </label>
          <div id="stripe-card-element" class="p-3 border rounded-lg bg-white">
            <!-- Stripe Elements will be mounted here -->
          </div>
          <div v-if="stripeError" class="text-red-600 text-sm mt-1">
            {{ stripeError }}
          </div>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 class="font-medium text-blue-900 mb-2">💳 Cartes de test Stripe</h4>
          <div class="text-sm text-blue-800 space-y-1">
            <p><strong>Succès:</strong> 4242 4242 4242 4242</p>
            <p><strong>Échec:</strong> 4000 0000 0000 0002</p>
            <p><strong>3D Secure:</strong> 4000 0025 0000 3155</p>
            <p><strong>Date:</strong> N'importe quelle date future</p>
            <p><strong>CVC:</strong> N'importe quel code à 3 chiffres</p>
          </div>
        </div>
      </div>

      <!-- Simulation Payment Form -->
      <div v-if="paymentMethod === 'card'" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Numéro de carte</label>
          <input v-model="cardData.number" type="text" placeholder="1234 5678 9012 3456"
                 class="input-field" maxlength="19" @input="formatCardNumber">
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Date d'expiration</label>
            <input v-model="cardData.expiry" type="text" placeholder="MM/AA"
                   class="input-field" maxlength="5" @input="formatExpiryDate">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">CVV</label>
            <input v-model="cardData.cvv" type="text" placeholder="123"
                   class="input-field" maxlength="4">
          </div>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Nom sur la carte</label>
          <input v-model="cardData.name" type="text" class="input-field">
        </div>
      </div>

      <!-- Payment Button -->
      <div class="mt-6">
        <button @click="processPayment" :disabled="processing"
                class="btn-primary w-full flex justify-center items-center">
          <span v-if="processing" class="loading-spinner w-4 h-4 mr-2"></span>
          {{ processing ? 'Traitement...' : `Payer $${amount}` }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { loadStripe } from '@stripe/stripe-js';

export default {
  name: 'StripePayment',
  props: {
    amount: {
      type: [String, Number],
      required: true
    },
    eventId: {
      type: [String, Number],
      required: true
    },
    quantity: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      processing: false,
      error: null,
      stripeError: null,
      paymentMethod: 'stripe',
      stripe: null,
      cardElement: null,
      clientSecret: null,
      paymentIntentId: null,
      cardData: {
        number: '',
        expiry: '',
        cvv: '',
        name: ''
      }
    }
  },
  async mounted() {
    await this.initializeStripe();
  },
  watch: {
    paymentMethod(newMethod) {
      if (newMethod === 'stripe' && this.stripe && !this.cardElement) {
        // Delay to ensure DOM is updated
        this.$nextTick(() => {
          this.setupStripeElements();
        });
      }
    }
  },
  methods: {
    async initializeStripe() {
      try {
        console.log('Initializing Stripe...');

        // Get Stripe publishable key
        const keyResponse = await this.$http.get('/stripe/publishable-key');
        console.log('Key response:', keyResponse.data);

        const publishableKey = keyResponse.data.publishable_key;

        if (!publishableKey) {
          throw new Error('Clé Stripe non configurée');
        }

        console.log('Loading Stripe with key:', publishableKey.substring(0, 20) + '...');

        // Initialize Stripe
        this.stripe = await loadStripe(publishableKey);

        if (!this.stripe) {
          throw new Error('Échec du chargement de Stripe');
        }

        console.log('Stripe loaded successfully');

        // Create payment intent
        await this.createPaymentIntent();

        console.log('Stripe initialization complete');

      } catch (error) {
        console.error('Stripe initialization error:', error);
        this.error = 'Erreur lors de l\'initialisation du système de paiement: ' + error.message;
      } finally {
        this.loading = false;
      }
    },

    async createPaymentIntent() {
      console.log('Creating payment intent for event:', this.eventId, 'quantity:', this.quantity);

      const response = await this.$http.post(`/stripe/create-payment-intent/${this.eventId}`, {
        quantity: this.quantity
      });

      console.log('Payment intent response:', response.data);

      this.clientSecret = response.data.client_secret;
      this.paymentIntentId = response.data.payment_intent_id;

      console.log('Payment intent created:', this.paymentIntentId);
    },

    setupStripeElements() {
      if (!this.stripe) {
        console.log('Stripe not loaded yet');
        return;
      }

      // Check if element exists in DOM
      const elementContainer = document.getElementById('stripe-card-element');
      if (!elementContainer) {
        console.log('Stripe card element container not found in DOM');
        return;
      }

      // Don't create if already exists
      if (this.cardElement) {
        console.log('Stripe Elements already created');
        return;
      }

      console.log('Setting up Stripe Elements...');

      const elements = this.stripe.elements();
      this.cardElement = elements.create('card', {
        style: {
          base: {
            fontSize: '16px',
            color: '#424770',
            '::placeholder': {
              color: '#aab7c4',
            },
          },
        },
      });

      this.cardElement.mount('#stripe-card-element');

      this.cardElement.on('change', (event) => {
        this.stripeError = event.error ? event.error.message : null;
      });

      console.log('Stripe Elements mounted successfully');
    },

    async processPayment() {
      this.processing = true;
      this.error = null;

      try {
        if (this.paymentMethod === 'stripe') {
          await this.processStripePayment();
        } else {
          await this.processSimulatedPayment();
        }
      } catch (error) {
        console.error('Payment error:', error);
        this.error = error.message || 'Erreur lors du paiement';
      } finally {
        this.processing = false;
      }
    },

    async processStripePayment() {
      const { error, paymentIntent } = await this.stripe.confirmCardPayment(this.clientSecret, {
        payment_method: {
          card: this.cardElement,
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      if (paymentIntent.status === 'succeeded') {
        // Confirm payment on backend
        await this.$http.post('/stripe/confirm-payment', {
          payment_intent_id: this.paymentIntentId,
          event_id: this.eventId,
          quantity: this.quantity
        });

        this.$emit('payment-success', {
          paymentIntentId: this.paymentIntentId,
          method: 'stripe'
        });
      }
    },

    async processSimulatedPayment() {
      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      this.$emit('payment-success', {
        method: 'card',
        cardData: this.cardData
      });
    },

    formatCardNumber(event) {
      let value = event.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
      let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
      this.cardData.number = formattedValue;
    },

    formatExpiryDate(event) {
      let value = event.target.value.replace(/\D/g, '');
      if (value.length >= 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
      }
      this.cardData.expiry = value;
    }
  }
}
</script>

<style scoped>
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
