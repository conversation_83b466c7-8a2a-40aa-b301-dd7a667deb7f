<template>
  <div>
    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
      <!-- Background with gradient and pattern -->
      <div class="absolute inset-0 hero-gradient"></div>
      <div class="absolute inset-0 bg-hero-pattern opacity-20"></div>

      <!-- Floating elements -->
      <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-bounce-gentle"></div>
      <div class="absolute top-40 right-20 w-16 h-16 bg-secondary-400/20 rounded-full animate-bounce-gentle" style="animation-delay: 1s;"></div>
      <div class="absolute bottom-40 left-20 w-12 h-12 bg-primary-400/20 rounded-full animate-bounce-gentle" style="animation-delay: 2s;"></div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 text-center">
        <div class="animate-fade-in">
          <h1 class="text-5xl md:text-7xl font-bold mb-8 text-white leading-tight">
            Dé<PERSON>uvrez la
            <span class="bg-gradient-to-r from-secondary-300 to-secondary-100 bg-clip-text text-transparent">
              Culture Kabyle
            </span>
          </h1>
          <p class="text-xl md:text-2xl mb-12 text-white/90 max-w-3xl mx-auto leading-relaxed">
            Participez aux plus beaux événements culturels kabyles au Canada.
            Connectez-vous avec votre communauté et célébrez nos traditions ancestrales.
          </p>
          <div class="flex flex-col sm:flex-row gap-6 justify-center items-center animate-slide-up">
            <router-link to="/events" class="btn-secondary text-lg px-8 py-4">
              <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              Voir les événements
            </router-link>
            <router-link to="/register" class="btn-outline text-lg px-8 py-4 bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-primary-600">
              <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Devenir organisateur
            </router-link>
          </div>
        </div>

        <!-- Scroll indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <svg class="w-6 h-6 text-white/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
          </svg>
        </div>
      </div>
    </section>

    <!-- Featured Events -->
    <section class="py-24 relative">
      <div class="absolute inset-0 bg-gradient-to-b from-white via-accent-50/30 to-white"></div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold text-gradient mb-6">Événements à venir</h2>
          <p class="text-xl text-accent-600 max-w-2xl mx-auto leading-relaxed">
            Ne manquez pas ces événements exceptionnels qui célèbrent notre riche patrimoine culturel
          </p>
        </div>

        <div v-if="loading" class="text-center py-16">
          <div class="loading-spinner w-12 h-12 mx-auto"></div>
          <p class="text-accent-600 mt-4">Chargement des événements...</p>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="event in featuredEvents" :key="event.id" class="event-card">
            <div class="event-image">
              <div class="text-center">
                <span class="text-white text-3xl font-bold">{{ event.title.substring(0, 2).toUpperCase() }}</span>
                <div class="mt-2 text-white/80 text-sm font-medium">{{ event.location || 'Canada' }}</div>
              </div>
            </div>

            <div class="p-6">
              <div class="flex items-center justify-between mb-3">
                <span class="price-tag">${{ event.ticket_price }}</span>
                <span class="text-sm text-accent-500 font-medium">{{ formatDate(event.event_date) }}</span>
              </div>

              <h3 class="text-xl font-bold text-accent-900 mb-3 line-clamp-2">{{ event.title }}</h3>
              <p class="text-accent-600 mb-6 line-clamp-3 leading-relaxed">{{ event.description }}</p>

              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-accent-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="text-sm text-accent-500">{{ event.location || 'Canada' }}</span>
                </div>
                <router-link :to="`/events/${event.id}`" class="btn-primary text-sm px-4 py-2">
                  Voir détails
                </router-link>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center mt-16">
          <router-link to="/events" class="btn-outline text-lg px-8 py-4">
            <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
            Voir tous les événements
          </router-link>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-24 relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-secondary-50"></div>
      <div class="absolute top-0 left-0 w-full h-full bg-hero-pattern opacity-5"></div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-20">
          <h2 class="text-4xl md:text-5xl font-bold text-gradient mb-6">Pourquoi choisir KabEvents ?</h2>
          <p class="text-xl text-accent-600 max-w-3xl mx-auto leading-relaxed">
            Une plateforme moderne et sécurisée dédiée à la préservation et à la célébration de notre patrimoine culturel
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
          <div class="text-center group">
            <div class="relative mb-8">
              <div class="w-24 h-24 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div class="absolute -top-2 -right-2 w-6 h-6 bg-secondary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <h3 class="text-2xl font-bold text-accent-900 mb-4">Événements authentiques</h3>
            <p class="text-accent-600 leading-relaxed text-lg">
              Des événements culturels kabyles authentiques organisés par et pour notre communauté,
              préservant nos traditions ancestrales
            </p>
          </div>

          <div class="text-center group">
            <div class="relative mb-8">
              <div class="w-24 h-24 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <div class="absolute -top-2 -right-2 w-6 h-6 bg-primary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <h3 class="text-2xl font-bold text-accent-900 mb-4">Réservation sécurisée</h3>
            <p class="text-accent-600 leading-relaxed text-lg">
              Système de paiement sécurisé avec billets électroniques, QR codes et protection
              complète de vos données personnelles
            </p>
          </div>

          <div class="text-center group">
            <div class="relative mb-8">
              <div class="w-24 h-24 bg-gradient-to-br from-success-500 to-success-600 rounded-3xl flex items-center justify-center mx-auto shadow-xl group-hover:shadow-2xl transform group-hover:-translate-y-2 transition-all duration-300">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <div class="absolute -top-2 -right-2 w-6 h-6 bg-secondary-400 rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <h3 class="text-2xl font-bold text-accent-900 mb-4">Communauté unie</h3>
            <p class="text-accent-600 leading-relaxed text-lg">
              Rassemblez-vous avec la communauté kabyle du Canada et créez des liens
              durables autour de notre culture commune
            </p>
          </div>
        </div>

        <!-- Call to action -->
        <div class="text-center mt-20">
          <div class="card max-w-4xl mx-auto">
            <h3 class="text-3xl font-bold text-accent-900 mb-4">Prêt à rejoindre notre communauté ?</h3>
            <p class="text-xl text-accent-600 mb-8 leading-relaxed">
              Découvrez des événements exceptionnels et connectez-vous avec des milliers de membres de notre communauté
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <router-link to="/register" class="btn-primary text-lg px-8 py-4">
                Créer un compte gratuit
              </router-link>
              <router-link to="/events" class="btn-outline text-lg px-8 py-4">
                Explorer les événements
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      featuredEvents: [],
      loading: true,
    }
  },
  methods: {
    async fetchFeaturedEvents() {
      try {
        const response = await this.$http.get('/events?limit=3');

        // Handle paginated response structure
        if (response.data.data && response.data.data.data) {
          this.featuredEvents = response.data.data.data;
        } else if (response.data.data) {
          this.featuredEvents = response.data.data;
        } else {
          this.featuredEvents = response.data;
        }
      } catch (error) {
        console.error('Error fetching events:', error);
      } finally {
        this.loading = false;
      }
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  },
  mounted() {
    this.fetchFeaturedEvents();
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}
</style>
