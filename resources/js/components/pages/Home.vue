<template>
  <div>
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-kabyle-600 to-amazigh-600 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-6">
            Découvrez la Culture Kabyle
          </h1>
          <p class="text-xl md:text-2xl mb-8 text-kabyle-100">
            Participez aux plus beaux événements culturels kabyles au Canada
          </p>
          <div class="space-x-4">
            <router-link to="/events" class="bg-white text-kabyle-600 hover:bg-gray-100 font-bold py-3 px-8 rounded-lg transition duration-200">
              Voir les événements
            </router-link>
            <router-link to="/register" class="border-2 border-white text-white hover:bg-white hover:text-kabyle-600 font-bold py-3 px-8 rounded-lg transition duration-200">
              Devenir organisateur
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Events -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Événements à venir</h2>
          <p class="text-lg text-gray-600">Ne manquez pas ces événements exceptionnels</p>
        </div>
        
        <div v-if="loading" class="text-center">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-kabyle-600"></div>
        </div>
        
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="event in featuredEvents" :key="event.id" class="card hover:shadow-xl transition duration-300">
            <div class="h-48 bg-gradient-to-r from-kabyle-400 to-amazigh-400 rounded-lg mb-4 flex items-center justify-center">
              <span class="text-white text-lg font-semibold">{{ event.title.substring(0, 2).toUpperCase() }}</span>
            </div>
            <h3 class="text-xl font-semibold mb-2">{{ event.title }}</h3>
            <p class="text-gray-600 mb-4 line-clamp-3">{{ event.description }}</p>
            <div class="flex justify-between items-center mb-4">
              <span class="text-sm text-gray-500">{{ formatDate(event.event_date) }}</span>
              <span class="text-lg font-bold text-kabyle-600">${{ event.ticket_price }}</span>
            </div>
            <router-link :to="`/events/${event.id}`" class="btn-primary w-full text-center">
              Voir détails
            </router-link>
          </div>
        </div>
        
        <div class="text-center mt-12">
          <router-link to="/events" class="btn-secondary">
            Voir tous les événements
          </router-link>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Pourquoi choisir KabEvents ?</h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-kabyle-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Événements authentiques</h3>
            <p class="text-gray-600">Des événements culturels kabyles authentiques organisés par la communauté</p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-amazigh-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Réservation sécurisée</h3>
            <p class="text-gray-600">Système de paiement sécurisé avec billets électroniques et QR codes</p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-kabyle-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Communauté unie</h3>
            <p class="text-gray-600">Rassemblez-vous avec la communauté kabyle du Canada</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      featuredEvents: [],
      loading: true,
    }
  },
  methods: {
    async fetchFeaturedEvents() {
      try {
        const response = await this.$http.get('/events?limit=3');
        this.featuredEvents = response.data.data || response.data;
      } catch (error) {
        console.error('Error fetching events:', error);
      } finally {
        this.loading = false;
      }
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  },
  mounted() {
    this.fetchFeaturedEvents();
  }
}
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
