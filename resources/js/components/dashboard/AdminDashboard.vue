<template>
  <div class="min-h-screen">
    <!-- Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-3xl font-bold text-gradient">Dashboard Administrateur</h1>
            <p class="text-accent-600 mt-1">Bienvenue, {{ user?.name }}</p>
          </div>
          <div class="flex items-center space-x-4">
            <span class="status-badge bg-danger-100 text-danger-800">
              Administrateur
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Navigation Tabs -->
      <div class="mb-8">
        <nav class="flex space-x-8">
          <button 
            @click="activeTab = 'overview'"
            :class="activeTab === 'overview' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Vue d'ensemble
          </button>
          <button 
            @click="activeTab = 'users'"
            :class="activeTab === 'users' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Utilisateurs
          </button>
          <button 
            @click="activeTab = 'events'"
            :class="activeTab === 'events' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Événements
          </button>
          <button 
            @click="activeTab = 'reservations'"
            :class="activeTab === 'reservations' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Réservations
          </button>
        </nav>
      </div>

      <!-- Overview Tab -->
      <div v-if="activeTab === 'overview'" class="space-y-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="card text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
            </div>
            <div class="text-3xl font-bold text-primary-600 mb-2">{{ adminStats.totalUsers }}</div>
            <div class="text-accent-600 font-medium">Utilisateurs</div>
          </div>

          <div class="card text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div class="text-3xl font-bold text-secondary-600 mb-2">{{ adminStats.totalEvents }}</div>
            <div class="text-accent-600 font-medium">Événements</div>
          </div>

          <div class="card text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
              </svg>
            </div>
            <div class="text-3xl font-bold text-success-600 mb-2">{{ adminStats.totalReservations }}</div>
            <div class="text-accent-600 font-medium">Réservations</div>
          </div>

          <div class="card text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div class="text-3xl font-bold text-accent-600 mb-2">${{ adminStats.totalRevenue }}</div>
            <div class="text-accent-600 font-medium">Revenus</div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
          <h3 class="text-xl font-bold text-accent-900 mb-6">Activité récente</h3>
          <div class="space-y-4">
            <div v-for="activity in recentActivity" :key="activity.id" class="flex items-center space-x-4 p-4 bg-accent-50 rounded-lg">
              <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="flex-1">
                <p class="text-accent-900 font-medium">{{ activity.description }}</p>
                <p class="text-accent-500 text-sm">{{ formatDate(activity.created_at) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Users Tab -->
      <div v-if="activeTab === 'users'" class="space-y-6">
        <div class="flex justify-between items-center">
          <h2 class="text-2xl font-bold text-accent-900">Gestion des utilisateurs</h2>
          <button @click="showCreateUserModal = true" class="btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nouvel utilisateur
          </button>
        </div>

        <div class="card">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-accent-200">
              <thead class="bg-accent-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider">Utilisateur</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider">Rôle</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider">Statut</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider">Inscrit le</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-accent-200">
                <tr v-for="user in users" :key="user.id" class="hover:bg-accent-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <span class="text-primary-600 font-medium">{{ user.name.charAt(0).toUpperCase() }}</span>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-accent-900">{{ user.name }}</div>
                        <div class="text-sm text-accent-500">{{ user.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="getRoleClass(user.role)" class="status-badge">
                      {{ getRoleText(user.role) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="user.email_verified_at ? 'status-active' : 'status-inactive'" class="status-badge">
                      {{ user.email_verified_at ? 'Vérifié' : 'Non vérifié' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-accent-500">
                    {{ formatDate(user.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button @click="editUser(user)" class="text-primary-600 hover:text-primary-900 mr-3">Modifier</button>
                    <button @click="deleteUser(user)" class="text-danger-600 hover:text-danger-900">Supprimer</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Events Tab -->
      <div v-if="activeTab === 'events'" class="space-y-6">
        <div class="flex justify-between items-center">
          <h2 class="text-2xl font-bold text-accent-900">Gestion des événements</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="event in events" :key="event.id" class="event-card">
            <div class="event-image">
              <div class="text-center">
                <span class="text-white text-2xl font-bold">{{ event.title.substring(0, 2).toUpperCase() }}</span>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-lg font-bold text-accent-900 mb-2">{{ event.title }}</h3>
              <p class="text-accent-600 text-sm mb-4">{{ event.description.substring(0, 100) }}...</p>
              <div class="flex justify-between items-center mb-4">
                <span class="price-tag">${{ event.ticket_price }}</span>
                <span :class="event.status === 'active' ? 'status-active' : 'status-inactive'" class="status-badge">
                  {{ event.status === 'active' ? 'Actif' : 'Inactif' }}
                </span>
              </div>
              <div class="flex space-x-2">
                <button @click="editEvent(event)" class="btn-primary text-sm px-3 py-1 flex-1">Modifier</button>
                <button @click="deleteEvent(event)" class="btn-outline text-sm px-3 py-1">Supprimer</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Reservations Tab -->
      <div v-if="activeTab === 'reservations'" class="space-y-6">
        <h2 class="text-2xl font-bold text-accent-900">Gestion des réservations</h2>

        <div class="card">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-accent-200">
              <thead class="bg-accent-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider">Réservation</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider">Utilisateur</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider">Événement</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider">Montant</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider">Statut</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-accent-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-accent-200">
                <tr v-for="reservation in reservations" :key="reservation.id" class="hover:bg-accent-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-accent-900">
                    #{{ reservation.id }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-accent-900">
                    {{ reservation.user?.name }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-accent-900">
                    {{ reservation.event?.title }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-accent-900">
                    ${{ reservation.total_amount }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="getStatusClass(reservation.status)" class="status-badge">
                      {{ getStatusText(reservation.status) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button @click="viewReservation(reservation)" class="text-primary-600 hover:text-primary-900">Voir</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminDashboard',
  data() {
    return {
      user: null,
      activeTab: 'overview',
      adminStats: {
        totalUsers: 0,
        totalEvents: 0,
        totalReservations: 0,
        totalRevenue: 0,
      },
      users: [],
      events: [],
      reservations: [],
      recentActivity: [],
      loading: true,
      showCreateUserModal: false,
    }
  },
  methods: {
    async fetchData() {
      try {
        // Get user info
        const userResponse = await this.$http.get('/me');
        this.user = userResponse.data.user;

        // Get admin stats
        const statsResponse = await this.$http.get('/admin/stats');
        this.adminStats = statsResponse.data;

        // Get users
        const usersResponse = await this.$http.get('/admin/users');
        this.users = usersResponse.data.data || usersResponse.data;

        // Get events
        const eventsResponse = await this.$http.get('/admin/events');
        this.events = eventsResponse.data.data || eventsResponse.data;

        // Get reservations
        const reservationsResponse = await this.$http.get('/admin/reservations');
        this.reservations = reservationsResponse.data.data || reservationsResponse.data;

      } catch (error) {
        console.error('Error fetching admin data:', error);
        if (error.response && error.response.status === 401) {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          this.$router.push('/login');
        }
      } finally {
        this.loading = false;
      }
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    },
    getRoleClass(role) {
      switch (role) {
        case 'admin':
          return 'bg-danger-100 text-danger-800';
        case 'organizer':
          return 'bg-secondary-100 text-secondary-800';
        default:
          return 'bg-primary-100 text-primary-800';
      }
    },
    getRoleText(role) {
      switch (role) {
        case 'admin':
          return 'Administrateur';
        case 'organizer':
          return 'Organisateur';
        default:
          return 'Utilisateur';
      }
    },
    getStatusClass(status) {
      switch (status) {
        case 'paid':
          return 'status-active';
        case 'pending':
          return 'bg-secondary-100 text-secondary-800';
        case 'cancelled':
          return 'status-inactive';
        default:
          return 'bg-accent-100 text-accent-800';
      }
    },
    getStatusText(status) {
      switch (status) {
        case 'paid':
          return 'Payé';
        case 'pending':
          return 'En attente';
        case 'cancelled':
          return 'Annulé';
        default:
          return status;
      }
    },
    editUser(user) {
      // TODO: Implement user editing
      console.log('Edit user:', user);
    },
    deleteUser(user) {
      // TODO: Implement user deletion
      console.log('Delete user:', user);
    },
    editEvent(event) {
      // TODO: Implement event editing
      console.log('Edit event:', event);
    },
    deleteEvent(event) {
      // TODO: Implement event deletion
      console.log('Delete event:', event);
    },
    viewReservation(reservation) {
      // TODO: Implement reservation viewing
      console.log('View reservation:', reservation);
    }
  },
  mounted() {
    const userData = localStorage.getItem('user');
    if (userData) {
      this.user = JSON.parse(userData);
    }
    this.fetchData();
  }
}
</script>
