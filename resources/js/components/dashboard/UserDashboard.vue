<template>
  <div class="min-h-screen">
    <!-- Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-3xl font-bold text-gradient">Mon Dashboard</h1>
            <p class="text-accent-600 mt-1">Bienvenue, {{ user?.name }}</p>
          </div>
          <div class="flex items-center space-x-4">
            <span class="status-badge bg-primary-100 text-primary-800">
              Utilisateur
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Navigation Tabs -->
      <div class="mb-8">
        <nav class="flex space-x-8">
          <button
            @click="activeTab = 'overview'"
            :class="activeTab === 'overview' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Vue d'ensemble
          </button>
          <button
            @click="activeTab = 'reservations'"
            :class="activeTab === 'reservations' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Mes réservations
          </button>
          <button
            @click="activeTab = 'events'"
            :class="activeTab === 'events' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Événements disponibles
          </button>
          <button
            @click="activeTab = 'profile'"
            :class="activeTab === 'profile' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Mon profil
          </button>
        </nav>
      </div>

      <!-- Overview Tab -->
      <div v-if="activeTab === 'overview'" class="space-y-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="card text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
              </svg>
            </div>
            <div class="text-3xl font-bold text-primary-600 mb-2">{{ userStats.totalReservations }}</div>
            <div class="text-accent-600 font-medium">Réservations totales</div>
          </div>

          <div class="card text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div class="text-3xl font-bold text-secondary-600 mb-2">{{ userStats.upcomingEvents }}</div>
            <div class="text-accent-600 font-medium">Événements à venir</div>
          </div>

          <div class="card text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div class="text-3xl font-bold text-success-600 mb-2">${{ userStats.totalSpent }}</div>
            <div class="text-accent-600 font-medium">Total dépensé</div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="card text-center group cursor-pointer" @click="activeTab = 'events'">
            <div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-accent-900 mb-3">Découvrir des événements</h3>
            <p class="text-accent-600 mb-6">Explorez les événements culturels kabyles disponibles</p>
            <div class="btn-primary inline-block">Explorer</div>
          </div>

          <div class="card text-center group cursor-pointer" @click="activeTab = 'reservations'">
            <div class="w-20 h-20 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-accent-900 mb-3">Mes réservations</h3>
            <p class="text-accent-600 mb-6">Gérez vos billets et réservations d'événements</p>
            <div class="btn-outline inline-block">Voir mes billets</div>
          </div>
        </div>

        <!-- Upcoming Events -->
        <div class="card">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-accent-900">Mes prochains événements</h3>
            <button @click="activeTab = 'reservations'" class="text-primary-600 hover:text-primary-700 font-medium">
              Voir tous
            </button>
          </div>

          <div v-if="upcomingReservations.length === 0" class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-accent-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-accent-900">Aucun événement à venir</h3>
            <p class="mt-1 text-sm text-accent-500">Découvrez et réservez votre prochain événement culturel.</p>
            <div class="mt-6">
              <button @click="activeTab = 'events'" class="btn-primary">
                Découvrir des événements
              </button>
            </div>
          </div>

          <div v-else class="space-y-4">
            <div v-for="reservation in upcomingReservations" :key="reservation.id" class="flex items-center space-x-4 p-4 bg-accent-50 rounded-lg hover:bg-accent-100 transition-colors duration-200">
              <div class="w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center">
                <span class="text-white font-bold">{{ reservation.event?.title.substring(0, 2).toUpperCase() }}</span>
              </div>
              <div class="flex-1">
                <h4 class="font-semibold text-accent-900">{{ reservation.event?.title }}</h4>
                <p class="text-accent-600 text-sm">{{ reservation.event?.location }}</p>
                <p class="text-accent-500 text-sm">{{ formatDate(reservation.event?.event_date) }}</p>
              </div>
              <div class="text-right">
                <span :class="getStatusClass(reservation.status)" class="status-badge">
                  {{ getStatusText(reservation.status) }}
                </span>
                <p class="text-accent-500 text-sm mt-1">{{ reservation.quantity }} billet(s)</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Reservations Tab -->
      <div v-if="activeTab === 'reservations'" class="space-y-6">
        <div class="flex justify-between items-center">
          <h2 class="text-2xl font-bold text-accent-900">Mes réservations</h2>
          <button @click="activeTab = 'events'" class="btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nouvelle réservation
          </button>
        </div>

        <div v-if="loading" class="text-center py-16">
          <div class="loading-spinner w-12 h-12 mx-auto"></div>
          <p class="text-accent-600 mt-4">Chargement de vos réservations...</p>
        </div>

        <div v-else-if="reservations.length === 0" class="text-center py-16">
          <svg class="mx-auto h-16 w-16 text-accent-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
          </svg>
          <h3 class="mt-4 text-lg font-medium text-accent-900">Aucune réservation</h3>
          <p class="mt-2 text-accent-500">Vous n'avez pas encore réservé d'événements.</p>
          <div class="mt-6">
            <button @click="activeTab = 'events'" class="btn-primary">
              Découvrir des événements
            </button>
          </div>
        </div>

        <div v-else class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div v-for="reservation in reservations" :key="reservation.id" class="card">
            <div class="flex items-start space-x-4">
              <div class="w-16 h-16 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center flex-shrink-0">
                <span class="text-white font-bold text-lg">{{ reservation.event?.title.substring(0, 2).toUpperCase() }}</span>
              </div>
              <div class="flex-1 min-w-0">
                <h3 class="font-bold text-lg text-accent-900 mb-1">{{ reservation.event?.title }}</h3>
                <p class="text-accent-600 text-sm mb-2">{{ reservation.event?.location }}</p>
                <p class="text-accent-500 text-sm mb-3">{{ formatDate(reservation.event?.event_date) }}</p>

                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center space-x-4">
                    <span class="text-sm text-accent-600">{{ reservation.quantity }} billet(s)</span>
                    <span class="font-semibold text-accent-900">${{ reservation.total_amount }}</span>
                  </div>
                  <span :class="getStatusClass(reservation.status)" class="status-badge">
                    {{ getStatusText(reservation.status) }}
                  </span>
                </div>

                <div class="flex space-x-2">
                  <router-link :to="`/events/${reservation.event?.id}`" class="btn-outline text-sm px-3 py-1 flex-1 text-center">
                    Voir l'événement
                  </router-link>
                  <button v-if="reservation.status === 'paid'" @click="downloadTicket(reservation)" class="btn-primary text-sm px-3 py-1">
                    Télécharger
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Events Tab -->
      <div v-if="activeTab === 'events'" class="space-y-6">
        <div class="flex justify-between items-center">
          <h2 class="text-2xl font-bold text-accent-900">Événements disponibles</h2>
          <router-link to="/events" class="btn-outline">
            Voir tous les événements
          </router-link>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="event in availableEvents" :key="event.id" class="event-card">
            <div class="event-image">
              <div class="text-center">
                <span class="text-white text-2xl font-bold">{{ event.title.substring(0, 2).toUpperCase() }}</span>
                <div class="mt-2 text-white/80 text-sm">{{ event.location }}</div>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-lg font-bold text-accent-900 mb-2">{{ event.title }}</h3>
              <p class="text-accent-600 text-sm mb-4 line-clamp-2">{{ event.description }}</p>
              <div class="flex justify-between items-center mb-4">
                <span class="price-tag">${{ event.ticket_price }}</span>
                <span class="text-sm text-accent-500">{{ formatDate(event.event_date) }}</span>
              </div>
              <router-link :to="`/events/${event.id}`" class="btn-primary w-full text-center">
                Réserver
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Tab -->
      <div v-if="activeTab === 'profile'" class="space-y-6">
        <h2 class="text-2xl font-bold text-accent-900">Mon profil</h2>

        <div class="card max-w-2xl">
          <form @submit.prevent="updateProfile" class="space-y-6">
            <div class="flex items-center space-x-6">
              <div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                <span class="text-white font-bold text-2xl">{{ user?.name?.charAt(0).toUpperCase() }}</span>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-accent-900">{{ user?.name }}</h3>
                <p class="text-accent-600">{{ user?.email }}</p>
                <span :class="getRoleClass(user?.role)" class="status-badge mt-2">
                  {{ getRoleText(user?.role) }}
                </span>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-accent-700 mb-2">Nom complet</label>
                <input v-model="profileForm.name" type="text" class="input-field" required>
              </div>
              <div>
                <label class="block text-sm font-medium text-accent-700 mb-2">Email</label>
                <input v-model="profileForm.email" type="email" class="input-field" required>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-accent-700 mb-2">Nouveau mot de passe (optionnel)</label>
              <input v-model="profileForm.password" type="password" class="input-field" placeholder="Laissez vide pour ne pas changer">
            </div>

            <div>
              <label class="block text-sm font-medium text-accent-700 mb-2">Confirmer le mot de passe</label>
              <input v-model="profileForm.password_confirmation" type="password" class="input-field">
            </div>

            <div class="flex justify-end">
              <button type="submit" :disabled="updating" class="btn-primary">
                <span v-if="updating" class="loading-spinner w-4 h-4 mr-2"></span>
                {{ updating ? 'Mise à jour...' : 'Mettre à jour' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserDashboard',
  data() {
    return {
      user: null,
      activeTab: 'overview',
      userStats: {
        totalReservations: 0,
        upcomingEvents: 0,
        totalSpent: 0,
      },
      reservations: [],
      upcomingReservations: [],
      availableEvents: [],
      profileForm: {
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
      },
      loading: true,
      updating: false,
    }
  },
  methods: {
    async fetchData() {
      try {
        // Get user info
        const userResponse = await this.$http.get('/me');
        this.user = userResponse.data.user;

        // Initialize profile form
        this.profileForm.name = this.user.name;
        this.profileForm.email = this.user.email;

        // Get user reservations
        const reservationsResponse = await this.$http.get('/user/reservations');
        this.reservations = reservationsResponse.data.data || reservationsResponse.data;

        // Calculate stats
        this.userStats.totalReservations = this.reservations.length;
        this.upcomingReservations = this.reservations.filter(r =>
          new Date(r.event?.event_date) > new Date()
        );
        this.userStats.upcomingEvents = this.upcomingReservations.length;
        this.userStats.totalSpent = this.reservations
          .filter(r => r.status === 'paid')
          .reduce((sum, r) => sum + parseFloat(r.total_amount), 0)
          .toFixed(2);

        // Get available events
        const eventsResponse = await this.$http.get('/events?limit=6');

        // Handle paginated response structure
        if (eventsResponse.data.data && eventsResponse.data.data.data) {
          this.availableEvents = eventsResponse.data.data.data;
        } else if (eventsResponse.data.data) {
          this.availableEvents = eventsResponse.data.data;
        } else {
          this.availableEvents = eventsResponse.data;
        }

      } catch (error) {
        console.error('Error fetching user data:', error);
        if (error.response && error.response.status === 401) {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          this.$router.push('/login');
        }
      } finally {
        this.loading = false;
      }
    },
    async updateProfile() {
      this.updating = true;
      try {
        const response = await this.$http.put('/user/profile', this.profileForm);
        this.user = response.data.user;
        localStorage.setItem('user', JSON.stringify(this.user));
        alert('Profil mis à jour avec succès !');
        this.profileForm.password = '';
        this.profileForm.password_confirmation = '';
      } catch (error) {
        console.error('Error updating profile:', error);
        alert('Erreur lors de la mise à jour du profil');
      } finally {
        this.updating = false;
      }
    },
    downloadTicket(reservation) {
      // TODO: Implement ticket download
      console.log('Download ticket for reservation:', reservation);
      alert('Fonctionnalité de téléchargement à implémenter');
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    getStatusClass(status) {
      switch (status) {
        case 'paid':
          return 'status-active';
        case 'pending':
          return 'bg-secondary-100 text-secondary-800';
        case 'cancelled':
          return 'status-inactive';
        default:
          return 'bg-accent-100 text-accent-800';
      }
    },
    getStatusText(status) {
      switch (status) {
        case 'paid':
          return 'Payé';
        case 'pending':
          return 'En attente';
        case 'cancelled':
          return 'Annulé';
        default:
          return status;
      }
    },
    getRoleClass(role) {
      switch (role) {
        case 'admin':
          return 'bg-danger-100 text-danger-800';
        case 'organizer':
          return 'bg-secondary-100 text-secondary-800';
        default:
          return 'bg-primary-100 text-primary-800';
      }
    },
    getRoleText(role) {
      switch (role) {
        case 'admin':
          return 'Administrateur';
        case 'organizer':
          return 'Organisateur';
        default:
          return 'Utilisateur';
      }
    }
  },
  mounted() {
    const userData = localStorage.getItem('user');
    if (userData) {
      this.user = JSON.parse(userData);
    }
    this.fetchData();
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
