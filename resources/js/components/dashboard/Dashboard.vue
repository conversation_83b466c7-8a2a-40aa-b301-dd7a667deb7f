<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p class="text-gray-600">Bienvenue, {{ user?.name }}</p>
          </div>
          <div class="flex items-center space-x-2">
            <span class="px-3 py-1 bg-kabyle-100 text-kabyle-800 rounded-full text-sm font-medium">
              {{ user?.role === 'admin' ? 'Administrateur' : user?.role === 'organizer' ? 'Organisateur' : 'Utilisateur' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- User Dashboard -->
      <div v-if="user?.role === 'user'" class="space-y-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="card">
            <div class="flex items-center">
              <div class="p-3 bg-kabyle-100 rounded-lg">
                <svg class="w-6 h-6 text-kabyle-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Réservations totales</p>
                <p class="text-2xl font-bold text-gray-900">{{ userStats.totalReservations }}</p>
              </div>
            </div>
          </div>
          
          <div class="card">
            <div class="flex items-center">
              <div class="p-3 bg-amazigh-100 rounded-lg">
                <svg class="w-6 h-6 text-amazigh-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Événements à venir</p>
                <p class="text-2xl font-bold text-gray-900">{{ userStats.upcomingEvents }}</p>
              </div>
            </div>
          </div>
          
          <div class="card">
            <div class="flex items-center">
              <div class="p-3 bg-green-100 rounded-lg">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total dépensé</p>
                <p class="text-2xl font-bold text-gray-900">${{ userStats.totalSpent }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- My Reservations -->
        <div class="card">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-semibold">Mes réservations</h2>
            <router-link to="/events" class="btn-primary">
              Réserver un événement
            </router-link>
          </div>
          
          <div v-if="loading" class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-kabyle-600"></div>
          </div>
          
          <div v-else-if="reservations.length === 0" class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune réservation</h3>
            <p class="mt-1 text-sm text-gray-500">Commencez par réserver votre premier événement.</p>
          </div>
          
          <div v-else class="space-y-4">
            <div v-for="reservation in reservations" :key="reservation.id" class="border rounded-lg p-4 hover:bg-gray-50">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <h3 class="font-semibold text-lg">{{ reservation.event?.title }}</h3>
                  <p class="text-gray-600 text-sm mb-2">{{ reservation.event?.location }}</p>
                  <p class="text-gray-600 text-sm">{{ formatDate(reservation.event?.event_date) }}</p>
                  <div class="mt-2 flex items-center space-x-4">
                    <span class="text-sm text-gray-500">{{ reservation.quantity }} billet(s)</span>
                    <span class="text-sm font-medium">${{ reservation.total_amount }}</span>
                    <span :class="getStatusClass(reservation.status)" class="px-2 py-1 rounded-full text-xs font-medium">
                      {{ getStatusText(reservation.status) }}
                    </span>
                  </div>
                </div>
                <div class="ml-4">
                  <router-link :to="`/events/${reservation.event?.id}`" class="text-kabyle-600 hover:text-kabyle-700 text-sm">
                    Voir l'événement
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Organizer Dashboard -->
      <div v-else-if="user?.role === 'organizer'" class="space-y-8">
        <div class="text-center">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">Dashboard Organisateur</h2>
          <p class="text-gray-600">Gérez vos événements et suivez vos ventes</p>
        </div>
        
        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="card text-center">
            <svg class="mx-auto h-12 w-12 text-kabyle-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <h3 class="text-lg font-semibold mb-2">Créer un événement</h3>
            <p class="text-gray-600 mb-4">Organisez votre prochain événement culturel</p>
            <button class="btn-primary">Créer un événement</button>
          </div>
          
          <div class="card text-center">
            <svg class="mx-auto h-12 w-12 text-amazigh-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <h3 class="text-lg font-semibold mb-2">Voir les statistiques</h3>
            <p class="text-gray-600 mb-4">Analysez les performances de vos événements</p>
            <button class="btn-secondary">Voir les stats</button>
          </div>
        </div>
      </div>

      <!-- Admin Dashboard -->
      <div v-else-if="user?.role === 'admin'" class="space-y-8">
        <div class="text-center">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">Dashboard Administrateur</h2>
          <p class="text-gray-600">Gérez la plateforme KabEvents</p>
        </div>
        
        <!-- Admin Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="card text-center">
            <div class="text-3xl font-bold text-kabyle-600 mb-2">{{ adminStats.totalUsers }}</div>
            <div class="text-gray-600">Utilisateurs</div>
          </div>
          <div class="card text-center">
            <div class="text-3xl font-bold text-amazigh-600 mb-2">{{ adminStats.totalEvents }}</div>
            <div class="text-gray-600">Événements</div>
          </div>
          <div class="card text-center">
            <div class="text-3xl font-bold text-green-600 mb-2">{{ adminStats.totalReservations }}</div>
            <div class="text-gray-600">Réservations</div>
          </div>
          <div class="card text-center">
            <div class="text-3xl font-bold text-purple-600 mb-2">${{ adminStats.totalRevenue }}</div>
            <div class="text-gray-600">Revenus</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      user: null,
      reservations: [],
      userStats: {
        totalReservations: 0,
        upcomingEvents: 0,
        totalSpent: 0,
      },
      adminStats: {
        totalUsers: 0,
        totalEvents: 0,
        totalReservations: 0,
        totalRevenue: 0,
      },
      loading: true,
    }
  },
  methods: {
    async fetchUserData() {
      try {
        // Get user info
        const userResponse = await this.$http.get('/me');
        this.user = userResponse.data.user;
        
        if (this.user.role === 'user') {
          // Get user reservations
          const reservationsResponse = await this.$http.get('/user/reservations');
          this.reservations = reservationsResponse.data.data || reservationsResponse.data;
          
          // Calculate stats
          this.userStats.totalReservations = this.reservations.length;
          this.userStats.upcomingEvents = this.reservations.filter(r => 
            new Date(r.event?.event_date) > new Date()
          ).length;
          this.userStats.totalSpent = this.reservations
            .filter(r => r.status === 'paid')
            .reduce((sum, r) => sum + parseFloat(r.total_amount), 0)
            .toFixed(2);
        }
        
      } catch (error) {
        console.error('Error fetching user data:', error);
        // Redirect to login if unauthorized
        if (error.response && error.response.status === 401) {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          this.$router.push('/login');
        }
      } finally {
        this.loading = false;
      }
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    getStatusClass(status) {
      switch (status) {
        case 'paid':
          return 'bg-green-100 text-green-800';
        case 'pending':
          return 'bg-yellow-100 text-yellow-800';
        case 'cancelled':
          return 'bg-red-100 text-red-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    },
    getStatusText(status) {
      switch (status) {
        case 'paid':
          return 'Payé';
        case 'pending':
          return 'En attente';
        case 'cancelled':
          return 'Annulé';
        default:
          return status;
      }
    }
  },
  mounted() {
    // Get user from localStorage if available
    const userData = localStorage.getItem('user');
    if (userData) {
      this.user = JSON.parse(userData);
    }
    
    this.fetchUserData();
  }
}
</script>
