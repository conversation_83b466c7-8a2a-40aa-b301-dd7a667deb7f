<template>
  <div class="min-h-screen">
    <!-- Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-3xl font-bold text-gradient">Dashboard Organisateur</h1>
            <p class="text-accent-600 mt-1">Bienvenue, {{ user?.name }}</p>
          </div>
          <div class="flex items-center space-x-4">
            <span class="status-badge bg-secondary-100 text-secondary-800">
              Organisateur
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Navigation Tabs -->
      <div class="mb-8">
        <nav class="flex space-x-8">
          <button 
            @click="activeTab = 'overview'"
            :class="activeTab === 'overview' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Vue d'ensemble
          </button>
          <button 
            @click="activeTab = 'events'"
            :class="activeTab === 'events' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Mes événements
          </button>
          <button 
            @click="activeTab = 'create'"
            :class="activeTab === 'create' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Créer un événement
          </button>
          <button 
            @click="activeTab = 'analytics'"
            :class="activeTab === 'analytics' ? 'border-primary-500 text-primary-600' : 'border-transparent text-accent-500 hover:text-accent-700'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          >
            Statistiques
          </button>
        </nav>
      </div>

      <!-- Overview Tab -->
      <div v-if="activeTab === 'overview'" class="space-y-8">
        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="card text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div class="text-3xl font-bold text-primary-600 mb-2">{{ organizerStats.totalEvents }}</div>
            <div class="text-accent-600 font-medium">Événements créés</div>
          </div>

          <div class="card text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
              </svg>
            </div>
            <div class="text-3xl font-bold text-secondary-600 mb-2">{{ organizerStats.totalReservations }}</div>
            <div class="text-accent-600 font-medium">Réservations</div>
          </div>

          <div class="card text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div class="text-3xl font-bold text-success-600 mb-2">${{ organizerStats.totalRevenue }}</div>
            <div class="text-accent-600 font-medium">Revenus</div>
          </div>

          <div class="card text-center">
            <div class="w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
            <div class="text-3xl font-bold text-accent-600 mb-2">{{ organizerStats.averageAttendance }}%</div>
            <div class="text-accent-600 font-medium">Taux de participation</div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="card text-center group cursor-pointer" @click="activeTab = 'create'">
            <div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-accent-900 mb-3">Créer un événement</h3>
            <p class="text-accent-600 mb-6">Organisez votre prochain événement culturel kabyle</p>
            <div class="btn-primary inline-block">Commencer</div>
          </div>

          <div class="card text-center group cursor-pointer" @click="activeTab = 'analytics'">
            <div class="w-20 h-20 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-105 transition-transform duration-200">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-accent-900 mb-3">Voir les statistiques</h3>
            <p class="text-accent-600 mb-6">Analysez les performances de vos événements</p>
            <div class="btn-outline inline-block">Analyser</div>
          </div>
        </div>

        <!-- Recent Events -->
        <div class="card">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-accent-900">Événements récents</h3>
            <button @click="activeTab = 'events'" class="text-primary-600 hover:text-primary-700 font-medium">
              Voir tous
            </button>
          </div>
          <div class="space-y-4">
            <div v-for="event in recentEvents" :key="event.id" class="flex items-center space-x-4 p-4 bg-accent-50 rounded-lg hover:bg-accent-100 transition-colors duration-200">
              <div class="w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-xl flex items-center justify-center">
                <span class="text-white font-bold">{{ event.title.substring(0, 2).toUpperCase() }}</span>
              </div>
              <div class="flex-1">
                <h4 class="font-semibold text-accent-900">{{ event.title }}</h4>
                <p class="text-accent-600 text-sm">{{ formatDate(event.event_date) }}</p>
              </div>
              <div class="text-right">
                <p class="font-semibold text-accent-900">${{ event.ticket_price }}</p>
                <p class="text-accent-500 text-sm">{{ event.reservations_count || 0 }} réservations</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Events Tab -->
      <div v-if="activeTab === 'events'" class="space-y-6">
        <div class="flex justify-between items-center">
          <h2 class="text-2xl font-bold text-accent-900">Mes événements</h2>
          <button @click="activeTab = 'create'" class="btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nouvel événement
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="event in myEvents" :key="event.id" class="event-card">
            <div class="event-image">
              <div class="text-center">
                <span class="text-white text-2xl font-bold">{{ event.title.substring(0, 2).toUpperCase() }}</span>
                <div class="mt-2 text-white/80 text-sm">{{ event.location }}</div>
              </div>
            </div>
            <div class="p-6">
              <div class="flex justify-between items-start mb-3">
                <h3 class="text-lg font-bold text-accent-900">{{ event.title }}</h3>
                <span :class="event.status === 'active' ? 'status-active' : 'status-inactive'" class="status-badge">
                  {{ event.status === 'active' ? 'Actif' : 'Inactif' }}
                </span>
              </div>
              <p class="text-accent-600 text-sm mb-4 line-clamp-2">{{ event.description }}</p>
              <div class="flex justify-between items-center mb-4">
                <span class="price-tag">${{ event.ticket_price }}</span>
                <span class="text-sm text-accent-500">{{ formatDate(event.event_date) }}</span>
              </div>
              <div class="flex space-x-2">
                <button @click="editEvent(event)" class="btn-primary text-sm px-3 py-1 flex-1">Modifier</button>
                <button @click="viewEventStats(event)" class="btn-outline text-sm px-3 py-1">Stats</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Create Event Tab -->
      <div v-if="activeTab === 'create'" class="space-y-6">
        <h2 class="text-2xl font-bold text-accent-900">Créer un nouvel événement</h2>

        <div class="card max-w-4xl">
          <form @submit.prevent="createEvent" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-accent-700 mb-2">Titre de l'événement</label>
                <input v-model="newEvent.title" type="text" class="input-field" placeholder="Ex: Festival de musique kabyle" required>
              </div>
              <div>
                <label class="block text-sm font-medium text-accent-700 mb-2">Lieu</label>
                <input v-model="newEvent.location" type="text" class="input-field" placeholder="Ex: Centre culturel, Montréal" required>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-accent-700 mb-2">Description</label>
              <textarea v-model="newEvent.description" rows="4" class="input-field" placeholder="Décrivez votre événement..." required></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label class="block text-sm font-medium text-accent-700 mb-2">Date et heure</label>
                <input v-model="newEvent.event_date" type="datetime-local" class="input-field" required>
              </div>
              <div>
                <label class="block text-sm font-medium text-accent-700 mb-2">Prix du billet ($)</label>
                <input v-model="newEvent.ticket_price" type="number" step="0.01" class="input-field" placeholder="25.00" required>
              </div>
              <div>
                <label class="block text-sm font-medium text-accent-700 mb-2">Nombre de places</label>
                <input v-model="newEvent.max_attendees" type="number" class="input-field" placeholder="100" required>
              </div>
            </div>

            <div class="flex justify-end space-x-4">
              <button type="button" @click="resetForm" class="btn-outline">Annuler</button>
              <button type="submit" :disabled="creating" class="btn-primary">
                <span v-if="creating" class="loading-spinner w-4 h-4 mr-2"></span>
                {{ creating ? 'Création...' : 'Créer l\'événement' }}
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Analytics Tab -->
      <div v-if="activeTab === 'analytics'" class="space-y-6">
        <h2 class="text-2xl font-bold text-accent-900">Statistiques et analyses</h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="card">
            <h3 class="text-lg font-bold text-accent-900 mb-4">Revenus par mois</h3>
            <div class="h-64 flex items-center justify-center bg-accent-50 rounded-lg">
              <p class="text-accent-500">Graphique des revenus (à implémenter)</p>
            </div>
          </div>

          <div class="card">
            <h3 class="text-lg font-bold text-accent-900 mb-4">Événements populaires</h3>
            <div class="space-y-3">
              <div v-for="event in popularEvents" :key="event.id" class="flex justify-between items-center p-3 bg-accent-50 rounded-lg">
                <div>
                  <p class="font-medium text-accent-900">{{ event.title }}</p>
                  <p class="text-sm text-accent-500">{{ event.reservations_count }} réservations</p>
                </div>
                <div class="text-right">
                  <p class="font-semibold text-success-600">${{ event.revenue }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OrganizerDashboard',
  data() {
    return {
      user: null,
      activeTab: 'overview',
      organizerStats: {
        totalEvents: 0,
        totalReservations: 0,
        totalRevenue: 0,
        averageAttendance: 0,
      },
      myEvents: [],
      recentEvents: [],
      popularEvents: [],
      newEvent: {
        title: '',
        description: '',
        location: '',
        event_date: '',
        ticket_price: '',
        max_attendees: '',
      },
      creating: false,
      loading: true,
    }
  },
  methods: {
    async fetchData() {
      try {
        // Get user info
        const userResponse = await this.$http.get('/me');
        this.user = userResponse.data.user;

        // Get organizer stats
        const statsResponse = await this.$http.get('/organizer/stats');
        this.organizerStats = statsResponse.data;

        // Get organizer events
        const eventsResponse = await this.$http.get('/organizer/events');
        this.myEvents = eventsResponse.data.data || eventsResponse.data;
        this.recentEvents = this.myEvents.slice(0, 5);

      } catch (error) {
        console.error('Error fetching organizer data:', error);
        if (error.response && error.response.status === 401) {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          this.$router.push('/login');
        }
      } finally {
        this.loading = false;
      }
    },
    async createEvent() {
      this.creating = true;
      try {
        const response = await this.$http.post('/organizer/events', this.newEvent);
        this.myEvents.unshift(response.data.event);
        this.resetForm();
        this.activeTab = 'events';
        // Show success message
        alert('Événement créé avec succès !');
      } catch (error) {
        console.error('Error creating event:', error);
        alert('Erreur lors de la création de l\'événement');
      } finally {
        this.creating = false;
      }
    },
    resetForm() {
      this.newEvent = {
        title: '',
        description: '',
        location: '',
        event_date: '',
        ticket_price: '',
        max_attendees: '',
      };
    },
    editEvent(event) {
      // TODO: Implement event editing
      console.log('Edit event:', event);
    },
    viewEventStats(event) {
      // TODO: Implement event stats viewing
      console.log('View event stats:', event);
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  },
  mounted() {
    const userData = localStorage.getItem('user');
    if (userData) {
      this.user = JSON.parse(userData);
    }
    this.fetchData();
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
