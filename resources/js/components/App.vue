<template>
  <div id="app" class="min-h-screen">
    <!-- Navigation -->
    <nav class="glass-effect fixed w-full z-50 backdrop-blur-md">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-20">
          <div class="flex items-center">
            <router-link to="/" class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200">
                <span class="text-white font-bold text-xl">K</span>
              </div>
              <span class="text-2xl font-bold text-gradient">KabEvents</span>
            </router-link>
          </div>

          <div class="hidden md:flex items-center space-x-6">
            <router-link to="/events" class="nav-link">
              Événements
            </router-link>

            <div v-if="!isAuthenticated" class="flex items-center space-x-4">
              <router-link to="/login" class="nav-link">
                Connexion
              </router-link>
              <router-link to="/register" class="btn-primary">
                S'inscrire
              </router-link>
            </div>

            <div v-else class="flex items-center space-x-4">
              <router-link to="/dashboard" class="nav-link">
                Dashboard
              </router-link>
              <button @click="logout" class="nav-link">
                Déconnexion
              </button>
            </div>
          </div>

          <!-- Mobile menu button -->
          <div class="md:hidden flex items-center">
            <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-accent-700 hover:text-primary-600 p-2 rounded-lg transition-colors duration-200">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path v-if="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Mobile menu -->
        <div v-if="mobileMenuOpen" class="md:hidden py-4 border-t border-white/20">
          <div class="flex flex-col space-y-2">
            <router-link to="/events" class="nav-link" @click="mobileMenuOpen = false">
              Événements
            </router-link>
            <div v-if="!isAuthenticated" class="flex flex-col space-y-2">
              <router-link to="/login" class="nav-link" @click="mobileMenuOpen = false">
                Connexion
              </router-link>
              <router-link to="/register" class="btn-primary text-center" @click="mobileMenuOpen = false">
                S'inscrire
              </router-link>
            </div>
            <div v-else class="flex flex-col space-y-2">
              <router-link to="/dashboard" class="nav-link" @click="mobileMenuOpen = false">
                Dashboard
              </router-link>
              <button @click="logout; mobileMenuOpen = false" class="nav-link text-left">
                Déconnexion
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main content -->
    <main class="flex-1">
      <router-view />
    </main>

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-accent-900 via-accent-800 to-primary-900 text-white relative overflow-hidden">
      <!-- Background pattern -->
      <div class="absolute inset-0 bg-hero-pattern opacity-10"></div>

      <div class="relative max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="md:col-span-2">
            <div class="flex items-center space-x-3 mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-lg">
                <span class="text-white font-bold text-xl">K</span>
              </div>
              <span class="text-2xl font-bold">KabEvents</span>
            </div>
            <p class="text-accent-200 text-lg leading-relaxed mb-6">
              La plateforme de référence pour les événements culturels kabyles au Canada.
              Connectons notre communauté à travers la culture et les traditions.
            </p>
            <div class="flex space-x-4">
              <a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                </svg>
              </a>
              <a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                </svg>
              </a>
              <a href="#" class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-200">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 class="text-xl font-semibold mb-6 text-white">Liens rapides</h3>
            <ul class="space-y-3">
              <li><router-link to="/events" class="text-accent-200 hover:text-white transition-colors duration-200 flex items-center">
                <span class="w-2 h-2 bg-secondary-400 rounded-full mr-3"></span>
                Événements
              </router-link></li>
              <li><router-link to="/register" class="text-accent-200 hover:text-white transition-colors duration-200 flex items-center">
                <span class="w-2 h-2 bg-secondary-400 rounded-full mr-3"></span>
                Devenir organisateur
              </router-link></li>
              <li><a href="#" class="text-accent-200 hover:text-white transition-colors duration-200 flex items-center">
                <span class="w-2 h-2 bg-secondary-400 rounded-full mr-3"></span>
                À propos
              </a></li>
            </ul>
          </div>

          <div>
            <h3 class="text-xl font-semibold mb-6 text-white">Contact</h3>
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <svg class="w-5 h-5 text-secondary-400 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <div>
                  <p class="text-accent-200"><EMAIL></p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <svg class="w-5 h-5 text-secondary-400 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <div>
                  <p class="text-accent-200">+1 (555) 123-4567</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-12 pt-8 border-t border-white/20">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-accent-200 text-sm">
              &copy; 2024 KabEvents. Tous droits réservés.
            </p>
            <div class="flex space-x-6 mt-4 md:mt-0">
              <a href="#" class="text-accent-200 hover:text-white text-sm transition-colors duration-200">Politique de confidentialité</a>
              <a href="#" class="text-accent-200 hover:text-white text-sm transition-colors duration-200">Conditions d'utilisation</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      user: null,
      mobileMenuOpen: false,
      isAuthenticated: false,
    }
  },
  mounted() {
    this.checkAuthStatus();

    // Listen for storage changes (when user logs in/out in another tab)
    window.addEventListener('storage', this.handleStorageChange);

    // Check auth status periodically (every 5 seconds)
    this.authCheckInterval = setInterval(() => {
      this.checkAuthStatus();
    }, 5000);
  },
  beforeUnmount() {
    window.removeEventListener('storage', this.handleStorageChange);
    if (this.authCheckInterval) {
      clearInterval(this.authCheckInterval);
    }
  },
  methods: {
    async logout() {
      try {
        await this.$http.post('/logout');
        this.clearAuthData();
      } catch (error) {
        console.error('Logout error:', error);
        // Force logout even if API call fails
        this.clearAuthData();
      }
    },
    clearAuthData() {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      delete this.$http.defaults.headers.common['Authorization'];
      this.user = null;
      this.isAuthenticated = false;
      this.mobileMenuOpen = false;

      // Force a re-render
      this.$forceUpdate();

      // Navigate to home
      this.$router.push('/').then(() => {
        // Ensure the state is updated after navigation
        this.$nextTick(() => {
          this.checkAuthStatus();
        });
      });
    },
    checkAuthStatus() {
      const token = localStorage.getItem('auth_token');
      const userData = localStorage.getItem('user');

      if (token && userData) {
        this.isAuthenticated = true;
        this.user = JSON.parse(userData);
        this.$http.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      } else {
        this.isAuthenticated = false;
        this.user = null;
      }
    },
    handleStorageChange(event) {
      if (event.key === 'auth_token' || event.key === 'user') {
        this.checkAuthStatus();
      }
    }
  },
  watch: {
    // Watch for route changes to update auth status
    '$route'() {
      this.checkAuthStatus();
    }
  }
}
</script>
