<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg border-b border-kabyle-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <router-link to="/" class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-blue-500 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-lg">K</span>
              </div>
              <span class="text-xl font-bold text-gray-900">KabEvents</span>
            </router-link>
          </div>

          <div class="flex items-center space-x-4">
            <router-link to="/events" class="text-gray-700 hover:text-orange-600 px-3 py-2 rounded-md text-sm font-medium">
              Événements
            </router-link>

            <div v-if="!isAuthenticated" class="flex items-center space-x-2">
              <router-link to="/login" class="text-gray-700 hover:text-orange-600 px-3 py-2 rounded-md text-sm font-medium">
                Connexion
              </router-link>
              <router-link to="/register" class="btn-primary">
                S'inscrire
              </router-link>
            </div>

            <div v-else class="flex items-center space-x-2">
              <router-link to="/dashboard" class="text-gray-700 hover:text-kabyle-600 px-3 py-2 rounded-md text-sm font-medium">
                Dashboard
              </router-link>
              <button @click="logout" class="text-gray-700 hover:text-red-600 px-3 py-2 rounded-md text-sm font-medium">
                Déconnexion
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main content -->
    <main class="flex-1">
      <router-view />
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 class="text-lg font-semibold mb-4">KabEvents</h3>
            <p class="text-gray-300">
              La plateforme de référence pour les événements culturels kabyles au Canada.
            </p>
          </div>
          <div>
            <h3 class="text-lg font-semibold mb-4">Liens rapides</h3>
            <ul class="space-y-2">
              <li><router-link to="/events" class="text-gray-300 hover:text-white">Événements</router-link></li>
              <li><router-link to="/register" class="text-gray-300 hover:text-white">Devenir organisateur</router-link></li>
            </ul>
          </div>
          <div>
            <h3 class="text-lg font-semibold mb-4">Contact</h3>
            <p class="text-gray-300">
              Email: <EMAIL><br>
              Téléphone: +****************
            </p>
          </div>
        </div>
        <div class="mt-8 pt-8 border-t border-gray-700 text-center">
          <p class="text-gray-300">&copy; 2024 KabEvents. Tous droits réservés.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      user: null,
    }
  },
  computed: {
    isAuthenticated() {
      return !!localStorage.getItem('auth_token');
    }
  },
  methods: {
    async logout() {
      try {
        await this.$http.post('/logout');
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
        delete this.$http.defaults.headers.common['Authorization'];
        this.$router.push('/');
      } catch (error) {
        console.error('Logout error:', error);
        // Force logout even if API call fails
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
        delete this.$http.defaults.headers.common['Authorization'];
        this.$router.push('/');
      }
    }
  },
  mounted() {
    // Check if user is logged in
    const user = localStorage.getItem('user');
    if (user) {
      this.user = JSON.parse(user);
    }
  }
}
</script>
