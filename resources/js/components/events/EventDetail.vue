<template>
  <div class="min-h-screen bg-gray-50">
    <div v-if="loading" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-kabyle-600"></div>
        <p class="mt-2 text-gray-600">Chargement de l'événement...</p>
      </div>
    </div>

    <div v-else-if="event" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Back button -->
      <button @click="$router.go(-1)" class="mb-6 flex items-center text-kabyle-600 hover:text-kabyle-700">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        Retour
      </button>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Event Details -->
        <div class="lg:col-span-2">
          <!-- Event Image -->
          <div class="h-64 md:h-96 bg-gradient-to-r from-kabyle-400 to-amazigh-400 rounded-xl mb-6 flex items-center justify-center">
            <span class="text-white text-4xl font-bold">{{ event.title.substring(0, 2).toUpperCase() }}</span>
          </div>

          <!-- Event Info -->
          <div class="bg-white rounded-xl shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ event.title }}</h1>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div class="flex items-center text-gray-600">
                <svg class="w-5 h-5 mr-3 text-kabyle-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <div>
                  <p class="font-medium">{{ formatDate(event.event_date) }}</p>
                  <p class="text-sm">{{ formatTime(event.event_date) }}</p>
                </div>
              </div>

              <div class="flex items-center text-gray-600">
                <svg class="w-5 h-5 mr-3 text-kabyle-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <div>
                  <p class="font-medium">{{ event.location }}</p>
                </div>
              </div>

              <div class="flex items-center text-gray-600">
                <svg class="w-5 h-5 mr-3 text-kabyle-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"></path>
                </svg>
                <div>
                  <p class="font-medium">{{ event.available_tickets }} places disponibles</p>
                  <p class="text-sm">sur {{ event.total_tickets }} places</p>
                </div>
              </div>

              <div class="flex items-center text-gray-600">
                <svg class="w-5 h-5 mr-3 text-kabyle-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"></path>
                </svg>
                <div>
                  <p class="font-medium">Organisé par</p>
                  <p class="text-sm">{{ event.organizer?.name || 'Organisateur' }}</p>
                </div>
              </div>
            </div>

            <div class="border-t pt-6">
              <h2 class="text-xl font-semibold mb-3">Description</h2>
              <p class="text-gray-700 leading-relaxed">{{ event.description }}</p>
            </div>
          </div>
        </div>

        <!-- Booking Card -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-xl shadow-lg p-6 sticky top-8">
            <div class="text-center mb-6">
              <p class="text-3xl font-bold text-kabyle-600">${{ event.ticket_price }}</p>
              <p class="text-gray-600">par billet</p>
            </div>

            <div v-if="!isAuthenticated" class="space-y-4">
              <p class="text-center text-gray-600 mb-4">
                Connectez-vous pour réserver vos billets
              </p>
              <router-link to="/login" class="btn-primary w-full text-center block">
                Se connecter
              </router-link>
              <router-link to="/register" class="btn-secondary w-full text-center block">
                Créer un compte
              </router-link>
            </div>

            <div v-else-if="event.available_tickets > 0" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Nombre de billets
                </label>
                <select v-model="quantity" class="input-field">
                  <option v-for="n in Math.min(10, event.available_tickets)" :key="n" :value="n">
                    {{ n }} billet{{ n > 1 ? 's' : '' }}
                  </option>
                </select>
              </div>

              <div class="border-t pt-4">
                <div class="flex justify-between mb-2">
                  <span>{{ quantity }} × ${{ event.ticket_price }}</span>
                  <span class="font-semibold">${{ (quantity * parseFloat(event.ticket_price)).toFixed(2) }}</span>
                </div>
                <div class="flex justify-between text-lg font-bold">
                  <span>Total</span>
                  <span>${{ (quantity * parseFloat(event.ticket_price)).toFixed(2) }}</span>
                </div>
              </div>

              <button
                @click="reserveTickets"
                :disabled="reserving"
                class="btn-primary w-full flex justify-center items-center"
              >
                <svg v-if="reserving" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ reserving ? 'Réservation...' : 'Réserver maintenant' }}
              </button>
            </div>

            <div v-else class="text-center">
              <p class="text-kabyle-600 font-semibold mb-4">Événement complet</p>
              <p class="text-gray-600 text-sm">
                Cet événement n'a plus de places disponibles.
              </p>
            </div>

            <div v-if="error" class="mt-4 bg-kabyle-50 border border-kabyle-200 text-kabyle-700 px-4 py-3 rounded">
              {{ error }}
            </div>

            <div v-if="success" class="mt-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
              {{ success }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Événement non trouvé</h2>
        <p class="text-gray-600 mb-4">L'événement que vous recherchez n'existe pas.</p>
        <router-link to="/events" class="btn-primary">
          Voir tous les événements
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EventDetail',
  props: ['id'],
  data() {
    return {
      event: null,
      loading: true,
      quantity: 1,
      reserving: false,
      error: null,
      success: null,
    }
  },
  computed: {
    isAuthenticated() {
      return !!localStorage.getItem('auth_token');
    }
  },
  methods: {
    async fetchEvent() {
      try {
        const response = await this.$http.get(`/events/${this.id}`);
        this.event = response.data.data || response.data;
      } catch (error) {
        console.error('Error fetching event:', error);
        this.event = null;
      } finally {
        this.loading = false;
      }
    },
    async reserveTickets() {
      this.reserving = true;
      this.error = null;
      this.success = null;

      try {
        const response = await this.$http.post(`/events/${this.id}/reserve`, {
          quantity: this.quantity
        });

        this.success = 'Réservation effectuée avec succès ! Vous recevrez un email de confirmation.';

        // Update available tickets
        this.event.available_tickets -= this.quantity;

        // Redirect to dashboard after a delay
        setTimeout(() => {
          this.$router.push('/dashboard');
        }, 2000);

      } catch (error) {
        if (error.response && error.response.data) {
          this.error = error.response.data.message || 'Erreur lors de la réservation';
        } else {
          this.error = 'Erreur lors de la réservation. Veuillez réessayer.';
        }
      } finally {
        this.reserving = false;
      }
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    },
    formatTime(dateString) {
      const date = new Date(dateString);
      return date.toLocaleTimeString('fr-CA', {
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  },
  mounted() {
    this.fetchEvent();
  },
  watch: {
    id() {
      this.fetchEvent();
    }
  }
}
</script>
