<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 class="text-3xl font-bold text-gray-900">Événements culturels kabyles</h1>
        <p class="mt-2 text-gray-600">Découvrez tous les événements à venir</p>
      </div>
    </div>

    <!-- Filters -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Rechercher</label>
            <input
              v-model="filters.search"
              type="text"
              placeholder="Nom de l'événement..."
              class="input-field"
              @input="filterEvents"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Ville</label>
            <select v-model="filters.city" class="input-field" @change="filterEvents">
              <option value="">Toutes les villes</option>
              <option value="Toronto">Toronto</option>
              <option value="Montréal">Montréal</option>
              <option value="Vancouver">Vancouver</option>
              <option value="Calgary">Calgary</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Prix max</label>
            <select v-model="filters.maxPrice" class="input-field" @change="filterEvents">
              <option value="">Tous les prix</option>
              <option value="50">Moins de 50$</option>
              <option value="75">Moins de 75$</option>
              <option value="100">Moins de 100$</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Trier par</label>
            <select v-model="filters.sortBy" class="input-field" @change="filterEvents">
              <option value="date">Date</option>
              <option value="price">Prix</option>
              <option value="title">Nom</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Events Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
      <div v-if="loading" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-kabyle-600"></div>
        <p class="mt-2 text-gray-600">Chargement des événements...</p>
      </div>

      <div v-else-if="filteredEvents.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun événement trouvé</h3>
        <p class="mt-1 text-sm text-gray-500">Essayez de modifier vos critères de recherche.</p>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div v-for="event in filteredEvents" :key="event.id" class="card hover:shadow-xl transition duration-300">
          <!-- Event Image Placeholder -->
          <div class="h-48 bg-gradient-to-r from-kabyle-400 to-amazigh-400 rounded-lg mb-4 flex items-center justify-center">
            <span class="text-white text-2xl font-bold">{{ event.title.substring(0, 2).toUpperCase() }}</span>
          </div>

          <!-- Event Info -->
          <div class="space-y-3">
            <h3 class="text-xl font-semibold text-gray-900">{{ event.title }}</h3>

            <p class="text-gray-600 text-sm line-clamp-3">{{ event.description }}</p>

            <div class="flex items-center text-sm text-gray-500">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              {{ formatDate(event.event_date) }}
            </div>

            <div class="flex items-center text-sm text-gray-500">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              {{ event.location }}
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                {{ event.available_tickets }} places disponibles
              </div>
              <span class="text-2xl font-bold text-kabyle-600">${{ event.ticket_price }}</span>
            </div>

            <router-link
              :to="`/events/${event.id}`"
              class="btn-primary w-full text-center block"
            >
              Voir détails
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Events',
  data() {
    return {
      events: [],
      filteredEvents: [],
      loading: true,
      filters: {
        search: '',
        city: '',
        maxPrice: '',
        sortBy: 'date',
      }
    }
  },
  methods: {
    async fetchEvents() {
      try {
        const response = await this.$http.get('/events');
        this.events = response.data.data || response.data;
        this.filteredEvents = [...this.events];
        this.filterEvents();
      } catch (error) {
        console.error('Error fetching events:', error);
      } finally {
        this.loading = false;
      }
    },
    filterEvents() {
      let filtered = [...this.events];

      // Search filter
      if (this.filters.search) {
        filtered = filtered.filter(event =>
          event.title.toLowerCase().includes(this.filters.search.toLowerCase()) ||
          event.description.toLowerCase().includes(this.filters.search.toLowerCase())
        );
      }

      // City filter
      if (this.filters.city) {
        filtered = filtered.filter(event =>
          event.location.includes(this.filters.city)
        );
      }

      // Price filter
      if (this.filters.maxPrice) {
        filtered = filtered.filter(event =>
          parseFloat(event.ticket_price) <= parseFloat(this.filters.maxPrice)
        );
      }

      // Sort
      filtered.sort((a, b) => {
        switch (this.filters.sortBy) {
          case 'price':
            return parseFloat(a.ticket_price) - parseFloat(b.ticket_price);
          case 'title':
            return a.title.localeCompare(b.title);
          case 'date':
          default:
            return new Date(a.event_date) - new Date(b.event_date);
        }
      });

      this.filteredEvents = filtered;
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  },
  mounted() {
    this.fetchEvents();
  }
}
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
