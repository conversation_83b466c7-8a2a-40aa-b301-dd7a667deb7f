<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Finaliser votre commande</h1>
        <p class="text-gray-600 mt-2">Complétez votre achat en toute sécurité</p>
      </div>

      <!-- Progress Steps -->
      <div class="mb-8">
        <div class="flex items-center justify-center space-x-4">
          <div class="flex items-center">
            <div :class="step >= 1 ? 'bg-primary-600 text-white' : 'bg-gray-300 text-gray-600'"
                 class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
              1
            </div>
            <span class="ml-2 text-sm font-medium text-gray-900">Détails</span>
          </div>
          <div class="w-16 h-0.5 bg-gray-300"></div>
          <div class="flex items-center">
            <div :class="step >= 2 ? 'bg-primary-600 text-white' : 'bg-gray-300 text-gray-600'"
                 class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
              2
            </div>
            <span class="ml-2 text-sm font-medium text-gray-900">Paiement</span>
          </div>
          <div class="w-16 h-0.5 bg-gray-300"></div>
          <div class="flex items-center">
            <div :class="step >= 3 ? 'bg-primary-600 text-white' : 'bg-gray-300 text-gray-600'"
                 class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
              3
            </div>
            <span class="ml-2 text-sm font-medium text-gray-900">Confirmation</span>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
          <!-- Step 1: Order Details -->
          <div v-if="step === 1" class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-6">Détails de votre commande</h2>

            <!-- Event Info -->
            <div class="border rounded-lg p-4 mb-6">
              <div class="flex items-start space-x-4">
                <div class="w-16 h-16 bg-gradient-to-r from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold">{{ event?.title?.substring(0, 2).toUpperCase() }}</span>
                </div>
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900">{{ event?.title }}</h3>
                  <p class="text-gray-600 text-sm">{{ event?.location }}</p>
                  <p class="text-gray-600 text-sm">{{ formatDate(event?.event_date) }}</p>
                </div>
              </div>
            </div>

            <!-- Quantity Selection -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Nombre de billets
              </label>
              <select v-model="orderData.quantity" class="input-field max-w-xs">
                <option v-for="n in Math.min(10, event?.available_tickets || 1)" :key="n" :value="n">
                  {{ n }} billet{{ n > 1 ? 's' : '' }}
                </option>
              </select>
            </div>

            <!-- Customer Info -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informations personnelles</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Prénom</label>
                  <input v-model="orderData.firstName" type="text" class="input-field" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Nom</label>
                  <input v-model="orderData.lastName" type="text" class="input-field" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input v-model="orderData.email" type="email" class="input-field" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                  <input v-model="orderData.phone" type="tel" class="input-field">
                </div>
              </div>
            </div>

            <div class="flex justify-end mt-6">
              <button @click="nextStep" class="btn-primary">
                Continuer vers le paiement
              </button>
            </div>
          </div>

          <!-- Step 2: Payment -->
          <div v-if="step === 2" class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-6">Informations de paiement</h2>

            <StripePayment
              :amount="totalAmount"
              :event-id="eventId"
              :quantity="orderData.quantity"
              @payment-success="handlePaymentSuccess"
            />

            <div class="flex justify-between mt-6">
              <button @click="prevStep" class="btn-outline">
                Retour
              </button>
            </div>
          </div>

          <!-- Step 3: Confirmation -->
          <div v-if="step === 3" class="bg-white rounded-lg shadow p-6 text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Paiement confirmé !</h2>
            <p class="text-gray-600 mb-6">Votre réservation a été effectuée avec succès.</p>

            <div class="bg-gray-50 rounded-lg p-4 mb-6">
              <p class="text-sm text-gray-600">Numéro de confirmation</p>
              <p class="text-lg font-bold text-gray-900">{{ confirmationNumber }}</p>
            </div>

            <div class="space-y-3">
              <button @click="downloadTicket" class="btn-primary w-full">
                Télécharger les billets
              </button>
              <router-link to="/dashboard" class="btn-outline w-full block text-center">
                Voir mes réservations
              </router-link>
            </div>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow p-6 sticky top-8">
            <h3 class="text-lg font-semibold mb-4">Résumé de la commande</h3>

            <div class="space-y-3 mb-4">
              <div class="flex justify-between">
                <span>{{ orderData.quantity }} × ${{ event?.ticket_price }}</span>
                <span>${{ subtotal }}</span>
              </div>
              <div class="flex justify-between">
                <span>Frais de service</span>
                <span>${{ serviceFee }}</span>
              </div>
              <div class="border-t pt-3 flex justify-between font-bold text-lg">
                <span>Total</span>
                <span>${{ totalAmount }}</span>
              </div>
            </div>

            <div class="text-xs text-gray-500">
              <p>• Billets électroniques envoyés par email</p>
              <p>• Remboursement possible jusqu'à 24h avant l'événement</p>
              <p>• Support client disponible 24/7</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StripePayment from '../payment/StripePayment.vue';

export default {
  name: 'Checkout',
  components: {
    StripePayment
  },
  props: ['eventId'],
  data() {
    return {
      step: 1,
      event: null,
      loading: true,
      processing: false,
      confirmationNumber: null,
      orderData: {
        quantity: 1,
        firstName: '',
        lastName: '',
        email: '',
        phone: ''
      },
      paymentResult: null
    }
  },
  computed: {
    subtotal() {
      return (this.orderData.quantity * parseFloat(this.event?.ticket_price || 0)).toFixed(2);
    },
    serviceFee() {
      return (parseFloat(this.subtotal) * 0.05).toFixed(2); // 5% service fee
    },
    totalAmount() {
      return (parseFloat(this.subtotal) + parseFloat(this.serviceFee)).toFixed(2);
    }
  },
  methods: {
    async fetchEvent() {
      try {
        const response = await this.$http.get(`/events/${this.eventId}`);
        this.event = response.data.data || response.data;

        // Pre-fill user data if available
        const userData = localStorage.getItem('user');
        if (userData) {
          const user = JSON.parse(userData);
          this.orderData.firstName = user.name?.split(' ')[0] || '';
          this.orderData.lastName = user.name?.split(' ').slice(1).join(' ') || '';
          this.orderData.email = user.email || '';
        }

        // Get quantity from localStorage if available
        const savedQuantity = localStorage.getItem('checkout_quantity');
        if (savedQuantity) {
          this.orderData.quantity = parseInt(savedQuantity);
          localStorage.removeItem('checkout_quantity'); // Clean up
        }
      } catch (error) {
        console.error('Error fetching event:', error);
        this.$router.push('/events');
      } finally {
        this.loading = false;
      }
    },
    nextStep() {
      if (this.validateStep()) {
        this.step++;
      }
    },
    prevStep() {
      this.step--;
    },
    validateStep() {
      if (this.step === 1) {
        return this.orderData.firstName && this.orderData.lastName && this.orderData.email;
      }
      return true;
    },
    async handlePaymentSuccess(paymentResult) {
      this.paymentResult = paymentResult;

      try {
        // Payment was successful, create reservation if not already done by Stripe
        if (paymentResult.method !== 'stripe') {
          const response = await this.$http.post(`/events/${this.eventId}/reserve`, {
            quantity: this.orderData.quantity,
            customer_info: this.orderData,
            payment_info: paymentResult
          });

          this.confirmationNumber = response.data.data.payment_reference || 'CONF_' + Date.now();
        } else {
          // For Stripe, reservation was already created in the confirm step
          this.confirmationNumber = paymentResult.paymentIntentId;
        }

        this.step = 3;

      } catch (error) {
        console.error('Reservation creation error:', error);
        alert('Erreur lors de la création de la réservation. Veuillez contacter le support.');
      }
    },
    downloadTicket() {
      // TODO: Implement ticket download
      alert('Téléchargement des billets (à implémenter)');
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  },
  mounted() {
    this.fetchEvent();
  }
}
</script>
