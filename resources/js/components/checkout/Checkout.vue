<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Finaliser votre commande</h1>
        <p class="text-gray-600 mt-2">Complétez votre achat en toute sécurité</p>
      </div>

      <!-- Progress Steps -->
      <div class="mb-8">
        <div class="flex items-center justify-center space-x-4">
          <div class="flex items-center">
            <div :class="step >= 1 ? 'bg-primary-600 text-white' : 'bg-gray-300 text-gray-600'"
                 class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
              1
            </div>
            <span class="ml-2 text-sm font-medium text-gray-900">Détails</span>
          </div>
          <div class="w-16 h-0.5 bg-gray-300"></div>
          <div class="flex items-center">
            <div :class="step >= 2 ? 'bg-primary-600 text-white' : 'bg-gray-300 text-gray-600'"
                 class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
              2
            </div>
            <span class="ml-2 text-sm font-medium text-gray-900">Paiement</span>
          </div>
          <div class="w-16 h-0.5 bg-gray-300"></div>
          <div class="flex items-center">
            <div :class="step >= 3 ? 'bg-primary-600 text-white' : 'bg-gray-300 text-gray-600'"
                 class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
              3
            </div>
            <span class="ml-2 text-sm font-medium text-gray-900">Confirmation</span>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
          <!-- Step 1: Order Details -->
          <div v-if="step === 1" class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-6">Détails de votre commande</h2>

            <!-- Event Info -->
            <div class="border rounded-lg p-4 mb-6">
              <div class="flex items-start space-x-4">
                <div class="w-16 h-16 bg-gradient-to-r from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold">{{ event?.title?.substring(0, 2).toUpperCase() }}</span>
                </div>
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900">{{ event?.title }}</h3>
                  <p class="text-gray-600 text-sm">{{ event?.location }}</p>
                  <p class="text-gray-600 text-sm">{{ formatDate(event?.event_date) }}</p>
                </div>
              </div>
            </div>

            <!-- Quantity Selection -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Nombre de billets
              </label>
              <select v-model="orderData.quantity" class="input-field max-w-xs">
                <option v-for="n in Math.min(10, event?.available_tickets || 1)" :key="n" :value="n">
                  {{ n }} billet{{ n > 1 ? 's' : '' }}
                </option>
              </select>
            </div>

            <!-- Customer Info -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informations personnelles</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Prénom</label>
                  <input v-model="orderData.firstName" type="text" class="input-field" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Nom</label>
                  <input v-model="orderData.lastName" type="text" class="input-field" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input v-model="orderData.email" type="email" class="input-field" required>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                  <input v-model="orderData.phone" type="tel" class="input-field">
                </div>
              </div>
            </div>

            <div class="flex justify-end mt-6">
              <button @click="nextStep" class="btn-primary">
                Continuer vers le paiement
              </button>
            </div>
          </div>

          <!-- Step 2: Payment -->
          <div v-if="step === 2" class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-6">Informations de paiement</h2>

            <!-- Payment Methods -->
            <div class="mb-6">
              <h3 class="text-lg font-medium mb-4">Méthode de paiement</h3>
              <div class="space-y-3">
                <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input v-model="paymentData.method" type="radio" value="card" class="mr-3">
                  <div class="flex items-center">
                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                    <span class="font-medium">Carte de crédit/débit</span>
                  </div>
                </label>
                <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input v-model="paymentData.method" type="radio" value="paypal" class="mr-3">
                  <div class="flex items-center">
                    <svg class="w-6 h-6 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.254-.93 4.778-4.005 6.430-7.97 6.430h-1.758a.525.525 0 0 0-.518.439l-1.24 7.858h2.302c.456 0 .845-.334.923-.773l.738-4.68a.525.525 0 0 1 .518-.439h1.05c3.365 0 6.001-1.369 6.766-5.32.319-1.648.141-3.027-.663-4.228z"/>
                    </svg>
                    <span class="font-medium">PayPal</span>
                  </div>
                </label>
              </div>
            </div>

            <!-- Card Details -->
            <div v-if="paymentData.method === 'card'" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Numéro de carte</label>
                <input v-model="paymentData.cardNumber" type="text" placeholder="1234 5678 9012 3456"
                       class="input-field" maxlength="19" @input="formatCardNumber">
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Date d'expiration</label>
                  <input v-model="paymentData.expiryDate" type="text" placeholder="MM/AA"
                         class="input-field" maxlength="5" @input="formatExpiryDate">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">CVV</label>
                  <input v-model="paymentData.cvv" type="text" placeholder="123"
                         class="input-field" maxlength="4">
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Nom sur la carte</label>
                <input v-model="paymentData.cardName" type="text" class="input-field">
              </div>
            </div>

            <div class="flex justify-between mt-6">
              <button @click="prevStep" class="btn-outline">
                Retour
              </button>
              <button @click="processPayment" :disabled="processing" class="btn-primary">
                <span v-if="processing" class="loading-spinner w-4 h-4 mr-2"></span>
                {{ processing ? 'Traitement...' : `Payer $${totalAmount}` }}
              </button>
            </div>
          </div>

          <!-- Step 3: Confirmation -->
          <div v-if="step === 3" class="bg-white rounded-lg shadow p-6 text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Paiement confirmé !</h2>
            <p class="text-gray-600 mb-6">Votre réservation a été effectuée avec succès.</p>

            <div class="bg-gray-50 rounded-lg p-4 mb-6">
              <p class="text-sm text-gray-600">Numéro de confirmation</p>
              <p class="text-lg font-bold text-gray-900">{{ confirmationNumber }}</p>
            </div>

            <div class="space-y-3">
              <button @click="downloadTicket" class="btn-primary w-full">
                Télécharger les billets
              </button>
              <router-link to="/dashboard" class="btn-outline w-full block text-center">
                Voir mes réservations
              </router-link>
            </div>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow p-6 sticky top-8">
            <h3 class="text-lg font-semibold mb-4">Résumé de la commande</h3>

            <div class="space-y-3 mb-4">
              <div class="flex justify-between">
                <span>{{ orderData.quantity }} × ${{ event?.ticket_price }}</span>
                <span>${{ subtotal }}</span>
              </div>
              <div class="flex justify-between">
                <span>Frais de service</span>
                <span>${{ serviceFee }}</span>
              </div>
              <div class="border-t pt-3 flex justify-between font-bold text-lg">
                <span>Total</span>
                <span>${{ totalAmount }}</span>
              </div>
            </div>

            <div class="text-xs text-gray-500">
              <p>• Billets électroniques envoyés par email</p>
              <p>• Remboursement possible jusqu'à 24h avant l'événement</p>
              <p>• Support client disponible 24/7</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Checkout',
  props: ['eventId'],
  data() {
    return {
      step: 1,
      event: null,
      loading: true,
      processing: false,
      confirmationNumber: null,
      orderData: {
        quantity: 1,
        firstName: '',
        lastName: '',
        email: '',
        phone: ''
      },
      paymentData: {
        method: 'card',
        cardNumber: '',
        expiryDate: '',
        cvv: '',
        cardName: ''
      }
    }
  },
  computed: {
    subtotal() {
      return (this.orderData.quantity * parseFloat(this.event?.ticket_price || 0)).toFixed(2);
    },
    serviceFee() {
      return (parseFloat(this.subtotal) * 0.05).toFixed(2); // 5% service fee
    },
    totalAmount() {
      return (parseFloat(this.subtotal) + parseFloat(this.serviceFee)).toFixed(2);
    }
  },
  methods: {
    async fetchEvent() {
      try {
        const response = await this.$http.get(`/events/${this.eventId}`);
        this.event = response.data.data || response.data;

        // Pre-fill user data if available
        const userData = localStorage.getItem('user');
        if (userData) {
          const user = JSON.parse(userData);
          this.orderData.firstName = user.name?.split(' ')[0] || '';
          this.orderData.lastName = user.name?.split(' ').slice(1).join(' ') || '';
          this.orderData.email = user.email || '';
        }

        // Get quantity from localStorage if available
        const savedQuantity = localStorage.getItem('checkout_quantity');
        if (savedQuantity) {
          this.orderData.quantity = parseInt(savedQuantity);
          localStorage.removeItem('checkout_quantity'); // Clean up
        }
      } catch (error) {
        console.error('Error fetching event:', error);
        this.$router.push('/events');
      } finally {
        this.loading = false;
      }
    },
    nextStep() {
      if (this.validateStep()) {
        this.step++;
      }
    },
    prevStep() {
      this.step--;
    },
    validateStep() {
      if (this.step === 1) {
        return this.orderData.firstName && this.orderData.lastName && this.orderData.email;
      }
      return true;
    },
    formatCardNumber(event) {
      let value = event.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
      let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
      this.paymentData.cardNumber = formattedValue;
    },
    formatExpiryDate(event) {
      let value = event.target.value.replace(/\D/g, '');
      if (value.length >= 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
      }
      this.paymentData.expiryDate = value;
    },
    async processPayment() {
      this.processing = true;

      try {
        // Simulate payment processing
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Create reservation
        const response = await this.$http.post(`/events/${this.eventId}/reserve`, {
          quantity: this.orderData.quantity,
          customer_info: this.orderData,
          payment_info: this.paymentData
        });

        this.confirmationNumber = response.data.data.payment_reference || 'CONF_' + Date.now();
        this.step = 3;

      } catch (error) {
        console.error('Payment error:', error);
        alert('Erreur lors du paiement. Veuillez réessayer.');
      } finally {
        this.processing = false;
      }
    },
    downloadTicket() {
      // TODO: Implement ticket download
      alert('Téléchargement des billets (à implémenter)');
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  },
  mounted() {
    this.fetchEvent();
  }
}
</script>
