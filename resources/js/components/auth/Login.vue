<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-12 bg-gradient-to-r from-kabyle-500 to-amazigh-500 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-xl">K</span>
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Connexion à votre compte
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Ou
          <router-link to="/register" class="font-medium text-kabyle-600 hover:text-kabyle-500">
            créez un nouveau compte
          </router-link>
        </p>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="login">
        <div v-if="error" class="bg-kabyle-50 border border-kabyle-200 text-kabyle-700 px-4 py-3 rounded">
          {{ error }}
        </div>

        <div class="space-y-4">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
              Adresse email
            </label>
            <input
              id="email"
              v-model="form.email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="input-field mt-1"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              Mot de passe
            </label>
            <input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              autocomplete="current-password"
              required
              class="input-field mt-1"
              placeholder="Votre mot de passe"
            />
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input
              id="remember-me"
              v-model="form.remember"
              name="remember-me"
              type="checkbox"
              class="h-4 w-4 text-kabyle-600 focus:ring-kabyle-500 border-gray-300 rounded"
            />
            <label for="remember-me" class="ml-2 block text-sm text-gray-900">
              Se souvenir de moi
            </label>
          </div>

          <div class="text-sm">
            <a href="#" class="font-medium text-kabyle-600 hover:text-kabyle-500">
              Mot de passe oublié ?
            </a>
          </div>
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading"
            class="btn-primary w-full flex justify-center items-center"
          >
            <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ loading ? 'Connexion...' : 'Se connecter' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      form: {
        email: '',
        password: '',
        remember: false,
      },
      loading: false,
      error: null,
    }
  },
  methods: {
    async login() {
      this.loading = true;
      this.error = null;

      try {
        const response = await this.$http.post('/login', {
          email: this.form.email,
          password: this.form.password,
        });

        // Store token and user data
        localStorage.setItem('auth_token', response.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.user));

        // Set authorization header for future requests
        this.$http.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

        // Redirect to dashboard or intended page
        const redirect = this.$route.query.redirect || '/dashboard';
        this.$router.push(redirect);

      } catch (error) {
        if (error.response && error.response.data) {
          this.error = error.response.data.message || 'Erreur de connexion';
        } else {
          this.error = 'Erreur de connexion. Veuillez réessayer.';
        }
      } finally {
        this.loading = false;
      }
    }
  },
  mounted() {
    // Redirect if already logged in
    if (localStorage.getItem('auth_token')) {
      this.$router.push('/dashboard');
    }
  }
}
</script>
