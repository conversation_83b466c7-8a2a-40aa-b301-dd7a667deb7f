<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-12 bg-gradient-to-r from-kabyle-500 to-amazigh-500 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-xl">K</span>
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Créer votre compte
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Ou
          <router-link to="/login" class="font-medium text-kabyle-600 hover:text-kabyle-500">
            connectez-vous à votre compte existant
          </router-link>
        </p>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="register">
        <div v-if="error" class="bg-kabyle-50 border border-kabyle-200 text-kabyle-700 px-4 py-3 rounded">
          {{ error }}
        </div>

        <div class="space-y-4">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">
              Nom complet
            </label>
            <input
              id="name"
              v-model="form.name"
              name="name"
              type="text"
              autocomplete="name"
              required
              class="input-field mt-1"
              placeholder="Votre nom complet"
            />
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
              Adresse email
            </label>
            <input
              id="email"
              v-model="form.email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="input-field mt-1"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label for="role" class="block text-sm font-medium text-gray-700">
              Type de compte
            </label>
            <select
              id="role"
              v-model="form.role"
              name="role"
              class="input-field mt-1"
            >
              <option value="user">Utilisateur (participer aux événements)</option>
              <option value="organizer">Organisateur (créer des événements)</option>
            </select>
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              Mot de passe
            </label>
            <input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              autocomplete="new-password"
              required
              class="input-field mt-1"
              placeholder="Minimum 8 caractères"
            />
          </div>

          <div>
            <label for="password_confirmation" class="block text-sm font-medium text-gray-700">
              Confirmer le mot de passe
            </label>
            <input
              id="password_confirmation"
              v-model="form.password_confirmation"
              name="password_confirmation"
              type="password"
              autocomplete="new-password"
              required
              class="input-field mt-1"
              placeholder="Répétez votre mot de passe"
            />
          </div>
        </div>

        <div class="flex items-center">
          <input
            id="terms"
            v-model="form.terms"
            name="terms"
            type="checkbox"
            required
            class="h-4 w-4 text-kabyle-600 focus:ring-kabyle-500 border-gray-300 rounded"
          />
          <label for="terms" class="ml-2 block text-sm text-gray-900">
            J'accepte les
            <a href="#" class="text-kabyle-600 hover:text-kabyle-500">conditions d'utilisation</a>
            et la
            <a href="#" class="text-kabyle-600 hover:text-kabyle-500">politique de confidentialité</a>
          </label>
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading"
            class="btn-primary w-full flex justify-center items-center"
          >
            <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ loading ? 'Création...' : 'Créer mon compte' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Register',
  data() {
    return {
      form: {
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'user',
        terms: false,
      },
      loading: false,
      error: null,
    }
  },
  methods: {
    async register() {
      this.loading = true;
      this.error = null;

      try {
        const response = await this.$http.post('/register', {
          name: this.form.name,
          email: this.form.email,
          password: this.form.password,
          password_confirmation: this.form.password_confirmation,
          role: this.form.role,
        });

        // Store token and user data
        localStorage.setItem('auth_token', response.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.user));

        // Set authorization header for future requests
        this.$http.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

        // Redirect to dashboard
        this.$router.push('/dashboard');

      } catch (error) {
        if (error.response && error.response.data) {
          if (error.response.data.errors) {
            // Handle validation errors
            const errors = Object.values(error.response.data.errors).flat();
            this.error = errors.join(', ');
          } else {
            this.error = error.response.data.message || 'Erreur lors de l\'inscription';
          }
        } else {
          this.error = 'Erreur lors de l\'inscription. Veuillez réessayer.';
        }
      } finally {
        this.loading = false;
      }
    }
  },
  mounted() {
    // Redirect if already logged in
    if (localStorage.getItem('auth_token')) {
      this.$router.push('/dashboard');
    }
  }
}
</script>
