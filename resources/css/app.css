@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Inter', sans-serif;
    @apply bg-gradient-to-br from-accent-50 to-primary-50 min-h-screen;
  }

  * {
    @apply scroll-smooth;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 ease-out;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 ease-out;
  }

  .btn-outline {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 ease-out;
  }

  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl hover:shadow-2xl p-8 border border-white/20 transition-all duration-300 ease-out hover:-translate-y-1;
  }

  .card-simple {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl p-6 border border-gray-100/50 transition-all duration-300 ease-out;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-accent-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white/80 backdrop-blur-sm transition-all duration-200;
  }

  .nav-link {
    @apply text-accent-700 hover:text-primary-600 px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:bg-white/50;
  }

  .hero-gradient {
    @apply bg-gradient-to-br from-primary-600 via-primary-700 to-accent-800;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .event-card {
    @apply bg-white rounded-2xl shadow-lg hover:shadow-2xl overflow-hidden transition-all duration-300 ease-out hover:-translate-y-2;
  }

  .event-image {
    @apply h-48 bg-gradient-to-br from-primary-400 via-primary-500 to-secondary-500 flex items-center justify-center transition-all duration-300;
  }

  .price-tag {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-800;
  }

  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-active {
    @apply bg-success-100 text-success-800;
  }

  .status-inactive {
    @apply bg-accent-100 text-accent-800;
  }

  .loading-spinner {
    @apply inline-block animate-spin rounded-full border-4 border-primary-200 border-t-primary-600;
  }
}
