<?php

namespace App\Services;

use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Exception\ApiErrorException;

class StripeService
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Create a payment intent for the given amount
     */
    public function createPaymentIntent($amount, $currency = 'cad', $metadata = [])
    {
        try {
            $paymentIntent = PaymentIntent::create([
                'amount' => $this->convertToStripeAmount($amount, $currency),
                'currency' => $currency,
                'metadata' => $metadata,
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
            ]);

            return [
                'success' => true,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
            ];
        } catch (ApiErrorException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Confirm a payment intent
     */
    public function confirmPaymentIntent($paymentIntentId)
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            
            return [
                'success' => true,
                'status' => $paymentIntent->status,
                'payment_intent' => $paymentIntent,
            ];
        } catch (ApiErrorException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Convert amount to Stripe format (cents)
     */
    private function convertToStripeAmount($amount, $currency)
    {
        // Most currencies use cents (multiply by 100)
        // Some currencies like JPY don't use decimal places
        $zeroDecimalCurrencies = ['jpy', 'krw', 'vnd'];
        
        if (in_array(strtolower($currency), $zeroDecimalCurrencies)) {
            return (int) $amount;
        }
        
        return (int) ($amount * 100);
    }

    /**
     * Convert amount from Stripe format to regular format
     */
    public function convertFromStripeAmount($amount, $currency)
    {
        $zeroDecimalCurrencies = ['jpy', 'krw', 'vnd'];
        
        if (in_array(strtolower($currency), $zeroDecimalCurrencies)) {
            return $amount;
        }
        
        return $amount / 100;
    }
}
