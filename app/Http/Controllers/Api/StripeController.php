<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\StripeService;
use App\Models\Event;
use Illuminate\Http\Request;

class StripeController extends Controller
{
    protected $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Create a payment intent for an event reservation
     */
    public function createPaymentIntent(Request $request, Event $event)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1|max:10',
        ]);

        // Check if event is available
        if (!$event->is_active || $event->available_tickets < $request->quantity) {
            return response()->json([
                'error' => 'Event is not available or insufficient tickets'
            ], 400);
        }

        // Calculate total amount (including service fee)
        $subtotal = $event->ticket_price * $request->quantity;
        $serviceFee = $subtotal * 0.05; // 5% service fee
        $totalAmount = $subtotal + $serviceFee;

        // Create payment intent
        $result = $this->stripeService->createPaymentIntent(
            $totalAmount,
            'cad',
            [
                'event_id' => $event->id,
                'user_id' => $request->user()->id,
                'quantity' => $request->quantity,
                'event_title' => $event->title,
            ]
        );

        if (!$result['success']) {
            return response()->json([
                'error' => 'Failed to create payment intent: ' . $result['error']
            ], 500);
        }

        return response()->json([
            'client_secret' => $result['client_secret'],
            'payment_intent_id' => $result['payment_intent_id'],
            'amount' => $totalAmount,
            'currency' => 'cad',
        ]);
    }

    /**
     * Confirm payment and create reservation
     */
    public function confirmPayment(Request $request)
    {
        $request->validate([
            'payment_intent_id' => 'required|string',
            'event_id' => 'required|exists:events,id',
            'quantity' => 'required|integer|min:1|max:10',
        ]);

        // Confirm payment with Stripe
        $result = $this->stripeService->confirmPaymentIntent($request->payment_intent_id);

        if (!$result['success']) {
            return response()->json([
                'error' => 'Payment confirmation failed: ' . $result['error']
            ], 400);
        }

        if ($result['status'] !== 'succeeded') {
            return response()->json([
                'error' => 'Payment not completed. Status: ' . $result['status']
            ], 400);
        }

        // Payment successful, create reservation
        $event = Event::findOrFail($request->event_id);

        // Create reservation using existing logic
        $reservationRequest = new Request([
            'quantity' => $request->quantity,
            'payment_info' => [
                'method' => 'stripe',
                'payment_intent_id' => $request->payment_intent_id,
            ]
        ]);
        $reservationRequest->setUserResolver(function () use ($request) {
            return $request->user();
        });

        $reservationController = new \App\Http\Controllers\Api\ReservationController();
        return $reservationController->store($reservationRequest, $event);
    }

    /**
     * Get Stripe publishable key
     */
    public function getPublishableKey()
    {
        return response()->json([
            'publishable_key' => config('services.stripe.key')
        ]);
    }
}
