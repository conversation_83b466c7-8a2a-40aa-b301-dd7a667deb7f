<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Reservation;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Support\Str;

class ReservationController extends Controller
{
    /**
     * Store a newly created reservation (book tickets)
     */
    public function store(Request $request, Event $event)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1|max:10',
        ]);

        // Check if event is active and has available tickets
        if (!$event->is_active) {
            return response()->json(['message' => 'Event is not active'], 400);
        }

        if ($event->available_tickets < $request->quantity) {
            return response()->json(['message' => 'Not enough tickets available'], 400);
        }

        // Check if event date is in the future
        if ($event->event_date <= now()) {
            return response()->json(['message' => 'Cannot book tickets for past events'], 400);
        }

        DB::beginTransaction();
        try {
            // Create reservation
            $reservation = new Reservation([
                'user_id' => $request->user()->id,
                'event_id' => $event->id,
                'quantity' => $request->quantity,
                'total_amount' => $event->ticket_price * $request->quantity,
                'status' => 'paid', // For now, we simulate immediate payment
                'payment_method' => 'fictif',
                'payment_reference' => 'PAY_' . Str::random(10),
            ]);

            $reservation->save();

            // Update available tickets
            $event->decrement('available_tickets', $request->quantity);

            // Generate QR code
            $qrCodeData = json_encode([
                'reservation_id' => $reservation->id,
                'event_id' => $event->id,
                'user_id' => $request->user()->id,
                'quantity' => $request->quantity,
                'verification_code' => Str::random(20),
            ]);

            $qrCodePath = 'qr_codes/reservation_' . $reservation->id . '.png';

            // Ensure the qr_codes directory exists
            $qrCodeDir = storage_path('app/public/qr_codes');
            if (!file_exists($qrCodeDir)) {
                mkdir($qrCodeDir, 0755, true);
            }

            // Generate QR code using endroid/qr-code (works with GD)
            $qrCode = new QrCode($qrCodeData);
            $qrCode->setSize(300);

            $writer = new PngWriter();
            $result = $writer->write($qrCode);

            // Save the QR code image
            file_put_contents(storage_path('app/public/' . $qrCodePath), $result->getString());

            $reservation->update([
                'qr_code_path' => $qrCodePath,
                'qr_code_data' => $qrCodeData
            ]);

            // Create payment record
            Payment::create([
                'reservation_id' => $reservation->id,
                'amount' => $reservation->total_amount,
                'provider' => 'fictif',
                'status' => 'completed',
                'transaction_id' => 'TXN_' . Str::random(15),
            ]);

            DB::commit();

            return response()->json([
                'data' => $reservation->load(['event', 'user']),
                'message' => 'Reservation created successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Error creating reservation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's reservations
     */
    public function userReservations(Request $request)
    {
        $reservations = Reservation::with(['event', 'payment'])
            ->where('user_id', $request->user()->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json([
            'data' => $reservations,
            'message' => 'User reservations retrieved successfully'
        ]);
    }

    /**
     * Get reservations for a specific event (for organizers)
     */
    public function eventReservations(Request $request, Event $event)
    {
        // Check if user owns this event
        if ($event->organizer_id !== $request->user()->id && !$request->user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $reservations = Reservation::with(['user', 'payment'])
            ->where('event_id', $event->id)
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        $stats = [
            'total_reservations' => $reservations->total(),
            'total_tickets_sold' => Reservation::where('event_id', $event->id)
                ->where('status', 'paid')
                ->sum('quantity'),
            'total_revenue' => Reservation::where('event_id', $event->id)
                ->where('status', 'paid')
                ->sum('total_amount'),
        ];

        return response()->json([
            'data' => $reservations,
            'stats' => $stats,
            'message' => 'Event reservations retrieved successfully'
        ]);
    }

    /**
     * Get all reservations for admin
     */
    public function adminReservations(Request $request)
    {
        $reservations = Reservation::with(['user', 'event', 'payment'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'data' => $reservations,
            'message' => 'All reservations retrieved successfully'
        ]);
    }

    /**
     * Cancel a reservation
     */
    public function cancel(Request $request, Reservation $reservation)
    {
        // Check if user owns this reservation or is admin
        if ($reservation->user_id !== $request->user()->id && !$request->user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Check if reservation can be cancelled (event is more than 24 hours away)
        if ($reservation->event->event_date <= now()->addDay()) {
            return response()->json([
                'message' => 'Cannot cancel reservation less than 24 hours before event'
            ], 400);
        }

        if ($reservation->status === 'cancelled') {
            return response()->json(['message' => 'Reservation already cancelled'], 400);
        }

        DB::beginTransaction();
        try {
            // Update reservation status
            $reservation->update(['status' => 'cancelled']);

            // Return tickets to available pool
            $reservation->event->increment('available_tickets', $reservation->quantity);

            // Update payment status
            if ($reservation->payment) {
                $reservation->payment->update(['status' => 'cancelled']);
            }

            DB::commit();

            return response()->json([
                'data' => $reservation->load(['event', 'user']),
                'message' => 'Reservation cancelled successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Error cancelling reservation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Reservation $reservation, Request $request)
    {
        // Check if user owns this reservation or is admin/organizer
        if ($reservation->user_id !== $request->user()->id &&
            $reservation->event->organizer_id !== $request->user()->id &&
            !$request->user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json([
            'data' => $reservation->load(['event', 'user', 'payment']),
            'message' => 'Reservation retrieved successfully'
        ]);
    }
}
