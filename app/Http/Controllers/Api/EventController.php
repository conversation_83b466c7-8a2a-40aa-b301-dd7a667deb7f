<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class EventController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Event::with('organizer')
            ->where('is_active', true)
            ->where('event_date', '>', now());

        // Apply filters
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        if ($request->has('city')) {
            $query->where('location', 'like', "%{$request->city}%");
        }

        if ($request->has('max_price')) {
            $query->where('ticket_price', '<=', $request->max_price);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'event_date');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination or limit
        if ($request->has('limit')) {
            $events = $query->limit($request->limit)->get();
        } else {
            $events = $query->paginate(12);
        }

        return response()->json([
            'data' => $events,
            'message' => 'Events retrieved successfully'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'event_date' => 'required|date|after:now',
            'location' => 'required|string|max:255',
            'total_tickets' => 'required|integer|min:1',
            'ticket_price' => 'required|numeric|min:0',
            'image' => 'sometimes|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $event = new Event($request->all());
        $event->organizer_id = $request->user()->id;
        $event->available_tickets = $request->total_tickets;

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('events', 'public');
            $event->image_path = $imagePath;
        }

        $event->save();

        return response()->json([
            'data' => $event->load('organizer'),
            'message' => 'Event created successfully'
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Event $event)
    {
        return response()->json([
            'data' => $event->load('organizer'),
            'message' => 'Event retrieved successfully'
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Event $event)
    {
        // Check if user owns this event
        if ($event->organizer_id !== $request->user()->id && !$request->user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'event_date' => 'sometimes|date|after:now',
            'location' => 'sometimes|string|max:255',
            'total_tickets' => 'sometimes|integer|min:1',
            'ticket_price' => 'sometimes|numeric|min:0',
            'image' => 'sometimes|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'sometimes|boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($event->image_path) {
                Storage::disk('public')->delete($event->image_path);
            }
            $imagePath = $request->file('image')->store('events', 'public');
            $event->image_path = $imagePath;
        }

        // Update available tickets if total tickets changed
        if ($request->has('total_tickets')) {
            $soldTickets = $event->total_tickets - $event->available_tickets;
            $event->available_tickets = $request->total_tickets - $soldTickets;
        }

        $event->update($request->except(['image']));

        return response()->json([
            'data' => $event->load('organizer'),
            'message' => 'Event updated successfully'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Event $event, Request $request)
    {
        // Check if user owns this event
        if ($event->organizer_id !== $request->user()->id && !$request->user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Check if event has reservations
        if ($event->reservations()->where('status', 'paid')->exists()) {
            return response()->json([
                'message' => 'Cannot delete event with paid reservations'
            ], 400);
        }

        // Delete image
        if ($event->image_path) {
            Storage::disk('public')->delete($event->image_path);
        }

        $event->delete();

        return response()->json([
            'message' => 'Event deleted successfully'
        ]);
    }

    /**
     * Get events for the authenticated organizer
     */
    public function organizerEvents(Request $request)
    {
        $events = Event::where('organizer_id', $request->user()->id)
            ->withCount(['reservations as total_reservations'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json([
            'data' => $events,
            'message' => 'Organizer events retrieved successfully'
        ]);
    }

    /**
     * Admin dashboard data
     */
    public function adminDashboard(Request $request)
    {
        $stats = [
            'total_events' => Event::count(),
            'active_events' => Event::where('is_active', true)->count(),
            'upcoming_events' => Event::where('event_date', '>', now())->count(),
            'total_revenue' => Event::join('reservations', 'events.id', '=', 'reservations.event_id')
                ->where('reservations.status', 'paid')
                ->sum('reservations.total_amount'),
        ];

        return response()->json([
            'data' => $stats,
            'message' => 'Admin dashboard data retrieved successfully'
        ]);
    }

    /**
     * Get all events for admin
     */
    public function adminEvents(Request $request)
    {
        $events = Event::with(['organizer', 'reservations'])
            ->withCount(['reservations as total_reservations'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'data' => $events,
            'message' => 'All events retrieved successfully'
        ]);
    }
}
