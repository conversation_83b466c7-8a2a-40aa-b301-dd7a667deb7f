<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Reservation extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'event_id',
        'quantity',
        'total_amount',
        'status',
        'qr_code_path',
        'qr_code_data',
        'payment_method',
        'payment_reference',
    ];

    protected function casts(): array
    {
        return [
            'total_amount' => 'decimal:2',
        ];
    }

    /**
     * The user who made this reservation
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * The event for this reservation
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Payment for this reservation
     */
    public function payment()
    {
        return $this->hasOne(Payment::class);
    }

    /**
     * Check if reservation is paid
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Check if reservation is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }
}
