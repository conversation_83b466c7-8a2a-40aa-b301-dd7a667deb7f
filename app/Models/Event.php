<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'organizer_id',
        'title',
        'description',
        'image_path',
        'event_date',
        'location',
        'total_tickets',
        'available_tickets',
        'ticket_price',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'event_date' => 'datetime',
            'ticket_price' => 'decimal:2',
            'is_active' => 'boolean',
        ];
    }

    /**
     * The organizer of this event
     */
    public function organizer()
    {
        return $this->belongsTo(User::class, 'organizer_id');
    }

    /**
     * Reservations for this event
     */
    public function reservations()
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Check if tickets are available
     */
    public function hasAvailableTickets(): bool
    {
        return $this->available_tickets > 0;
    }

    /**
     * Get sold tickets count
     */
    public function getSoldTicketsAttribute(): int
    {
        return $this->total_tickets - $this->available_tickets;
    }
}
