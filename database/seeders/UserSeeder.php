<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Admin user
        User::create([
            'name' => 'Admin Kabyle',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
        ]);

        // Organizer user
        User::create([
            'name' => 'Organisateur Amazigh',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'organizer',
        ]);

        // Regular user
        User::create([
            'name' => 'Utilisateur Test',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'user',
        ]);

        // Additional test users
        User::factory(10)->create();
    }
}
