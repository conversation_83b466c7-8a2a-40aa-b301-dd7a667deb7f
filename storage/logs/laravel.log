[2025-05-29 17:47:43] local.ERROR: Database file at path [/Applications/AMPPS/www/kabevents/database/database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select exists (select 1 from "main".sqlite_master where name = 'migrations' and type = 'table') as "exists") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [/Applications/AMPPS/www/kabevents/database/database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select exists (select 1 from \"main\".sqlite_master where name = 'migrations' and type = 'table') as \"exists\") at /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/FreshCommand.php(67): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\FreshCommand->{closure:Illuminate\\Database\\Console\\Migrations\\FreshCommand::handle():66}()
#9 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/FreshCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#10 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#11 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#12 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#16 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 /Applications/AMPPS/www/kabevents/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [/Applications/AMPPS/www/kabevents/database/database.sqlite] does not exist. Ensure this is an absolute path to the database. at /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connectors/SQLiteConnector.php:59)
[stacktrace]
#0 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connectors/SQLiteConnector.php(19): Illuminate\\Database\\Connectors\\SQLiteConnector->parseDatabasePath(false)
#1 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#2 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithoutHosts():223}()
#3 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1228): call_user_func(Object(Closure))
#4 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#5 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#6 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#7 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select exists (...', Array)
#8 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#9 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#10 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#11 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#12 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#13 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#14 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/FreshCommand.php(67): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\FreshCommand->{closure:Illuminate\\Database\\Console\\Migrations\\FreshCommand::handle():66}()
#17 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/FreshCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#18 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#19 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#20 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Applications/AMPPS/www/kabevents/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-29 17:48:27] local.ERROR: Database file at path [/Applications/AMPPS/www/kabevents/database/database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: delete from "cache") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [/Applications/AMPPS/www/kabevents/database/database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: delete from \"cache\") at /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#1 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#2 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#3 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(4041): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#4 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#5 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(807): Illuminate\\Cache\\DatabaseStore->flush()
#6 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Cache/Console/ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#7 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#8 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#9 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Cache\\Console\\ClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Applications/AMPPS/www/kabevents/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#21 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [/Applications/AMPPS/www/kabevents/database/database.sqlite] does not exist. Ensure this is an absolute path to the database. at /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connectors/SQLiteConnector.php:59)
[stacktrace]
#0 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connectors/SQLiteConnector.php(19): Illuminate\\Database\\Connectors\\SQLiteConnector->parseDatabasePath(false)
#1 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#2 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithoutHosts():223}()
#3 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1228): call_user_func(Object(Closure))
#4 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(589): Illuminate\\Database\\Connection->getPdo()
#5 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::affectingStatement():581}('delete from \"ca...', Array)
#6 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#7 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#8 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#9 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(4041): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#10 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#11 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(807): Illuminate\\Cache\\DatabaseStore->flush()
#12 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Cache/Console/ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#13 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#14 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#15 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#19 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Cache\\Console\\ClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 /Applications/AMPPS/www/kabevents/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 /Applications/AMPPS/www/kabevents/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 /Applications/AMPPS/www/kabevents/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
